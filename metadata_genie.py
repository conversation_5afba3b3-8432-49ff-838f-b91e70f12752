#!/usr/bin/env python3
"""
Metadata Genie AI

A tool for automatically generating and embedding metadata into images
using advanced vision models.
"""
import sys
import traceback

try:
    from PyQt5.QtWidgets import QApplication
    from metadata_genie.gui.main_window import MainWindow

    app = QApplication(sys.argv)
    app.setApplicationName("Metadata Genie AI")
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
except Exception as e:
    traceback.print_exc()
    print(f'Error: {str(e)}')
    input("Press Enter to exit...")
