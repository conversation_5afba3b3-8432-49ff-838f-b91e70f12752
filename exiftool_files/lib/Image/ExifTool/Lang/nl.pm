#------------------------------------------------------------------------------
# File:         nl.pm
#
# Description:  ExifTool Dutch language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::nl;

use strict;
use vars qw($VERSION);

$VERSION = '1.13';

%Image::ExifTool::Lang::nl::Translate = (
   'AEBAutoCancel' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AELock' => {
      Description => 'AE-vergrendeling',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AELockButton' => {
      Description => 'AE-L/AF-L',
      PrintConv => {
        'AE Lock (hold)' => 'AE-vergrendeling vast',
        'AE Lock Only' => 'AE-vergrendeling',
        'AE-L/AF Area' => 'AE-L/AF veld',
        'AE-L/AF-L/AF Area' => 'AE-L/AF-L/AF veld',
        'AE/AF Lock' => 'AE/AF-vergrendeling',
        'AF Lock Only' => 'AF-vergrendeling',
        'AF-L/AF Area' => 'AF-L/AF veld',
        'AF-ON/AF Area' => 'AF-ON/AF veld',
        'FV Lock' => 'FV-vergrendeling',
        'Focus Area Selection' => 'AF-veld',
      },
    },
   'AEMeteringMode' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'AEProgramMode' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'AF-CPrioritySelection' => {
      Description => 'Selectie AF-C-prioriteit',
      PrintConv => {
        'Focus' => 'Scherpstelling',
        'Release' => 'Ontspannen',
        'Release + Focus' => 'Ontspannen + scherpstelling',
      },
    },
   'AF-OnForMB-D10' => {
      Description => 'Functie AF-ON-knop MBD10',
      PrintConv => {
        'AE Lock (hold)' => 'AE-vergrendeling (vast)',
        'AE Lock (reset on release)' => 'AE-vergr. (herstel na ontspan.)',
        'AE Lock Only' => 'AE-vergrendeling',
        'AE/AF Lock' => 'AE/AF-vergrendeling',
        'AF Lock Only' => 'AF-vergrendeling',
        'AF-On' => 'AF-ON',
        'Same as FUNC Button' => 'Zelfde als FUNC.-knop',
      },
    },
   'AF-SPrioritySelection' => {
      Description => 'Selectie AF-S-prioriteit',
      PrintConv => {
        'Focus' => 'Scherpstelling',
        'Release' => 'Ontspannen',
      },
    },
   'AFActivation' => {
      Description => 'AF activering',
      PrintConv => {
        'AF-On Only' => 'Alleen AF-ON',
        'Shutter/AF-On' => 'Ontspanknop/AF-ON',
      },
    },
   'AFAreaIllumination' => {
      Description => 'AF-veld verlichting',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AFAreaMode' => {
      Description => 'AF-veldstand',
      PrintConv => {
        'Auto-area' => 'Automatischveld AF',
        'Dynamic Area' => 'Dynamisch veld',
        'Single Area' => 'Enkelveld',
      },
    },
   'AFAreaModeSetting' => {
      Description => 'AF-veldstand',
      PrintConv => {
        'Closest Subject' => 'Dichtstbz. onderw.',
        'Dynamic Area' => 'Dynamisch veld',
        'Single Area' => 'Enkelveld',
      },
    },
   'AFAssist' => {
      Description => 'AF-hulpverlichting',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AFPoint' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'AFPointActivationArea' => {
      PrintConv => {
        'Standard' => 'Standaard',
      },
    },
   'AFPointBrightness' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'AFPointDisplayDuringFocus' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AFPointIllumination' => {
      Description => 'Verlichting scherpstelpunt',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AFPointMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'AFPointRegistration' => {
      PrintConv => {
        'Automatic' => 'Automatisch',
      },
    },
   'AFPointSelected' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'AFPointSelected2' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'AFPointSelection' => {
      Description => 'Selectie scherpstelpunt',
      PrintConv => {
        '11 Points' => '11 punten',
        '51 Points' => '51 punten (3D-tracking)',
      },
    },
   'AFPointSelectionMethod' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'AFPointsInFocus' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'AFPointsUnknown2' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'AIServoTrackingSensitivity' => {
      PrintConv => {
        'Standard' => 'Standaard',
      },
    },
   'APEVersion' => 'APE versie',
   'ARMIdentifier' => 'ARM herkenningscode',
   'ARMVersion' => 'ARM versie',
   'ActionAdvised' => {
      Description => 'Actie advies',
      PrintConv => {
        'Object Append' => 'Object toevoegen',
        'Object Kill' => 'Object verwijderen',
        'Object Reference' => 'Object referentie',
        'Object Replace' => 'Object vervangen',
        'Ojbect Append' => 'Object toevoegen',
      },
    },
   'ActiveD-Lighting' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ActiveD-LightingMode' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
      },
    },
   'AddAspectRatioInfo' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'AddOriginalDecisionData' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AdultContentWarning' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'AdvancedRaw' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AlphaByteCount' => 'Aantal alphagegevens in bytes',
   'AlphaDataDiscard' => {
      Description => 'Afgedankte alphagegevens',
      PrintConv => {
        'Flexbits Discarded' => 'Afgedankte flexbits',
        'Full Resolution' => 'Volledige resolutie',
        'HighPass Frequency Data Discarded' => 'Afgedankte hoogdoorlaat frequentiegegevens',
        'Highpass and LowPass Frequency Data Discarded' => 'Afgedankte hoogdoorlaat en laagdoorlaat frequentiegegevens',
      },
    },
   'AlphaOffset' => 'Alphaverplaatsing',
   'Anti-Blur' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'Aperture' => 'Diafragma',
   'ApertureValue' => 'Diafragma',
   'ApplicationRecordVersion' => 'Gegevensversie',
   'Artist' => 'Maker van de afbeelding',
   'AssistButtonFunction' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'Audio' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'AudioDuration' => 'Audio duur',
   'AudioOutcue' => 'Audio eindaftiteling',
   'AudioSamplingRate' => 'Audio bemonsteringssnelheid',
   'AudioSamplingResolution' => 'Audio bemonsteringsresolutie',
   'AudioType' => {
      Description => 'Type audio',
      PrintConv => {
        'Mono Actuality' => 'Actualiteit (mono (1 kanaal) audio)',
        'Mono Music' => 'Muziek, zelf verstuurd (mono (1 kanaal) audio)',
        'Mono Question and Answer Session' => 'Vraag en antwoord sessie (mono (1 kanaal) audio)',
        'Mono Raw Sound' => 'Ruwe geluid (mono (1 kanaal) audio)',
        'Mono Response to a Question' => 'Beantwoord een vraag (mono (1 kanaal) audio)',
        'Mono Scener' => 'Toneel (mono (1 kanaal) audio)',
        'Mono Voicer' => 'Stem (mono (1 kanaals) audio)',
        'Mono Wrap' => 'Wrap (mono (1 kanaals) audio)',
        'Stereo Actuality' => 'Actualiteit (stereo (2 kanalen) audio)',
        'Stereo Music' => 'Muziek, zelf verstuurd (stereo (2 kanalen) audio)',
        'Stereo Question and Answer Session' => 'Vraag en antwoord sessie (stereo (2 kanalen) audio)',
        'Stereo Raw Sound' => 'Ruwe geluid (stereo (2 kanalen) audio)',
        'Stereo Response to a Question' => 'Beantwoord een vraag (stereo (2 kanalen) audio)',
        'Stereo Scener' => 'Toneel (stereo (2 kanaals) audio)',
        'Stereo Voicer' => 'Stem (stereo (2 kanaals) audio)',
        'Stereo Wrap' => 'Wrap (stereo (2 kanaals) audio)',
        'Text Only' => 'Alleen tekst (geen objectgegevens)',
      },
    },
   'Author' => 'Auteur',
   'AuthorsPosition' => 'Positie van de auteur',
   'AutoAperture' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoBracketModeM' => {
      Description => 'Auto bracketing (M-stand)',
      PrintConv => {
        'Flash Only' => 'Alleen flits',
        'Flash/Aperture' => 'Flits/diafragma',
        'Flash/Speed' => 'Flits/sluitertijd',
        'Flash/Speed/Aperture' => 'Flits/sluitertijd/diafragma',
      },
    },
   'AutoBracketOrder' => 'Bracketingvolgorde',
   'AutoBracketSet' => {
      Description => 'Inst. voor auto bracketing',
      PrintConv => {
        'AE & Flash' => 'AE & flits',
        'AE Only' => 'Alleen AE',
        'Flash Only' => 'Alleen flits',
        'WB Bracketing' => 'Witbalans bracketing',
      },
    },
   'AutoBracketing' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'AutoBracketingSet' => 'Inst. voor auto bracketing',
   'AutoExposureBracketing' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoFP' => {
      Description => 'Auto FP',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoFocus' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoISO' => {
      Description => 'ISO auto',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoISOMax' => 'ISO auto Maximale',
   'AutoISOMinShutterSpeed' => 'ISO auto Langste sluitertijd',
   'AutoLightingOptimizer' => {
      PrintConv => {
        'Low' => 'Laag',
        'Off' => 'Uit',
        'Standard' => 'Standaard',
      },
    },
   'AutoLightingOptimizerOn' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'AutoRedEye' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'AutoRotate' => {
      PrintConv => {
        'None' => 'Geen',
        'Rotate 180' => '180° (onder/rechts)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
        'n/a' => 'Onbekend',
      },
    },
   'BWMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'BackgroundColorIndicator' => 'Achtergrond kleur indicator',
   'BackgroundColorValue' => 'Achtergrond kleur waarde',
   'BackgroundTiling' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'BatteryLevel' => 'Batterij status',
   'BatteryOrder' => {
      Description => 'Batterijvolgorde',
      PrintConv => {
        'Camera Battery First' => 'Camerabatterij eerst',
        'MB-D10 First' => 'MB-D10 batterijen eerst',
      },
    },
   'Beep' => {
      Description => 'Signaal',
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'BitsPerSample' => 'Aantal Bits per component',
   'BlueMatrixColumn' => 'Blauwe matrixkolom',
   'BlueTRC' => 'Blauwe toon reproductie curve',
   'BlurWarning' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'BracketMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'BracketStep' => {
      PrintConv => {
        '1 EV' => '1 stop',
        '1/3 EV' => '1/3 stop',
        '2/3 EV' => '2/3 stop',
      },
    },
   'Brightness' => 'Helderheid',
   'BrightnessValue' => 'Helderheid',
   'BurstMode' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'By-line' => 'Maker',
   'By-lineTitle' => 'Beroep van de maker',
   'CFAPattern' => 'Kleur filter matrix',
   'CFAPattern2' => 'Kleurfiltermatrix 2',
   'CFARepeatPatternDim' => 'Kleurfiltermatrix grootte',
   'CLModeShootingSpeed' => 'Opnamesnelheid',
   'CMMFlags' => 'CMM vlaggen',
   'CPUType' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => 'Bewolkt',
        'Cool White Fluorescent' => 'Koel wit TL-licht',
        'Day White Fluorescent' => 'Daglicht wit TL-licht',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Fine Weather' => 'Onbewolkt',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'ISO Studio Tungsten' => 'ISO studio kunstlicht (gloeilamp)',
        'Other' => 'Andere lichtbron',
        'Shade' => 'Schaduw',
        'Standard Light A' => 'Standaard licht A',
        'Standard Light B' => 'Standaard licht B',
        'Standard Light C' => 'Standaard licht C',
        'Tungsten (Incandescent)' => 'Kunstlicht (gloeilamp)',
        'Unknown' => 'Onbekend',
        'Warm White Fluorescent' => 'Warm wit TL-licht',
        'White Fluorescent' => 'Wit TL-licht',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => 'Bewolkt',
        'Cool White Fluorescent' => 'Koel wit TL-licht',
        'Day White Fluorescent' => 'Daglicht wit TL-licht',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Fine Weather' => 'Onbewolkt',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'ISO Studio Tungsten' => 'ISO studio kunstlicht (gloeilamp)',
        'Other' => 'Andere lichtbron',
        'Shade' => 'Schaduw',
        'Standard Light A' => 'Standaard licht A',
        'Standard Light B' => 'Standaard licht B',
        'Standard Light C' => 'Standaard licht C',
        'Tungsten (Incandescent)' => 'Kunstlicht (gloeilamp)',
        'Unknown' => 'Onbekend',
        'Warm White Fluorescent' => 'Warm wit TL-licht',
        'White Fluorescent' => 'Wit TL-licht',
      },
    },
   'CameraOrientation' => {
      Description => 'Oriëntatie van de afbeelding',
      PrintConv => {
        'Horizontal (normal)' => '0° (boven/links)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
      },
    },
   'CanonExposureMode' => {
      PrintConv => {
        'Aperture-priority AE' => 'Diafragmaprioriteit',
        'Manual' => 'Handmatig',
        'Shutter speed priority AE' => 'Sluiterprioriteit',
      },
    },
   'CanonFlashMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Caption-Abstract' => 'Titel/Beschrijving',
   'CaptionWriter' => 'Schrijver van het onderschrift',
   'CaptureXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micrometer)',
      },
    },
   'CaptureYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micrometer)',
      },
    },
   'Categories' => 'Categorieën',
   'Category' => 'Categorie',
   'CellLength' => 'Cel lengte',
   'CellWidth' => 'Cel breedte',
   'CenterAFArea' => {
      Description => 'Centrale AF-veld',
      PrintConv => {
        'Normal Zone' => 'Normaal',
        'Wide Zone' => 'Breed',
      },
    },
   'CenterWeightedAreaSize' => {
      Description => 'Grootte meetgebied',
      PrintConv => {
        'Average' => 'Gemiddeld',
      },
    },
   'CharacterSet' => 'Tekenset',
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
      },
    },
   'City' => 'Plaats',
   'ClassifyState' => 'Rangschik status',
   'CodedCharacterSet' => 'Gecodeerde character set',
   'ColorAberrationControl' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ColorAdjustmentMode' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ColorBalanceAdj' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ColorBooster' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ColorEffect' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ColorFilter' => {
      Description => 'Kleurfilter',
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ColorMode' => {
      Description => 'Instellingen',
      PrintConv => {
        'Autumn Leaves' => 'Herfstbladeren',
        'B&W' => 'Zwart-wit',
        'Clear' => 'Doorzichtig',
        'Deep' => 'Diep',
        'Evening' => 'Avond',
        'Landscape' => 'Landschap',
        'Light' => 'Licht',
        'Neutral' => 'Neutraal',
        'Night View' => 'Nacht',
        'Night View/Portrait' => 'Nachtportret',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
        'Sunset' => 'Zonsondergang',
        'Vivid' => 'Levendige kleuren',
      },
    },
   'ColorMoireReduction' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ColorMoireReductionMode' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
      },
    },
   'ColorSpace' => {
      Description => 'Kleur ruimte',
      PrintConv => {
        'ICC Profile' => 'ICC-profiel',
        'Uncalibrated' => 'Niet vastgelegd',
      },
    },
   'ColorSpaceData' => 'Gegevenskleurenruimte',
   'ColorTable' => 'Kleur tabel',
   'ColorTemperature' => 'Kleurtemperatuur',
   'ColorTone' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'CommandDials' => {
      Description => 'Instelschijven',
      PrintConv => {
        'Reversed (Main Aperture, Sub Shutter)' => 'Verwissel hoofd/sec.',
        'Standard (Main Shutter, Sub Aperture)' => 'Standaard',
      },
    },
   'CommandDialsApertureSetting' => {
      Description => 'Functie instelschijven inst. Instellen diafragma',
      PrintConv => {
        'Aperture Ring' => 'Diafragmaring',
        'Sub-command Dial' => 'Secundaire instelschijf',
      },
    },
   'CommandDialsChangeMainSub' => {
      Description => 'Functie instelschijven inst. Verwissel hoofd/secundair',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'CommandDialsMenuAndPlayback' => {
      Description => 'Functie instelschijven inst. Menu’s en weergave',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'CommandDialsReverseRotation' => {
      Description => 'Functie instelschijven inst. Rotatie omkeren',
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'CommanderChannel' => 'Commanderstand Kanaal',
   'CommanderGroupAManualOutput' => 'Commanderstand Groep A M Corrct',
   'CommanderGroupAMode' => {
      Description => 'Commanderstand Groep A Stand',
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'TTL' => 'DDL',
      },
    },
   'CommanderGroupA_TTL-AAComp' => 'Commanderstand Groep A DDL/AA Corrct',
   'CommanderGroupBManualOutput' => 'Commanderstand Groep B M Corrct',
   'CommanderGroupBMode' => {
      Description => 'Commanderstand Groep B Stand',
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'TTL' => 'DDL',
      },
    },
   'CommanderGroupB_TTL-AAComp' => 'Commanderstand Groep B DDL/AA Corrct',
   'CommanderInternalFlash' => {
      Description => 'Commanderstand Ingb. flitsr Stand',
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'TTL' => 'DDL',
      },
    },
   'CommanderInternalManualOutput' => 'Commanderstand Ingb. flitsr M Corrct',
   'CommanderInternalTTLComp' => 'Commanderstand Ingb. flitsr DDL Corrct',
   'Comment' => 'Kommentaar',
   'Compilation' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ComponentsConfiguration' => 'Betekenis van elke component',
   'CompressedBitsPerPixel' => 'Afbeelding compressie modus',
   'Compression' => {
      Description => 'Compressie schema',
      PrintConv => {
        'JPEG' => 'JPEG-compressie',
        'JPEG (old-style)' => 'JPEG (oude versie)',
        'Kodak DCR Compressed' => 'Kodak DCR gcomprimeerd',
        'Kodak KDC Compressed' => 'Kodak KDC gecomprimeerd',
        'Next' => 'NeXT 2-Bit codering',
        'Nikon NEF Compressed' => 'Nikon NEF gecomprimeerd',
        'None' => 'Geen',
        'Pentax PEF Compressed' => 'Pentax PEF gecomprimeerd',
        'SGILog' => 'SGI 32-Bit Log Luminance gecodeerd',
        'SGILog24' => 'SGI 24-Bit Log Luminance gecodeerd',
        'Sony ARW Compressed' => 'Sony ARW gecomprimeerd',
        'Thunderscan' => 'ThunderScan 4-Bit codering',
        'Uncompressed' => 'Niet gecomprimeerd',
      },
    },
   'CompressionType' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'ConnectionSpaceIlluminant' => 'Witpunt van connectiekleurruimte',
   'ContentLocationCode' => 'Locatiecode van inhoud',
   'ContentLocationName' => 'Locatienaam van inhoud',
   'ContinuousDrive' => {
      PrintConv => {
        'Continuous' => 'Continu',
      },
    },
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Normal' => 'Normaal',
      },
    },
   'ConversionLens' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'Copyright' => 'Copyright houder',
   'CopyrightNotice' => 'Copyright vermelding',
   'CopyrightStatus' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'Country' => 'Land',
   'Country-PrimaryLocationCode' => 'ISO landcode',
   'Country-PrimaryLocationName' => 'Land',
   'CreateDate' => 'Datum van de originele data generatie',
   'CreationDate' => 'Opname datum',
   'Creator' => 'Maker',
   'CreatorAddress' => 'Maker - Adres',
   'CreatorCity' => 'Maker - Plaats',
   'CreatorCountry' => 'Maker - Land',
   'CreatorPostalCode' => 'Maker - Postcode',
   'CreatorRegion' => 'Maker - Provincie',
   'CreatorWorkEmail' => 'Maker - E-mail',
   'CreatorWorkTelephone' => 'Maker - Telefoonnummer',
   'CreatorWorkURL' => 'Maker - Website(s)',
   'Credit' => 'Leverancier',
   'CropActive' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'Curves' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'CustomRendered' => {
      Description => 'Gebruiker gedefineerde beeldverwerking',
      PrintConv => {
        'Custom' => 'Gebruiker gedefineerd proces',
        'Normal' => 'Standaard proces',
      },
    },
   'D-LightingHQ' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'D-LightingHQSelected' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'D-LightingHS' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'DataImprint' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'DateCreated' => 'Opnamedatum',
   'DateSent' => 'Datum van zenden',
   'DateStampMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'DateTime' => 'Datum bestand wijziging',
   'DateTimeOriginal' => 'Datum van de originele data generatie',
   'DaylightSavings' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'Description' => 'Beschrijving',
   'Destination' => 'Bestemming',
   'DestinationDST' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'DeviceAttributes' => 'Apparaateigenschappen',
   'DeviceManufacturer' => 'Apparaatproducent',
   'DeviceMfgDesc' => 'Apparaatproducent kenmerk',
   'DeviceModel' => 'Apparaatmodel',
   'DeviceModelDesc' => 'Apparaatmodel kenmerk',
   'DeviceSettingDescription' => 'Toestelinstellingen',
   'DialDirectionTvAv' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'DigitalCreationDate' => 'Digitale opnamedatum',
   'DigitalCreationTime' => 'Digitaal opnametijdstip',
   'DigitalZoom' => {
      Description => 'Digitaal zoomen',
      PrintConv => {
        'None' => 'Geen',
        'Off' => 'Uit',
      },
    },
   'DigitalZoomOn' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'DigitalZoomRatio' => 'Digitale zoom factor',
   'Directory' => 'Plaats van het bestand',
   'DisplaySize' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'DisplayXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micrometer)',
      },
    },
   'DisplayYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (micrometer)',
      },
    },
   'DistortionCorrection' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'DistortionCorrection2' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'DjVuVersion' => 'DjVu versie',
   'DocumentHistory' => 'Documentgeschiedenis',
   'DocumentName' => 'Document naam',
   'DocumentNotes' => 'Documentopmerkingen',
   'DriveMode' => {
      Description => 'Ontspannermodus',
      PrintConv => {
        'Continuous' => 'Continu',
        'Continuous Shooting' => 'Continu',
        'Off' => 'Uit',
        'Self-timer' => 'Zelfontspanner',
        'Self-timer Operation' => 'Zelfontspanner',
        'Single' => 'Enkel beeld',
        'Single Frame' => 'Enkel beeld',
        'Single Shot' => 'Enkel beeld',
        'Single-frame Shooting' => 'Enkel beeld',
      },
    },
   'DynamicAFArea' => {
      Description => 'Dynamisch AF-veld',
      PrintConv => {
        '21 Points' => '21 punten',
        '51 Points' => '51 punten',
        '51 Points (3D-tracking)' => '51 punten (3D-tracking)',
        '9 Points' => '9 punten',
      },
    },
   'DynamicRange' => {
      PrintConv => {
        'Standard' => 'Standaard',
      },
    },
   'DynamicRangeExpansion' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'DynamicRangeOptimizer' => {
      Description => 'Dyn.-bereikoptim',
      PrintConv => {
        'Advanced Auto' => 'Geavancrd Auto',
        'Advanced Lv1' => 'Geavanceerd-1',
        'Advanced Lv2' => 'Geavanceerd-2',
        'Advanced Lv3' => 'Geavanceerd-3',
        'Advanced Lv4' => 'Geavanceerd-4',
        'Advanced Lv5' => 'Geavanceerd-5',
        'Auto' => 'Automatisch',
        'Off' => 'Uit',
        'Standard' => 'Standaard',
      },
    },
   'ETTLII' => {
      PrintConv => {
        'Average' => 'Gemiddeld',
      },
    },
   'EVStepSize' => {
      Description => 'LW stapgrootte',
      PrintConv => {
        '1/2 EV' => '1/2 stop',
        '1/3 EV' => '1/3 stop',
      },
    },
   'EasyExposureCompensation' => {
      Description => 'Eenv. belichtingscorrectie',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
        'On (auto reset)' => 'Aan (autoherstel)',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatig',
        'Night' => 'Nachtscene',
        'Portrait' => 'Portret',
      },
    },
   'EdgeNoiseReduction' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'EditStatus' => 'Bewerkingsstatus',
   'EditorialUpdate' => {
      Description => 'Redactionele bewerking',
      PrintConv => {
        'Additional language' => 'Extra taal',
      },
    },
   'Emphasis' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'EnhanceDarkTones' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Enhancement' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'EnvelopeNumber' => 'Basisdatanummer',
   'EnvelopePriority' => {
      Description => 'Prioriteit',
      PrintConv => {
        '0 (reserved)' => '0 (Gereserveerd voor toekomstig gebruik)',
        '1 (most urgent)' => '1 (Meest belangrijk)',
        '5 (normal urgency)' => '5 (Normaal)',
        '8 (least urgent)' => '8 (Minst belangrijk)',
        '9 (user-defined priority)' => '9 (Door gebruiker aangegeven prioriteit)',
      },
    },
   'EnvelopeRecordVersion' => 'Recordversie',
   'ExifCameraInfo' => 'Exif Camera-informatie',
   'ExifImageHeight' => 'Afbeelding hoogte',
   'ExifImageWidth' => 'Afbeelding breedte',
   'ExifOffset' => 'Exif IFD-wijzer',
   'ExifToolVersion' => 'ExifTool versie',
   'ExifVersion' => 'Exif versie',
   'ExpandFilm' => 'Breid film uit',
   'ExpandFilterLens' => 'Breid filterlens uit',
   'ExpandFlashLamp' => 'Breid flitser uit',
   'ExpandLens' => 'Breid objectief',
   'ExpandScanner' => 'Breid scanner uit',
   'ExpandSoftware' => 'Breid software uit',
   'ExpirationDate' => 'Verloopdatum',
   'ExpirationTime' => 'Verlooptijdstip',
   'ExposureCompStepSize' => {
      Description => 'Stapgrootte belichtingscorr.',
      PrintConv => {
        '1 EV' => '1 stop',
        '1/2 EV' => '1/2 stop',
        '1/3 EV' => '1/3 stop',
      },
    },
   'ExposureCompensation' => 'Belichtingscorrectie',
   'ExposureControlStepSize' => {
      Description => 'Stapgrootte inst. belichting',
      PrintConv => {
        '1 EV' => '1 stop',
        '1/2 EV' => '1/2 stop',
        '1/3 EV' => '1/3 stop',
      },
    },
   'ExposureDelayMode' => {
      Description => 'Spiegelvoorontspanning',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ExposureIndex' => 'Belichtingsindex',
   'ExposureLevelIncrements' => {
      Description => 'Stapgrootte inst. belichting',
      PrintConv => {
        '1/2 Stop' => '1/2 stop',
        '1/3 Stop' => '1/3 stop',
      },
    },
   'ExposureMode' => {
      Description => 'Belichting modus',
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Aperture-priority AE' => 'Diafragmaprioriteit',
        'Auto' => 'Automatische belichting',
        'Auto bracket' => 'Belichting serie',
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatige belichting',
        'Portrait' => 'Portret',
        'Shutter Priority' => 'Sluiterprioriteit',
        'Shutter speed priority AE' => 'Sluiterprioriteit',
      },
    },
   'ExposureModeInManual' => {
      PrintConv => {
        'Center-weighted average' => 'Centrum gemiddelde',
        'Partial metering' => 'Gedeelte',
      },
    },
   'ExposureProgram' => {
      Description => 'Belichtingsprogramma',
      PrintConv => {
        'Action (High speed)' => 'Actie programma (georiënteerd op snelle sluitertijden)',
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Aperture-priority AE' => 'Diafragmaprioriteit',
        'Creative (Slow speed)' => 'Creatief programma (georiënteerd op scherptediepte)',
        'Landscape' => 'Landschap modus',
        'Manual' => 'Handmatig',
        'Not Defined' => 'Niet gedefinieerd',
        'Portrait' => 'Portret modus',
        'Program AE' => 'Normaal programma',
        'Shutter Priority' => 'Sluiterprioriteit',
        'Shutter speed priority AE' => 'Sluiterprioriteit',
      },
    },
   'ExposureTime' => 'Belichtingstijd',
   'ExposureTime2' => 'Belichtingstijd 2',
   'ExtendedWBDetect' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ExternalFlash' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ExternalFlashBounce' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ExternalFlashMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ExtraSamples' => 'Extra componenten',
   'FNumber' => 'F waarde',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (boven/links)',
        'Rotate 180' => '180° (onder/rechts)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
      },
    },
   'FastSeek' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'FaxProfile' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'FaxRecvParams' => 'Fax ontvangst parameters',
   'FaxRecvTime' => 'Fax ontvangst tijd',
   'FaxSubAddress' => 'Fax sub adres',
   'FileFormat' => 'Fileformaat',
   'FileModifyDate' => 'Datum actualisering',
   'FileName' => 'Bestandnaam',
   'FileNumberMemory' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FileNumberSequence' => {
      Description => 'Opeenvolgende nummering',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FileSize' => 'Bestandgrootte',
   'FileSource' => {
      Description => 'Bestand bron',
      PrintConv => {
        'Digital Camera' => 'Digitale camera',
        'Film Scanner' => 'Film scanner',
        'Reflection Print Scanner' => 'Scanner',
      },
    },
   'FileType' => 'Bestandtype',
   'FileVersion' => 'Fileformaat versie',
   'Filename' => 'Bestandnaam',
   'FillOrder' => {
      Description => 'Vul volgorde',
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'Filter' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'FilterEffect' => {
      PrintConv => {
        'None' => 'Geen',
        'Off' => 'Uit',
      },
    },
   'FilterEffectMonochrome' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'FinderDisplayDuringExposure' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FineTuneOptCenterWeighted' => 'Fijnafst. voor opt. belichting Centrumgericht',
   'FineTuneOptMatrixMetering' => 'Fijnafst. voor opt. belichting Matrixmeting',
   'FineTuneOptSpotMetering' => 'Fijnafst. voor opt. belichting Spotmeting',
   'FixtureIdentifier' => 'Kenmerk',
   'Flash' => {
      Description => 'Flits',
      PrintConv => {
        'Auto, Did not fire' => 'Flits werd niet ontstoken, automodus',
        'Auto, Did not fire, Red-eye reduction' => 'Flits werd niet ontstoken, rode ogen reductie',
        'Auto, Fired' => 'Flits werd ontstoken, automodus',
        'Auto, Fired, Red-eye reduction' => 'Flits werd ontstoken, automodus, rode ogen reductie',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Flits werd ontstoken, automodus, gereflecteerd flitslicht, rode ogen reductie',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Flits werd ontstoken, automodus, geen gereflecteerd flitslicht, rode ogen reductie',
        'Auto, Fired, Return detected' => 'Flits werd ontstoken, automodus, gereflecteerd flitslicht',
        'Auto, Fired, Return not detected' => 'Flits werd ontstoken, automodus, geen gereflecteerd flitslicht',
        'Did not fire' => 'Geen flits',
        'Fired' => 'Flits afgevuurd',
        'Fired, Red-eye reduction' => 'Flits werd ontstoken, Rode ogen reductie',
        'Fired, Red-eye reduction, Return detected' => 'Flits werd ontstoken, rode ogen reductie, gereflecteerd flitslicht',
        'Fired, Red-eye reduction, Return not detected' => 'Flits werd ontstoken, rode oen reductie, geen gereflecteerd flitslicht',
        'Fired, Return detected' => 'Gereflecteerd flitslicht gedetecteerd',
        'Fired, Return not detected' => 'Geen gereflecteerd flitslicht gedetecteerd',
        'No Flash' => 'Geen flits',
        'No flash function' => 'Geen flits functie',
        'Off' => 'Uit',
        'Off, Did not fire' => 'Flits werd niet ontstoken, flits onderdruk modus',
        'Off, Did not fire, Return not detected' => 'Gedeactiveerd, flits werd niet ontstoken, geen gereflecteerd flitslicht',
        'Off, No flash function' => 'Gedeactiveerd, geen flits functie',
        'Off, Red-eye reduction' => 'Gedeactiveerd, rode ogen reductie',
        'On' => 'Aan',
        'On, Did not fire' => 'Aan, flits werd niet ontstoken',
        'On, Fired' => 'Flits werd ontstoken, flits afdwing modus',
        'On, Red-eye reduction' => 'Flits werd ontstoken, flits afdwing modus, rode ogen reductie',
        'On, Red-eye reduction, Return detected' => 'Flits werd ontstoken, flits afdwing modus, rode ogen reductie, gereflecteerd flitslicht',
        'On, Red-eye reduction, Return not detected' => 'Flits werd ontstoken, flits afdwing modus, rode ogen reductie, geen gereflecteerd flitslicht',
        'On, Return detected' => 'Flits werd ontstoken, flits afdwing modus, gereflecteerd flitslicht gedetecteerd',
        'On, Return not detected' => 'Flits werd ontstoken, flits afdwing modus, geen gereflecteerd flitslicht gedetecteerd',
      },
    },
   'FlashCommanderMode' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FlashCompensation' => 'Flitscorrectie',
   'FlashControlMode' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'Repeating Flash' => 'Stroboscopisch flitsen',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'FlashEnergy' => 'Flits energie',
   'FlashExposureComp' => 'Flitscompensatie',
   'FlashExposureLock' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FlashFired' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'FlashGroupAControlMode' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'Repeating Flash' => 'Stroboscopisch flitsen',
      },
    },
   'FlashGroupBControlMode' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'Repeating Flash' => 'Stroboscopisch flitsen',
      },
    },
   'FlashGroupCControlMode' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Off' => 'Uit',
        'Repeating Flash' => 'Stroboscopisch flitsen',
      },
    },
   'FlashIntensity' => {
      PrintConv => {
        'High' => 'Hoog',
        'Normal' => 'Normaal',
      },
    },
   'FlashLevel' => 'Flitscorrectie',
   'FlashMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
        'Unknown' => 'Onbekend',
      },
    },
   'FlashModel' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'FlashOptions' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Normal' => 'Normaal',
      },
    },
   'FlashOptions2' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Normal' => 'Normaal',
      },
    },
   'FlashShutterSpeed' => 'Langste sluitertijd bij flits',
   'FlashStatus' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'FlashSyncSpeed' => 'Flitssynchronisatie snelheid',
   'FlashSyncSpeedAv' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'FlashType' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'FlashWarning' => {
      Description => 'Flitswaarschuwing',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FlashpixVersion' => 'Ondersteunde Flashpix versie',
   'FlickerReduce' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'FlipHorizontal' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'FocalLength' => 'Brandpuntafstand',
   'FocalLength35efl' => 'Brandpuntafstand',
   'FocalLengthIn35mmFormat' => 'Brandpuntafstand in 35 mm kleinbeeld formaat',
   'FocalPlaneResolutionUnit' => {
      Description => 'Sensor resolutie eenheid',
      PrintConv => {
        'None' => 'Geen',
        'inches' => 'inch',
        'um' => 'µm (micrometer)',
      },
    },
   'FocalPlaneXResolution' => 'Horizontale sensor resolutie',
   'FocalPlaneYResolution' => 'Verticale sensor resolutie',
   'Focus' => {
      Description => 'Scherpstelling',
      PrintConv => {
        'Manual' => 'Handmatig',
      },
    },
   'FocusArea' => 'Scherpstelveld',
   'FocusAreaSelection' => {
      Description => 'Doorloop scherpstelpunt',
      PrintConv => {
        'No Wrap' => 'Geen doorloop',
        'Wrap' => 'Doorloop',
      },
    },
   'FocusContinuous' => {
      PrintConv => {
        'Continuous' => 'Continu',
        'Manual' => 'Handmatig',
      },
    },
   'FocusMode' => {
      Description => 'Focus modus',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Continuous' => 'Continu',
        'Manual' => 'Handmatig',
        'Normal' => 'Normaal',
      },
    },
   'FocusMode2' => {
      PrintConv => {
        'Manual' => 'Handmatig',
      },
    },
   'FocusModeSetting' => {
      Description => 'Scherpstelstand',
      PrintConv => {
        'AF-A' => 'Automatische AF',
        'AF-C' => 'Continue AF',
        'AF-S' => 'Enkelvoudige AF',
        'Manual' => 'Handmatig',
      },
    },
   'FocusPointWrap' => {
      Description => 'Doorloop scherpstelpunt',
      PrintConv => {
        'No Wrap' => 'Geen doorloop',
        'Wrap' => 'Doorloop',
      },
    },
   'FocusRange' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Manual' => 'Handmatig',
        'Normal' => 'Normaal',
      },
    },
   'FocusTrackingLockOn' => {
      Description => 'Focus Tracking met Lock-On',
      PrintConv => {
        'Long' => 'Lang',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'Short' => 'Kort',
      },
    },
   'FrameRate' => 'Beeldwisselsnelheid',
   'FrameSize' => 'Beeldformaat',
   'FreeByteCounts' => 'Aantal bytes van het lege databereik',
   'FreeOffsets' => 'Vrije data posities',
   'FujiFlashMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'FunctionButton' => {
      Description => 'FUNC. knop',
      PrintConv => {
        'AF-area Mode' => 'AF-veldstand',
        'Center AF Area' => 'Centrale AF-veld',
        'Center-weighted' => 'Centrumgericht',
        'FV Lock' => 'FV-vergrendeling',
        'Flash Off' => 'Flitser uit',
        'Framing Grid' => 'Rasterweergave',
        'ISO Display' => 'ISO-weergave',
        'Matrix Metering' => 'Matrixmeting',
        'Spot Metering' => 'Spotmeting',
      },
    },
   'GIFVersion' => 'GIF versie',
   'GPSAltitude' => 'Hoogte',
   'GPSAltitudeRef' => {
      Description => 'GPS hoogte - referentie',
      PrintConv => {
        'Above Sea Level' => 'Boven zeeniveau',
        'Below Sea Level' => 'Onder zeeniveau',
      },
    },
   'GPSAreaInformation' => 'GPS naam van het gebied',
   'GPSDOP' => 'GPS meetnauwkeurigheid',
   'GPSDateStamp' => 'GPS UTC datum',
   'GPSDateTime' => 'GPS UTC datum en tijd',
   'GPSDestBearing' => 'GPS peiling van bestemming',
   'GPSDestBearingRef' => {
      Description => 'GPS peiling van bestemming - referentie',
      PrintConv => {
        'Magnetic North' => 'Magnetische noorden',
        'True North' => 'Geografische noorden',
      },
    },
   'GPSDestDistance' => 'GPS afstand tot bestemming',
   'GPSDestDistanceRef' => {
      Description => 'GPS afstand tot bestemming - referentie',
      PrintConv => {
        'Miles' => 'Engelse mijlen',
        'Nautical Miles' => 'Zeemijlen',
      },
    },
   'GPSDestLatitude' => 'GPS breedtegraad van bestemming',
   'GPSDestLatitudeRef' => {
      Description => 'GPS breedtegraad van bestemming - referentie',
      PrintConv => {
        'North' => 'Noorderbreedte',
        'South' => 'Zuiderbreedte',
      },
    },
   'GPSDestLongitude' => 'GPS lengtegraad van bestemming',
   'GPSDestLongitudeRef' => {
      Description => 'GPS lengtegraad van bestemming - referentie',
      PrintConv => {
        'East' => 'Oosterlengte',
        'West' => 'Westerlengte',
      },
    },
   'GPSDifferential' => {
      Description => 'GPS differentiaal correctie',
      PrintConv => {
        'Differential Corrected' => 'Met differentiaal correctie',
        'No Correction' => 'Zonder differentiaal correctie',
      },
    },
   'GPSImgDirection' => 'GPS peiling van de afbeelding',
   'GPSImgDirectionRef' => {
      Description => 'GPS peiling van de afbeelding - referentie',
      PrintConv => {
        'Magnetic North' => 'Magnetische noorden',
        'True North' => 'Geografische noorden',
      },
    },
   'GPSInfo' => 'GPS Info',
   'GPSLatitude' => 'GPS breedtegraad',
   'GPSLatitudeRef' => {
      Description => 'GPS breedtegraad - referentie',
      PrintConv => {
        'North' => 'Noorderbreedte',
        'South' => 'Zuiderbreedte',
      },
    },
   'GPSLongitude' => 'GPS lengtegraad',
   'GPSLongitudeRef' => {
      Description => 'GPS lengtegraad - referentie',
      PrintConv => {
        'East' => 'Oosterlengte',
        'West' => 'Westerlengte',
      },
    },
   'GPSMapDatum' => 'GPS geodetisch datum',
   'GPSMeasureMode' => {
      Description => 'GPS meetmethode',
      PrintConv => {
        '2-D' => 'Tweedimensionale meting',
        '2-Dimensional' => 'Tweedimensionale meting',
        '2-Dimensional Measurement' => 'Tweedimensionale meting',
        '3-D' => 'Driedimensionale meting',
        '3-Dimensional' => 'Driedimensionale meting',
        '3-Dimensional Measurement' => 'Driedimensionale meting',
      },
    },
   'GPSPosition' => 'GPS positie',
   'GPSProcessingMethod' => 'GPS verwerkingsmethode',
   'GPSSatellites' => 'GPS satellieten gebruikt voor de meting',
   'GPSSpeed' => 'GPS ontvanger bewegingssnelheid',
   'GPSSpeedRef' => {
      Description => 'GPS ontvanger bewegingssnelheid - referentie',
      PrintConv => {
        'km/h' => 'Kilometer per uur',
        'knots' => 'Knopen',
        'mph' => 'Mijl per uur',
      },
    },
   'GPSStatus' => {
      Description => 'GPS ontvanger status',
      PrintConv => {
        'Measurement Active' => 'Actuele meting beschikbaar',
        'Measurement Void' => 'Actuele meting niet beschikbaar',
      },
    },
   'GPSTimeStamp' => 'GPS UTC tijd',
   'GPSTrack' => 'GPS ontvanger bewegingsrichting',
   'GPSTrackRef' => {
      Description => 'GPS ontvanger bewegingsrichting - referentie',
      PrintConv => {
        'Magnetic North' => 'Magnetische noorden',
        'True North' => 'Geografische noorden',
      },
    },
   'GPSVersionID' => 'GPS versie ID',
   'GainControl' => {
      Description => 'Belichtingsversterking',
      PrintConv => {
        'High gain down' => 'Hoge helderheidsverminderring',
        'High gain up' => 'Hoge helderheidsvesterking',
        'Low gain down' => 'Kleine helderheidsverminderring',
        'Low gain up' => 'Kleine helderheidsvesterking',
        'None' => 'Geen',
      },
    },
   'Gapless' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'Gradation' => 'Levendig',
   'GrayResponseUnit' => {
      PrintConv => {
        '0.0001' => 'Nummer stelt een 1000ste van een eenheid voor',
        '0.001' => 'Nummer stelt een 100ste van een eenheid voor',
        '0.1' => 'Nummer stelt een 10de van een eenheid voor',
        '1e-05' => 'Nummer stelt een 10000ste van een eenheid voor',
        '1e-06' => 'Nummer stelt een 100000ste van een eenheid voor',
      },
    },
   'GrayTRC' => 'Grijze toon reproductie curve',
   'GreenMatrixColumn' => 'Groene matrixkolom',
   'GreenTRC' => 'Groene toon reproductie curve',
   'GridDisplay' => {
      Description => 'Rasterweergave in zoeker',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'HDR' => {
      Description => 'Auto HDR',
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'Headline' => 'Opschrift',
   'HeightResolution' => 'Beeldresolutie verticaal',
   'HighISONoiseReduction' => {
      Description => 'NR bij hoge-ISO',
      PrintConv => {
        'Auto' => 'Automatisch',
        'High' => 'Hi',
        'Low' => 'Laag',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
        'Standard' => 'Standaard',
      },
    },
   'HighlightTonePriority' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'HometownDST' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'Hue' => 'Kleurtoon',
   'ICCProfile' => 'ICC Profiel',
   'IPTC-NAA' => 'IPTC-NAA metadata',
   'ISO' => 'ISO gevoeligheid',
   'ISOAuto' => 'ISO auto',
   'ISODisplay' => 'ISO-weergave',
   'ISOExpansion' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ISOExpansion2' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ISOSetting' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Manual' => 'Handmatig',
      },
    },
   'ISOSpeedExpansion' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ISOSpeedIncrements' => {
      Description => 'ISO-stapgrootte',
      PrintConv => {
        '1 Stop' => '1 stop',
        '1/3 Stop' => '1/3 stop',
      },
    },
   'ISOStepSize' => {
      Description => 'ISO-stapgrootte',
      PrintConv => {
        '1 EV' => '1 stop',
        '1/2 EV' => '1/2 stop',
        '1/3 EV' => '1/3 stop',
      },
    },
   'Illumination' => {
      Description => 'Verlichting',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ImageAuthentication' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ImageByteCount' => 'Aantal beeldgegevens in bytes',
   'ImageColorIndicator' => 'Afbeelding kleur indicator',
   'ImageColorValue' => 'Afbeelding kleur waarde',
   'ImageDataDiscard' => {
      Description => 'Afgedankte beeldgegevens',
      PrintConv => {
        'Flexbits Discarded' => 'Afgedankte flexbits',
        'Full Resolution' => 'Volledige resolutie',
        'HighPass Frequency Data Discarded' => 'Afgedankte hoogdoorlaat frequentiegegevens',
        'Highpass and LowPass Frequency Data Discarded' => 'Afgedankte hoogdoorlaat en laagdoorlaat frequentiegegevens',
      },
    },
   'ImageDescription' => 'Afbeelding beschrijving',
   'ImageDustOff' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ImageHeight' => 'Afbeeldingshoogte',
   'ImageHistory' => 'Afbeelding geschiedenis',
   'ImageNumber' => 'Afbeelding nummer',
   'ImageOffset' => 'Beeldverplaatsing',
   'ImageOrientation' => {
      Description => 'Foto oriëntatie',
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
        'Square' => 'Vierkant',
      },
    },
   'ImageQuality' => {
      Description => 'Bldkwaliteit',
      PrintConv => {
        'High' => 'Hoog',
        'Normal' => 'Normaal',
      },
    },
   'ImageQuality2' => 'Bldkwaliteit 2',
   'ImageReview' => {
      Description => 'Beeld terugspelen',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ImageReviewTime' => 'Timers uit Beeld terugspelen',
   'ImageRotated' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ImageSize' => 'Beeldformaat',
   'ImageStabilization' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ImageTone' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
      },
    },
   'ImageType' => 'Beeldtype',
   'ImageUniqueID' => 'Uniek afbeeldings ID',
   'ImageWidth' => 'Afbeeldingsbreedte',
   'InitialZoomSetting' => {
      Description => 'Aanvankelijke zoominstelling',
      PrintConv => {
        'High Magnification' => 'Hoge zoom',
        'Low Magnification' => 'Lage zoom',
        'Medium Magnification' => 'Gemiddelde zoom',
      },
    },
   'Instructions' => 'Instructies',
   'IntellectualGenre' => 'Genre',
   'IntensityStereo' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'InternalFlash' => {
      Description => 'Flitssturing ingeb. flitser',
      PrintConv => {
        'Commander Mode' => 'Commanderstand',
        'Fired' => 'Flits afgevuurd',
        'Manual' => 'Handmatig',
        'No' => 'Geen flits',
        'Off' => 'Uit',
        'On' => 'Aan',
        'Repeating Flash' => 'Stroboscopisch flitsen',
        'TTL' => 'DDL',
      },
    },
   'InternalFlashMode' => {
      PrintConv => {
        'On' => 'Aan',
      },
    },
   'InteropIndex' => {
      Description => 'Interoperabiliteits Identificatie',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: DCF Optie formaat (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98: DCF Basis formaat (sRGB)',
        'THM - DCF thumbnail file' => 'THM: DCF Miniatuur formaat',
      },
    },
   'InteropOffset' => 'Interoperabiliteit-tag',
   'InteropVersion' => 'Interoperabiliteits versie',
   'JFIFVersion' => 'JFIF versie',
   'JPEGQuality' => {
      Description => 'Beeldkwaliteit',
      PrintConv => {
        'Extra Fine' => 'Extra fijn',
        'Fine' => 'Fijn',
        'Standard' => 'Normaal',
      },
    },
   'JobID' => 'ID van baan',
   'Keyword' => 'Trefwoorden',
   'Keywords' => 'Trefwoord',
   'LCDIllumination' => {
      Description => 'LCD-verlichting',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'LCDIlluminationDuringBulb' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'LCHEditor' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'LanguageIdentifier' => 'Taalherkenning',
   'Lens' => 'Objectief',
   'LensInfo' => 'Lensgegevens',
   'LicenseType' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'LightSource' => {
      Description => 'Lichtbron',
      PrintConv => {
        'Cloudy' => 'Bewolkt',
        'Cool White Fluorescent' => 'Koel wit TL-licht',
        'Day White Fluorescent' => 'Daglicht wit TL-licht',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Fine Weather' => 'Onbewolkt',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'ISO Studio Tungsten' => 'ISO studio kunstlicht (gloeilamp)',
        'Other' => 'Andere lichtbron',
        'Shade' => 'Schaduw',
        'Standard Light A' => 'Standaard licht A',
        'Standard Light B' => 'Standaard licht B',
        'Standard Light C' => 'Standaard licht C',
        'Tungsten (Incandescent)' => 'Kunstlicht (gloeilamp)',
        'Unknown' => 'Onbekend',
        'Warm White Fluorescent' => 'Warm wit TL-licht',
        'White Fluorescent' => 'Wit TL-licht',
      },
    },
   'LightSourceSpecial' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Lightness' => 'Helderheid',
   'Lit' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'LiveViewShooting' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Location' => 'Lokatie',
   'LongExposureNoiseReduction' => {
      Description => 'NR lang-belicht',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'LoopStyle' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
      },
    },
   'MB-D10Batteries' => 'MB-D10 batterijen',
   'MB-D10BatteryType' => 'MB-D10 batterijen',
   'MB-D80Batteries' => 'MB-D80 batterijen',
   'MIEVersion' => 'MIE versie',
   'MSStereo' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Macro' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MacroMode' => {
      PrintConv => {
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MainDialExposureComp' => {
      Description => 'Main Dial Belichtingscorrectie',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Make' => 'Fabrikant',
   'MakeAndModel' => 'Producent en model',
   'MakerNote' => 'Fabrikant informatie',
   'MakerNotes' => 'Wenken van de fabrikant',
   'ManualFlashOutput' => {
      Description => 'Ingebouwde flitser Handmatig Sterkte',
      PrintConv => {
        'Low' => 'Laag',
      },
    },
   'MasterDocumentID' => 'ID van hoofddocument',
   'MatrixMetering' => 'Matrixmeting',
   'MaxAperture' => 'Maximale lensopening',
   'MaxApertureValue' => 'Grootste diafragma',
   'MaxContinuousRelease' => 'Max. aant. continuopnamen',
   'MaxSampleValue' => 'Max sample waarde',
   'MediaBlackPoint' => 'Media zwartpunt',
   'MediaType' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'MediaWhitePoint' => 'Media Witpunt',
   'Metering' => {
      Description => 'Lichtmeting',
      PrintConv => {
        'Center-weighted' => 'Centrumgericht',
        'Matrix' => 'Matrixmeting',
      },
    },
   'MeteringMode' => {
      Description => 'Belichting meet methode',
      PrintConv => {
        'Average' => 'Gemiddeld',
        'Center-weighted average' => 'Centrum gemiddelde',
        'Multi-segment' => 'Multi segment',
        'Multi-spot' => 'MultiSpot',
        'Other' => 'Andere',
        'Partial' => 'Gedeelte',
        'Unknown' => 'Onbekend',
      },
    },
   'MeteringMode2' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'MeteringMode3' => {
      PrintConv => {
        'Multi-segment' => 'Multi segment',
      },
    },
   'MeteringTime' => {
      Description => 'Timers uit Belichtingsmeters',
      PrintConv => {
        'No Limit' => 'Altijd aan',
      },
    },
   'MinSampleValue' => 'Min sample waarde',
   'MinoltaQuality' => {
      PrintConv => {
        'Normal' => 'Normaal',
        'Standard' => 'Standaard',
      },
    },
   'Model' => 'Camera model',
   'Model2' => 'Camera model (2)',
   'ModelingFlash' => {
      Description => 'Instellicht',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ModifiedPictureStyle' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'None' => 'Geen',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'ModifiedSaturation' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Standard' => 'Standaard',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Standard' => 'Standaard',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Cloudy' => 'Bewolkt',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'Shade' => 'Schaduw',
        'Tungsten' => 'Kunstlicht (gloeilamp)',
      },
    },
   'ModifyDate' => 'Datum bestand wijziging',
   'MoireFilter' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MonitorOffTime' => 'Monitor uit',
   'MonochromeFilterEffect' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'MonochromeLinear' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'MonochromeToningEffect' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'MultiExposureAutoGain' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MultiExposureMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'MultiFrameNoiseReduction' => {
      Description => 'Ruisond. Multi Frame',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MultiSelector' => {
      Description => 'Multi-selector',
      PrintConv => {
        'Do Nothing' => 'Doe niets',
        'Reset Meter-off Delay' => 'Activeer lichtmeter',
      },
    },
   'MultiSelectorPlaybackMode' => {
      Description => 'Centrale knop multiselector Weergavestand',
      PrintConv => {
        'Choose Folder' => 'Map selecteren',
        'Thumbnail On/Off' => 'Miniatuur aan/uit',
        'View Histograms' => 'Histogrammen weergeven',
        'Zoom On/Off' => 'Zoom aan/uit',
      },
    },
   'MultiSelectorShootMode' => {
      Description => 'Centrale knop multiselector Opnamestand',
      PrintConv => {
        'Highlight Active Focus Point' => 'Actieve AF-punt markeren',
        'Not Used' => 'Geen functie',
        'Select Center Focus Point' => 'Middelste AF-punt selecteren',
      },
    },
   'MultipleExposureSet' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Mute' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'MyColorMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'NDFilter' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'NEFCompression' => {
      PrintConv => {
        'Uncompressed' => 'Niet gecomprimeerd',
      },
    },
   'NikonImageSize' => 'Beeldformaat',
   'NoMemoryCard' => {
      Description => 'Geen geheugenkaart',
      PrintConv => {
        'Enable Release' => 'Ontgrendel ontspanknop',
        'Release Locked' => 'Vergrendel ontspanknop',
      },
    },
   'Noise' => 'Ruis',
   'NoiseFilter' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Off' => 'Uit',
        'Standard' => 'Standaard',
      },
    },
   'NoiseReduction' => {
      Description => 'Ruisreductie',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
        'Standard' => 'Standaard',
      },
    },
   'ObjectAttributeReference' => 'Intellectuele genre',
   'ObjectCycle' => {
      Description => 'Objectcyclus',
      PrintConv => {
        'Both Morning and Evening' => 'Beide',
        'Evening' => 'Avond',
        'Morning' => 'Ochtend',
      },
    },
   'ObjectFileType' => {
      PrintConv => {
        'None' => 'Geen',
        'Unknown' => 'Onbekend',
      },
    },
   'ObjectName' => 'Titel',
   'ObjectPreviewData' => 'Voorvertoning objectgegevens',
   'ObjectPreviewFileFormat' => 'Voorvertoning objectgegevens bestandsformaat',
   'ObjectPreviewFileVersion' => 'Voorvertoning objectgegevens bestandsformaatversie',
   'ObjectTypeReference' => 'Referentie van Object type',
   'OldSubfileType' => 'Subbestand type',
   'OneTouchWB' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'OpticalZoomMode' => {
      PrintConv => {
        'Standard' => 'Standaard',
      },
    },
   'OpticalZoomOn' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Opto-ElectricConvFactor' => 'Opto elektronische omreken factor',
   'Orientation' => {
      Description => 'Oriëntatie van de afbeelding',
      PrintConv => {
        'Horizontal (normal)' => '0° (boven/links)',
        'Mirror horizontal' => 'Horizontaal gespiegeld',
        'Mirror horizontal and rotate 270 CW' => 'Spiegel horizontaal en draai 270° met de klok mee',
        'Mirror horizontal and rotate 90 CW' => 'Spiegel horizontaal en draai 90° met de klok mee',
        'Mirror vertical' => 'Vertikaal gespiegeld',
        'Rotate 180' => '180° (onder/rechts)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
      },
    },
   'OriginalTransmissionReference' => 'Werknummer',
   'OriginatingProgram' => 'Oorspronkelijk programma',
   'OwnerID' => 'ID van eigenaar',
   'PEFVersion' => 'PEF versie',
   'Padding' => 'Plaatshouder',
   'PageName' => 'Pagina naam',
   'PageNumber' => 'Pagina nummer',
   'PhotoEffect' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'PhotoEffects' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'PhotoEffectsType' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'PhotoInfoPlayback' => {
      Description => 'Functie van multi-selector',
      PrintConv => {
        'Info Left-right, Playback Up-down' => 'Info <> / Foto’s',
        'Info Up-down, Playback Left-right' => 'Info / Foto’s <>',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Pixel schema',
      PrintConv => {
        'BlackIsZero' => 'Zwart is nul',
        'Color Filter Array' => 'CFA (Kleur Filter Matrix)',
        'Pixar LogL' => 'CIE Log2(L) (Log Luminantie)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (Log Luminantie en Chrominantie)',
        'RGB Palette' => 'RGB palet',
        'Transparency Mask' => 'Transparent masker',
        'WhiteIsZero' => 'Wit is nul',
      },
    },
   'PhotoshopFormat' => {
      PrintConv => {
        'Standard' => 'Standaard',
      },
    },
   'PictureControl' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'PictureControlActive' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'PictureFinish' => {
      PrintConv => {
        'Portrait' => 'Portret',
      },
    },
   'PictureMode' => {
      PrintConv => {
        'Aperture-priority AE' => 'Diafragmaprioriteit',
        'Auto' => 'Automatisch',
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatig',
        'Portrait' => 'Portret',
        'Shutter speed priority AE' => 'Sluiterprioriteit',
        'Standard' => 'Standaard',
      },
    },
   'PictureMode2' => {
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Manual' => 'Handmatig',
        'Shutter Speed Priority' => 'Sluiterprioriteit',
      },
    },
   'PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'None' => 'Geen',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'PixelFormat' => 'Pixelformaat',
   'PixelUnits' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'PlanarConfiguration' => {
      Description => 'Afbeelding data arrangement',
      PrintConv => {
        'Chunky' => 'Chunky Formaat (Interleaved)',
        'Planar' => 'Planar Formaat',
      },
    },
   'Predictor' => {
      PrintConv => {
        'Horizontal differencing' => 'Horizontale differencering',
        'None' => 'Geen prodictor schema in gebruik',
      },
    },
   'PreviewColorSpace' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'PreviewImage' => 'Voorbeeld',
   'PreviewImageValid' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'PreviewQuality' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'PrimaryChromaticities' => 'Chromaticiteit van primaire kleuren',
   'PrimaryPlatform' => 'Primaire platform',
   'ProcessingSoftware' => 'Verwerkingssoftware',
   'ProductID' => 'Produkt ID',
   'ProfileCMMType' => 'Profiel CMM-type',
   'ProfileClass' => {
      Description => 'Profielklasse',
      PrintConv => {
        'Abstract Profile' => 'Abstractprofiel',
        'ColorSpace Conversion Profile' => 'Kleurenruimteprofiel',
        'DeviceLink Profile' => 'Apparaatverbindingsprofiel',
        'Display Device Profile' => 'Beeldschermapparaatprofiel',
        'Input Device Profile' => 'Invoerapparaatprofiel',
        'NamedColor Profile' => 'Genoemd kleurenprofiel',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Nikonprofiel ("nkpf")',
        'Output Device Profile' => 'Uitvoerapparaatprofiel',
      },
    },
   'ProfileConnectionSpace' => 'Profielconnectieruimte',
   'ProfileCopyright' => 'Profielcopyright',
   'ProfileCreator' => 'Profielproducent',
   'ProfileDateTime' => 'Datum 1e aanmaak profiel',
   'ProfileDescription' => 'Profielbeschrijving',
   'ProfileDescriptionML' => 'Profielbeschrijving ML',
   'ProfileFileSignature' => 'Profiel filekenmerk',
   'ProfileID' => 'Profiel-ID',
   'ProfileSequenceDesc' => 'Profielvolgorde beschrijving',
   'ProfileVersion' => 'Profielversie',
   'ProgramLine' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'ProgramMode' => {
      PrintConv => {
        'None' => 'Geen',
        'Portrait' => 'Portret',
      },
    },
   'ProgramVersion' => 'Programmaversie',
   'Province-State' => 'Provincie',
   'Quality' => {
      Description => 'Beeldkwaliteit',
      PrintConv => {
        'Compressed RAW' => 'cRAW',
        'Compressed RAW + JPEG' => 'cRAW+JPEG',
        'Extra Fine' => 'Extra fijn',
        'Fine' => 'Fijn',
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Normal' => 'Normaal',
        'RAW + JPEG' => 'RAW+JPEG',
        'Standard' => 'Standaard',
      },
    },
   'QualityMode' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'QuickShot' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'RAFVersion' => 'RAF versie',
   'RasterizedCaption' => 'Gerasterde titel',
   'Rating' => 'Waardering',
   'RatingPercent' => 'Waardering in procent',
   'RawDevAutoGradation' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'RawJpgQuality' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'RecordMode' => {
      Description => 'Opnamemodus',
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Manual' => 'Handmatig',
        'Shutter Priority' => 'Sluiterprioriteit',
      },
    },
   'RecordingMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatig',
        'Portrait' => 'Portret',
      },
    },
   'RedEyeCorrection' => {
      PrintConv => {
        'Automatic' => 'Automatisch',
        'Off' => 'Uit',
      },
    },
   'RedEyeReduction' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'RedMatrixColumn' => 'Rode matrixkolom',
   'RedTRC' => 'Rode toon reproductie curve',
   'ReferenceBlackWhite' => 'Zwart/wit referentie punten',
   'ReferenceDate' => 'Referentiedatum',
   'ReferenceNumber' => 'Referentienummer',
   'ReferenceService' => 'Referentieservice',
   'RelatedImageFileFormat' => 'Bestandsformaat van de afbeelding',
   'RelatedImageHeight' => 'Afbeeldingshoogte',
   'RelatedImageWidth' => 'Afbeeldingsbreedte',
   'RelatedSoundFile' => 'Bijbehorend audio bestand',
   'ReleaseButtonToUseDial' => {
      Description => 'Knop loslaten voor instelsch.',
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ReleaseDate' => 'Datum van vrijgave',
   'ReleaseTime' => 'Tijdstip van vrijgave',
   'RemoteOnDuration' => 'Afstandsbediening',
   'RenderingIntent' => {
      Description => 'Rendermethode',
      PrintConv => {
        'ICC-Absolute Colorimetric' => 'Absoluut colorimetrisch',
        'Media-Relative Colorimetric' => 'Relatieve colorimetrisch',
        'Perceptual' => 'Waarnemend',
        'Saturation' => 'Verzadiging',
      },
    },
   'RepeatingFlashCount' => 'Stroboscopisch flitsen Aantal',
   'RepeatingFlashOutput' => 'Stroboscopisch flitsen Sterkte',
   'RepeatingFlashRate' => 'Stroboscopisch flitsen Freq.',
   'ResampleParamsQuality' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
      },
    },
   'Resaved' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ResolutionUnit' => {
      Description => 'Eenheid van de X und Y resolutie',
      PrintConv => {
        'None' => 'Geen',
        'cm' => 'centimeter',
      },
    },
   'RetouchHistory' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'ReverseIndicators' => 'Aanduidingen omkeren',
   'Rotation' => {
      PrintConv => {
        'Horizontal' => '0° (boven/links)',
        'Horizontal (Normal)' => '0° (boven/links)',
        'Horizontal (normal)' => '0° (boven/links)',
        'Rotate 180' => '180° (onder/rechts)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
        'Rotated 180' => '180° (onder/rechts)',
        'Rotated 270 CW' => 'Draai 270° met de klok mee',
        'Rotated 90 CW' => '90° tegen de klok in (rechts/boven)',
      },
    },
   'RowsPerStrip' => 'Aantal lijnen in de afbeelding',
   'SPIFFVersion' => 'SPIFF versie',
   'SRActive' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'SVGVersion' => 'SVG versie',
   'SampleFormat' => {
      Description => 'Sample Formaat',
      PrintConv => {
        'Complex int' => 'Complexe integer',
        'Float' => 'Drijvende komma waarde',
        'Signed' => 'Integer met voorteken',
        'Undefined' => 'Niet gedefinierd',
        'Unsigned' => 'Integer zonder voorteken',
      },
    },
   'SamplesPerPixel' => 'Aantal van de componenten',
   'Saturation' => {
      Description => 'Kleurverzadiging',
      PrintConv => {
        'High' => 'Hoge kleurvezadiging',
        'Low' => 'Lage kleurverzadiging',
        'Normal' => 'Normaal',
      },
    },
   'ScanImageEnhancer' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'SceneCaptureType' => {
      Description => 'Scene opname type',
      PrintConv => {
        'Landscape' => 'Landschap',
        'Night' => 'Nachtscene',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'SceneMode' => {
      Description => 'Scènekeuze',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => 'Anti-bewegingswaas',
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Auto' => 'Automatisch',
        'Cont. Priority AE' => 'Continuvoork. AE',
        'Handheld Night Shot' => 'Nachtopname uit hand',
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatig',
        'Night Portrait' => 'Nachtportret',
        'Night Scene' => 'Nacht',
        'Night View/Portrait' => 'Nacht/portret',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'Portrait' => 'Portret',
        'Shutter Priority' => 'Sluiterprioriteit',
        'Sports' => 'Sportactie',
        'Standard' => 'Standaard',
        'Sunset' => 'Zonsondergang',
        'Sweep Panorama' => 'Panorama d. beweg.',
      },
    },
   'SceneModeUsed' => {
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Landscape' => 'Landschap',
        'Manual' => 'Handmatig',
        'Portrait' => 'Portret',
        'Shutter Priority' => 'Sluiterprioriteit',
      },
    },
   'SceneSelect' => {
      PrintConv => {
        'Night' => 'Nachtscene',
        'Off' => 'Uit',
      },
    },
   'SceneType' => {
      Description => 'Scene type',
      PrintConv => {
        'Directly photographed' => 'Direkt opgenomen afbeelding',
      },
    },
   'SecurityClassification' => {
      Description => 'Veiligheid classificering',
      PrintConv => {
        'Confidential' => 'Vertrouwelijk',
        'Restricted' => 'Begrensd',
        'Secret' => 'Geheim',
        'Top Secret' => 'Streng geheim',
        'Unclassified' => 'Niet geclassificeerd',
      },
    },
   'SelfTimer' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'SelfTimerMode' => 'Zelfontspanner mode',
   'SelfTimerTime' => 'Vertraging zelfontspanner',
   'SensingMethod' => {
      Description => 'Meet methode',
      PrintConv => {
        'Color sequential area' => 'Color sequential area sensor',
        'Color sequential linear' => 'Kleur sequentiële lineaire sensor',
        'Monochrome area' => 'Monochrome sensor',
        'Monochrome linear' => 'Monochrome lineaire sensor',
        'Not defined' => 'Niet gedefineerd',
        'One-chip color area' => 'Één chip kleur sensor',
        'Three-chip color area' => 'Drie chip kleur sensor',
        'Trilinear' => 'Trilineaire sensor',
        'Two-chip color area' => 'Twee chip kleur sensor',
      },
    },
   'SequentialShot' => {
      PrintConv => {
        'None' => 'Geen',
        'Standard' => 'Standaard',
      },
    },
   'SerialNumber' => 'Camera-ID',
   'ServiceIdentifier' => 'Service ID',
   'SetButtonCrossKeysFunc' => {
      PrintConv => {
        'Normal' => 'Normaal',
      },
    },
   'ShadingCompensation' => {
      Description => 'Schaduwcompensatie',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ShadingCompensation2' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ShakeReduction' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Sharpness' => {
      Description => 'Scherpte',
      PrintConv => {
        'Hard' => '+',
        'Normal' => 'Normaal',
        'Soft' => 'Zacht',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
        'Standard' => 'Standaard',
      },
    },
   'ShootingInfoDisplay' => {
      Description => 'Weergave opname-info',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Manual (dark on light)' => 'Handmatig - Donker op licht',
        'Manual (light on dark)' => 'Handmatig - Licht op donker',
      },
    },
   'ShootingMode' => {
      Description => 'Opnamestand',
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Manual' => 'Handmatig',
        'Normal' => 'Normaal',
        'Portrait' => 'Portret',
        'Shutter Priority' => 'Sluiterprioriteit',
      },
    },
   'ShootingModeSetting' => {
      Description => 'Opnamestand',
      PrintConv => {
        'Continuous' => 'Continu',
        'Delayed Remote' => 'Vertraagd op afstand',
        'Quick-response Remote' => 'Direct op afstand',
        'Self-timer' => 'Zelfontspanner',
        'Single Frame' => 'Enkel beeld',
      },
    },
   'ShortDocumentID' => 'Korte foto ID',
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => 'Diafragmaprioriteit',
        'Auto' => 'Automatisch',
      },
    },
   'ShutterReleaseButtonAE-L' => {
      Description => 'AE-vergr. ontspanknop',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ShutterReleaseNoCFCard' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ShutterSpeed' => 'Belichtingstijd',
   'ShutterSpeedValue' => 'Belichtingstijd',
   'SimilarityIndex' => 'Fotogelijkheidsindex',
   'SlideShow' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'SlowShutter' => {
      PrintConv => {
        'None' => 'Geen',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'SlowSync' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'Software' => 'Gebruikte software',
   'Source' => 'Bron',
   'SpatialFrequencyResponse' => 'Spatial frequency response',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'SpecialInstructions' => 'Instructies',
   'SpectralSensitivity' => 'Spectrale gevoeligheid',
   'State' => 'Staat',
   'StripByteCounts' => 'Aantal bytes per gecomprimeerd afbeelding deel',
   'StripOffsets' => 'Positie van afbeelding data',
   'Sub-location' => 'Locatie',
   'SubSecCreateDate' => 'Datum van de originele data generatie',
   'SubSecDateTimeOriginal' => 'Datum van de originele data generatie',
   'SubSecModifyDate' => 'Datum bestand wijziging',
   'SubSecTime' => 'DateTime 1/100 seconden',
   'SubSecTimeDigitized' => 'DateTimeDigitized 1/100 seconden',
   'SubSecTimeOriginal' => 'DateTimeOriginal 1/100 seconden',
   'SubfileType' => 'Nieuw subbestand type',
   'Subject' => 'Onderwerp',
   'SubjectArea' => 'Positie hoofdobject',
   'SubjectDistance' => 'Object afstand',
   'SubjectDistanceRange' => {
      Description => 'Objectief afstandsbereik',
      PrintConv => {
        'Close' => 'Dichtbij',
        'Distant' => 'Verweg',
        'Unknown' => 'Onbekend',
      },
    },
   'SubjectLocation' => 'Positie hoofdobject',
   'SubjectProgram' => {
      PrintConv => {
        'None' => 'Geen',
        'Portrait' => 'Portret',
      },
    },
   'SubjectReference' => 'Onderwerp code',
   'Subsystem' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'SuperMacro' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'SuperimposedDisplay' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'SupplementalCategories' => 'Aanvullende categorie',
   'T4Options' => 'Opvul bit toegevoegd',
   'T6Options' => 'T6 Opties',
   'Tagged' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'TargetPrinter' => 'Doel Printer',
   'Technology' => {
      Description => 'Technologie',
      PrintConv => {
        'Active Matrix Display' => 'Actieve matrixdisplay',
        'Cathode Ray Tube Display' => 'CRT-beeldscherm',
        'Digital Camera' => 'Digitale camera',
        'Dye Sublimation Printer' => 'Dye sublimation printer',
        'Electrophotographic Printer' => 'Laserprinter',
        'Electrostatic Printer' => 'Electrostatische printer',
        'Film Scanner' => 'Filmscanner',
        'Film Writer' => 'Filmprinter',
        'Flexography' => 'Flexografie',
        'Gravure' => 'Fotogravure - koperdiepdruk',
        'Ink Jet Printer' => 'Inkjet printer',
        'Offset Lithography' => 'Offset Lithografie',
        'Passive Matrix Display' => 'Passieve matrixdisplay',
        'Photo CD' => 'Photo-CD',
        'Photo Image Setter' => 'Fotofilmbelichter',
        'Photographic Paper Printer' => 'Fotopapier printer',
        'Projection Television' => 'Projectietelevisie',
        'Reflective Scanner' => 'Reflectieve Scanner',
        'Thermal Wax Printer' => 'Thermische was printer',
        'Video Camera' => 'Videocamera',
        'Video Monitor' => 'Videomonitor',
      },
    },
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'TextStamp' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ThumbnailImage' => 'Miniatuur',
   'ThumbnailImageSize' => 'Thumbnail formaat',
   'TimeCreated' => 'Opnametijdstip',
   'TimeScaleParamsQuality' => {
      PrintConv => {
        'High' => 'Hoog',
        'Low' => 'Laag',
      },
    },
   'TimeSent' => 'Tijdstip van zenden',
   'TimerFunctionButton' => {
      Description => 'Fn-knop',
      PrintConv => {
        'ISO' => 'ISO-gevoeligheid',
        'Image Quality/Size' => 'Bldkwaliteit/-formaat',
        'Self-timer' => 'Zelfontspanner',
        'Shooting Mode' => 'Opnamestand',
        'White Balance' => 'Witbalans',
      },
    },
   'Title' => 'Titel',
   'ToneCurve' => {
      PrintConv => {
        'Manual' => 'Handmatig',
        'Standard' => 'Standaard',
      },
    },
   'ToneCurveActive' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'ToningEffect' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'ToningEffectMonochrome' => {
      PrintConv => {
        'None' => 'Geen',
      },
    },
   'TransferFunction' => 'Transformatie functie',
   'Transformation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (boven/links)',
        'Mirror horizontal' => 'Horizontaal gespiegeld',
        'Mirror horizontal and rotate 270 CW' => 'Spiegel horizontaal en draai 270° met de klok mee',
        'Mirror horizontal and rotate 90 CW' => 'Spiegel horizontaal en draai 90° met de klok mee',
        'Mirror vertical' => 'Vertikaal gespiegeld',
        'Rotate 180' => '180° (onder/rechts)',
        'Rotate 270 CW' => 'Draai 270° met de klok mee',
        'Rotate 90 CW' => '90° tegen de klok in (rechts/boven)',
      },
    },
   'TransmissionReference' => 'Transmissiereferentie',
   'Trapped' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'Uncompressed' => {
      Description => 'Niet gecomprimeerd',
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'UniqueDocumentID' => 'Uniek foto ID',
   'UniqueObjectName' => 'Unieke Naam van het Object',
   'Unknown' => 'Onbekend',
   'UnsharpMask' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'Urgency' => {
      Description => 'Urgentie',
      PrintConv => {
        '0 (reserved)' => '0 (Gereserveerd voor toekomstig gebruik)',
        '1 (most urgent)' => '1 (zeer dringend)',
        '5 (normal urgency)' => '5 (dringend)',
        '8 (least urgent)' => '8 (minst dringend)',
        '9 (user-defined priority)' => '9 (Gereserveerd voor toekomstig gebruik)',
      },
    },
   'UserComment' => 'Gebruiker kommentaar',
   'UserDef1PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'UserDef2PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'UserDef3PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Landschap',
        'Portrait' => 'Portret',
        'Standard' => 'Standaard',
      },
    },
   'VRDVersion' => 'VRD versie',
   'VR_0x66' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'Version' => 'Versie',
   'VibrationReduction' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'ViewfinderWarning' => {
      Description => 'Zoekerwaarschuwing',
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'VignetteControl' => {
      PrintConv => {
        'High' => 'Hoog',
        'Normal' => 'Normaal',
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'VoiceMemo' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'WBAdjLighting' => {
      PrintConv => {
        'Daylight (cloudy)' => 'Daglicht (2)',
        'Daylight (direct sunlight)' => 'Daglicht (0)',
        'Daylight (shade)' => 'Daglicht (1)',
        'Flash' => 'Flits',
        'None' => 'Geen',
      },
    },
   'WBBracketMode' => {
      PrintConv => {
        'Off' => 'Uit',
      },
    },
   'WBFineTuneActive' => {
      PrintConv => {
        'No' => 'Nee',
        'Yes' => 'Ja',
      },
    },
   'WBMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'WCSProfiles' => 'Windows kleursysteemprofiel',
   'WhiteBalance' => {
      Description => 'Witbalans',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Black & White' => 'Monochroom',
        'Cloudy' => 'Bewolkt',
        'Color Temperature/Color Filter' => 'Kleurtemperatuur / Kleurfilter',
        'Cool White Fluorescent' => 'Koelwit TL-licht',
        'Custom' => 'Eigen instel.',
        'Custom 1' => 'VOORKEUR 1',
        'Custom 2' => 'VOORKEUR 2',
        'Custom 3' => 'VOORKEUR 3',
        'Custom 4' => 'VOORKEUR 4',
        'Day White Fluorescent' => 'Daglicht wit TL-licht',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'Manual' => 'Handmatig',
        'Shade' => 'Schaduw',
        'Tungsten' => 'Kunstlicht (gloeilamp)',
        'Unknown' => 'Onbekend',
        'Warm White Fluorescent' => 'Warm wit TL-licht',
        'White Fluorescent' => 'Wit TL-licht',
      },
    },
   'WhiteBalance2' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'WhiteBalanceAdj' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Cloudy' => 'Bewolkt',
        'Daylight' => 'Daglicht',
        'Flash' => 'Flits',
        'Fluorescent' => 'Fluoresceren',
        'Off' => 'Uit',
        'On' => 'Aan',
        'Shade' => 'Schaduw',
        'Tungsten' => 'Kunstlicht (gloeilamp)',
      },
    },
   'WhiteBalanceMode' => {
      PrintConv => {
        'Unknown' => 'Onbekend',
      },
    },
   'WhiteBalanceSet' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Cloudy' => 'Bewolkt',
        'Daylight' => 'Daglicht',
        'Daylight Fluorescent' => 'Daglicht TL-licht',
        'Flash' => 'Flits',
        'Manual' => 'Handmatig',
        'Shade' => 'Schaduw',
        'Tungsten' => 'Kunstlicht (gloeilamp)',
        'White Fluorescent' => 'Warm wit TL-licht',
      },
    },
   'WhitePoint' => 'Wit punt chromaticiteit',
   'WideRange' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
   'WidthResolution' => 'Beeldresolutie horizontaal',
   'Writer-Editor' => 'Titel/Beschrijving auteur',
   'XMP' => 'XMP metadata',
   'XPAuthor' => 'Auteur',
   'XPComment' => 'Kommentaar',
   'XPKeywords' => 'Trefwoorden',
   'XPSubject' => 'Onderwerp',
   'XPTitle' => 'Titel',
   'XPosition' => 'X positie',
   'XResolution' => 'Horizontale afbeelding resolutie',
   'YCbCrCoefficients' => 'YCbCr coëfficiënt',
   'YCbCrPositioning' => {
      Description => 'Y en C positie',
      PrintConv => {
        'Centered' => 'Gecentreerd',
        'Co-sited' => 'Naast liggend',
      },
    },
   'YCbCrSubSampling' => 'Subsampling ratio van Y tot C',
   'YPosition' => 'Y positie',
   'YResolution' => 'Vertikale afbeelding resolutie',
   'ZoneMatching' => {
      Description => 'Zoneaanpassing',
      PrintConv => {
        'High Key' => 'Hi',
        'ISO Setting Used' => 'Uit',
        'Low Key' => 'Lo',
      },
    },
   'ZoneMatchingOn' => {
      PrintConv => {
        'Off' => 'Uit',
        'On' => 'Aan',
      },
    },
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::nl.pm - ExifTool Dutch language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Peter Moonen, Herman Beld and Peter van der Laan for
providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
