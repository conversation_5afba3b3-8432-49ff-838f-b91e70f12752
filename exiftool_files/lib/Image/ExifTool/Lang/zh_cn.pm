#------------------------------------------------------------------------------
# File:         zh_cn.pm
#
# Description:  ExifTool Simplified Chinese language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::zh_cn;

use strict;
use vars qw($VERSION);

$VERSION = '1.08';

%Image::ExifTool::Lang::zh_cn::Translate = (
   'AEProgramMode' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'AFAreaIllumination' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'AFPointIllumination' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'AFPointMode' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'AFPointRegistration' => {
      PrintConv => {
        'Automatic' => '自动',
      },
    },
   'AFPointSelected' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'AFPointSelected2' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'AFPointsUnknown2' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'APEVersion' => 'APE 版本',
   'AdultContentWarning' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'Album' => '相册',
   'Anti-Blur' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'Aperture' => '光圈数',
   'ApertureValue' => '光圈',
   'Artist' => '图像作者',
   'Author' => '作者',
   'AuthorsPosition' => '职位',
   'AutoLightingOptimizer' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'AutoRotate' => {
      PrintConv => {
        'Rotate 180' => '180° (底/右)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
        'n/a' => '未知',
      },
    },
   'BatteryLevel' => '电池电量',
   'BitsPerSample' => '每个组件的比特数',
   'BracketShotNumber' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'Brightness' => '亮度',
   'BrightnessValue' => '亮度',
   'By-line' => '作者',
   'CFAPattern' => 'CFA 模式',
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => '阴天',
        'Cool White Fluorescent' => '冷白色荧光灯（W3800-4500K）',
        'Day White Fluorescent' => '日光白色荧光灯（N4600-5500K）',
        'Daylight' => '太阳光',
        'Daylight Fluorescent' => '日光色荧光灯（D5700-7100K）',
        'Fine Weather' => '晴天',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'ISO Studio Tungsten' => 'ISO相室白炽灯',
        'Other' => '其他光源',
        'Shade' => '阴影',
        'Standard Light A' => '标准光A',
        'Standard Light B' => '标准光B',
        'Standard Light C' => '标准光C',
        'Tungsten (Incandescent)' => '白炽灯',
        'Unknown' => '未知',
        'Warm White Fluorescent' => '暖白荧光灯（L2600-3250K）',
        'White Fluorescent' => '白色荧光灯（WW3250-3800K）',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => '阴天',
        'Cool White Fluorescent' => '冷白色荧光灯（W3800-4500K）',
        'Day White Fluorescent' => '日光白色荧光灯（N4600-5500K）',
        'Daylight' => '太阳光',
        'Daylight Fluorescent' => '日光色荧光灯（D5700-7100K）',
        'Fine Weather' => '晴天',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'ISO Studio Tungsten' => 'ISO相室白炽灯',
        'Other' => '其他光源',
        'Shade' => '阴影',
        'Standard Light A' => '标准光A',
        'Standard Light B' => '标准光B',
        'Standard Light C' => '标准光C',
        'Tungsten (Incandescent)' => '白炽灯',
        'Unknown' => '未知',
        'Warm White Fluorescent' => '暖白荧光灯（L2600-3250K）',
        'White Fluorescent' => '白色荧光灯（WW3250-3800K）',
      },
    },
   'CameraOrientation' => {
      Description => '图像取向',
      PrintConv => {
        'Horizontal (normal)' => '0° (上/左)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
      },
    },
   'CanonExposureMode' => {
      PrintConv => {
        'Aperture-priority AE' => '光圈优先',
        'Manual' => '手动',
        'Shutter speed priority AE' => '快门优先',
      },
    },
   'CanonFlashMode' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'Caption-Abstract' => '说明',
   'CaptionWriter' => '说明作者',
   'CaptureXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (微米)',
      },
    },
   'CaptureYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (微米)',
      },
    },
   'Categories' => '类别',
   'Category' => '类别',
   'City' => '城市',
   'ColorEffect' => {
      PrintConv => {
        'Sepia' => '怀旧深咖啡色',
      },
    },
   'ColorFilter' => '颜色滤镜',
   'ColorMode' => {
      Description => '创意风格',
      PrintConv => {
        'Autumn Leaves' => '红叶',
        'B&W' => '黑白',
        'Clear' => '清澈',
        'Deep' => '深色',
        'Landscape' => '风景',
        'Light' => '轻淡',
        'Neutral' => '中性',
        'Night View' => '夜景',
        'Night View/Portrait' => '夜晚肖像',
        'Portrait' => '人物',
        'Standard' => '标准',
        'Sunset' => '黄昏',
        'Vivid' => '生动色彩',
      },
    },
   'ColorSpace' => {
      Description => '色彩空间信息',
      PrintConv => {
        'ICC Profile' => '色彩特性文件',
        'Uncalibrated' => '自发',
      },
    },
   'ColorTemperature' => '色温',
   'CommanderGroupAMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'CommanderGroupBMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'CommanderInternalFlash' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'Comment' => '注释',
   'ComponentsConfiguration' => '各分组的含义',
   'CompressedBitsPerPixel' => '图像压缩模式',
   'Compression' => {
      Description => '压缩方案',
      PrintConv => {
        'JPEG' => 'JEPG压缩率',
        'JPEG (old-style)' => 'JPEG (旧样式)',
        'Kodak DCR Compressed' => '柯达 DCR 压缩',
        'Kodak KDC Compressed' => '柯达 KDC 压缩',
        'Next' => 'NeXT 2比特编码',
        'Nikon NEF Compressed' => '尼康 NEF 压缩',
        'Pentax PEF Compressed' => '宾得 PEF 压缩',
        'SGILog' => 'SGI 32比特对数亮度编码',
        'SGILog24' => 'SGI 24比特对数亮度编码',
        'Sony ARW Compressed' => '索尼 ARW 压缩',
        'Thunderscan' => 'ThunderScan 4比特编码',
        'Uncompressed' => '未压缩',
      },
    },
   'Contrast' => {
      Description => '对比度',
      PrintConv => {
        'High' => '硬调',
        'Low' => '软调',
        'Normal' => '标准',
      },
    },
   'ControlMode' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'Copyright' => '专利拥有者',
   'CopyrightNotice' => '版权信息',
   'CopyrightStatus' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'Country' => '国家',
   'Country-PrimaryLocationName' => '国家',
   'CreateDate' => '数字数据产生的日期和时间',
   'Credit' => '作者',
   'CustomRendered' => {
      Description => '用户自定义图像处理',
      PrintConv => {
        'Custom' => '自定义处理',
        'Normal' => '普通模式',
      },
    },
   'DateCreated' => '创建日期',
   'DateTime' => '文件改变的日期和时间',
   'DateTimeOriginal' => '原始数据产生的日期和时间',
   'DeviceSettingDescription' => '设备设定说明',
   'DigitalZoom' => '数码变焦',
   'DigitalZoomRatio' => '数码变焦比',
   'Directory' => '文件存储位置',
   'DisplayXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (微米)',
      },
    },
   'DisplayYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (微米)',
      },
    },
   'DjVuVersion' => 'DjVu 版本',
   'DriveMode' => '驱动模式',
   'DynamicRangeOptimizer' => {
      Description => '动态范围优化',
      PrintConv => {
        'Advanced Auto' => '高级自动',
        'Advanced Lv1' => '高级优化程度 1',
        'Advanced Lv2' => '高级优化程度 2',
        'Advanced Lv3' => '高级优化程度 3',
        'Advanced Lv4' => '高级优化程度 4',
        'Advanced Lv5' => '高级优化程度 5',
        'Auto' => '自动',
        'Off' => '关',
        'Standard' => '标准',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Landscape' => '风景',
        'Manual' => '手动',
        'Night' => '夜景',
        'Portrait' => '人物',
      },
    },
   'ExifImageHeight' => '像高',
   'ExifImageWidth' => '像宽',
   'ExifOffset' => 'Exif IFD 指针',
   'ExifToolVersion' => 'ExifTool 版本',
   'ExifVersion' => 'Exif 版本',
   'ExpandFilm' => '胶片扩展',
   'ExpandFilterLens' => '滤镜扩展',
   'ExpandFlashLamp' => '闪光灯扩展',
   'ExpandLens' => '镜头扩展',
   'ExpandScanner' => '扫描仪扩展',
   'ExpandSoftware' => '软件扩展',
   'ExposureCompensation' => '曝光偏差',
   'ExposureIndex' => '曝光索引',
   'ExposureMode' => {
      Description => '曝光模式',
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Aperture-priority AE' => '光圈优先',
        'Auto' => '自动曝光',
        'Auto bracket' => '自动支架',
        'Landscape' => '风景',
        'Manual' => '手动曝光',
        'Portrait' => '人物',
        'Shutter Priority' => '快门优先',
        'Shutter speed priority AE' => '快门优先',
        'n/a' => '未设置',
      },
    },
   'ExposureProgram' => {
      Description => '曝光程序',
      PrintConv => {
        'Action (High speed)' => '运动摄影',
        'Aperture Priority' => '光圈优先',
        'Aperture-priority AE' => '光圈优先',
        'Creative (Slow speed)' => '创意摄影',
        'Landscape' => '风景',
        'Manual' => '手动',
        'Portrait' => '人物',
        'Program AE' => '一般程序',
        'Shutter Priority' => '快门优先',
        'Shutter speed priority AE' => '快门优先',
      },
    },
   'ExposureTime' => '曝光时间',
   'ExposureTime2' => '曝光时间 2',
   'ExternalFlashBounce' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'ExternalFlashExposureComp' => {
      PrintConv => {
        'n/a' => '未设置 (Off or Auto Modes)',
        'n/a (Manual Mode)' => '未设置 (Manual Mode)',
      },
    },
   'FNumber' => '光圈数',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (上/左)',
        'Rotate 180' => '180° (底/右)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
      },
    },
   'FaxProfile' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'FileFormat' => '格式',
   'FileModifyDate' => '更新日期',
   'FileName' => '文件名',
   'FileSize' => '文件大小',
   'FileSource' => {
      Description => '文件来源',
      PrintConv => {
        'Digital Camera' => '数码相机',
        'Film Scanner' => '胶片扫描仪',
        'Reflection Print Scanner' => '反射型扫描仪',
      },
    },
   'FileType' => '文件格式',
   'FilterEffect' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'Flash' => {
      Description => '闪光',
      PrintConv => {
        'Auto, Fired' => '开 (自动闪光)',
        'Auto, Fired, Red-eye reduction' => '开 (自动闪光，减轻红眼)',
        'Auto, Fired, Red-eye reduction, Return detected' => '开 (强制闪光，减轻红眼，检测返回光)',
        'Auto, Fired, Return detected' => '开 (自动闪光，检测返回光)',
        'Did not fire' => '闪光灯未亮',
        'Fired' => '闪光灯亮',
        'Fired, Red-eye reduction' => '开 (减轻红眼)',
        'Fired, Red-eye reduction, Return detected' => '开 (减轻红眼，检测返回光)',
        'Fired, Return detected' => '开 (检测返回光)',
        'No Flash' => '无闪光功能',
        'On, Fired' => '开 (强制闪光)',
        'On, Red-eye reduction' => '开 (强制闪光(减轻红眼))',
        'On, Red-eye reduction, Return detected' => '开 (强制闪光，减轻红眼，检测返回光)',
        'On, Return detected' => '开 (强制闪光，检测返回光)',
      },
    },
   'FlashControlMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FlashEnergy' => '闪光强度',
   'FlashExposureComp' => '闪光补偿',
   'FlashGroupAControlMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FlashGroupBControlMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FlashGroupCControlMode' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FlashMode' => {
      PrintConv => {
        'Auto' => '自动',
        'Unknown' => '未知',
      },
    },
   'FlashOptions' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'FlashOptions2' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'FlashSyncSpeedAv' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'FlashpixVersion' => '支持的 Flashpix 版本',
   'FocalLength' => '焦距',
   'FocalLength35efl' => '焦点距离(35 mm 换算)',
   'FocalLengthIn35mmFormat' => '35 mm 换算镜头焦点距离',
   'FocalPlaneResolutionUnit' => {
      Description => '焦平面分辨率单位',
      PrintConv => {
        'inches' => '英寸',
        'um' => 'µm (微米)',
      },
    },
   'FocalPlaneXResolution' => '焦平面 X 分辨率',
   'FocalPlaneYResolution' => '焦平面 Y 分辨率',
   'Focus' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FocusContinuous' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FocusMode' => {
      Description => '对焦模式',
      PrintConv => {
        'Auto' => '自动',
        'Manual' => '手动',
      },
    },
   'FocusMode2' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FocusModeSetting' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'FocusRange' => {
      PrintConv => {
        'Auto' => '自动',
        'Manual' => '手动',
      },
    },
   'FrameRate' => '更新速率',
   'FrameSize' => '画面尺寸',
   'FreeByteCounts' => '空白字节数',
   'FujiFlashMode' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'GIFVersion' => 'GIF 版本',
   'GPSAltitude' => '海拔高度',
   'GPSAltitudeRef' => {
      Description => '高度基准',
      PrintConv => {
        'Above Sea Level' => '海平面',
        'Below Sea Level' => '海拔(负值)',
      },
    },
   'GPSAreaInformation' => 'GPS区域名',
   'GPSDOP' => '测量精度',
   'GPSDateStamp' => 'GPS日期',
   'GPSDestBearing' => '目的地方位',
   'GPSDestBearingRef' => '目的地方位参照',
   'GPSDestDistance' => '离终点的距离',
   'GPSDestDistanceRef' => {
      Description => '距目的地距离参照',
      PrintConv => {
        'Kilometers' => '公里数',
        'Miles' => '英里数',
        'Nautical Miles' => '节数',
      },
    },
   'GPSDestLatitude' => '终点纬度',
   'GPSDestLatitudeRef' => {
      Description => '目的地的北纬或南纬',
      PrintConv => {
        'North' => '北纬',
        'South' => '南纬',
      },
    },
   'GPSDestLongitude' => '终点经度',
   'GPSDestLongitudeRef' => {
      Description => '目的地的东经或西经',
      PrintConv => {
        'East' => '东经',
        'West' => '西经',
      },
    },
   'GPSDifferential' => {
      Description => 'GPS 差分校正',
      PrintConv => {
        'Differential Corrected' => '差分校正定位',
        'No Correction' => '非差分校正测量',
      },
    },
   'GPSImgDirection' => '图像方向',
   'GPSImgDirectionRef' => '图像方向参照',
   'GPSInfo' => 'GPS Info IFD 指针',
   'GPSLatitude' => '纬度',
   'GPSLatitudeRef' => {
      Description => '北纬或者南纬',
      PrintConv => {
        'North' => '北纬',
        'South' => '南纬',
      },
    },
   'GPSLongitude' => '经度',
   'GPSLongitudeRef' => {
      Description => '东经或者西经',
      PrintConv => {
        'East' => '东经',
        'West' => '西经',
      },
    },
   'GPSMapDatum' => '使用的大地测量数据',
   'GPSMeasureMode' => {
      Description => 'GPS测量模式',
      PrintConv => {
        '2-D' => '2维测量',
        '2-Dimensional' => '2维测量',
        '2-Dimensional Measurement' => '2维测量',
        '3-D' => '3维测量',
        '3-Dimensional' => '3维测量',
        '3-Dimensional Measurement' => '3维测量',
      },
    },
   'GPSProcessingMethod' => '定位方式名称',
   'GPSSatellites' => '用于定位的 GPS 卫星信号',
   'GPSSpeed' => 'GPS接收器的速度',
   'GPSSpeedRef' => {
      Description => '速度单位',
      PrintConv => {
        'km/h' => '公里/小时',
        'knots' => '海里/小时',
        'mph' => '英里/小时',
      },
    },
   'GPSStatus' => {
      Description => 'GPS 接收器状态',
      PrintConv => {
        'Measurement Active' => '正在测量中',
        'Measurement Void' => '测量中断',
      },
    },
   'GPSTimeStamp' => 'GPS 时间(原子钟)',
   'GPSTrack' => '运动方向',
   'GPSTrackRef' => {
      Description => '运动方向参照',
      PrintConv => {
        'Magnetic North' => '磁场方向',
        'True North' => '真方向',
      },
    },
   'GPSVersionID' => 'GPS标签版本',
   'GainControl' => {
      Description => '增益控制',
      PrintConv => {
        'High gain down' => '强减少',
        'High gain up' => '强增益',
        'Low gain down' => '弱减少',
        'Low gain up' => '弱增益',
        'None' => '无',
      },
    },
   'Gamma' => '对比系数',
   'Gradation' => '灰阶',
   'HDR' => {
      Description => '自动HDR',
      PrintConv => {
        'Off' => '关',
      },
    },
   'Headline' => '标题',
   'HighISONoiseReduction' => {
      Description => '高ISO降噪',
      PrintConv => {
        'Auto' => '自动',
        'High' => '强',
        'Low' => '弱',
        'Normal' => '标准',
        'Off' => '关',
      },
    },
   'Hue' => '色相',
   'ICCProfile' => 'ICC 规范',
   'IPTC-NAA' => 'IPTC-NAA 元数据',
   'ISOSetting' => {
      PrintConv => {
        'Auto' => '自动',
        'Manual' => '手动',
      },
    },
   'ImageDescription' => '图像标题',
   'ImageHeight' => '像高',
   'ImageOrientation' => {
      PrintConv => {
        'Portrait' => '人物',
      },
    },
   'ImageSize' => '图像尺寸',
   'ImageTone' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'ImageUniqueID' => '图像唯一标识',
   'ImageWidth' => '像宽',
   'Index' => '索引',
   'Instructions' => '指示',
   'InternalFlash' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'InteropIndex' => {
      Description => '互用标识',
      PrintConv => {
        'THM - DCF thumbnail file' => 'THM: DCF 缩略图文件',
      },
    },
   'InteropOffset' => '互用指针',
   'InteropVersion' => '互用版本',
   'JFIFVersion' => 'JFIF 版本',
   'JPEGQuality' => {
      Description => '图像质量',
      PrintConv => {
        'Extra Fine' => '超精细',
        'Fine' => '精细',
        'Standard' => '标准画质',
        'n/a' => '未设置',
      },
    },
   'Keyword' => '关键词',
   'Keywords' => '关键字',
   'Lens' => '镜头',
   'LensInfo' => '镜头信息',
   'LensType' => '未设置',
   'LicenseType' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'LightSource' => {
      Description => '光源',
      PrintConv => {
        'Cloudy' => '阴天',
        'Cool White Fluorescent' => '冷白色荧光灯（W3800-4500K）',
        'Day White Fluorescent' => '日光白色荧光灯（N4600-5500K）',
        'Daylight' => '太阳光',
        'Daylight Fluorescent' => '日光色荧光灯（D5700-7100K）',
        'Fine Weather' => '晴天',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'ISO Studio Tungsten' => 'ISO相室白炽灯',
        'Other' => '其他光源',
        'Shade' => '阴影',
        'Standard Light A' => '标准光A',
        'Standard Light B' => '标准光B',
        'Standard Light C' => '标准光C',
        'Tungsten (Incandescent)' => '白炽灯',
        'Unknown' => '未知',
        'Warm White Fluorescent' => '暖白荧光灯（L2600-3250K）',
        'White Fluorescent' => '白色荧光灯（WW3250-3800K）',
      },
    },
   'Lightness' => '明暗度',
   'LongExposureNoiseReduction' => {
      Description => '长时间曝光降噪',
      PrintConv => {
        'Auto' => '自动',
        'Off' => '关',
        'On' => '开',
        'n/a' => '未设置',
      },
    },
   'MIEVersion' => 'MIE 版本',
   'Macro' => {
      PrintConv => {
        'Manual' => '手动',
        'n/a' => '未设置',
      },
    },
   'Make' => '厂商',
   'MakerNote' => '厂商注释',
   'MakerNotes' => '制造商记录',
   'ManualFlashOutput' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'MaxAperture' => '镜头光圈最大值',
   'MaxApertureValue' => '最大镜头光圈',
   'MeteringMode' => {
      Description => '测量方式',
      PrintConv => {
        'Average' => '平均',
        'Center-weighted average' => '中央重点',
        'Multi-segment' => '分割测光',
        'Multi-spot' => '多点',
        'Other' => '其它',
        'Partial' => '部分测光',
        'Spot' => '点测光',
        'Unknown' => '未知',
      },
    },
   'Model' => '型号',
   'ModifiedPictureStyle' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Auto' => '自动',
        'Cloudy' => '阴天',
        'Daylight' => '太阳光',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'Shade' => '阴影',
      },
    },
   'ModifyDate' => '文件改变的日期和时间',
   'MultiFrameNoiseReduction' => {
      Description => '多帧降噪',
      PrintConv => {
        'Off' => '关',
        'On' => '开',
      },
    },
   'NEFCompression' => {
      PrintConv => {
        'Uncompressed' => '未压缩',
      },
    },
   'Noise' => '噪声',
   'NoiseReduction' => {
      Description => '减少噪声',
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'ObjectFileType' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'Opto-ElectricConvFactor' => '光电转换因子',
   'Orientation' => {
      Description => '图像取向',
      PrintConv => {
        'Horizontal (normal)' => '0° (上/左)',
        'Mirror horizontal' => '0° (上/右)',
        'Mirror horizontal and rotate 270 CW' => '90° CW (左/上)',
        'Mirror horizontal and rotate 90 CW' => '90° CCW (右/底)',
        'Mirror vertical' => '180° (底/左)',
        'Rotate 180' => '180° (底/右)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
      },
    },
   'PEFVersion' => 'PEF 版本',
   'PageNumber' => '页数',
   'PhotometricInterpretation' => {
      Description => '像素方案',
      PrintConv => {
        'BlackIsZero' => '零黑色',
        'Color Filter Array' => 'CFA (颜色滤镜矩阵)',
        'Pixar LogL' => 'CIE Log2(L) (对数亮度)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (对数亮度和色度)',
        'RGB Palette' => '调色板颜色',
        'Transparency Mask' => '透明蒙板',
        'WhiteIsZero' => '零白色',
      },
    },
   'PictureFinish' => {
      PrintConv => {
        'Portrait' => '人物',
      },
    },
   'PictureMode' => {
      PrintConv => {
        'Aperture-priority AE' => '光圈优先',
        'Auto' => '自动',
        'Landscape' => '风景',
        'Manual' => '手动',
        'Portrait' => '人物',
        'Shutter speed priority AE' => '快门优先',
      },
    },
   'PictureMode2' => {
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Manual' => '手动',
        'Shutter Speed Priority' => '快门优先',
      },
    },
   'PictureModeBWFilter' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'PictureModeTone' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'PictureStyle' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'PixelUnits' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'PlanarConfiguration' => {
      Description => '图像数据排列',
      PrintConv => {
        'Chunky' => '点顺序格式',
        'Planar' => '平面格式',
      },
    },
   'PreviewColorSpace' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'PrimaryChromaticities' => '原色色度坐标值',
   'ProgramMode' => {
      PrintConv => {
        'Portrait' => '人物',
      },
    },
   'Province-State' => '州／省',
   'Quality' => {
      Description => '图像质量',
      PrintConv => {
        'Compressed RAW' => 'cRAW',
        'Compressed RAW + JPEG' => 'cRAW+JPEG',
        'Extra Fine' => '超精细',
        'Fine' => '精细',
        'Low' => '低画质',
        'Normal' => '标准画质',
        'RAW + JPEG' => 'RAW+JPEG',
        'Standard' => '标准',
        'n/a' => '未设置',
      },
    },
   'RAFVersion' => 'RAF 版本',
   'Rating' => '评分',
   'RatingPercent' => '百分比评级',
   'RecordMode' => {
      Description => '记录模式',
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Manual' => '手动',
        'Shutter Priority' => '快门优先',
      },
    },
   'RecordingMode' => {
      PrintConv => {
        'Auto' => '自动',
        'Landscape' => '风景',
        'Manual' => '手动',
        'Portrait' => '人物',
      },
    },
   'RedEyeCorrection' => {
      PrintConv => {
        'Automatic' => '自动',
      },
    },
   'ReferenceBlackWhite' => '黑白参照值对',
   'RelatedImageFileFormat' => '相关图像文件格式',
   'RelatedImageHeight' => '相关图像高度',
   'RelatedImageWidth' => '相关图像宽度',
   'RelatedSoundFile' => '相关的音频文件',
   'ResolutionUnit' => {
      Description => '图像高宽分辨率单位',
      PrintConv => {
        'cm' => '像素/厘米',
        'inches' => '英寸',
      },
    },
   'Rotation' => {
      PrintConv => {
        'Horizontal' => '0° (上/左)',
        'Horizontal (Normal)' => '0° (上/左)',
        'Horizontal (normal)' => '0° (上/左)',
        'Rotate 180' => '180° (底/右)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
        'Rotated 180' => '180° (底/右)',
        'Rotated 270 CW' => '90° CW (左/底)',
        'Rotated 90 CW' => '90° CCW (右/上)',
      },
    },
   'RowsPerStrip' => '每条带的行数',
   'SPIFFVersion' => 'SPIFF 版本',
   'SRAWQuality' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'SVGVersion' => 'SVG 版本',
   'SamplesPerPixel' => '组件数',
   'Saturation' => {
      Description => '饱和度',
      PrintConv => {
        'High' => '高饱和度',
        'Low' => '低饱和度',
        'None' => '未设置',
        'Normal' => '标准',
      },
    },
   'SceneCaptureType' => {
      Description => '取景类型',
      PrintConv => {
        'Landscape' => '风景',
        'Night' => '夜景',
        'Portrait' => '人物',
        'Standard' => '标准',
      },
    },
   'SceneMode' => {
      Description => '场景选择',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => '动作防抖',
        'Aperture Priority' => '光圈优先',
        'Auto' => '自动',
        'Cont. Priority AE' => '连续拍摄优先AE',
        'Handheld Night Shot' => '手持夜景拍摄',
        'Landscape' => '风景',
        'Macro' => '微距',
        'Manual' => '手动',
        'Night Portrait' => '夜晚肖像',
        'Night Scene' => '夜景',
        'Night View/Portrait' => '夜景/肖像',
        'Portrait' => '人物',
        'Shutter Priority' => '快门优先',
        'Sports' => '运动模式',
        'Sunset' => '夕阳',
        'Sweep Panorama' => '扫描全景',
      },
    },
   'SceneModeUsed' => {
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Landscape' => '风景',
        'Manual' => '手动',
        'Portrait' => '人物',
        'Shutter Priority' => '快门优先',
      },
    },
   'SceneSelect' => {
      PrintConv => {
        'Night' => '夜景',
      },
    },
   'SceneType' => {
      Description => '场景类型',
      PrintConv => {
        'Directly photographed' => '直接拍摄的图像',
      },
    },
   'SensingMethod' => {
      Description => '感应方法',
      PrintConv => {
        'Color sequential area' => '色彩连续区感应器',
        'Color sequential linear' => '色彩连续线性感应器',
        'One-chip color area' => '单片色彩区感应器',
        'Three-chip color area' => '3 片色彩区传感器',
        'Trilinear' => '3 线传感器',
        'Two-chip color area' => '2 片色彩区传感器',
      },
    },
   'SerialNumber' => '照相机标识',
   'ShadingCompensation' => '阴影补偿',
   'Sharpness' => {
      Description => '锐度',
      PrintConv => {
        'Hard' => '强',
        'Normal' => '标准',
        'Soft' => '弱',
        'n/a' => '未设置',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'ShootingInfoDisplay' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'ShootingMode' => {
      Description => '拍摄模式',
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Manual' => '手动',
        'Portrait' => '人物',
        'Shutter Priority' => '快门优先',
      },
    },
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => '光圈优先',
        'Auto' => '自动',
      },
    },
   'ShutterSpeed' => '曝光时间',
   'ShutterSpeedValue' => '快门速度',
   'Software' => '使用软件',
   'Source' => '来源',
   'SpatialFrequencyResponse' => '空间频率响应',
   'SpectralSensitivity' => '光谱灵敏度',
   'State' => '州',
   'StripByteCounts' => '每压缩条带的字节数',
   'StripOffsets' => '图像数据位置',
   'SubSecCreateDate' => '数字数据产生的日期和时间',
   'SubSecDateTimeOriginal' => '原始数据产生的日期和时间',
   'SubSecModifyDate' => '文件改变的日期和时间',
   'SubSecTime' => '时间的次秒',
   'SubSecTimeDigitized' => '数字化时间的次秒',
   'SubSecTimeOriginal' => '原始时间的次秒',
   'Subject' => '主题',
   'SubjectArea' => '被摄对象区域',
   'SubjectDistance' => '目标距离',
   'SubjectDistanceRange' => {
      Description => '被摄对象距离范围',
      PrintConv => {
        'Close' => '近景',
        'Distant' => '远景',
        'Macro' => '微距',
        'Unknown' => '未知',
      },
    },
   'SubjectLocation' => '被摄对象位置',
   'SubjectProgram' => {
      PrintConv => {
        'Portrait' => '人物',
      },
    },
   'Subsystem' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'SupplementalCategories' => '追加类别',
   'SupplementalType' => {
      PrintConv => {
        'Main Image' => '未设置',
      },
    },
   'T4Options' => '未压缩',
   'T6Options' => 'T6 选项',
   'ThumbnailImage' => '缩略图',
   'ThumbnailImageSize' => '缩图尺寸',
   'Title' => '标题',
   'ToneCurve' => {
      PrintConv => {
        'Manual' => '手动',
      },
    },
   'ToningEffect' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'TransferFunction' => '传送功能',
   'Transformation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (上/左)',
        'Mirror horizontal' => '0° (上/右)',
        'Mirror horizontal and rotate 270 CW' => '90° CW (左/上)',
        'Mirror horizontal and rotate 90 CW' => '90° CCW (右/底)',
        'Mirror vertical' => '180° (底/左)',
        'Rotate 180' => '180° (底/右)',
        'Rotate 270 CW' => '90° CW (左/底)',
        'Rotate 90 CW' => '90° CCW (右/上)',
      },
    },
   'TransmissionReference' => '传输记录',
   'Trapped' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'Urgency' => '紧急度',
   'UserComment' => '用户注释',
   'UserDef1PictureStyle' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'UserDef2PictureStyle' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'UserDef3PictureStyle' => {
      PrintConv => {
        'Landscape' => '风景',
        'Portrait' => '人物',
      },
    },
   'VRDVersion' => 'VRD 版本',
   'Version' => '版本',
   'VibrationReduction' => {
      PrintConv => {
        'n/a' => '未设置',
      },
    },
   'WBAdjLighting' => {
      PrintConv => {
        'Daylight (direct sunlight)' => '太阳光 (0)',
        'Daylight (shade)' => '太阳光 (1)',
        'Daylight (cloudy)' => '太阳光 (2)',
        'Flash' => '闪光',
      },
    },
   'WBMode' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'WhiteBalance' => {
      Description => '白平衡',
      PrintConv => {
        'Auto' => '自动',
        'Black & White' => '黑白',
        'Cloudy' => '阴天',
        'Color Temperature/Color Filter' => '色温 / 彩色滤光片',
        'Cool White Fluorescent' => '白色荧光灯',
        'Custom' => '自定义',
        'Custom 1' => '自定义1',
        'Custom 2' => '自定义2',
        'Custom 3' => '自定义3',
        'Custom 4' => '自定义4',
        'Day White Fluorescent' => '日光白色荧光灯',
        'Daylight' => '太阳光',
        'Daylight Fluorescent' => '日光色荧光灯',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'Manual' => '手动',
        'Shade' => '阴影',
        'Tungsten' => '白炽灯',
        'Unknown' => '未知',
        'Warm White Fluorescent' => '暖白荧光灯',
        'White Fluorescent' => '白色荧光灯',
      },
    },
   'WhiteBalance2' => {
      PrintConv => {
        'Auto' => '自动',
      },
    },
   'WhiteBalanceAdj' => {
      PrintConv => {
        'Auto' => '自动',
        'Cloudy' => '阴天',
        'Daylight' => '太阳光',
        'Flash' => '闪光',
        'Fluorescent' => '荧光',
        'Shade' => '阴影',
      },
    },
   'WhiteBalanceMode' => {
      PrintConv => {
        'Unknown' => '未知',
      },
    },
   'WhiteBalanceSet' => {
      PrintConv => {
        'Auto' => '自动',
        'Cloudy' => '阴天',
        'Daylight' => '太阳光',
        'Flash' => '闪光',
        'Manual' => '手动',
        'Shade' => '阴影',
      },
    },
   'WhitePoint' => '白点色度',
   'Writer-Editor' => '说明作者',
   'XMP' => 'XMP 元数据',
   'XPAuthor' => '作者',
   'XPComment' => '注释',
   'XPKeywords' => '关键词',
   'XPSubject' => '主题',
   'XPTitle' => '标题',
   'YCbCrCoefficients' => '色彩空间变换矩阵系数',
   'YCbCrPositioning' => {
      Description => 'YCC 像素结构(Y 和 C 的位置)',
      PrintConv => {
        'Centered' => '中心的',
        'Co-sited' => '一致',
      },
    },
   'YCbCrSubSampling' => 'YCC 像素结构(Y 至 C 的子采样率)',
   'ZoneMatching' => {
      Description => '区域匹配',
      PrintConv => {
        'High Key' => '强',
        'ISO Setting Used' => '关',
        'Low Key' => '弱',
      },
    },
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::zh_cn.pm - ExifTool Simplified Chinese language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke and Haibing Zhong for providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
