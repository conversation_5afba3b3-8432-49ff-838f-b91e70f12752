# -*- buffer-read-only: t -*-
#
# This file is auto-generated by ext/Errno/Errno_pm.PL.
# ***ANY*** changes here will be lost.
#

package Errno;
require Exporter;
use strict;

use Config;
"$Config{'archname'}-$Config{'osvers'}" eq
"MSWin32-x86-multi-thread-64int-10.0.19042.746" or
	die "Errno architecture (MSWin32-x86-multi-thread-64int-10.0.19042.746) does not match executable architecture ($Config{'archname'}-$Config{'osvers'})";

our $VERSION = "1.30";
$VERSION = eval $VERSION;
our @ISA = 'Exporter';

my %err;

BEGIN {
    %err = (
	ENUM_REGISTRY_SETTINGS => -2,
	ENUM_CURRENT_SETTINGS => -1,
	EXCEPTION_CONTINUE_EXECUTION => -1,
	EC_ENABLEALL => 0,
	ELF_CULTURE_LATIN => 0,
	ELF_VERSION => 0,
	EMARCH_ENC_I17_IMM41c_INST_WORD_POS_X => 0,
	EMARCH_ENC_I17_IMM7B_VAL_POS_X => 0,
	EMBDHLP_CREATENOW => 0,
	EMBDHLP_INPROC_HANDLER => 0,
	ERROR => 0,
	ERROR_BIDI_STATUS_OK => 0,
	ERROR_SEVERITY_SUCCESS => 0,
	ERROR_SUCCESS => 0,
	ESB_ENABLE_BOTH => 0,
	ES_LEFT => 0,
	EVENTLOG_FULL_INFO => 0,
	EVENTLOG_SUCCESS => 0,
	EWX_LOGOFF => 0,
	EXCEPTION_CONTINUE_SEARCH => 0,
	EXCEPTION_READ_FAULT => 0,
	EXIT_SUCCESS => 0,
	ExceptionContinueExecution => 0,
	EC_LEFTMARGIN => 1,
	EDD_GET_DEVICE_INTERFACE_NAME => 1,
	EEInfoPreviousRecordsMissing => 1,
	EFS_USE_RECOVERY_KEYS => 1,
	EIMES_GETCOMPSTRATONCE => 1,
	ELEMENT_STATUS_FULL => 1,
	EMARCH_ENC_I17_IC_SIZE_X => 1,
	EMARCH_ENC_I17_IMM41a_INST_WORD_X => 1,
	EMARCH_ENC_I17_IMM41b_INST_WORD_X => 1,
	EMARCH_ENC_I17_SIGN_SIZE_X => 1,
	EMBDHLP_INPROC_SERVER => 1,
	EMR_HEADER => 1,
	EMR_MIN => 1,
	EMSIS_COMPOSITIONSTRING => 1,
	ENABLE_PROCESSED_INPUT => 1,
	ENABLE_PROCESSED_OUTPUT => 1,
	ENCRYPTION_FORMAT_DEFAULT => 1,
	ENDSESSION_CLOSEAPP => 1,
	ENLISTMENT_MAXIMUM_OPTION => 1,
	ENLISTMENT_QUERY_INFORMATION => 1,
	ENLISTMENT_SUPERIOR => 1,
	EPERM => 1,
	ERROR_INVALID_FUNCTION => 1,
	ERROR_LABEL_UNREADABLE => 1,
	ESB_DISABLE_LEFT => 1,
	ESB_DISABLE_LTUP => 1,
	ESB_DISABLE_UP => 1,
	ES_CENTER => 1,
	ES_SYSTEM_REQUIRED => 1,
	EVENTLOG_ERROR_TYPE => 1,
	EVENTLOG_SEQUENTIAL_READ => 1,
	EVENTLOG_START_PAIRED_EVENT => 1,
	EVENT_MIN => 1,
	EVENT_SYSTEM_SOUND => 1,
	EV_RXCHAR => 1,
	EWX_SHUTDOWN => 1,
	EXCEPTION_DEBUG_EVENT => 1,
	EXCEPTION_EXECUTE_HANDLER => 1,
	EXCEPTION_NONCONTINUABLE => 1,
	EXCEPTION_WRITE_FAULT => 1,
	EXIT_FAILURE => 1,
	ExceptionContinueSearch => 1,
	EC_QUERYWAITING => 2,
	EC_RIGHTMARGIN => 2,
	EDS_RAWMODE => 2,
	EEInfoNextRecordsMissing => 2,
	EIMES_CANCELCOMPSTRINFOCUS => 2,
	ELEMENT_STATUS_IMPEXP => 2,
	EMARCH_ENC_I17_IMM41c_INST_WORD_X => 2,
	EMR_POLYBEZIER => 2,
	ENABLE_LINE_INPUT => 2,
	ENABLE_WRAP_AT_EOL_OUTPUT => 2,
	ENLISTMENT_SET_INFORMATION => 2,
	ENOENT => 2,
	ENOFILE => 2,
	ERROR_FILE_NOT_FOUND => 2,
	ERROR_LABEL_QUESTIONABLE => 2,
	ESB_DISABLE_DOWN => 2,
	ESB_DISABLE_RIGHT => 2,
	ESB_DISABLE_RTDN => 2,
	ES_DISPLAY_REQUIRED => 2,
	ES_RIGHT => 2,
	ETO_OPAQUE => 2,
	EVENPARITY => 2,
	EVENTLOG_END_PAIRED_EVENT => 2,
	EVENTLOG_SEEK_READ => 2,
	EVENTLOG_WARNING_TYPE => 2,
	EVENT_MODIFY_STATE => 2,
	EVENT_SYSTEM_ALERT => 2,
	EV_RXFLAG => 2,
	EWX_REBOOT => 2,
	EXCEPTION_UNWINDING => 2,
	EXTEND_IEPORT => 2,
	ExceptionNestedException => 2,
	EMARCH_ENC_I17_IC_INST_WORD_X => 3,
	EMARCH_ENC_I17_IMM5C_INST_WORD_X => 3,
	EMARCH_ENC_I17_IMM7B_INST_WORD_X => 3,
	EMARCH_ENC_I17_IMM9D_INST_WORD_X => 3,
	EMARCH_ENC_I17_SIGN_INST_WORD_X => 3,
	EMR_POLYGON => 3,
	ERROR_PATH_NOT_FOUND => 3,
	ESB_DISABLE_BOTH => 3,
	ESRCH => 3,
	EVENT_SYSTEM_FOREGROUND => 3,
	ExceptionCollidedUnwind => 3,
	EEInfoUseFileTime => 4,
	EIMES_COMPLETECOMPSTRKILLFOCUS => 4,
	EINTR => 4,
	ELEMENT_STATUS_EXCEPT => 4,
	ELF_VENDOR_SIZE => 4,
	EMARCH_ENC_I17_IMM7B_INST_WORD_POS_X => 4,
	EMR_POLYLINE => 4,
	ENABLE_ECHO_INPUT => 4,
	ENLISTMENT_RECOVER => 4,
	ERROR_SLOT_NOT_PRESENT => 4,
	ERROR_TOO_MANY_OPEN_FILES => 4,
	ES_MULTILINE => 4,
	ES_USER_PRESENT => 4,
	ETO_CLIPPED => 4,
	EVENTLOG_END_ALL_PAIRED_EVENTS => 4,
	EVENTLOG_FORWARDS_READ => 4,
	EVENTLOG_INFORMATION_TYPE => 4,
	EVENT_SYSTEM_MENUSTART => 4,
	EV_TXEMPTY => 4,
	EWX_FORCE => 4,
	EXCEPTION_EXIT_UNWIND => 4,
	EXIT_THREAD_DEBUG_EVENT => 4,
	EXPORT_PRIVATE_KEYS => 4,
	ExceptionExecuteHandler => 4,
	EDGE_RAISED => 5,
	EIO => 5,
	EMARCH_ENC_I17_IMM5C_SIZE_X => 5,
	EMR_POLYBEZIERTO => 5,
	ERROR_ACCESS_DENIED => 5,
	EVENT_SYSTEM_MENUEND => 5,
	EXIT_PROCESS_DEBUG_EVENT => 5,
	EDGE_ETCHED => 6,
	EMR_POLYLINETO => 6,
	ENXIO => 6,
	ERROR_INVALID_HANDLE => 6,
	EVENT_SYSTEM_MENUPOPUPSTART => 6,
	E2BIG => 7,
	EMARCH_ENC_I17_IMM7B_SIZE_X => 7,
	EMARCH_ENC_I17_IMM9D_VAL_POS_X => 7,
	EMR_POLYPOLYLINE => 7,
	ERROR_ARENA_TRASHED => 7,
	EVENT_SYSTEM_MENUPOPUPEND => 7,
	EC_DISABLE => 8,
	EFSRPC_SECURE_ONLY => 8,
	ELEMENT_STATUS_ACCESS => 8,
	EMARCH_ENC_I17_IMM41b_SIZE_X => 8,
	EMR_POLYPOLYGON => 8,
	ENABLE_WINDOW_INPUT => 8,
	ENLISTMENT_SUBORDINATE_RIGHTS => 8,
	ENOEXEC => 8,
	ERROR_DRIVE_NOT_INSTALLED => 8,
	ERROR_NOT_ENOUGH_MEMORY => 8,
	ES_UPPERCASE => 8,
	EVENTLOG_AUDIT_SUCCESS => 8,
	EVENTLOG_BACKWARDS_READ => 8,
	EVENTLOG_PAIRED_EVENT_ACTIVE => 8,
	EVENT_SYSTEM_CAPTURESTART => 8,
	EV_CTS => 8,
	EWX_POWEROFF => 8,
	EXCEPTION_EXECUTE_FAULT => 8,
	EXCEPTION_STACK_INVALID => 8,
	EBADF => 9,
	EDGE_BUMP => 9,
	EMARCH_ENC_I17_IMM9D_SIZE_X => 9,
	EMR_SETWINDOWEXTEX => 9,
	ERROR_INVALID_BLOCK => 9,
	EVENT_SYSTEM_CAPTUREEND => 9,
	ECHILD => 10,
	EDGE_SUNKEN => 10,
	EMARCH_ENC_I17_IMM41a_SIZE_X => 10,
	EMR_SETWINDOWORGEX => 10,
	ERROR_BAD_ENVIRONMENT => 10,
	EVENT_SYSTEM_MOVESIZESTART => 10,
	EAGAIN => 11,
	EEInfoGCCOM => 11,
	EMR_SETVIEWPORTEXTEX => 11,
	ENDDOC => 11,
	ERROR_BAD_FORMAT => 11,
	EVENT_SYSTEM_MOVESIZEEND => 11,
	EEInfoGCFRS => 12,
	EMARCH_ENC_I17_IC_INST_WORD_POS_X => 12,
	EMR_SETVIEWPORTORGEX => 12,
	ENOMEM => 12,
	ERROR_INVALID_ACCESS => 12,
	EVENT_SYSTEM_CONTEXTHELPSTART => 12,
	EACCES => 13,
	EMARCH_ENC_I17_IMM5C_INST_WORD_POS_X => 13,
	EMR_SETBRUSHORGEX => 13,
	ERROR_INVALID_DATA => 13,
	EVENT_SYSTEM_CONTEXTHELPEND => 13,
	EFAULT => 14,
	EMARCH_ENC_I17_IMM41a_INST_WORD_POS_X => 14,
	EMR_EOF => 14,
	ERROR_OUTOFMEMORY => 14,
	EVENT_SYSTEM_DRAGDROPSTART => 14,
	EMR_SETPIXELV => 15,
	ERROR_INVALID_DRIVE => 15,
	EVENT_SYSTEM_DRAGDROPEND => 15,
	EXCEPTION_MAXIMUM_PARAMETERS => 15,
	EBUSY => 16,
	ELEMENT_STATUS_EXENAB => 16,
	EMARCH_ENC_I17_IMM5C_VAL_POS_X => 16,
	EMR_SETMAPPERFLAGS => 16,
	ENABLE_MOUSE_INPUT => 16,
	ENLISTMENT_SUPERIOR_RIGHTS => 16,
	ERROR_CURRENT_DIRECTORY => 16,
	ERROR_TRAY_MALFUNCTION => 16,
	ES_LOWERCASE => 16,
	ETO_GLYPH_INDEX => 16,
	EVENTLOG_AUDIT_FAILURE => 16,
	EVENTLOG_PAIRED_EVENT_INACTIVE => 16,
	EVENT_SYSTEM_DIALOGSTART => 16,
	EV_DSR => 16,
	EWX_FORCEIFHUNG => 16,
	EXCEPTION_NESTED_CALL => 16,
	EEXIST => 17,
	EMR_SETMAPMODE => 17,
	ERROR_INIT_STATUS_NEEDED => 17,
	ERROR_NOT_SAME_DEVICE => 17,
	EVENT_SYSTEM_DIALOGEND => 17,
	EMARCH_ENC_I17_IMM9D_INST_WORD_POS_X => 18,
	EMR_SETBKMODE => 18,
	ERROR_NO_MORE_FILES => 18,
	EVENT_SYSTEM_SCROLLINGSTART => 18,
	EXDEV => 18,
	EMR_SETPOLYFILLMODE => 19,
	ENODEV => 19,
	ERROR_WRITE_PROTECT => 19,
	EVENT_SYSTEM_SCROLLINGEND => 19,
	EMR_SETROP2 => 20,
	ENOTDIR => 20,
	ERROR_BAD_UNIT => 20,
	EVENT_SYSTEM_SWITCHSTART => 20,
	EISDIR => 21,
	EMARCH_ENC_I17_IC_VAL_POS_X => 21,
	EMR_SETSTRETCHBLTMODE => 21,
	ERROR_NOT_READY => 21,
	EVENT_SYSTEM_SWITCHEND => 21,
	EINVAL => 22,
	EMARCH_ENC_I17_IMM41a_VAL_POS_X => 22,
	EMR_SETTEXTALIGN => 22,
	ERROR_BAD_COMMAND => 22,
	EVENT_SYSTEM_MINIMIZESTART => 22,
	EMARCH_ENC_I17_IMM41c_SIZE_X => 23,
	EMR_SETCOLORADJUSTMENT => 23,
	ENFILE => 23,
	ERROR_CRC => 23,
	EVENT_SYSTEM_MINIMIZEEND => 23,
	EMARCH_ENC_I17_IMM41b_INST_WORD_POS_X => 24,
	EMFILE => 24,
	EMR_SETTEXTCOLOR => 24,
	ERROR_BAD_LENGTH => 24,
	EMR_SETBKCOLOR => 25,
	ENOTTY => 25,
	ERROR_SEEK => 25,
	EMR_OFFSETCLIPRGN => 26,
	ERROR_NOT_DOS_DISK => 26,
	EFBIG => 27,
	EMARCH_ENC_I17_SIGN_INST_WORD_POS_X => 27,
	EMR_MOVETOEX => 27,
	ERROR_SECTOR_NOT_FOUND => 27,
	EMR_SETMETARGN => 28,
	ENABLEDUPLEX => 28,
	ENOSPC => 28,
	ERROR_OUT_OF_PAPER => 28,
	EMR_EXCLUDECLIPRECT => 29,
	ERROR_WRITE_FAULT => 29,
	ESPIPE => 29,
	EMR_INTERSECTCLIPRECT => 30,
	EROFS => 30,
	ERROR_READ_FAULT => 30,
	EMLINK => 31,
	EMR_SCALEVIEWPORTEXTEX => 31,
	ENUMPAPERBINS => 31,
	ERROR_GEN_FAILURE => 31,
	ELEMENT_STATUS_INENAB => 32,
	EMARCH_ENC_I17_IMM41b_VAL_POS_X => 32,
	EMR_SCALEWINDOWEXTEX => 32,
	ENABLE_INSERT_MODE => 32,
	EPIPE => 32,
	ERROR_SHARING_VIOLATION => 32,
	ES_PASSWORD => 32,
	EV_RLSD => 32,
	EWX_QUICKRESOLVE => 32,
	EXCEPTION_TARGET_UNWIND => 32,
	EDOM => 33,
	EMR_SAVEDC => 33,
	EPSPRINTING => 33,
	ERROR_LOCK_VIOLATION => 33,
	EMR_RESTOREDC => 34,
	ENUMPAPERMETRICS => 34,
	ERANGE => 34,
	ERROR_WRONG_DISK => 34,
	EMR_SETWORLDTRANSFORM => 35,
	EDEADLK => 36,
	EDEADLOCK => 36,
	EMR_MODIFYWORLDTRANSFORM => 36,
	ERROR_SHARING_BUFFER_EXCEEDED => 36,
	EMR_SELECTOBJECT => 37,
	EMR_CREATEPEN => 38,
	ENAMETOOLONG => 38,
	ERROR_HANDLE_EOF => 38,
	EMR_CREATEBRUSHINDIRECT => 39,
	ENOLCK => 39,
	ERROR_HANDLE_DISK_FULL => 39,
	EMARCH_ENC_I17_IMM41c_VAL_POS_X => 40,
	EMR_DELETEOBJECT => 40,
	ENOSYS => 40,
	EMR_ANGLEARC => 41,
	ENOTEMPTY => 41,
	EILSEQ => 42,
	EMR_ELLIPSE => 42,
	EMR_RECTANGLE => 43,
	EMR_ROUNDRECT => 44,
	EMR_ARC => 45,
	EMR_CHORD => 46,
	EMR_PIE => 47,
	EMR_SELECTPALETTE => 48,
	EMR_CREATEPALETTE => 49,
	EMR_SETPALETTEENTRIES => 50,
	ERROR_BIDI_NOT_SUPPORTED => 50,
	ERROR_NOT_SUPPORTED => 50,
	EMR_RESIZEPALETTE => 51,
	ERROR_REM_NOT_LIST => 51,
	EMR_REALIZEPALETTE => 52,
	ERROR_DUP_NAME => 52,
	EMR_EXTFLOODFILL => 53,
	ERROR_BAD_NETPATH => 53,
	EMR_LINETO => 54,
	ERROR_NETWORK_BUSY => 54,
	EMR_ARCTO => 55,
	ERROR_DEV_NOT_EXIST => 55,
	EMR_POLYDRAW => 56,
	ERROR_TOO_MANY_CMDS => 56,
	EMR_SETARCDIRECTION => 57,
	ERROR_ADAP_HDW_ERR => 57,
	EMR_SETMITERLIMIT => 58,
	ERROR_BAD_NET_RESP => 58,
	EMR_BEGINPATH => 59,
	ERROR_UNEXP_NET_ERR => 59,
	EMR_ENDPATH => 60,
	ERROR_BAD_REM_ADAP => 60,
	EMR_CLOSEFIGURE => 61,
	ERROR_PRINTQ_FULL => 61,
	EMR_FILLPATH => 62,
	ERROR_NO_SPOOL_SPACE => 62,
	EMARCH_ENC_I17_SIGN_VAL_POS_X => 63,
	EMR_STROKEANDFILLPATH => 63,
	ERROR_PRINT_CANCELLED => 63,
	ELEMENT_STATUS_PRODUCT_DATA => 64,
	EMR_STROKEPATH => 64,
	ENABLE_QUICK_EDIT_MODE => 64,
	ERROR_NETNAME_DELETED => 64,
	ES_AUTOVSCROLL => 64,
	ES_AWAYMODE_REQUIRED => 64,
	EV_BREAK => 64,
	EXCEPTION_COLLIDED_UNWIND => 64,
	EMR_FLATTENPATH => 65,
	ERROR_NETWORK_ACCESS_DENIED => 65,
	EMR_WIDENPATH => 66,
	ERROR_BAD_DEV_TYPE => 66,
	EMR_SELECTCLIPPATH => 67,
	ERROR_BAD_NET_NAME => 67,
	EMR_ABORTPATH => 68,
	ERROR_TOO_MANY_NAMES => 68,
	ERROR_TOO_MANY_SESS => 69,
	EMR_GDICOMMENT => 70,
	ERROR_SHARING_PAUSED => 70,
	EMR_FILLRGN => 71,
	ERROR_REQ_NOT_ACCEP => 71,
	EMR_FRAMERGN => 72,
	ERROR_REDIR_PAUSED => 72,
	EMR_INVERTRGN => 73,
	EMR_PAINTRGN => 74,
	EMR_EXTSELECTCLIPRGN => 75,
	EMR_BITBLT => 76,
	ECC_CMS_SHARED_INFO => 77,
	EMR_STRETCHBLT => 77,
	EMR_MASKBLT => 78,
	EMR_PLGBLT => 79,
	EMR_SETDIBITSTODEVICE => 80,
	ERROR_FILE_EXISTS => 80,
	EMR_STRETCHDIBITS => 81,
	EMR_EXTCREATEFONTINDIRECTW => 82,
	ERROR_CANNOT_MAKE => 82,
	EMR_EXTTEXTOUTA => 83,
	ERROR_FAIL_I24 => 83,
	EMR_EXTTEXTOUTW => 84,
	ERROR_OUT_OF_STRUCTURES => 84,
	EMR_POLYBEZIER16 => 85,
	ERROR_ALREADY_ASSIGNED => 85,
	EMR_POLYGON16 => 86,
	ERROR_INVALID_PASSWORD => 86,
	EMR_POLYLINE16 => 87,
	ERROR_INVALID_PARAMETER => 87,
	EMR_POLYBEZIERTO16 => 88,
	ERROR_NET_WRITE_FAULT => 88,
	EMR_POLYLINETO16 => 89,
	ERROR_NO_PROC_SLOTS => 89,
	EMR_POLYPOLYLINE16 => 90,
	EMR_POLYPOLYGON16 => 91,
	EMR_POLYDRAW16 => 92,
	EMR_CREATEMONOBRUSH => 93,
	EMR_CREATEDIBPATTERNBRUSHPT => 94,
	EMR_EXTCREATEPEN => 95,
	EMR_POLYTEXTOUTA => 96,
	EMR_POLYTEXTOUTW => 97,
	EMR_SETICMMODE => 98,
	EMR_CREATECOLORSPACE => 99,
	EADDRINUSE => 100,
	EMR_SETCOLORSPACE => 100,
	ERROR_TOO_MANY_SEMAPHORES => 100,
	EADDRNOTAVAIL => 101,
	EMR_DELETECOLORSPACE => 101,
	ERROR_EXCL_SEM_ALREADY_OWNED => 101,
	EAFNOSUPPORT => 102,
	EMR_GLSRECORD => 102,
	ERROR_SEM_IS_SET => 102,
	EXCEPTION_UNWIND => 102,
	EALREADY => 103,
	EMR_GLSBOUNDEDRECORD => 103,
	ERROR_TOO_MANY_SEM_REQUESTS => 103,
	EBADMSG => 104,
	EMR_PIXELFORMAT => 104,
	ERROR_INVALID_AT_INTERRUPT_TIME => 104,
	ECANCELED => 105,
	EMR_RESERVED_105 => 105,
	ERROR_SEM_OWNER_DIED => 105,
	ECONNABORTED => 106,
	EMR_RESERVED_106 => 106,
	ERROR_SEM_USER_LIMIT => 106,
	ECONNREFUSED => 107,
	EMR_RESERVED_107 => 107,
	ERROR_DISK_CHANGE => 107,
	ECONNRESET => 108,
	EMR_RESERVED_108 => 108,
	ERROR_DRIVE_LOCKED => 108,
	EDESTADDRREQ => 109,
	EMR_RESERVED_109 => 109,
	ERROR_BROKEN_PIPE => 109,
	EHOSTUNREACH => 110,
	EMR_RESERVED_110 => 110,
	ERROR_OPEN_FAILED => 110,
	EIDRM => 111,
	EMR_COLORCORRECTPALETTE => 111,
	ERROR_BUFFER_OVERFLOW => 111,
	EINPROGRESS => 112,
	EMR_SETICMPROFILEA => 112,
	ERROR_DISK_FULL => 112,
	EISCONN => 113,
	EMR_SETICMPROFILEW => 113,
	ERROR_NO_MORE_SEARCH_HANDLES => 113,
	ELOOP => 114,
	EMR_ALPHABLEND => 114,
	ERROR_INVALID_TARGET_HANDLE => 114,
	EMR_SETLAYOUT => 115,
	EMSGSIZE => 115,
	EMR_TRANSPARENTBLT => 116,
	ENETDOWN => 116,
	EMR_RESERVED_117 => 117,
	ENETRESET => 117,
	ERROR_INVALID_CATEGORY => 117,
	EMR_GRADIENTFILL => 118,
	ENETUNREACH => 118,
	ERROR_INVALID_VERIFY_SWITCH => 118,
	EMR_RESERVED_119 => 119,
	ENOBUFS => 119,
	ERROR_BAD_DRIVER_LEVEL => 119,
	EMR_RESERVED_120 => 120,
	ENODATA => 120,
	ERROR_CALL_NOT_IMPLEMENTED => 120,
	EMR_COLORMATCHTOTARGETW => 121,
	ENOLINK => 121,
	ERROR_SEM_TIMEOUT => 121,
	EMR_CREATECOLORSPACEW => 122,
	EMR_MAX => 122,
	ENOMSG => 122,
	ERROR_INSUFFICIENT_BUFFER => 122,
	ENOPROTOOPT => 123,
	ERROR_INVALID_NAME => 123,
	ENOSR => 124,
	ERROR_INVALID_LEVEL => 124,
	ENOSTR => 125,
	ERROR_NO_VOLUME_LABEL => 125,
	ENOTCONN => 126,
	ERROR_MOD_NOT_FOUND => 126,
	ENOTRECOVERABLE => 127,
	ERROR_PROC_NOT_FOUND => 127,
	EC_ENABLEONE => 128,
	ENABLE_EXTENDED_FLAGS => 128,
	ENOTSOCK => 128,
	ERROR_WAIT_NO_CHILDREN => 128,
	ES_AUTOHSCROLL => 128,
	ETO_RTLREADING => 128,
	EV_ERR => 128,
	ENOTSUP => 129,
	ERROR_CHILD_NOT_COMPLETE => 129,
	EOPNOTSUPP => 130,
	ERROR_DIRECT_ACCESS_HANDLE => 130,
	ERROR_NEGATIVE_SEEK => 131,
	EOVERFLOW => 132,
	ERROR_SEEK_ON_DEVICE => 132,
	EOWNERDEAD => 133,
	ERROR_IS_JOIN_TARGET => 133,
	EPROTO => 134,
	ERROR_IS_JOINED => 134,
	EPROTONOSUPPORT => 135,
	ERROR_IS_SUBSTED => 135,
	EPROTOTYPE => 136,
	ERROR_NOT_JOINED => 136,
	ERROR_NOT_SUBSTED => 137,
	ETIME => 137,
	ERROR_JOIN_TO_JOIN => 138,
	ETIMEDOUT => 138,
	ERROR_SUBST_TO_SUBST => 139,
	ETXTBSY => 139,
	ERROR_JOIN_TO_SUBST => 140,
	EWOULDBLOCK => 140,
	ERROR_SUBST_TO_JOIN => 141,
	ERROR_BUSY_DRIVE => 142,
	ERROR_SAME_DRIVE => 143,
	ERROR_DIR_NOT_ROOT => 144,
	ERROR_DIR_NOT_EMPTY => 145,
	ERROR_IS_SUBST_PATH => 146,
	ERROR_IS_JOIN_PATH => 147,
	ERROR_PATH_BUSY => 148,
	ERROR_IS_SUBST_TARGET => 149,
	ERROR_SYSTEM_TRACE => 150,
	ERROR_INVALID_EVENT_COUNT => 151,
	ERROR_TOO_MANY_MUXWAITERS => 152,
	ERROR_INVALID_LIST_FORMAT => 153,
	ERROR_LABEL_TOO_LONG => 154,
	ERROR_TOO_MANY_TCBS => 155,
	ERROR_SIGNAL_REFUSED => 156,
	ERROR_DISCARDED => 157,
	ERROR_NOT_LOCKED => 158,
	ERROR_BAD_THREADID_ADDR => 159,
	ERROR_BAD_ARGUMENTS => 160,
	ERROR_BAD_PATHNAME => 161,
	ERROR_SIGNAL_PENDING => 162,
	ERROR_MAX_THRDS_REACHED => 164,
	ERROR_LOCK_FAILED => 167,
	ERROR_BUSY => 170,
	ERROR_CANCEL_VIOLATION => 173,
	ERROR_ATOMIC_LOCKS_NOT_SUPPORTED => 174,
	EM_GETSEL => 176,
	EM_SETSEL => 177,
	EM_GETRECT => 178,
	EM_SETRECT => 179,
	EM_SETRECTNP => 180,
	ERROR_INVALID_SEGMENT_NUMBER => 180,
	EM_SCROLL => 181,
	EM_LINESCROLL => 182,
	ERROR_INVALID_ORDINAL => 182,
	EM_SCROLLCARET => 183,
	ERROR_ALREADY_EXISTS => 183,
	EM_GETMODIFY => 184,
	EM_SETMODIFY => 185,
	EM_GETLINECOUNT => 186,
	ERROR_INVALID_FLAG_NUMBER => 186,
	EM_LINEINDEX => 187,
	ERROR_SEM_NOT_FOUND => 187,
	EM_SETHANDLE => 188,
	ERROR_INVALID_STARTING_CODESEG => 188,
	EM_GETHANDLE => 189,
	ERROR_INVALID_STACKSEG => 189,
	EM_GETTHUMB => 190,
	ERROR_INVALID_MODULETYPE => 190,
	ERROR_INVALID_EXE_SIGNATURE => 191,
	ERROR_EXE_MARKED_INVALID => 192,
	EM_LINELENGTH => 193,
	ERROR_BAD_EXE_FORMAT => 193,
	EM_REPLACESEL => 194,
	ERROR_ITERATED_DATA_EXCEEDS_64k => 194,
	ERROR_INVALID_MINALLOCSIZE => 195,
	EM_GETLINE => 196,
	ERROR_DYNLINK_FROM_INVALID_RING => 196,
	EM_LIMITTEXT => 197,
	EM_SETLIMITTEXT => 197,
	ERROR_IOPL_NOT_ENABLED => 197,
	EM_CANUNDO => 198,
	ERROR_INVALID_SEGDPL => 198,
	EM_UNDO => 199,
	ERROR_AUTODATASEG_EXCEEDS_64k => 199,
	EM_FMTLINES => 200,
	ERROR_RING2SEG_MUST_BE_MOVABLE => 200,
	EM_LINEFROMCHAR => 201,
	ERROR_RELOC_CHAIN_XEEDS_SEGLIM => 201,
	ERROR_INFLOOP_IN_RELOC_CHAIN => 202,
	EM_SETTABSTOPS => 203,
	ERROR_ENVVAR_NOT_FOUND => 203,
	EM_SETPASSWORDCHAR => 204,
	EM_EMPTYUNDOBUFFER => 205,
	ERROR_NO_SIGNAL_SENT => 205,
	EM_GETFIRSTVISIBLELINE => 206,
	ERROR_FILENAME_EXCED_RANGE => 206,
	EM_SETREADONLY => 207,
	ERROR_RING2_STACK_IN_USE => 207,
	EM_SETWORDBREAKPROC => 208,
	ERROR_META_EXPANSION_TOO_LONG => 208,
	EM_GETWORDBREAKPROC => 209,
	ERROR_INVALID_SIGNAL_NUMBER => 209,
	EM_GETPASSWORDCHAR => 210,
	ENABLE_DISABLE_AUTOSAVE => 210,
	ERROR_THREAD_1_INACTIVE => 210,
	EM_SETMARGINS => 211,
	EM_GETMARGINS => 212,
	ERROR_LOCKED => 212,
	EXECUTE_OFFLINE_DIAGS => 212,
	EM_GETLIMITTEXT => 213,
	EM_POSFROMCHAR => 214,
	ERROR_TOO_MANY_MODULES => 214,
	EM_CHARFROMPOS => 215,
	ERROR_NESTING_NOT_ALLOWED => 215,
	EM_SETIMESTATUS => 216,
	ENABLE_SMART => 216,
	ERROR_EXE_MACHINE_TYPE_MISMATCH => 216,
	EM_GETIMESTATUS => 217,
	ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY => 217,
	ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY => 218,
	ENABLE_DISABLE_AUTO_OFFLINE => 219,
	ERROR_FILE_CHECKED_OUT => 220,
	ERROR_CHECKOUT_REQUIRED => 221,
	ERROR_BAD_FILE_TYPE => 222,
	ERROR_FILE_TOO_LARGE => 223,
	ERROR_FORMS_AUTH_REQUIRED => 224,
	ERROR_PIPE_LOCAL => 229,
	ERROR_BAD_PIPE => 230,
	ERROR_PIPE_BUSY => 231,
	ERROR_NO_DATA => 232,
	ERROR_PIPE_NOT_CONNECTED => 233,
	ERROR_MORE_DATA => 234,
	EASTEUROPE_CHARSET => 238,
	ERROR_VC_DISCONNECTED => 240,
	ERROR_INVALID_EA_NAME => 254,
	ERROR_EA_LIST_INCONSISTENT => 255,
	ENABLE_AUTO_POSITION => 256,
	ENHANCED_KEY => 256,
	EN_SETFOCUS => 256,
	ES_NOHIDESEL => 256,
	EV_RING => 256,
	ERROR_NO_MORE_ITEMS => 259,
	ERROR_CANNOT_COPY => 266,
	ERROR_DIRECTORY => 267,
	ERROR_EAS_DIDNT_FIT => 275,
	ERROR_EA_FILE_CORRUPT => 276,
	ERROR_EA_TABLE_FULL => 277,
	ERROR_INVALID_EA_HANDLE => 278,
	ERROR_EAS_NOT_SUPPORTED => 282,
	ERROR_NOT_OWNER => 288,
	ERROR_TOO_MANY_POSTS => 298,
	ERROR_PARTIAL_COPY => 299,
	ERROR_OPLOCK_NOT_GRANTED => 300,
	ERROR_INVALID_OPLOCK_PROTOCOL => 301,
	ERROR_DISK_TOO_FRAGMENTED => 302,
	ERROR_DELETE_PENDING => 303,
	ERROR_INVALID_TOKEN => 315,
	ERROR_MR_MID_NOT_FOUND => 317,
	ERROR_SCOPE_NOT_FOUND => 318,
	ERROR_INVALID_ADDRESS => 487,
	EN_KILLFOCUS => 512,
	EV_PERR => 512,
	EXTTEXTOUT => 512,
	ERROR_ARITHMETIC_OVERFLOW => 534,
	ERROR_PIPE_CONNECTED => 535,
	ERROR_PIPE_LISTENING => 536,
	ERROR_WAKE_SYSTEM => 730,
	ERROR_WAIT_1 => 731,
	ERROR_WAIT_2 => 732,
	ERROR_WAIT_3 => 733,
	ERROR_WAIT_63 => 734,
	ERROR_ABANDONED_WAIT_0 => 735,
	ERROR_ABANDONED_WAIT_63 => 736,
	ERROR_USER_APC => 737,
	ERROR_KERNEL_APC => 738,
	ERROR_ALERTED => 739,
	ENABLERELATIVEWIDTHS => 768,
	EN_CHANGE => 768,
	ENABLEPAIRKERNING => 769,
	ERROR_EA_ACCESS_DENIED => 994,
	ERROR_OPERATION_ABORTED => 995,
	ERROR_IO_INCOMPLETE => 996,
	ERROR_IO_PENDING => 997,
	ERROR_NOACCESS => 998,
	ERROR_SWAPERROR => 999,
	ERROR_STACK_OVERFLOW => 1001,
	ERROR_INVALID_MESSAGE => 1002,
	ERROR_CAN_NOT_COMPLETE => 1003,
	ERROR_INVALID_FLAGS => 1004,
	ERROR_UNRECOGNIZED_VOLUME => 1005,
	ERROR_FILE_INVALID => 1006,
	ERROR_FULLSCREEN_MODE => 1007,
	ERROR_NO_TOKEN => 1008,
	ERROR_BADDB => 1009,
	ERROR_BADKEY => 1010,
	ERROR_CANTOPEN => 1011,
	ERROR_CANTREAD => 1012,
	ERROR_CANTWRITE => 1013,
	ERROR_REGISTRY_RECOVERED => 1014,
	ERROR_REGISTRY_CORRUPT => 1015,
	ERROR_REGISTRY_IO_FAILED => 1016,
	ERROR_NOT_REGISTRY_FILE => 1017,
	ERROR_KEY_DELETED => 1018,
	ERROR_NO_LOG_SPACE => 1019,
	ERROR_KEY_HAS_CHILDREN => 1020,
	ERROR_CHILD_MUST_BE_VOLATILE => 1021,
	ERROR_NOTIFY_ENUM_DIR => 1022,
	EN_UPDATE => 1024,
	ES_OEMCONVERT => 1024,
	ETO_NUMERICSLOCAL => 1024,
	EV_RX80FULL => 1024,
	ERROR_DEPENDENT_SERVICES_RUNNING => 1051,
	ERROR_INVALID_SERVICE_CONTROL => 1052,
	ERROR_SERVICE_REQUEST_TIMEOUT => 1053,
	ERROR_SERVICE_NO_THREAD => 1054,
	ERROR_SERVICE_DATABASE_LOCKED => 1055,
	ERROR_SERVICE_ALREADY_RUNNING => 1056,
	ERROR_INVALID_SERVICE_ACCOUNT => 1057,
	ERROR_SERVICE_DISABLED => 1058,
	ERROR_CIRCULAR_DEPENDENCY => 1059,
	ERROR_SERVICE_DOES_NOT_EXIST => 1060,
	ERROR_SERVICE_CANNOT_ACCEPT_CTRL => 1061,
	ERROR_SERVICE_NOT_ACTIVE => 1062,
	ERROR_FAILED_SERVICE_CONTROLLER_CONNECT => 1063,
	ERROR_EXCEPTION_IN_SERVICE => 1064,
	ERROR_DATABASE_DOES_NOT_EXIST => 1065,
	ERROR_SERVICE_SPECIFIC_ERROR => 1066,
	ERROR_PROCESS_ABORTED => 1067,
	ERROR_SERVICE_DEPENDENCY_FAIL => 1068,
	ERROR_SERVICE_LOGON_FAILED => 1069,
	ERROR_SERVICE_START_HANG => 1070,
	ERROR_INVALID_SERVICE_LOCK => 1071,
	ERROR_SERVICE_MARKED_FOR_DELETE => 1072,
	ERROR_SERVICE_EXISTS => 1073,
	ERROR_ALREADY_RUNNING_LKG => 1074,
	ERROR_SERVICE_DEPENDENCY_DELETED => 1075,
	ERROR_BOOT_ALREADY_ACCEPTED => 1076,
	ERROR_SERVICE_NEVER_STARTED => 1077,
	ERROR_DUPLICATE_SERVICE_NAME => 1078,
	ERROR_DIFFERENT_SERVICE_ACCOUNT => 1079,
	ERROR_CANNOT_DETECT_DRIVER_FAILURE => 1080,
	ERROR_CANNOT_DETECT_PROCESS_ABORT => 1081,
	ERROR_NO_RECOVERY_PROGRAM => 1082,
	ERROR_SERVICE_NOT_IN_EXE => 1083,
	ERROR_NOT_SAFEBOOT_SERVICE => 1084,
	ERROR_END_OF_MEDIA => 1100,
	ERROR_FILEMARK_DETECTED => 1101,
	ERROR_BEGINNING_OF_MEDIA => 1102,
	ERROR_SETMARK_DETECTED => 1103,
	ERROR_NO_DATA_DETECTED => 1104,
	ERROR_PARTITION_FAILURE => 1105,
	ERROR_INVALID_BLOCK_LENGTH => 1106,
	ERROR_DEVICE_NOT_PARTITIONED => 1107,
	ERROR_UNABLE_TO_LOCK_MEDIA => 1108,
	ERROR_UNABLE_TO_UNLOAD_MEDIA => 1109,
	ERROR_MEDIA_CHANGED => 1110,
	ERROR_BUS_RESET => 1111,
	ERROR_NO_MEDIA_IN_DRIVE => 1112,
	ERROR_NO_UNICODE_TRANSLATION => 1113,
	ERROR_DLL_INIT_FAILED => 1114,
	ERROR_SHUTDOWN_IN_PROGRESS => 1115,
	ERROR_NO_SHUTDOWN_IN_PROGRESS => 1116,
	ERROR_IO_DEVICE => 1117,
	ERROR_SERIAL_NO_DEVICE => 1118,
	ERROR_IRQ_BUSY => 1119,
	ERROR_MORE_WRITES => 1120,
	ERROR_COUNTER_TIMEOUT => 1121,
	ERROR_FLOPPY_ID_MARK_NOT_FOUND => 1122,
	ERROR_FLOPPY_WRONG_CYLINDER => 1123,
	ERROR_FLOPPY_UNKNOWN_ERROR => 1124,
	ERROR_FLOPPY_BAD_REGISTERS => 1125,
	ERROR_DISK_RECALIBRATE_FAILED => 1126,
	ERROR_DISK_OPERATION_FAILED => 1127,
	ERROR_DISK_RESET_FAILED => 1128,
	ERROR_EOM_OVERFLOW => 1129,
	ERROR_NOT_ENOUGH_SERVER_MEMORY => 1130,
	ERROR_POSSIBLE_DEADLOCK => 1131,
	ERROR_MAPPED_ALIGNMENT => 1132,
	ERROR_SET_POWER_STATE_VETOED => 1140,
	ERROR_SET_POWER_STATE_FAILED => 1141,
	ERROR_TOO_MANY_LINKS => 1142,
	ERROR_OLD_WIN_VERSION => 1150,
	ERROR_APP_WRONG_OS => 1151,
	ERROR_SINGLE_INSTANCE_APP => 1152,
	ERROR_RMODE_APP => 1153,
	ERROR_INVALID_DLL => 1154,
	ERROR_NO_ASSOCIATION => 1155,
	ERROR_DDE_FAIL => 1156,
	ERROR_DLL_NOT_FOUND => 1157,
	ERROR_NO_MORE_USER_HANDLES => 1158,
	ERROR_MESSAGE_SYNC_ONLY => 1159,
	ERROR_SOURCE_ELEMENT_EMPTY => 1160,
	ERROR_DESTINATION_ELEMENT_FULL => 1161,
	ERROR_ILLEGAL_ELEMENT_ADDRESS => 1162,
	ERROR_MAGAZINE_NOT_PRESENT => 1163,
	ERROR_DEVICE_REINITIALIZATION_NEEDED => 1164,
	ERROR_DEVICE_REQUIRES_CLEANING => 1165,
	ERROR_DEVICE_DOOR_OPEN => 1166,
	ERROR_DEVICE_NOT_CONNECTED => 1167,
	ERROR_NOT_FOUND => 1168,
	ERROR_NO_MATCH => 1169,
	ERROR_SET_NOT_FOUND => 1170,
	ERROR_POINT_NOT_FOUND => 1171,
	ERROR_NO_TRACKING_SERVICE => 1172,
	ERROR_NO_VOLUME_ID => 1173,
	ERROR_UNABLE_TO_REMOVE_REPLACED => 1175,
	ERROR_UNABLE_TO_MOVE_REPLACEMENT => 1176,
	ERROR_UNABLE_TO_MOVE_REPLACEMENT_2 => 1177,
	ERROR_JOURNAL_DELETE_IN_PROGRESS => 1178,
	ERROR_JOURNAL_NOT_ACTIVE => 1179,
	ERROR_POTENTIAL_FILE_FOUND => 1180,
	ERROR_JOURNAL_ENTRY_DELETED => 1181,
	ERROR_BAD_DEVICE => 1200,
	ERROR_CONNECTION_UNAVAIL => 1201,
	ERROR_DEVICE_ALREADY_REMEMBERED => 1202,
	ERROR_NO_NET_OR_BAD_PATH => 1203,
	ERROR_BAD_PROVIDER => 1204,
	ERROR_CANNOT_OPEN_PROFILE => 1205,
	ERROR_BAD_PROFILE => 1206,
	ERROR_NOT_CONTAINER => 1207,
	ERROR_EXTENDED_ERROR => 1208,
	ERROR_INVALID_GROUPNAME => 1209,
	ERROR_INVALID_COMPUTERNAME => 1210,
	ERROR_INVALID_EVENTNAME => 1211,
	ERROR_INVALID_DOMAINNAME => 1212,
	ERROR_INVALID_SERVICENAME => 1213,
	ERROR_INVALID_NETNAME => 1214,
	ERROR_INVALID_SHARENAME => 1215,
	ERROR_INVALID_PASSWORDNAME => 1216,
	ERROR_INVALID_MESSAGENAME => 1217,
	ERROR_INVALID_MESSAGEDEST => 1218,
	ERROR_SESSION_CREDENTIAL_CONFLICT => 1219,
	ERROR_REMOTE_SESSION_LIMIT_EXCEEDED => 1220,
	ERROR_DUP_DOMAINNAME => 1221,
	ERROR_NO_NETWORK => 1222,
	ERROR_CANCELLED => 1223,
	ERROR_USER_MAPPED_FILE => 1224,
	ERROR_CONNECTION_REFUSED => 1225,
	ERROR_GRACEFUL_DISCONNECT => 1226,
	ERROR_ADDRESS_ALREADY_ASSOCIATED => 1227,
	ERROR_ADDRESS_NOT_ASSOCIATED => 1228,
	ERROR_CONNECTION_INVALID => 1229,
	ERROR_CONNECTION_ACTIVE => 1230,
	ERROR_NETWORK_UNREACHABLE => 1231,
	ERROR_HOST_UNREACHABLE => 1232,
	ERROR_PROTOCOL_UNREACHABLE => 1233,
	ERROR_PORT_UNREACHABLE => 1234,
	ERROR_REQUEST_ABORTED => 1235,
	ERROR_CONNECTION_ABORTED => 1236,
	ERROR_RETRY => 1237,
	ERROR_CONNECTION_COUNT_LIMIT => 1238,
	ERROR_LOGIN_TIME_RESTRICTION => 1239,
	ERROR_LOGIN_WKSTA_RESTRICTION => 1240,
	ERROR_INCORRECT_ADDRESS => 1241,
	ERROR_ALREADY_REGISTERED => 1242,
	ERROR_SERVICE_NOT_FOUND => 1243,
	ERROR_NOT_AUTHENTICATED => 1244,
	ERROR_NOT_LOGGED_ON => 1245,
	ERROR_CONTINUE => 1246,
	ERROR_ALREADY_INITIALIZED => 1247,
	ERROR_NO_MORE_DEVICES => 1248,
	ERROR_NO_SUCH_SITE => 1249,
	ERROR_DOMAIN_CONTROLLER_EXISTS => 1250,
	ERROR_ONLY_IF_CONNECTED => 1251,
	ERROR_OVERRIDE_NOCHANGES => 1252,
	ERROR_BAD_USER_PROFILE => 1253,
	ERROR_NOT_SUPPORTED_ON_SBS => 1254,
	ERROR_SERVER_SHUTDOWN_IN_PROGRESS => 1255,
	ERROR_HOST_DOWN => 1256,
	ERROR_NON_ACCOUNT_SID => 1257,
	ERROR_NON_DOMAIN_SID => 1258,
	ERROR_APPHELP_BLOCK => 1259,
	ERROR_ACCESS_DISABLED_BY_POLICY => 1260,
	ERROR_REG_NAT_CONSUMPTION => 1261,
	ERROR_CSCSHARE_OFFLINE => 1262,
	ERROR_PKINIT_FAILURE => 1263,
	ERROR_SMARTCARD_SUBSYSTEM_FAILURE => 1264,
	ERROR_DOWNGRADE_DETECTED => 1265,
	ERROR_MACHINE_LOCKED => 1271,
	ERROR_CALLBACK_SUPPLIED_INVALID_DATA => 1273,
	ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED => 1274,
	ERROR_DRIVER_BLOCKED => 1275,
	ERROR_INVALID_IMPORT_OF_NON_DLL => 1276,
	ERROR_ACCESS_DISABLED_WEBBLADE => 1277,
	ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER => 1278,
	ERROR_RECOVERY_FAILURE => 1279,
	EN_ERRSPACE => 1280,
	ERROR_ALREADY_FIBER => 1280,
	EN_MAXTEXT => 1281,
	ERROR_ALREADY_THREAD => 1281,
	ERROR_STACK_BUFFER_OVERRUN => 1282,
	ERROR_PARAMETER_QUOTA_EXCEEDED => 1283,
	ERROR_DEBUGGER_INACTIVE => 1284,
	ERROR_DELAY_LOAD_FAILED => 1285,
	ERROR_VDM_DISALLOWED => 1286,
	ERROR_UNIDENTIFIED_ERROR => 1287,
	ERROR_NOT_ALL_ASSIGNED => 1300,
	ERROR_SOME_NOT_MAPPED => 1301,
	ERROR_NO_QUOTAS_FOR_ACCOUNT => 1302,
	ERROR_LOCAL_USER_SESSION_KEY => 1303,
	ERROR_NULL_LM_PASSWORD => 1304,
	ERROR_UNKNOWN_REVISION => 1305,
	ERROR_REVISION_MISMATCH => 1306,
	ERROR_INVALID_OWNER => 1307,
	ERROR_INVALID_PRIMARY_GROUP => 1308,
	ERROR_NO_IMPERSONATION_TOKEN => 1309,
	ERROR_CANT_DISABLE_MANDATORY => 1310,
	ERROR_NO_LOGON_SERVERS => 1311,
	ERROR_NO_SUCH_LOGON_SESSION => 1312,
	ERROR_NO_SUCH_PRIVILEGE => 1313,
	ERROR_PRIVILEGE_NOT_HELD => 1314,
	ERROR_INVALID_ACCOUNT_NAME => 1315,
	ERROR_USER_EXISTS => 1316,
	ERROR_NO_SUCH_USER => 1317,
	ERROR_GROUP_EXISTS => 1318,
	ERROR_NO_SUCH_GROUP => 1319,
	ERROR_MEMBER_IN_GROUP => 1320,
	ERROR_MEMBER_NOT_IN_GROUP => 1321,
	ERROR_LAST_ADMIN => 1322,
	ERROR_WRONG_PASSWORD => 1323,
	ERROR_ILL_FORMED_PASSWORD => 1324,
	ERROR_PASSWORD_RESTRICTION => 1325,
	ERROR_LOGON_FAILURE => 1326,
	ERROR_ACCOUNT_RESTRICTION => 1327,
	ERROR_INVALID_LOGON_HOURS => 1328,
	ERROR_INVALID_WORKSTATION => 1329,
	ERROR_PASSWORD_EXPIRED => 1330,
	ERROR_ACCOUNT_DISABLED => 1331,
	ERROR_NONE_MAPPED => 1332,
	ERROR_TOO_MANY_LUIDS_REQUESTED => 1333,
	ERROR_LUIDS_EXHAUSTED => 1334,
	ERROR_INVALID_SUB_AUTHORITY => 1335,
	ERROR_INVALID_ACL => 1336,
	ERROR_INVALID_SID => 1337,
	ERROR_INVALID_SECURITY_DESCR => 1338,
	ERROR_BAD_INHERITANCE_ACL => 1340,
	ERROR_SERVER_DISABLED => 1341,
	ERROR_SERVER_NOT_DISABLED => 1342,
	ERROR_INVALID_ID_AUTHORITY => 1343,
	ERROR_ALLOTTED_SPACE_EXCEEDED => 1344,
	ERROR_INVALID_GROUP_ATTRIBUTES => 1345,
	ERROR_BAD_IMPERSONATION_LEVEL => 1346,
	ERROR_CANT_OPEN_ANONYMOUS => 1347,
	ERROR_BAD_VALIDATION_CLASS => 1348,
	ERROR_BAD_TOKEN_TYPE => 1349,
	ERROR_NO_SECURITY_ON_OBJECT => 1350,
	ERROR_CANT_ACCESS_DOMAIN_INFO => 1351,
	ERROR_INVALID_SERVER_STATE => 1352,
	ERROR_INVALID_DOMAIN_STATE => 1353,
	ERROR_INVALID_DOMAIN_ROLE => 1354,
	ERROR_NO_SUCH_DOMAIN => 1355,
	ERROR_DOMAIN_EXISTS => 1356,
	ERROR_DOMAIN_LIMIT_EXCEEDED => 1357,
	ERROR_INTERNAL_DB_CORRUPTION => 1358,
	ERROR_INTERNAL_ERROR => 1359,
	ERROR_GENERIC_NOT_MAPPED => 1360,
	ERROR_BAD_DESCRIPTOR_FORMAT => 1361,
	ERROR_NOT_LOGON_PROCESS => 1362,
	ERROR_LOGON_SESSION_EXISTS => 1363,
	ERROR_NO_SUCH_PACKAGE => 1364,
	ERROR_BAD_LOGON_SESSION_STATE => 1365,
	ERROR_LOGON_SESSION_COLLISION => 1366,
	ERROR_INVALID_LOGON_TYPE => 1367,
	ERROR_CANNOT_IMPERSONATE => 1368,
	ERROR_RXACT_INVALID_STATE => 1369,
	ERROR_RXACT_COMMIT_FAILURE => 1370,
	ERROR_SPECIAL_ACCOUNT => 1371,
	ERROR_SPECIAL_GROUP => 1372,
	ERROR_SPECIAL_USER => 1373,
	ERROR_MEMBERS_PRIMARY_GROUP => 1374,
	ERROR_TOKEN_ALREADY_IN_USE => 1375,
	ERROR_NO_SUCH_ALIAS => 1376,
	ERROR_MEMBER_NOT_IN_ALIAS => 1377,
	ERROR_MEMBER_IN_ALIAS => 1378,
	ERROR_ALIAS_EXISTS => 1379,
	ERROR_LOGON_NOT_GRANTED => 1380,
	ERROR_TOO_MANY_SECRETS => 1381,
	ERROR_SECRET_TOO_LONG => 1382,
	ERROR_INTERNAL_DB_ERROR => 1383,
	ERROR_TOO_MANY_CONTEXT_IDS => 1384,
	ERROR_LOGON_TYPE_NOT_GRANTED => 1385,
	ERROR_NT_CROSS_ENCRYPTION_REQUIRED => 1386,
	ERROR_NO_SUCH_MEMBER => 1387,
	ERROR_INVALID_MEMBER => 1388,
	ERROR_TOO_MANY_SIDS => 1389,
	ERROR_LM_CROSS_ENCRYPTION_REQUIRED => 1390,
	ERROR_NO_INHERITANCE => 1391,
	ERROR_FILE_CORRUPT => 1392,
	ERROR_DISK_CORRUPT => 1393,
	ERROR_NO_USER_SESSION_KEY => 1394,
	ERROR_LICENSE_QUOTA_EXCEEDED => 1395,
	ERROR_WRONG_TARGET_NAME => 1396,
	ERROR_MUTUAL_AUTH_FAILED => 1397,
	ERROR_TIME_SKEW => 1398,
	ERROR_CURRENT_DOMAIN_NOT_ALLOWED => 1399,
	ERROR_INVALID_WINDOW_HANDLE => 1400,
	ERROR_INVALID_MENU_HANDLE => 1401,
	ERROR_INVALID_CURSOR_HANDLE => 1402,
	ERROR_INVALID_ACCEL_HANDLE => 1403,
	ERROR_INVALID_HOOK_HANDLE => 1404,
	ERROR_INVALID_DWP_HANDLE => 1405,
	ERROR_TLW_WITH_WSCHILD => 1406,
	ERROR_CANNOT_FIND_WND_CLASS => 1407,
	ERROR_WINDOW_OF_OTHER_THREAD => 1408,
	ERROR_HOTKEY_ALREADY_REGISTERED => 1409,
	ERROR_CLASS_ALREADY_EXISTS => 1410,
	ERROR_CLASS_DOES_NOT_EXIST => 1411,
	ERROR_CLASS_HAS_WINDOWS => 1412,
	ERROR_INVALID_INDEX => 1413,
	ERROR_INVALID_ICON_HANDLE => 1414,
	ERROR_PRIVATE_DIALOG_INDEX => 1415,
	ERROR_LISTBOX_ID_NOT_FOUND => 1416,
	ERROR_NO_WILDCARD_CHARACTERS => 1417,
	ERROR_CLIPBOARD_NOT_OPEN => 1418,
	ERROR_HOTKEY_NOT_REGISTERED => 1419,
	ERROR_WINDOW_NOT_DIALOG => 1420,
	ERROR_CONTROL_ID_NOT_FOUND => 1421,
	ERROR_INVALID_COMBOBOX_MESSAGE => 1422,
	ERROR_WINDOW_NOT_COMBOBOX => 1423,
	ERROR_INVALID_EDIT_HEIGHT => 1424,
	ERROR_DC_NOT_FOUND => 1425,
	ERROR_INVALID_HOOK_FILTER => 1426,
	ERROR_INVALID_FILTER_PROC => 1427,
	ERROR_HOOK_NEEDS_HMOD => 1428,
	ERROR_GLOBAL_ONLY_HOOK => 1429,
	ERROR_JOURNAL_HOOK_SET => 1430,
	ERROR_HOOK_NOT_INSTALLED => 1431,
	ERROR_INVALID_LB_MESSAGE => 1432,
	ERROR_SETCOUNT_ON_BAD_LB => 1433,
	ERROR_LB_WITHOUT_TABSTOPS => 1434,
	ERROR_DESTROY_OBJECT_OF_OTHER_THREAD => 1435,
	ERROR_CHILD_WINDOW_MENU => 1436,
	ERROR_NO_SYSTEM_MENU => 1437,
	ERROR_INVALID_MSGBOX_STYLE => 1438,
	ERROR_INVALID_SPI_VALUE => 1439,
	ERROR_SCREEN_ALREADY_LOCKED => 1440,
	ERROR_HWNDS_HAVE_DIFF_PARENT => 1441,
	ERROR_NOT_CHILD_WINDOW => 1442,
	ERROR_INVALID_GW_COMMAND => 1443,
	ERROR_INVALID_THREAD_ID => 1444,
	ERROR_NON_MDICHILD_WINDOW => 1445,
	ERROR_POPUP_ALREADY_ACTIVE => 1446,
	ERROR_NO_SCROLLBARS => 1447,
	ERROR_INVALID_SCROLLBAR_RANGE => 1448,
	ERROR_INVALID_SHOWWIN_COMMAND => 1449,
	ERROR_NO_SYSTEM_RESOURCES => 1450,
	ERROR_NONPAGED_SYSTEM_RESOURCES => 1451,
	ERROR_PAGED_SYSTEM_RESOURCES => 1452,
	ERROR_WORKING_SET_QUOTA => 1453,
	ERROR_PAGEFILE_QUOTA => 1454,
	ERROR_COMMITMENT_LIMIT => 1455,
	ERROR_MENU_ITEM_NOT_FOUND => 1456,
	ERROR_INVALID_KEYBOARD_HANDLE => 1457,
	ERROR_HOOK_TYPE_NOT_ALLOWED => 1458,
	ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION => 1459,
	ERROR_TIMEOUT => 1460,
	ERROR_INVALID_MONITOR_HANDLE => 1461,
	ERROR_INCORRECT_SIZE => 1462,
	ERROR_SYMLINK_CLASS_DISABLED => 1463,
	ERROR_SYMLINK_NOT_SUPPORTED => 1464,
	ERROR_XML_PARSE_ERROR => 1465,
	ERROR_XMLDSIG_ERROR => 1466,
	ERROR_RESTART_APPLICATION => 1467,
	ERROR_WRONG_COMPARTMENT => 1468,
	ERROR_AUTHIP_FAILURE => 1469,
	ERROR_NO_NVRAM_RESOURCES => 1470,
	ERROR_NOT_GUI_PROCESS => 1471,
	ERROR_EVENTLOG_FILE_CORRUPT => 1500,
	ERROR_EVENTLOG_CANT_START => 1501,
	ERROR_LOG_FILE_FULL => 1502,
	ERROR_EVENTLOG_FILE_CHANGED => 1503,
	EN_HSCROLL => 1537,
	EN_VSCROLL => 1538,
	ERROR_INSTALL_SERVICE_FAILURE => 1601,
	ERROR_INSTALL_USEREXIT => 1602,
	ERROR_INSTALL_FAILURE => 1603,
	ERROR_INSTALL_SUSPEND => 1604,
	ERROR_UNKNOWN_PRODUCT => 1605,
	ERROR_UNKNOWN_FEATURE => 1606,
	ERROR_UNKNOWN_COMPONENT => 1607,
	ERROR_UNKNOWN_PROPERTY => 1608,
	ERROR_INVALID_HANDLE_STATE => 1609,
	ERROR_BAD_CONFIGURATION => 1610,
	ERROR_INDEX_ABSENT => 1611,
	ERROR_INSTALL_SOURCE_ABSENT => 1612,
	ERROR_INSTALL_PACKAGE_VERSION => 1613,
	ERROR_PRODUCT_UNINSTALLED => 1614,
	ERROR_BAD_QUERY_SYNTAX => 1615,
	ERROR_INVALID_FIELD => 1616,
	ERROR_DEVICE_REMOVED => 1617,
	ERROR_INSTALL_ALREADY_RUNNING => 1618,
	ERROR_INSTALL_PACKAGE_OPEN_FAILED => 1619,
	ERROR_INSTALL_PACKAGE_INVALID => 1620,
	ERROR_INSTALL_UI_FAILURE => 1621,
	ERROR_INSTALL_LOG_FAILURE => 1622,
	ERROR_INSTALL_LANGUAGE_UNSUPPORTED => 1623,
	ERROR_INSTALL_TRANSFORM_FAILURE => 1624,
	ERROR_INSTALL_PACKAGE_REJECTED => 1625,
	ERROR_FUNCTION_NOT_CALLED => 1626,
	ERROR_FUNCTION_FAILED => 1627,
	ERROR_INVALID_TABLE => 1628,
	ERROR_DATATYPE_MISMATCH => 1629,
	ERROR_UNSUPPORTED_TYPE => 1630,
	ERROR_CREATE_FAILED => 1631,
	ERROR_INSTALL_TEMP_UNWRITABLE => 1632,
	ERROR_INSTALL_PLATFORM_UNSUPPORTED => 1633,
	ERROR_INSTALL_NOTUSED => 1634,
	ERROR_PATCH_PACKAGE_OPEN_FAILED => 1635,
	ERROR_PATCH_PACKAGE_INVALID => 1636,
	ERROR_PATCH_PACKAGE_UNSUPPORTED => 1637,
	ERROR_PRODUCT_VERSION => 1638,
	ERROR_INVALID_COMMAND_LINE => 1639,
	ERROR_INSTALL_REMOTE_DISALLOWED => 1640,
	ERROR_SUCCESS_REBOOT_INITIATED => 1641,
	ERROR_PATCH_TARGET_NOT_FOUND => 1642,
	ERROR_PATCH_PACKAGE_REJECTED => 1643,
	ERROR_INSTALL_TRANSFORM_REJECTED => 1644,
	ERROR_INSTALL_REMOTE_PROHIBITED => 1645,
	EPT_S_INVALID_ENTRY => 1751,
	EPT_S_CANT_PERFORM_OP => 1752,
	EPT_S_NOT_REGISTERED => 1753,
	ERROR_INVALID_USER_BUFFER => 1784,
	ERROR_UNRECOGNIZED_MEDIA => 1785,
	ERROR_NO_TRUST_LSA_SECRET => 1786,
	ERROR_NO_TRUST_SAM_ACCOUNT => 1787,
	ERROR_TRUSTED_DOMAIN_FAILURE => 1788,
	ERROR_TRUSTED_RELATIONSHIP_FAILURE => 1789,
	ERROR_TRUST_FAILURE => 1790,
	EN_ALIGN_LTR_EC => 1792,
	ERROR_NETLOGON_NOT_STARTED => 1792,
	EN_ALIGN_RTL_EC => 1793,
	ERROR_ACCOUNT_EXPIRED => 1793,
	ERROR_REDIRECTOR_HAS_OPEN_HANDLES => 1794,
	ERROR_PRINTER_DRIVER_ALREADY_INSTALLED => 1795,
	ERROR_UNKNOWN_PORT => 1796,
	ERROR_UNKNOWN_PRINTER_DRIVER => 1797,
	ERROR_UNKNOWN_PRINTPROCESSOR => 1798,
	ERROR_INVALID_SEPARATOR_FILE => 1799,
	ERROR_INVALID_PRIORITY => 1800,
	ERROR_INVALID_PRINTER_NAME => 1801,
	ERROR_PRINTER_ALREADY_EXISTS => 1802,
	ERROR_INVALID_PRINTER_COMMAND => 1803,
	ERROR_INVALID_DATATYPE => 1804,
	ERROR_INVALID_ENVIRONMENT => 1805,
	ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT => 1807,
	ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT => 1808,
	ERROR_NOLOGON_SERVER_TRUST_ACCOUNT => 1809,
	ERROR_DOMAIN_TRUST_INCONSISTENT => 1810,
	ERROR_SERVER_HAS_OPEN_HANDLES => 1811,
	ERROR_RESOURCE_DATA_NOT_FOUND => 1812,
	ERROR_RESOURCE_TYPE_NOT_FOUND => 1813,
	ERROR_RESOURCE_NAME_NOT_FOUND => 1814,
	ERROR_RESOURCE_LANG_NOT_FOUND => 1815,
	ERROR_NOT_ENOUGH_QUOTA => 1816,
	EPT_S_CANT_CREATE => 1899,
	ERROR_INVALID_TIME => 1901,
	ERROR_INVALID_FORM_NAME => 1902,
	ERROR_INVALID_FORM_SIZE => 1903,
	ERROR_ALREADY_WAITING => 1904,
	ERROR_PRINTER_DELETED => 1905,
	ERROR_INVALID_PRINTER_STATE => 1906,
	ERROR_PASSWORD_MUST_CHANGE => 1907,
	ERROR_DOMAIN_CONTROLLER_NOT_FOUND => 1908,
	ERROR_ACCOUNT_LOCKED_OUT => 1909,
	ERROR_NO_SITENAME => 1919,
	ERROR_CANT_ACCESS_FILE => 1920,
	ERROR_CANT_RESOLVE_FILENAME => 1921,
	ERROR_KM_DRIVER_BLOCKED => 1930,
	ERROR_CONTEXT_EXPIRED => 1931,
	ERROR_PER_USER_TRUST_QUOTA_EXCEEDED => 1932,
	ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED => 1933,
	ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED => 1934,
	ERROR_AUTHENTICATION_FIREWALL_FAILED => 1935,
	ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED => 1936,
	ERROR_INVALID_PIXEL_FORMAT => 2000,
	ERROR_BAD_DRIVER => 2001,
	ERROR_INVALID_WINDOW_STYLE => 2002,
	ERROR_METAFILE_NOT_SUPPORTED => 2003,
	ERROR_TRANSFORM_NOT_SUPPORTED => 2004,
	ERROR_CLIPPING_NOT_SUPPORTED => 2005,
	ERROR_INVALID_CMM => 2010,
	ERROR_INVALID_PROFILE => 2011,
	ERROR_TAG_NOT_FOUND => 2012,
	ERROR_TAG_NOT_PRESENT => 2013,
	ERROR_DUPLICATE_TAG => 2014,
	ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE => 2015,
	ERROR_PROFILE_NOT_FOUND => 2016,
	ERROR_INVALID_COLORSPACE => 2017,
	ERROR_ICM_NOT_ENABLED => 2018,
	ERROR_DELETING_ICM_XFORM => 2019,
	ERROR_INVALID_TRANSFORM => 2020,
	ERROR_COLORSPACE_MISMATCH => 2021,
	ERROR_INVALID_COLORINDEX => 2022,
	ES_READONLY => 2048,
	ETO_NUMERICSLATIN => 2048,
	EV_EVENT1 => 2048,
	ERROR_CONNECTED_OTHER_PASSWORD => 2108,
	ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT => 2109,
	ERROR_BAD_USERNAME => 2202,
	ERROR_NOT_CONNECTED => 2250,
	ERROR_OPEN_FILES => 2401,
	ERROR_ACTIVE_CONNECTIONS => 2402,
	ERROR_DEVICE_IN_USE => 2404,
	ERROR_UNKNOWN_PRINT_MONITOR => 3000,
	ERROR_PRINTER_DRIVER_IN_USE => 3001,
	ERROR_SPOOL_FILE_NOT_FOUND => 3002,
	ERROR_SPL_NO_STARTDOC => 3003,
	ERROR_SPL_NO_ADDJOB => 3004,
	ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED => 3005,
	ERROR_PRINT_MONITOR_ALREADY_INSTALLED => 3006,
	ERROR_INVALID_PRINT_MONITOR => 3007,
	ERROR_PRINT_MONITOR_IN_USE => 3008,
	ERROR_PRINTER_HAS_JOBS_QUEUED => 3009,
	ERROR_SUCCESS_REBOOT_REQUIRED => 3010,
	ERROR_SUCCESS_RESTART_REQUIRED => 3011,
	ERROR_PRINTER_NOT_FOUND => 3012,
	ERROR_PRINTER_DRIVER_WARNED => 3013,
	ERROR_PRINTER_DRIVER_BLOCKED => 3014,
	ERROR_WINS_INTERNAL => 4000,
	ERROR_CAN_NOT_DEL_LOCAL_WINS => 4001,
	ERROR_STATIC_INIT => 4002,
	ERROR_INC_BACKUP => 4003,
	ERROR_FULL_BACKUP => 4004,
	ERROR_REC_NON_EXISTENT => 4005,
	ERROR_RPL_NOT_ALLOWED => 4006,
	ELEMENT_STATUS_LUN_VALID => 4096,
	ES_WANTRETURN => 4096,
	ETO_IGNORELANGUAGE => 4096,
	EV_EVENT2 => 4096,
	END_PATH => 4098,
	EXT_DEVICE_CAPS => 4099,
	ERROR_DHCP_ADDRESS_CONFLICT => 4100,
	ENCAPSULATED_POSTSCRIPT => 4116,
	ERROR_WMI_GUID_NOT_FOUND => 4200,
	ERROR_WMI_INSTANCE_NOT_FOUND => 4201,
	ERROR_WMI_ITEMID_NOT_FOUND => 4202,
	ERROR_WMI_TRY_AGAIN => 4203,
	ERROR_WMI_DP_NOT_FOUND => 4204,
	ERROR_WMI_UNRESOLVED_INSTANCE_REF => 4205,
	ERROR_WMI_ALREADY_ENABLED => 4206,
	ERROR_WMI_GUID_DISCONNECTED => 4207,
	ERROR_WMI_SERVER_UNAVAILABLE => 4208,
	ERROR_WMI_DP_FAILED => 4209,
	ERROR_WMI_INVALID_MOF => 4210,
	ERROR_WMI_INVALID_REGINFO => 4211,
	ERROR_WMI_ALREADY_DISABLED => 4212,
	ERROR_WMI_READ_ONLY => 4213,
	ERROR_WMI_SET_FAILURE => 4214,
	ERROR_INVALID_MEDIA => 4300,
	ERROR_INVALID_LIBRARY => 4301,
	ERROR_INVALID_MEDIA_POOL => 4302,
	ERROR_DRIVE_MEDIA_MISMATCH => 4303,
	ERROR_MEDIA_OFFLINE => 4304,
	ERROR_LIBRARY_OFFLINE => 4305,
	ERROR_EMPTY => 4306,
	ERROR_NOT_EMPTY => 4307,
	ERROR_MEDIA_UNAVAILABLE => 4308,
	ERROR_RESOURCE_DISABLED => 4309,
	ERROR_INVALID_CLEANER => 4310,
	ERROR_UNABLE_TO_CLEAN => 4311,
	ERROR_OBJECT_NOT_FOUND => 4312,
	ERROR_DATABASE_FAILURE => 4313,
	ERROR_DATABASE_FULL => 4314,
	ERROR_MEDIA_INCOMPATIBLE => 4315,
	ERROR_RESOURCE_NOT_PRESENT => 4316,
	ERROR_INVALID_OPERATION => 4317,
	ERROR_MEDIA_NOT_AVAILABLE => 4318,
	ERROR_DEVICE_NOT_AVAILABLE => 4319,
	ERROR_REQUEST_REFUSED => 4320,
	ERROR_INVALID_DRIVE_OBJECT => 4321,
	ERROR_LIBRARY_FULL => 4322,
	ERROR_MEDIUM_NOT_ACCESSIBLE => 4323,
	ERROR_UNABLE_TO_LOAD_MEDIUM => 4324,
	ERROR_UNABLE_TO_INVENTORY_DRIVE => 4325,
	ERROR_UNABLE_TO_INVENTORY_SLOT => 4326,
	ERROR_UNABLE_TO_INVENTORY_TRANSPORT => 4327,
	ERROR_TRANSPORT_FULL => 4328,
	ERROR_CONTROLLING_IEPORT => 4329,
	ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA => 4330,
	ERROR_CLEANER_SLOT_SET => 4331,
	ERROR_CLEANER_SLOT_NOT_SET => 4332,
	ERROR_CLEANER_CARTRIDGE_SPENT => 4333,
	ERROR_UNEXPECTED_OMID => 4334,
	ERROR_CANT_DELETE_LAST_ITEM => 4335,
	ERROR_MESSAGE_EXCEEDS_MAX_SIZE => 4336,
	ERROR_VOLUME_CONTAINS_SYS_FILES => 4337,
	ERROR_INDIGENOUS_TYPE => 4338,
	ERROR_NO_SUPPORTING_DRIVES => 4339,
	ERROR_CLEANER_CARTRIDGE_INSTALLED => 4340,
	ERROR_IEPORT_FULL => 4341,
	ERROR_FILE_OFFLINE => 4350,
	ERROR_REMOTE_STORAGE_NOT_ACTIVE => 4351,
	ERROR_REMOTE_STORAGE_MEDIA_ERROR => 4352,
	ERROR_NOT_A_REPARSE_POINT => 4390,
	ERROR_REPARSE_ATTRIBUTE_CONFLICT => 4391,
	ERROR_INVALID_REPARSE_DATA => 4392,
	ERROR_REPARSE_TAG_INVALID => 4393,
	ERROR_REPARSE_TAG_MISMATCH => 4394,
	ERROR_VOLUME_NOT_SIS_ENABLED => 4500,
	ERROR_DEPENDENT_RESOURCE_EXISTS => 5001,
	ERROR_DEPENDENCY_NOT_FOUND => 5002,
	ERROR_DEPENDENCY_ALREADY_EXISTS => 5003,
	ERROR_RESOURCE_NOT_ONLINE => 5004,
	ERROR_HOST_NODE_NOT_AVAILABLE => 5005,
	ERROR_RESOURCE_NOT_AVAILABLE => 5006,
	ERROR_RESOURCE_NOT_FOUND => 5007,
	ERROR_SHUTDOWN_CLUSTER => 5008,
	ERROR_CANT_EVICT_ACTIVE_NODE => 5009,
	ERROR_OBJECT_ALREADY_EXISTS => 5010,
	ERROR_OBJECT_IN_LIST => 5011,
	ERROR_GROUP_NOT_AVAILABLE => 5012,
	ERROR_GROUP_NOT_FOUND => 5013,
	ERROR_GROUP_NOT_ONLINE => 5014,
	ERROR_HOST_NODE_NOT_RESOURCE_OWNER => 5015,
	ERROR_HOST_NODE_NOT_GROUP_OWNER => 5016,
	ERROR_RESMON_CREATE_FAILED => 5017,
	ERROR_RESMON_ONLINE_FAILED => 5018,
	ERROR_RESOURCE_ONLINE => 5019,
	ERROR_QUORUM_RESOURCE => 5020,
	ERROR_NOT_QUORUM_CAPABLE => 5021,
	ERROR_CLUSTER_SHUTTING_DOWN => 5022,
	ERROR_INVALID_STATE => 5023,
	ERROR_RESOURCE_PROPERTIES_STORED => 5024,
	ERROR_NOT_QUORUM_CLASS => 5025,
	ERROR_CORE_RESOURCE => 5026,
	ERROR_QUORUM_RESOURCE_ONLINE_FAILED => 5027,
	ERROR_QUORUMLOG_OPEN_FAILED => 5028,
	ERROR_CLUSTERLOG_CORRUPT => 5029,
	ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE => 5030,
	ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE => 5031,
	ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND => 5032,
	ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE => 5033,
	ERROR_QUORUM_OWNER_ALIVE => 5034,
	ERROR_NETWORK_NOT_AVAILABLE => 5035,
	ERROR_NODE_NOT_AVAILABLE => 5036,
	ERROR_ALL_NODES_NOT_AVAILABLE => 5037,
	ERROR_RESOURCE_FAILED => 5038,
	ERROR_CLUSTER_INVALID_NODE => 5039,
	ERROR_CLUSTER_NODE_EXISTS => 5040,
	ERROR_CLUSTER_JOIN_IN_PROGRESS => 5041,
	ERROR_CLUSTER_NODE_NOT_FOUND => 5042,
	ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND => 5043,
	ERROR_CLUSTER_NETWORK_EXISTS => 5044,
	ERROR_CLUSTER_NETWORK_NOT_FOUND => 5045,
	ERROR_CLUSTER_NETINTERFACE_EXISTS => 5046,
	ERROR_CLUSTER_NETINTERFACE_NOT_FOUND => 5047,
	ERROR_CLUSTER_INVALID_REQUEST => 5048,
	ERROR_CLUSTER_INVALID_NETWORK_PROVIDER => 5049,
	ERROR_CLUSTER_NODE_DOWN => 5050,
	ERROR_CLUSTER_NODE_UNREACHABLE => 5051,
	ERROR_CLUSTER_NODE_NOT_MEMBER => 5052,
	ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS => 5053,
	ERROR_CLUSTER_INVALID_NETWORK => 5054,
	ERROR_CLUSTER_NODE_UP => 5056,
	ERROR_CLUSTER_IPADDR_IN_USE => 5057,
	ERROR_CLUSTER_NODE_NOT_PAUSED => 5058,
	ERROR_CLUSTER_NO_SECURITY_CONTEXT => 5059,
	ERROR_CLUSTER_NETWORK_NOT_INTERNAL => 5060,
	ERROR_CLUSTER_NODE_ALREADY_UP => 5061,
	ERROR_CLUSTER_NODE_ALREADY_DOWN => 5062,
	ERROR_CLUSTER_NETWORK_ALREADY_ONLINE => 5063,
	ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE => 5064,
	ERROR_CLUSTER_NODE_ALREADY_MEMBER => 5065,
	ERROR_CLUSTER_LAST_INTERNAL_NETWORK => 5066,
	ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS => 5067,
	ERROR_INVALID_OPERATION_ON_QUORUM => 5068,
	ERROR_DEPENDENCY_NOT_ALLOWED => 5069,
	ERROR_CLUSTER_NODE_PAUSED => 5070,
	ERROR_NODE_CANT_HOST_RESOURCE => 5071,
	ERROR_CLUSTER_NODE_NOT_READY => 5072,
	ERROR_CLUSTER_NODE_SHUTTING_DOWN => 5073,
	ERROR_CLUSTER_JOIN_ABORTED => 5074,
	ERROR_CLUSTER_INCOMPATIBLE_VERSIONS => 5075,
	ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED => 5076,
	ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED => 5077,
	ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND => 5078,
	ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED => 5079,
	ERROR_CLUSTER_RESNAME_NOT_FOUND => 5080,
	ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED => 5081,
	ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST => 5082,
	ERROR_CLUSTER_DATABASE_SEQMISMATCH => 5083,
	ERROR_RESMON_INVALID_STATE => 5084,
	ERROR_CLUSTER_GUM_NOT_LOCKER => 5085,
	ERROR_QUORUM_DISK_NOT_FOUND => 5086,
	ERROR_DATABASE_BACKUP_CORRUPT => 5087,
	ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT => 5088,
	ERROR_RESOURCE_PROPERTY_UNCHANGEABLE => 5089,
	ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE => 5890,
	ERROR_CLUSTER_QUORUMLOG_NOT_FOUND => 5891,
	ERROR_CLUSTER_MEMBERSHIP_HALT => 5892,
	ERROR_CLUSTER_INSTANCE_ID_MISMATCH => 5893,
	ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP => 5894,
	ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH => 5895,
	ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP => 5896,
	ERROR_CLUSTER_PARAMETER_MISMATCH => 5897,
	ERROR_NODE_CANNOT_BE_CLUSTERED => 5898,
	ERROR_CLUSTER_WRONG_OS_VERSION => 5899,
	ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME => 5900,
	ERROR_CLUSCFG_ALREADY_COMMITTED => 5901,
	ERROR_CLUSCFG_ROLLBACK_FAILED => 5902,
	ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT => 5903,
	ERROR_CLUSTER_OLD_VERSION => 5904,
	ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME => 5905,
	ERROR_ENCRYPTION_FAILED => 6000,
	ERROR_DECRYPTION_FAILED => 6001,
	ERROR_FILE_ENCRYPTED => 6002,
	ERROR_NO_RECOVERY_POLICY => 6003,
	ERROR_NO_EFS => 6004,
	ERROR_WRONG_EFS => 6005,
	ERROR_NO_USER_KEYS => 6006,
	ERROR_FILE_NOT_ENCRYPTED => 6007,
	ERROR_NOT_EXPORT_FORMAT => 6008,
	ERROR_FILE_READ_ONLY => 6009,
	ERROR_DIR_EFS_DISALLOWED => 6010,
	ERROR_EFS_SERVER_NOT_TRUSTED => 6011,
	ERROR_BAD_RECOVERY_POLICY => 6012,
	ERROR_EFS_ALG_BLOB_TOO_BIG => 6013,
	ERROR_VOLUME_NOT_SUPPORT_EFS => 6014,
	ERROR_EFS_DISABLED => 6015,
	ERROR_EFS_VERSION_NOT_SUPPORT => 6016,
	ERROR_NO_BROWSER_SERVERS_FOUND => 6118,
	ERROR_CTX_WINSTATION_NAME_INVALID => 7001,
	ERROR_CTX_INVALID_PD => 7002,
	ERROR_CTX_PD_NOT_FOUND => 7003,
	ERROR_CTX_WD_NOT_FOUND => 7004,
	ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY => 7005,
	ERROR_CTX_SERVICE_NAME_COLLISION => 7006,
	ERROR_CTX_CLOSE_PENDING => 7007,
	ERROR_CTX_NO_OUTBUF => 7008,
	ERROR_CTX_MODEM_INF_NOT_FOUND => 7009,
	ERROR_CTX_INVALID_MODEMNAME => 7010,
	ERROR_CTX_MODEM_RESPONSE_ERROR => 7011,
	ERROR_CTX_MODEM_RESPONSE_TIMEOUT => 7012,
	ERROR_CTX_MODEM_RESPONSE_NO_CARRIER => 7013,
	ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE => 7014,
	ERROR_CTX_MODEM_RESPONSE_BUSY => 7015,
	ERROR_CTX_MODEM_RESPONSE_VOICE => 7016,
	ERROR_CTX_TD_ERROR => 7017,
	ERROR_CTX_WINSTATION_NOT_FOUND => 7022,
	ERROR_CTX_WINSTATION_ALREADY_EXISTS => 7023,
	ERROR_CTX_WINSTATION_BUSY => 7024,
	ERROR_CTX_BAD_VIDEO_MODE => 7025,
	ERROR_CTX_GRAPHICS_INVALID => 7035,
	ERROR_CTX_LOGON_DISABLED => 7037,
	ERROR_CTX_NOT_CONSOLE => 7038,
	ERROR_CTX_CLIENT_QUERY_TIMEOUT => 7040,
	ERROR_CTX_CONSOLE_DISCONNECT => 7041,
	ERROR_CTX_CONSOLE_CONNECT => 7042,
	ERROR_CTX_SHADOW_DENIED => 7044,
	ERROR_CTX_WINSTATION_ACCESS_DENIED => 7045,
	ERROR_CTX_INVALID_WD => 7049,
	ERROR_CTX_SHADOW_INVALID => 7050,
	ERROR_CTX_SHADOW_DISABLED => 7051,
	ERROR_CTX_CLIENT_LICENSE_IN_USE => 7052,
	ERROR_CTX_CLIENT_LICENSE_NOT_SET => 7053,
	ERROR_CTX_LICENSE_NOT_AVAILABLE => 7054,
	ERROR_CTX_LICENSE_CLIENT_INVALID => 7055,
	ERROR_CTX_LICENSE_EXPIRED => 7056,
	ERROR_CTX_SHADOW_NOT_RUNNING => 7057,
	ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE => 7058,
	ERROR_ACTIVATION_COUNT_EXCEEDED => 7059,
	ELEMENT_STATUS_ID_VALID => 8192,
	ES_NUMBER => 8192,
	ETO_PDY => 8192,
	ERROR_DS_NOT_INSTALLED => 8200,
	ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY => 8201,
	ERROR_DS_NO_ATTRIBUTE_OR_VALUE => 8202,
	ERROR_DS_INVALID_ATTRIBUTE_SYNTAX => 8203,
	ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED => 8204,
	ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS => 8205,
	ERROR_DS_BUSY => 8206,
	ERROR_DS_UNAVAILABLE => 8207,
	ERROR_DS_NO_RIDS_ALLOCATED => 8208,
	ERROR_DS_NO_MORE_RIDS => 8209,
	ERROR_DS_INCORRECT_ROLE_OWNER => 8210,
	ERROR_DS_RIDMGR_INIT_ERROR => 8211,
	ERROR_DS_OBJ_CLASS_VIOLATION => 8212,
	ERROR_DS_CANT_ON_NON_LEAF => 8213,
	ERROR_DS_CANT_ON_RDN => 8214,
	ERROR_DS_CANT_MOD_OBJ_CLASS => 8215,
	ERROR_DS_CROSS_DOM_MOVE_ERROR => 8216,
	ERROR_DS_GC_NOT_AVAILABLE => 8217,
	ERROR_SHARED_POLICY => 8218,
	ERROR_POLICY_OBJECT_NOT_FOUND => 8219,
	ERROR_POLICY_ONLY_IN_DS => 8220,
	ERROR_PROMOTION_ACTIVE => 8221,
	ERROR_NO_PROMOTION_ACTIVE => 8222,
	ERROR_DS_OPERATIONS_ERROR => 8224,
	ERROR_DS_PROTOCOL_ERROR => 8225,
	ERROR_DS_TIMELIMIT_EXCEEDED => 8226,
	ERROR_DS_SIZELIMIT_EXCEEDED => 8227,
	ERROR_DS_ADMIN_LIMIT_EXCEEDED => 8228,
	ERROR_DS_COMPARE_FALSE => 8229,
	ERROR_DS_COMPARE_TRUE => 8230,
	ERROR_DS_AUTH_METHOD_NOT_SUPPORTED => 8231,
	ERROR_DS_STRONG_AUTH_REQUIRED => 8232,
	ERROR_DS_INAPPROPRIATE_AUTH => 8233,
	ERROR_DS_AUTH_UNKNOWN => 8234,
	ERROR_DS_REFERRAL => 8235,
	ERROR_DS_UNAVAILABLE_CRIT_EXTENSION => 8236,
	ERROR_DS_CONFIDENTIALITY_REQUIRED => 8237,
	ERROR_DS_INAPPROPRIATE_MATCHING => 8238,
	ERROR_DS_CONSTRAINT_VIOLATION => 8239,
	ERROR_DS_NO_SUCH_OBJECT => 8240,
	ERROR_DS_ALIAS_PROBLEM => 8241,
	ERROR_DS_INVALID_DN_SYNTAX => 8242,
	ERROR_DS_IS_LEAF => 8243,
	ERROR_DS_ALIAS_DEREF_PROBLEM => 8244,
	ERROR_DS_UNWILLING_TO_PERFORM => 8245,
	ERROR_DS_LOOP_DETECT => 8246,
	ERROR_DS_NAMING_VIOLATION => 8247,
	ERROR_DS_OBJECT_RESULTS_TOO_LARGE => 8248,
	ERROR_DS_AFFECTS_MULTIPLE_DSAS => 8249,
	ERROR_DS_SERVER_DOWN => 8250,
	ERROR_DS_LOCAL_ERROR => 8251,
	ERROR_DS_ENCODING_ERROR => 8252,
	ERROR_DS_DECODING_ERROR => 8253,
	ERROR_DS_FILTER_UNKNOWN => 8254,
	ERROR_DS_PARAM_ERROR => 8255,
	ERROR_DS_NOT_SUPPORTED => 8256,
	ERROR_DS_NO_RESULTS_RETURNED => 8257,
	ERROR_DS_CONTROL_NOT_FOUND => 8258,
	ERROR_DS_CLIENT_LOOP => 8259,
	ERROR_DS_REFERRAL_LIMIT_EXCEEDED => 8260,
	ERROR_DS_SORT_CONTROL_MISSING => 8261,
	ERROR_DS_OFFSET_RANGE_ERROR => 8262,
	ERROR_DS_ROOT_MUST_BE_NC => 8301,
	ERROR_DS_ADD_REPLICA_INHIBITED => 8302,
	ERROR_DS_ATT_NOT_DEF_IN_SCHEMA => 8303,
	ERROR_DS_MAX_OBJ_SIZE_EXCEEDED => 8304,
	ERROR_DS_OBJ_STRING_NAME_EXISTS => 8305,
	ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA => 8306,
	ERROR_DS_RDN_DOESNT_MATCH_SCHEMA => 8307,
	ERROR_DS_NO_REQUESTED_ATTS_FOUND => 8308,
	ERROR_DS_USER_BUFFER_TO_SMALL => 8309,
	ERROR_DS_ATT_IS_NOT_ON_OBJ => 8310,
	ERROR_DS_ILLEGAL_MOD_OPERATION => 8311,
	ERROR_DS_OBJ_TOO_LARGE => 8312,
	ERROR_DS_BAD_INSTANCE_TYPE => 8313,
	ERROR_DS_MASTERDSA_REQUIRED => 8314,
	ERROR_DS_OBJECT_CLASS_REQUIRED => 8315,
	ERROR_DS_MISSING_REQUIRED_ATT => 8316,
	ERROR_DS_ATT_NOT_DEF_FOR_CLASS => 8317,
	ERROR_DS_ATT_ALREADY_EXISTS => 8318,
	ERROR_DS_CANT_ADD_ATT_VALUES => 8320,
	ERROR_DS_SINGLE_VALUE_CONSTRAINT => 8321,
	ERROR_DS_RANGE_CONSTRAINT => 8322,
	ERROR_DS_ATT_VAL_ALREADY_EXISTS => 8323,
	ERROR_DS_CANT_REM_MISSING_ATT => 8324,
	ERROR_DS_CANT_REM_MISSING_ATT_VAL => 8325,
	ERROR_DS_ROOT_CANT_BE_SUBREF => 8326,
	ERROR_DS_NO_CHAINING => 8327,
	ERROR_DS_NO_CHAINED_EVAL => 8328,
	ERROR_DS_NO_PARENT_OBJECT => 8329,
	ERROR_DS_PARENT_IS_AN_ALIAS => 8330,
	ERROR_DS_CANT_MIX_MASTER_AND_REPS => 8331,
	ERROR_DS_CHILDREN_EXIST => 8332,
	ERROR_DS_OBJ_NOT_FOUND => 8333,
	ERROR_DS_ALIASED_OBJ_MISSING => 8334,
	ERROR_DS_BAD_NAME_SYNTAX => 8335,
	ERROR_DS_ALIAS_POINTS_TO_ALIAS => 8336,
	ERROR_DS_CANT_DEREF_ALIAS => 8337,
	ERROR_DS_OUT_OF_SCOPE => 8338,
	ERROR_DS_OBJECT_BEING_REMOVED => 8339,
	ERROR_DS_CANT_DELETE_DSA_OBJ => 8340,
	ERROR_DS_GENERIC_ERROR => 8341,
	ERROR_DS_DSA_MUST_BE_INT_MASTER => 8342,
	ERROR_DS_CLASS_NOT_DSA => 8343,
	ERROR_DS_INSUFF_ACCESS_RIGHTS => 8344,
	ERROR_DS_ILLEGAL_SUPERIOR => 8345,
	ERROR_DS_ATTRIBUTE_OWNED_BY_SAM => 8346,
	ERROR_DS_NAME_TOO_MANY_PARTS => 8347,
	ERROR_DS_NAME_TOO_LONG => 8348,
	ERROR_DS_NAME_VALUE_TOO_LONG => 8349,
	ERROR_DS_NAME_UNPARSEABLE => 8350,
	ERROR_DS_NAME_TYPE_UNKNOWN => 8351,
	ERROR_DS_NOT_AN_OBJECT => 8352,
	ERROR_DS_SEC_DESC_TOO_SHORT => 8353,
	ERROR_DS_SEC_DESC_INVALID => 8354,
	ERROR_DS_NO_DELETED_NAME => 8355,
	ERROR_DS_SUBREF_MUST_HAVE_PARENT => 8356,
	ERROR_DS_NCNAME_MUST_BE_NC => 8357,
	ERROR_DS_CANT_ADD_SYSTEM_ONLY => 8358,
	ERROR_DS_CLASS_MUST_BE_CONCRETE => 8359,
	ERROR_DS_INVALID_DMD => 8360,
	ERROR_DS_OBJ_GUID_EXISTS => 8361,
	ERROR_DS_NOT_ON_BACKLINK => 8362,
	ERROR_DS_NO_CROSSREF_FOR_NC => 8363,
	ERROR_DS_SHUTTING_DOWN => 8364,
	ERROR_DS_UNKNOWN_OPERATION => 8365,
	ERROR_DS_INVALID_ROLE_OWNER => 8366,
	ERROR_DS_COULDNT_CONTACT_FSMO => 8367,
	ERROR_DS_CROSS_NC_DN_RENAME => 8368,
	ERROR_DS_CANT_MOD_SYSTEM_ONLY => 8369,
	ERROR_DS_REPLICATOR_ONLY => 8370,
	ERROR_DS_OBJ_CLASS_NOT_DEFINED => 8371,
	ERROR_DS_OBJ_CLASS_NOT_SUBCLASS => 8372,
	ERROR_DS_NAME_REFERENCE_INVALID => 8373,
	ERROR_DS_CROSS_REF_EXISTS => 8374,
	ERROR_DS_CANT_DEL_MASTER_CROSSREF => 8375,
	ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD => 8376,
	ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX => 8377,
	ERROR_DS_DUP_RDN => 8378,
	ERROR_DS_DUP_OID => 8379,
	ERROR_DS_DUP_MAPI_ID => 8380,
	ERROR_DS_DUP_SCHEMA_ID_GUID => 8381,
	ERROR_DS_DUP_LDAP_DISPLAY_NAME => 8382,
	ERROR_DS_SEMANTIC_ATT_TEST => 8383,
	ERROR_DS_SYNTAX_MISMATCH => 8384,
	ERROR_DS_EXISTS_IN_MUST_HAVE => 8385,
	ERROR_DS_EXISTS_IN_MAY_HAVE => 8386,
	ERROR_DS_NONEXISTENT_MAY_HAVE => 8387,
	ERROR_DS_NONEXISTENT_MUST_HAVE => 8388,
	ERROR_DS_AUX_CLS_TEST_FAIL => 8389,
	ERROR_DS_NONEXISTENT_POSS_SUP => 8390,
	ERROR_DS_SUB_CLS_TEST_FAIL => 8391,
	ERROR_DS_BAD_RDN_ATT_ID_SYNTAX => 8392,
	ERROR_DS_EXISTS_IN_AUX_CLS => 8393,
	ERROR_DS_EXISTS_IN_SUB_CLS => 8394,
	ERROR_DS_EXISTS_IN_POSS_SUP => 8395,
	ERROR_DS_RECALCSCHEMA_FAILED => 8396,
	ERROR_DS_TREE_DELETE_NOT_FINISHED => 8397,
	ERROR_DS_CANT_DELETE => 8398,
	ERROR_DS_ATT_SCHEMA_REQ_ID => 8399,
	ERROR_DS_BAD_ATT_SCHEMA_SYNTAX => 8400,
	ERROR_DS_CANT_CACHE_ATT => 8401,
	ERROR_DS_CANT_CACHE_CLASS => 8402,
	ERROR_DS_CANT_REMOVE_ATT_CACHE => 8403,
	ERROR_DS_CANT_REMOVE_CLASS_CACHE => 8404,
	ERROR_DS_CANT_RETRIEVE_DN => 8405,
	ERROR_DS_MISSING_SUPREF => 8406,
	ERROR_DS_CANT_RETRIEVE_INSTANCE => 8407,
	ERROR_DS_CODE_INCONSISTENCY => 8408,
	ERROR_DS_DATABASE_ERROR => 8409,
	ERROR_DS_GOVERNSID_MISSING => 8410,
	ERROR_DS_MISSING_EXPECTED_ATT => 8411,
	ERROR_DS_NCNAME_MISSING_CR_REF => 8412,
	ERROR_DS_SECURITY_CHECKING_ERROR => 8413,
	ERROR_DS_SCHEMA_NOT_LOADED => 8414,
	ERROR_DS_SCHEMA_ALLOC_FAILED => 8415,
	ERROR_DS_ATT_SCHEMA_REQ_SYNTAX => 8416,
	ERROR_DS_GCVERIFY_ERROR => 8417,
	ERROR_DS_DRA_SCHEMA_MISMATCH => 8418,
	ERROR_DS_CANT_FIND_DSA_OBJ => 8419,
	ERROR_DS_CANT_FIND_EXPECTED_NC => 8420,
	ERROR_DS_CANT_FIND_NC_IN_CACHE => 8421,
	ERROR_DS_CANT_RETRIEVE_CHILD => 8422,
	ERROR_DS_SECURITY_ILLEGAL_MODIFY => 8423,
	ERROR_DS_CANT_REPLACE_HIDDEN_REC => 8424,
	ERROR_DS_BAD_HIERARCHY_FILE => 8425,
	ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED => 8426,
	ERROR_DS_CONFIG_PARAM_MISSING => 8427,
	ERROR_DS_COUNTING_AB_INDICES_FAILED => 8428,
	ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED => 8429,
	ERROR_DS_INTERNAL_FAILURE => 8430,
	ERROR_DS_UNKNOWN_ERROR => 8431,
	ERROR_DS_ROOT_REQUIRES_CLASS_TOP => 8432,
	ERROR_DS_REFUSING_FSMO_ROLES => 8433,
	ERROR_DS_MISSING_FSMO_SETTINGS => 8434,
	ERROR_DS_UNABLE_TO_SURRENDER_ROLES => 8435,
	ERROR_DS_DRA_GENERIC => 8436,
	ERROR_DS_DRA_INVALID_PARAMETER => 8437,
	ERROR_DS_DRA_BUSY => 8438,
	ERROR_DS_DRA_BAD_DN => 8439,
	ERROR_DS_DRA_BAD_NC => 8440,
	ERROR_DS_DRA_DN_EXISTS => 8441,
	ERROR_DS_DRA_INTERNAL_ERROR => 8442,
	ERROR_DS_DRA_INCONSISTENT_DIT => 8443,
	ERROR_DS_DRA_CONNECTION_FAILED => 8444,
	ERROR_DS_DRA_BAD_INSTANCE_TYPE => 8445,
	ERROR_DS_DRA_OUT_OF_MEM => 8446,
	ERROR_DS_DRA_MAIL_PROBLEM => 8447,
	ERROR_DS_DRA_REF_ALREADY_EXISTS => 8448,
	ERROR_DS_DRA_REF_NOT_FOUND => 8449,
	ERROR_DS_DRA_OBJ_IS_REP_SOURCE => 8450,
	ERROR_DS_DRA_DB_ERROR => 8451,
	ERROR_DS_DRA_NO_REPLICA => 8452,
	ERROR_DS_DRA_ACCESS_DENIED => 8453,
	ERROR_DS_DRA_NOT_SUPPORTED => 8454,
	ERROR_DS_DRA_RPC_CANCELLED => 8455,
	ERROR_DS_DRA_SOURCE_DISABLED => 8456,
	ERROR_DS_DRA_SINK_DISABLED => 8457,
	ERROR_DS_DRA_NAME_COLLISION => 8458,
	ERROR_DS_DRA_SOURCE_REINSTALLED => 8459,
	ERROR_DS_DRA_MISSING_PARENT => 8460,
	ERROR_DS_DRA_PREEMPTED => 8461,
	ERROR_DS_DRA_ABANDON_SYNC => 8462,
	ERROR_DS_DRA_SHUTDOWN => 8463,
	ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET => 8464,
	ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA => 8465,
	ERROR_DS_DRA_EXTN_CONNECTION_FAILED => 8466,
	ERROR_DS_INSTALL_SCHEMA_MISMATCH => 8467,
	ERROR_DS_DUP_LINK_ID => 8468,
	ERROR_DS_NAME_ERROR_RESOLVING => 8469,
	ERROR_DS_NAME_ERROR_NOT_FOUND => 8470,
	ERROR_DS_NAME_ERROR_NOT_UNIQUE => 8471,
	ERROR_DS_NAME_ERROR_NO_MAPPING => 8472,
	ERROR_DS_NAME_ERROR_DOMAIN_ONLY => 8473,
	ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING => 8474,
	ERROR_DS_CONSTRUCTED_ATT_MOD => 8475,
	ERROR_DS_WRONG_OM_OBJ_CLASS => 8476,
	ERROR_DS_DRA_REPL_PENDING => 8477,
	ERROR_DS_DS_REQUIRED => 8478,
	ERROR_DS_INVALID_LDAP_DISPLAY_NAME => 8479,
	ERROR_DS_NON_BASE_SEARCH => 8480,
	ERROR_DS_CANT_RETRIEVE_ATTS => 8481,
	ERROR_DS_BACKLINK_WITHOUT_LINK => 8482,
	ERROR_DS_EPOCH_MISMATCH => 8483,
	ERROR_DS_SRC_NAME_MISMATCH => 8484,
	ERROR_DS_SRC_AND_DST_NC_IDENTICAL => 8485,
	ERROR_DS_DST_NC_MISMATCH => 8486,
	ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC => 8487,
	ERROR_DS_SRC_GUID_MISMATCH => 8488,
	ERROR_DS_CANT_MOVE_DELETED_OBJECT => 8489,
	ERROR_DS_PDC_OPERATION_IN_PROGRESS => 8490,
	ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD => 8491,
	ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION => 8492,
	ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS => 8493,
	ERROR_DS_NC_MUST_HAVE_NC_PARENT => 8494,
	ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE => 8495,
	ERROR_DS_DST_DOMAIN_NOT_NATIVE => 8496,
	ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER => 8497,
	ERROR_DS_CANT_MOVE_ACCOUNT_GROUP => 8498,
	ERROR_DS_CANT_MOVE_RESOURCE_GROUP => 8499,
	ERROR_DS_INVALID_SEARCH_FLAG => 8500,
	ERROR_DS_NO_TREE_DELETE_ABOVE_NC => 8501,
	ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE => 8502,
	ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE => 8503,
	ERROR_DS_SAM_INIT_FAILURE => 8504,
	ERROR_DS_SENSITIVE_GROUP_VIOLATION => 8505,
	ERROR_DS_CANT_MOD_PRIMARYGROUPID => 8506,
	ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD => 8507,
	ERROR_DS_NONSAFE_SCHEMA_CHANGE => 8508,
	ERROR_DS_SCHEMA_UPDATE_DISALLOWED => 8509,
	ERROR_DS_CANT_CREATE_UNDER_SCHEMA => 8510,
	ERROR_DS_INSTALL_NO_SRC_SCH_VERSION => 8511,
	ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE => 8512,
	ERROR_DS_INVALID_GROUP_TYPE => 8513,
	ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN => 8514,
	ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN => 8515,
	ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER => 8516,
	ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER => 8517,
	ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER => 8518,
	ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER => 8519,
	ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER => 8520,
	ERROR_DS_HAVE_PRIMARY_MEMBERS => 8521,
	ERROR_DS_STRING_SD_CONVERSION_FAILED => 8522,
	ERROR_DS_NAMING_MASTER_GC => 8523,
	ERROR_DS_DNS_LOOKUP_FAILURE => 8524,
	ERROR_DS_COULDNT_UPDATE_SPNS => 8525,
	ERROR_DS_CANT_RETRIEVE_SD => 8526,
	ERROR_DS_KEY_NOT_UNIQUE => 8527,
	ERROR_DS_WRONG_LINKED_ATT_SYNTAX => 8528,
	ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD => 8529,
	ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY => 8530,
	ERROR_DS_CANT_START => 8531,
	ERROR_DS_INIT_FAILURE => 8532,
	ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION => 8533,
	ERROR_DS_SOURCE_DOMAIN_IN_FOREST => 8534,
	ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST => 8535,
	ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED => 8536,
	ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN => 8537,
	ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER => 8538,
	ERROR_DS_SRC_SID_EXISTS_IN_FOREST => 8539,
	ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH => 8540,
	ERROR_SAM_INIT_FAILURE => 8541,
	ERROR_DS_DRA_SCHEMA_INFO_SHIP => 8542,
	ERROR_DS_DRA_SCHEMA_CONFLICT => 8543,
	ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT => 8544,
	ERROR_DS_DRA_OBJ_NC_MISMATCH => 8545,
	ERROR_DS_NC_STILL_HAS_DSAS => 8546,
	ERROR_DS_GC_REQUIRED => 8547,
	ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY => 8548,
	ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS => 8549,
	ERROR_DS_CANT_ADD_TO_GC => 8550,
	ERROR_DS_NO_CHECKPOINT_WITH_PDC => 8551,
	ERROR_DS_SOURCE_AUDITING_NOT_ENABLED => 8552,
	ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC => 8553,
	ERROR_DS_INVALID_NAME_FOR_SPN => 8554,
	ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS => 8555,
	ERROR_DS_UNICODEPWD_NOT_IN_QUOTES => 8556,
	ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED => 8557,
	ERROR_DS_MUST_BE_RUN_ON_DST_DC => 8558,
	ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER => 8559,
	ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ => 8560,
	ERROR_DS_INIT_FAILURE_CONSOLE => 8561,
	ERROR_DS_SAM_INIT_FAILURE_CONSOLE => 8562,
	ERROR_DS_FOREST_VERSION_TOO_HIGH => 8563,
	ERROR_DS_DOMAIN_VERSION_TOO_HIGH => 8564,
	ERROR_DS_FOREST_VERSION_TOO_LOW => 8565,
	ERROR_DS_DOMAIN_VERSION_TOO_LOW => 8566,
	ERROR_DS_INCOMPATIBLE_VERSION => 8567,
	ERROR_DS_LOW_DSA_VERSION => 8568,
	ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN => 8569,
	ERROR_DS_NOT_SUPPORTED_SORT_ORDER => 8570,
	ERROR_DS_NAME_NOT_UNIQUE => 8571,
	ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 => 8572,
	ERROR_DS_OUT_OF_VERSION_STORE => 8573,
	ERROR_DS_INCOMPATIBLE_CONTROLS_USED => 8574,
	ERROR_DS_NO_REF_DOMAIN => 8575,
	ERROR_DS_RESERVED_LINK_ID => 8576,
	ERROR_DS_LINK_ID_NOT_AVAILABLE => 8577,
	ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER => 8578,
	ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE => 8579,
	ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC => 8580,
	ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG => 8581,
	ERROR_DS_MODIFYDN_WRONG_GRANDPARENT => 8582,
	ERROR_DS_NAME_ERROR_TRUST_REFERRAL => 8583,
	ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER => 8584,
	ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD => 8585,
	ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 => 8586,
	ERROR_DS_THREAD_LIMIT_EXCEEDED => 8587,
	ERROR_DS_NOT_CLOSEST => 8588,
	ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF => 8589,
	ERROR_DS_SINGLE_USER_MODE_FAILED => 8590,
	ERROR_DS_NTDSCRIPT_SYNTAX_ERROR => 8591,
	ERROR_DS_NTDSCRIPT_PROCESS_ERROR => 8592,
	ERROR_DS_DIFFERENT_REPL_EPOCHS => 8593,
	ERROR_DS_DRS_EXTENSIONS_CHANGED => 8594,
	ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR => 8595,
	ERROR_DS_NO_MSDS_INTID => 8596,
	ERROR_DS_DUP_MSDS_INTID => 8597,
	ERROR_DS_EXISTS_IN_RDNATTID => 8598,
	ERROR_DS_AUTHORIZATION_FAILED => 8599,
	ERROR_DS_INVALID_SCRIPT => 8600,
	ERROR_DS_REMOTE_CROSSREF_OP_FAILED => 8601,
	ERROR_DS_CROSS_REF_BUSY => 8602,
	ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN => 8603,
	ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC => 8604,
	ERROR_DS_DUPLICATE_ID_FOUND => 8605,
	ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT => 8606,
	ERROR_DS_GROUP_CONVERSION_ERROR => 8607,
	ERROR_DS_CANT_MOVE_APP_BASIC_GROUP => 8608,
	ERROR_DS_CANT_MOVE_APP_QUERY_GROUP => 8609,
	ERROR_DS_ROLE_NOT_VERIFIED => 8610,
	ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL => 8611,
	ERROR_DS_DOMAIN_RENAME_IN_PROGRESS => 8612,
	ERROR_DS_EXISTING_AD_CHILD_NC => 8613,
	ERROR_DS_REPL_LIFETIME_EXCEEDED => 8614,
	ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER => 8615,
	ERROR_DS_LDAP_SEND_QUEUE_FULL => 8616,
	ERROR_DS_DRA_OUT_SCHEDULE_WINDOW => 8617,
	WSAEINTR => 10004,
	WSAEBADF => 10009,
	WSAEACCES => 10013,
	WSAEFAULT => 10014,
	WSAEINVAL => 10022,
	WSAEMFILE => 10024,
	WSAEWOULDBLOCK => 10035,
	WSAEINPROGRESS => 10036,
	WSAEALREADY => 10037,
	WSAENOTSOCK => 10038,
	WSAEDESTADDRREQ => 10039,
	WSAEMSGSIZE => 10040,
	WSAEPROTOTYPE => 10041,
	WSAENOPROTOOPT => 10042,
	WSAEPROTONOSUPPORT => 10043,
	ESOCKTNOSUPPORT => 10044,
	WSAESOCKTNOSUPPORT => 10044,
	WSAEOPNOTSUPP => 10045,
	EPFNOSUPPORT => 10046,
	WSAEPFNOSUPPORT => 10046,
	WSAEAFNOSUPPORT => 10047,
	WSAEADDRINUSE => 10048,
	WSAEADDRNOTAVAIL => 10049,
	WSAENETDOWN => 10050,
	WSAENETUNREACH => 10051,
	WSAENETRESET => 10052,
	WSAECONNABORTED => 10053,
	WSAECONNRESET => 10054,
	WSAENOBUFS => 10055,
	WSAEISCONN => 10056,
	WSAENOTCONN => 10057,
	ESHUTDOWN => 10058,
	WSAESHUTDOWN => 10058,
	ETOOMANYREFS => 10059,
	WSAETOOMANYREFS => 10059,
	WSAETIMEDOUT => 10060,
	WSAECONNREFUSED => 10061,
	WSAELOOP => 10062,
	WSAENAMETOOLONG => 10063,
	WSAEHOSTDOWN => 10064,
	WSAEHOSTUNREACH => 10065,
	WSAENOTEMPTY => 10066,
	EPROCLIM => 10067,
	WSAEPROCLIM => 10067,
	EUSERS => 10068,
	WSAEUSERS => 10068,
	EDQUOT => 10069,
	WSAEDQUOT => 10069,
	ESTALE => 10070,
	WSAESTALE => 10070,
	EREMOTE => 10071,
	WSAEREMOTE => 10071,
	WSAEDISCON => 10101,
	WSAENOMORE => 10102,
	WSAECANCELLED => 10103,
	WSAEINVALIDPROCTABLE => 10104,
	WSAEINVALIDPROVIDER => 10105,
	WSAEPROVIDERFAILEDINIT => 10106,
	WSAEREFUSED => 10112,
	ERROR_BIDI_ERROR_BASE => 13000,
	ERROR_IPSEC_QM_POLICY_EXISTS => 13000,
	ERROR_BIDI_STATUS_WARNING => 13001,
	ERROR_IPSEC_QM_POLICY_NOT_FOUND => 13001,
	ERROR_BIDI_SCHEMA_READ_ONLY => 13002,
	ERROR_IPSEC_QM_POLICY_IN_USE => 13002,
	ERROR_BIDI_SERVER_OFFLINE => 13003,
	ERROR_IPSEC_MM_POLICY_EXISTS => 13003,
	ERROR_BIDI_DEVICE_OFFLINE => 13004,
	ERROR_IPSEC_MM_POLICY_NOT_FOUND => 13004,
	ERROR_BIDI_SCHEMA_NOT_SUPPORTED => 13005,
	ERROR_IPSEC_MM_POLICY_IN_USE => 13005,
	ERROR_IPSEC_MM_FILTER_EXISTS => 13006,
	ERROR_IPSEC_MM_FILTER_NOT_FOUND => 13007,
	ERROR_IPSEC_TRANSPORT_FILTER_EXISTS => 13008,
	ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND => 13009,
	ERROR_IPSEC_MM_AUTH_EXISTS => 13010,
	ERROR_IPSEC_MM_AUTH_NOT_FOUND => 13011,
	ERROR_IPSEC_MM_AUTH_IN_USE => 13012,
	ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND => 13013,
	ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND => 13014,
	ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND => 13015,
	ERROR_IPSEC_TUNNEL_FILTER_EXISTS => 13016,
	ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND => 13017,
	ERROR_IPSEC_MM_FILTER_PENDING_DELETION => 13018,
	ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION => 13019,
	ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION => 13020,
	ERROR_IPSEC_MM_POLICY_PENDING_DELETION => 13021,
	ERROR_IPSEC_MM_AUTH_PENDING_DELETION => 13022,
	ERROR_IPSEC_QM_POLICY_PENDING_DELETION => 13023,
	ERROR_IPSEC_IKE_NEG_STATUS_BEGIN => 13800,
	ERROR_IPSEC_IKE_AUTH_FAIL => 13801,
	ERROR_IPSEC_IKE_ATTRIB_FAIL => 13802,
	ERROR_IPSEC_IKE_NEGOTIATION_PENDING => 13803,
	ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR => 13804,
	ERROR_IPSEC_IKE_TIMED_OUT => 13805,
	ERROR_IPSEC_IKE_NO_CERT => 13806,
	ERROR_IPSEC_IKE_SA_DELETED => 13807,
	ERROR_IPSEC_IKE_SA_REAPED => 13808,
	ERROR_IPSEC_IKE_MM_ACQUIRE_DROP => 13809,
	ERROR_IPSEC_IKE_QM_ACQUIRE_DROP => 13810,
	ERROR_IPSEC_IKE_QUEUE_DROP_MM => 13811,
	ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM => 13812,
	ERROR_IPSEC_IKE_DROP_NO_RESPONSE => 13813,
	ERROR_IPSEC_IKE_MM_DELAY_DROP => 13814,
	ERROR_IPSEC_IKE_QM_DELAY_DROP => 13815,
	ERROR_IPSEC_IKE_ERROR => 13816,
	ERROR_IPSEC_IKE_CRL_FAILED => 13817,
	ERROR_IPSEC_IKE_INVALID_KEY_USAGE => 13818,
	ERROR_IPSEC_IKE_INVALID_CERT_TYPE => 13819,
	ERROR_IPSEC_IKE_NO_PRIVATE_KEY => 13820,
	ERROR_IPSEC_IKE_DH_FAIL => 13822,
	ERROR_IPSEC_IKE_INVALID_HEADER => 13824,
	ERROR_IPSEC_IKE_NO_POLICY => 13825,
	ERROR_IPSEC_IKE_INVALID_SIGNATURE => 13826,
	ERROR_IPSEC_IKE_KERBEROS_ERROR => 13827,
	ERROR_IPSEC_IKE_NO_PUBLIC_KEY => 13828,
	ERROR_IPSEC_IKE_PROCESS_ERR => 13829,
	ERROR_IPSEC_IKE_PROCESS_ERR_SA => 13830,
	ERROR_IPSEC_IKE_PROCESS_ERR_PROP => 13831,
	ERROR_IPSEC_IKE_PROCESS_ERR_TRANS => 13832,
	ERROR_IPSEC_IKE_PROCESS_ERR_KE => 13833,
	ERROR_IPSEC_IKE_PROCESS_ERR_ID => 13834,
	ERROR_IPSEC_IKE_PROCESS_ERR_CERT => 13835,
	ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ => 13836,
	ERROR_IPSEC_IKE_PROCESS_ERR_HASH => 13837,
	ERROR_IPSEC_IKE_PROCESS_ERR_SIG => 13838,
	ERROR_IPSEC_IKE_PROCESS_ERR_NONCE => 13839,
	ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY => 13840,
	ERROR_IPSEC_IKE_PROCESS_ERR_DELETE => 13841,
	ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR => 13842,
	ERROR_IPSEC_IKE_INVALID_PAYLOAD => 13843,
	ERROR_IPSEC_IKE_LOAD_SOFT_SA => 13844,
	ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN => 13845,
	ERROR_IPSEC_IKE_INVALID_COOKIE => 13846,
	ERROR_IPSEC_IKE_NO_PEER_CERT => 13847,
	ERROR_IPSEC_IKE_PEER_CRL_FAILED => 13848,
	ERROR_IPSEC_IKE_POLICY_CHANGE => 13849,
	ERROR_IPSEC_IKE_NO_MM_POLICY => 13850,
	ERROR_IPSEC_IKE_NOTCBPRIV => 13851,
	ERROR_IPSEC_IKE_SECLOADFAIL => 13852,
	ERROR_IPSEC_IKE_FAILSSPINIT => 13853,
	ERROR_IPSEC_IKE_FAILQUERYSSP => 13854,
	ERROR_IPSEC_IKE_SRVACQFAIL => 13855,
	ERROR_IPSEC_IKE_SRVQUERYCRED => 13856,
	ERROR_IPSEC_IKE_GETSPIFAIL => 13857,
	ERROR_IPSEC_IKE_INVALID_FILTER => 13858,
	ERROR_IPSEC_IKE_OUT_OF_MEMORY => 13859,
	ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED => 13860,
	ERROR_IPSEC_IKE_INVALID_POLICY => 13861,
	ERROR_IPSEC_IKE_UNKNOWN_DOI => 13862,
	ERROR_IPSEC_IKE_INVALID_SITUATION => 13863,
	ERROR_IPSEC_IKE_DH_FAILURE => 13864,
	ERROR_IPSEC_IKE_INVALID_GROUP => 13865,
	ERROR_IPSEC_IKE_ENCRYPT => 13866,
	ERROR_IPSEC_IKE_DECRYPT => 13867,
	ERROR_IPSEC_IKE_POLICY_MATCH => 13868,
	ERROR_IPSEC_IKE_UNSUPPORTED_ID => 13869,
	ERROR_IPSEC_IKE_INVALID_HASH => 13870,
	ERROR_IPSEC_IKE_INVALID_HASH_ALG => 13871,
	ERROR_IPSEC_IKE_INVALID_HASH_SIZE => 13872,
	ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG => 13873,
	ERROR_IPSEC_IKE_INVALID_AUTH_ALG => 13874,
	ERROR_IPSEC_IKE_INVALID_SIG => 13875,
	ERROR_IPSEC_IKE_LOAD_FAILED => 13876,
	ERROR_IPSEC_IKE_RPC_DELETE => 13877,
	ERROR_IPSEC_IKE_BENIGN_REINIT => 13878,
	ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY => 13879,
	ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN => 13881,
	ERROR_IPSEC_IKE_MM_LIMIT => 13882,
	ERROR_IPSEC_IKE_NEGOTIATION_DISABLED => 13883,
	ERROR_IPSEC_IKE_QM_LIMIT => 13884,
	ERROR_IPSEC_IKE_MM_EXPIRED => 13885,
	ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID => 13886,
	ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH => 13887,
	ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID => 13888,
	ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD => 13889,
	ERROR_IPSEC_IKE_DOS_COOKIE_SENT => 13890,
	ERROR_IPSEC_IKE_SHUTTING_DOWN => 13891,
	ERROR_IPSEC_IKE_CGA_AUTH_FAILED => 13892,
	ERROR_IPSEC_IKE_PROCESS_ERR_NATOA => 13893,
	ERROR_IPSEC_IKE_INVALID_MM_FOR_QM => 13894,
	ERROR_IPSEC_IKE_QM_EXPIRED => 13895,
	ERROR_IPSEC_IKE_TOO_MANY_FILTERS => 13896,
	ERROR_IPSEC_IKE_NEG_STATUS_END => 13897,
	ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL => 13898,
	ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE => 13899,
	ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING => 13900,
	ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING => 13901,
	ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS => 13902,
	ERROR_IPSEC_IKE_RATELIMIT_DROP => 13903,
	ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE => 13904,
	ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE => 13905,
	ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE => 13906,
	ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY => 13907,
	ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE => 13908,
	ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END => 13909,
	ERROR_IPSEC_BAD_SPI => 13910,
	ERROR_IPSEC_SA_LIFETIME_EXPIRED => 13911,
	ERROR_IPSEC_WRONG_SA => 13912,
	ERROR_IPSEC_REPLAY_CHECK_FAILED => 13913,
	ERROR_IPSEC_INVALID_PACKET => 13914,
	ERROR_IPSEC_INTEGRITY_CHECK_FAILED => 13915,
	ERROR_IPSEC_CLEAR_TEXT_DROP => 13916,
	ERROR_IPSEC_AUTH_FIREWALL_DROP => 13917,
	ERROR_IPSEC_THROTTLE_DROP => 13918,
	ERROR_IPSEC_DOSP_BLOCK => 13925,
	ERROR_IPSEC_DOSP_RECEIVED_MULTICAST => 13926,
	ERROR_IPSEC_DOSP_INVALID_PACKET => 13927,
	ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED => 13928,
	ERROR_IPSEC_DOSP_MAX_ENTRIES => 13929,
	ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED => 13930,
	ERROR_IPSEC_DOSP_NOT_INSTALLED => 13931,
	ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES => 13932,
	ERROR_SXS_SECTION_NOT_FOUND => 14000,
	ERROR_SXS_CANT_GEN_ACTCTX => 14001,
	ERROR_SXS_INVALID_ACTCTXDATA_FORMAT => 14002,
	ERROR_SXS_ASSEMBLY_NOT_FOUND => 14003,
	ERROR_SXS_MANIFEST_FORMAT_ERROR => 14004,
	ERROR_SXS_MANIFEST_PARSE_ERROR => 14005,
	ERROR_SXS_ACTIVATION_CONTEXT_DISABLED => 14006,
	ERROR_SXS_KEY_NOT_FOUND => 14007,
	ERROR_SXS_VERSION_CONFLICT => 14008,
	ERROR_SXS_WRONG_SECTION_TYPE => 14009,
	ERROR_SXS_THREAD_QUERIES_DISABLED => 14010,
	ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET => 14011,
	ERROR_SXS_UNKNOWN_ENCODING_GROUP => 14012,
	ERROR_SXS_UNKNOWN_ENCODING => 14013,
	ERROR_SXS_INVALID_XML_NAMESPACE_URI => 14014,
	ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED => 14015,
	ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED => 14016,
	ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE => 14017,
	ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE => 14018,
	ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE => 14019,
	ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT => 14020,
	ERROR_SXS_DUPLICATE_DLL_NAME => 14021,
	ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME => 14022,
	ERROR_SXS_DUPLICATE_CLSID => 14023,
	ERROR_SXS_DUPLICATE_IID => 14024,
	ERROR_SXS_DUPLICATE_TLBID => 14025,
	ERROR_SXS_DUPLICATE_PROGID => 14026,
	ERROR_SXS_DUPLICATE_ASSEMBLY_NAME => 14027,
	ERROR_SXS_FILE_HASH_MISMATCH => 14028,
	ERROR_SXS_POLICY_PARSE_ERROR => 14029,
	ERROR_SXS_XML_E_MISSINGQUOTE => 14030,
	ERROR_SXS_XML_E_COMMENTSYNTAX => 14031,
	ERROR_SXS_XML_E_BADSTARTNAMECHAR => 14032,
	ERROR_SXS_XML_E_BADNAMECHAR => 14033,
	ERROR_SXS_XML_E_BADCHARINSTRING => 14034,
	ERROR_SXS_XML_E_XMLDECLSYNTAX => 14035,
	ERROR_SXS_XML_E_BADCHARDATA => 14036,
	ERROR_SXS_XML_E_MISSINGWHITESPACE => 14037,
	ERROR_SXS_XML_E_EXPECTINGTAGEND => 14038,
	ERROR_SXS_XML_E_MISSINGSEMICOLON => 14039,
	ERROR_SXS_XML_E_UNBALANCEDPAREN => 14040,
	ERROR_SXS_XML_E_INTERNALERROR => 14041,
	ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE => 14042,
	ERROR_SXS_XML_E_INCOMPLETE_ENCODING => 14043,
	ERROR_SXS_XML_E_MISSING_PAREN => 14044,
	ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE => 14045,
	ERROR_SXS_XML_E_MULTIPLE_COLONS => 14046,
	ERROR_SXS_XML_E_INVALID_DECIMAL => 14047,
	ERROR_SXS_XML_E_INVALID_HEXIDECIMAL => 14048,
	ERROR_SXS_XML_E_INVALID_UNICODE => 14049,
	ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK => 14050,
	ERROR_SXS_XML_E_UNEXPECTEDENDTAG => 14051,
	ERROR_SXS_XML_E_UNCLOSEDTAG => 14052,
	ERROR_SXS_XML_E_DUPLICATEATTRIBUTE => 14053,
	ERROR_SXS_XML_E_MULTIPLEROOTS => 14054,
	ERROR_SXS_XML_E_INVALIDATROOTLEVEL => 14055,
	ERROR_SXS_XML_E_BADXMLDECL => 14056,
	ERROR_SXS_XML_E_MISSINGROOT => 14057,
	ERROR_SXS_XML_E_UNEXPECTEDEOF => 14058,
	ERROR_SXS_XML_E_BADPEREFINSUBSET => 14059,
	ERROR_SXS_XML_E_UNCLOSEDSTARTTAG => 14060,
	ERROR_SXS_XML_E_UNCLOSEDENDTAG => 14061,
	ERROR_SXS_XML_E_UNCLOSEDSTRING => 14062,
	ERROR_SXS_XML_E_UNCLOSEDCOMMENT => 14063,
	ERROR_SXS_XML_E_UNCLOSEDDECL => 14064,
	ERROR_SXS_XML_E_UNCLOSEDCDATA => 14065,
	ERROR_SXS_XML_E_RESERVEDNAMESPACE => 14066,
	ERROR_SXS_XML_E_INVALIDENCODING => 14067,
	ERROR_SXS_XML_E_INVALIDSWITCH => 14068,
	ERROR_SXS_XML_E_BADXMLCASE => 14069,
	ERROR_SXS_XML_E_INVALID_STANDALONE => 14070,
	ERROR_SXS_XML_E_UNEXPECTED_STANDALONE => 14071,
	ERROR_SXS_XML_E_INVALID_VERSION => 14072,
	ERROR_SXS_XML_E_MISSINGEQUALS => 14073,
	ERROR_SXS_PROTECTION_RECOVERY_FAILED => 14074,
	ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT => 14075,
	ERROR_SXS_PROTECTION_CATALOG_NOT_VALID => 14076,
	ERROR_SXS_UNTRANSLATABLE_HRESULT => 14077,
	ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING => 14078,
	ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE => 14079,
	ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME => 14080,
	ERROR_SXS_ASSEMBLY_MISSING => 14081,
	ERROR_SXS_CORRUPT_ACTIVATION_STACK => 14082,
	ERROR_SXS_CORRUPTION => 14083,
	ERROR_SXS_EARLY_DEACTIVATION => 14084,
	ERROR_SXS_INVALID_DEACTIVATION => 14085,
	ERROR_SXS_MULTIPLE_DEACTIVATION => 14086,
	ERROR_SXS_PROCESS_TERMINATION_REQUESTED => 14087,
	ERROR_SXS_RELEASE_ACTIVATION_CONTEXT => 14088,
	ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY => 14089,
	ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE => 14090,
	ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME => 14091,
	ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE => 14092,
	ERROR_SXS_IDENTITY_PARSE_ERROR => 14093,
	ERROR_MALFORMED_SUBSTITUTION_STRING => 14094,
	ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN => 14095,
	ERROR_UNMAPPED_SUBSTITUTION_STRING => 14096,
	ERROR_SXS_ASSEMBLY_NOT_LOCKED => 14097,
	ERROR_SXS_COMPONENT_STORE_CORRUPT => 14098,
	ERROR_ADVANCED_INSTALLER_FAILED => 14099,
	ERROR_XML_ENCODING_MISMATCH => 14100,
	ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT => 14101,
	ERROR_SXS_IDENTITIES_DIFFERENT => 14102,
	ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT => 14103,
	ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY => 14104,
	ERROR_SXS_MANIFEST_TOO_BIG => 14105,
	ERROR_SXS_SETTING_NOT_REGISTERED => 14106,
	ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE => 14107,
	ERROR_SMI_PRIMITIVE_INSTALLER_FAILED => 14108,
	ERROR_GENERIC_COMMAND_FAILED => 14109,
	ERROR_SXS_FILE_HASH_MISSING => 14110,
	ERROR_EVT_INVALID_CHANNEL_PATH => 15000,
	ERROR_EVT_INVALID_QUERY => 15001,
	ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND => 15002,
	ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND => 15003,
	ERROR_EVT_INVALID_PUBLISHER_NAME => 15004,
	ERROR_EVT_INVALID_EVENT_DATA => 15005,
	ERROR_EVT_CHANNEL_NOT_FOUND => 15007,
	ERROR_EVT_MALFORMED_XML_TEXT => 15008,
	ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL => 15009,
	ERROR_EVT_CONFIGURATION_ERROR => 15010,
	ERROR_EVT_QUERY_RESULT_STALE => 15011,
	ERROR_EVT_QUERY_RESULT_INVALID_POSITION => 15012,
	ERROR_EVT_NON_VALIDATING_MSXML => 15013,
	ERROR_EVT_FILTER_ALREADYSCOPED => 15014,
	ERROR_EVT_FILTER_NOTELTSET => 15015,
	ERROR_EVT_FILTER_INVARG => 15016,
	ERROR_EVT_FILTER_INVTEST => 15017,
	ERROR_EVT_FILTER_INVTYPE => 15018,
	ERROR_EVT_FILTER_PARSEERR => 15019,
	ERROR_EVT_FILTER_UNSUPPORTEDOP => 15020,
	ERROR_EVT_FILTER_UNEXPECTEDTOKEN => 15021,
	ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL => 15022,
	ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE => 15023,
	ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE => 15024,
	ERROR_EVT_CHANNEL_CANNOT_ACTIVATE => 15025,
	ERROR_EVT_FILTER_TOO_COMPLEX => 15026,
	ERROR_EVT_MESSAGE_NOT_FOUND => 15027,
	ERROR_EVT_MESSAGE_ID_NOT_FOUND => 15028,
	ERROR_EVT_UNRESOLVED_VALUE_INSERT => 15029,
	ERROR_EVT_UNRESOLVED_PARAMETER_INSERT => 15030,
	ERROR_EVT_MAX_INSERTS_REACHED => 15031,
	ERROR_EVT_EVENT_DEFINITION_NOT_FOUND => 15032,
	ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND => 15033,
	ERROR_EVT_VERSION_TOO_OLD => 15034,
	ERROR_EVT_VERSION_TOO_NEW => 15035,
	ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY => 15036,
	ERROR_EVT_PUBLISHER_DISABLED => 15037,
	ERROR_EVT_FILTER_OUT_OF_RANGE => 15038,
	ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE => 15080,
	ERROR_EC_LOG_DISABLED => 15081,
	ERROR_EC_CIRCULAR_FORWARDING => 15082,
	ERROR_EC_CREDSTORE_FULL => 15083,
	ERROR_EC_CRED_NOT_FOUND => 15084,
	ERROR_EC_NO_ACTIVE_CHANNEL => 15085,
	ERROR_MUI_FILE_NOT_FOUND => 15100,
	ERROR_MUI_INVALID_FILE => 15101,
	ERROR_MUI_INVALID_RC_CONFIG => 15102,
	ERROR_MUI_INVALID_LOCALE_NAME => 15103,
	ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME => 15104,
	ERROR_MUI_FILE_NOT_LOADED => 15105,
	ERROR_RESOURCE_ENUM_USER_STOP => 15106,
	ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED => 15107,
	ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME => 15108,
	ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE => 15110,
	ERROR_MRM_INVALID_PRICONFIG => 15111,
	ERROR_MRM_INVALID_FILE_TYPE => 15112,
	ERROR_MRM_UNKNOWN_QUALIFIER => 15113,
	ERROR_MRM_INVALID_QUALIFIER_VALUE => 15114,
	ERROR_MRM_NO_CANDIDATE => 15115,
	ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE => 15116,
	ERROR_MRM_RESOURCE_TYPE_MISMATCH => 15117,
	ERROR_MRM_DUPLICATE_MAP_NAME => 15118,
	ERROR_MRM_DUPLICATE_ENTRY => 15119,
	ERROR_MRM_INVALID_RESOURCE_IDENTIFIER => 15120,
	ERROR_MRM_FILEPATH_TOO_LONG => 15121,
	ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE => 15122,
	ERROR_MRM_INVALID_PRI_FILE => 15126,
	ERROR_MRM_NAMED_RESOURCE_NOT_FOUND => 15127,
	ERROR_MRM_MAP_NOT_FOUND => 15135,
	ERROR_MRM_UNSUPPORTED_PROFILE_TYPE => 15136,
	ERROR_MRM_INVALID_QUALIFIER_OPERATOR => 15137,
	ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE => 15138,
	ERROR_MRM_AUTOMERGE_ENABLED => 15139,
	ERROR_MRM_TOO_MANY_RESOURCES => 15140,
	ERROR_MCA_INVALID_CAPABILITIES_STRING => 15200,
	ERROR_MCA_INVALID_VCP_VERSION => 15201,
	ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION => 15202,
	ERROR_MCA_MCCS_VERSION_MISMATCH => 15203,
	ERROR_MCA_UNSUPPORTED_MCCS_VERSION => 15204,
	ERROR_MCA_INTERNAL_ERROR => 15205,
	ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED => 15206,
	ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE => 15207,
	ERROR_AMBIGUOUS_SYSTEM_DEVICE => 15250,
	ERROR_SYSTEM_DEVICE_NOT_FOUND => 15299,
	ERROR_HASH_NOT_SUPPORTED => 15300,
	ERROR_HASH_NOT_PRESENT => 15301,
	ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED => 15321,
	ERROR_GPIO_CLIENT_INFORMATION_INVALID => 15322,
	ERROR_GPIO_VERSION_NOT_SUPPORTED => 15323,
	ERROR_GPIO_INVALID_REGISTRATION_PACKET => 15324,
	ERROR_GPIO_OPERATION_DENIED => 15325,
	ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE => 15326,
	ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED => 15327,
	ERROR_CANNOT_SWITCH_RUNLEVEL => 15400,
	ERROR_INVALID_RUNLEVEL_SETTING => 15401,
	ERROR_RUNLEVEL_SWITCH_TIMEOUT => 15402,
	ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT => 15403,
	ERROR_RUNLEVEL_SWITCH_IN_PROGRESS => 15404,
	ERROR_SERVICES_FAILED_AUTOSTART => 15405,
	ERROR_COM_TASK_STOP_PENDING => 15501,
	ERROR_INSTALL_OPEN_PACKAGE_FAILED => 15600,
	ERROR_INSTALL_PACKAGE_NOT_FOUND => 15601,
	ERROR_INSTALL_INVALID_PACKAGE => 15602,
	ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED => 15603,
	ERROR_INSTALL_OUT_OF_DISK_SPACE => 15604,
	ERROR_INSTALL_NETWORK_FAILURE => 15605,
	ERROR_INSTALL_REGISTRATION_FAILURE => 15606,
	ERROR_INSTALL_DEREGISTRATION_FAILURE => 15607,
	ERROR_INSTALL_CANCEL => 15608,
	ERROR_INSTALL_FAILED => 15609,
	ERROR_REMOVE_FAILED => 15610,
	ERROR_PACKAGE_ALREADY_EXISTS => 15611,
	ERROR_NEEDS_REMEDIATION => 15612,
	ERROR_INSTALL_PREREQUISITE_FAILED => 15613,
	ERROR_PACKAGE_REPOSITORY_CORRUPTED => 15614,
	ERROR_INSTALL_POLICY_FAILURE => 15615,
	ERROR_PACKAGE_UPDATING => 15616,
	ERROR_DEPLOYMENT_BLOCKED_BY_POLICY => 15617,
	ERROR_PACKAGES_IN_USE => 15618,
	ERROR_RECOVERY_FILE_CORRUPT => 15619,
	ERROR_INVALID_STAGED_SIGNATURE => 15620,
	ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED => 15621,
	ERROR_INSTALL_PACKAGE_DOWNGRADE => 15622,
	ERROR_SYSTEM_NEEDS_REMEDIATION => 15623,
	ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN => 15624,
	ERROR_RESILIENCY_FILE_CORRUPT => 15625,
	ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING => 15626,
	ERROR_STATE_LOAD_STORE_FAILED => 15800,
	ERROR_STATE_GET_VERSION_FAILED => 15801,
	ERROR_STATE_SET_VERSION_FAILED => 15802,
	ERROR_STATE_STRUCTURED_RESET_FAILED => 15803,
	ERROR_STATE_OPEN_CONTAINER_FAILED => 15804,
	ERROR_STATE_CREATE_CONTAINER_FAILED => 15805,
	ERROR_STATE_DELETE_CONTAINER_FAILED => 15806,
	ERROR_STATE_READ_SETTING_FAILED => 15807,
	ERROR_STATE_WRITE_SETTING_FAILED => 15808,
	ERROR_STATE_DELETE_SETTING_FAILED => 15809,
	ERROR_STATE_QUERY_SETTING_FAILED => 15810,
	ERROR_STATE_READ_COMPOSITE_SETTING_FAILED => 15811,
	ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED => 15812,
	ERROR_STATE_ENUMERATE_CONTAINER_FAILED => 15813,
	ERROR_STATE_ENUMERATE_SETTINGS_FAILED => 15814,
	ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED => 15815,
	ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED => 15816,
	ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED => 15817,
	ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED => 15818,
	ERROR_API_UNAVAILABLE => 15841,
	EVENT_CONSOLE_CARET => 16385,
	EVENT_CONSOLE_UPDATE_REGION => 16386,
	EVENT_CONSOLE_UPDATE_SIMPLE => 16387,
	EVENT_CONSOLE_UPDATE_SCROLL => 16388,
	EVENT_CONSOLE_LAYOUT => 16389,
	EVENT_CONSOLE_START_APPLICATION => 16390,
	EVENT_CONSOLE_END_APPLICATION => 16391,
	ELEMENT_STATUS_NOT_BUS => 32768,
	EVENT_OBJECT_CREATE => 32768,
	EVENT_OBJECT_DESTROY => 32769,
	EVENT_OBJECT_SHOW => 32770,
	EVENT_OBJECT_HIDE => 32771,
	EVENT_OBJECT_REORDER => 32772,
	EVENT_OBJECT_FOCUS => 32773,
	EVENT_OBJECT_SELECTION => 32774,
	EVENT_OBJECT_SELECTIONADD => 32775,
	EVENT_OBJECT_SELECTIONREMOVE => 32776,
	EVENT_OBJECT_SELECTIONWITHIN => 32777,
	EVENT_OBJECT_STATECHANGE => 32778,
	EVENT_OBJECT_LOCATIONCHANGE => 32779,
	EVENT_OBJECT_NAMECHANGE => 32780,
	EVENT_OBJECT_DESCRIPTIONCHANGE => 32781,
	EVENT_OBJECT_VALUECHANGE => 32782,
	EVENT_OBJECT_PARENTCHANGE => 32783,
	EVENT_OBJECT_HELPCHANGE => 32784,
	EVENT_OBJECT_DEFACTIONCHANGE => 32785,
	EVENT_OBJECT_ACCELERATORCHANGE => 32786,
	EC_USEFONTINFO => 65535,
	EMBDHLP_DELAYCREATE => 65536,
	ENLISTMENT_GENERIC_READ => 131073,
	ENLISTMENT_GENERIC_EXECUTE => 131100,
	ENLISTMENT_GENERIC_WRITE => 131102,
	ENUM_S_FIRST => 262576,
	ENUM_S_LAST => 262591,
	EVENT_S_FIRST => 262656,
	EVENT_S_SOME_SUBSCRIBERS_FAILED => 262656,
	EVENT_S_NOSUBSCRIBERS => 262658,
	EVENT_S_LAST => 262687,
	EXTENDED_STARTUPINFO_PRESENT => 524288,
	ENLISTMENT_ALL_ACCESS => 983071,
	ERROR_FLT_IO_COMPLETE => 2031617,
	EVENT_ALL_ACCESS => 2031619,
	ELEMENT_STATUS_INVERT => 4194304,
	EWX_HYBRID_SHUTDOWN => 4194304,
	ELEMENT_STATUS_SVALID => 8388608,
	EWX_BOOTOPTIONS => 16777216,
	ELEMENT_STATUS_PVOLTAG => 268435456,
	ELEMENT_STATUS_AVOLTAG => 536870912,
	ENDSESSION_CRITICAL => 1073741824,
	ERROR_SEVERITY_INFORMATIONAL => 1073741824,
	ENHMETA_SIGNATURE => 1179469088,
	EPS_SIGNATURE => 1179865157,
	EVENT_MAX => 2147483647,
	ENDSESSION_LOGOFF => 2147483648,
	ENHMETA_STOCK_OBJECT => 2147483648,
	ERROR_SEVERITY_WARNING => 2147483648,
	ES_CONTINUOUS => 2147483648,
	EXCEPTION_GUARD_PAGE => 2147483649,
	EXCEPTION_DATATYPE_MISALIGNMENT => 2147483650,
	EXCEPTION_BREAKPOINT => 2147483651,
	EXCEPTION_SINGLE_STEP => 2147483652,
	E_PENDING => 2147483658,
	E_NOTIMPL => 2147500033,
	E_NOINTERFACE => 2147500034,
	E_POINTER => 2147500035,
	E_ABORT => 2147500036,
	E_FAIL => 2147500037,
	E_UNEXPECTED => 2147549183,
	E_DRAW => 2147746112,
	ENUM_E_FIRST => 2147746224,
	ENUM_E_LAST => 2147746239,
	EVENT_E_FIRST => 2147746304,
	EVENT_E_ALL_SUBSCRIBERS_FAILED => 2147746305,
	EVENT_E_QUERYSYNTAX => 2147746307,
	EVENT_E_QUERYFIELD => 2147746308,
	EVENT_E_INTERNALEXCEPTION => 2147746309,
	EVENT_E_INTERNALERROR => 2147746310,
	EVENT_E_INVALID_PER_USER_SID => 2147746311,
	EVENT_E_USER_EXCEPTION => 2147746312,
	EVENT_E_TOO_MANY_METHODS => 2147746313,
	EVENT_E_MISSING_EVENTCLASS => 2147746314,
	EVENT_E_NOT_ALL_REMOVED => 2147746315,
	EVENT_E_COMPLUS_NOT_INSTALLED => 2147746316,
	EVENT_E_CANT_MODIFY_OR_DELETE_UNCONFIGURED_OBJECT => 2147746317,
	EVENT_E_CANT_MODIFY_OR_DELETE_CONFIGURED_OBJECT => 2147746318,
	EVENT_E_INVALID_EVENT_CLASS_PARTITION => 2147746319,
	EVENT_E_PER_USER_SID_NOT_LOGGED_ON => 2147746320,
	EVENT_E_LAST => 2147746335,
	E_ACCESSDENIED => 2147942405,
	E_HANDLE => 2147942406,
	E_OUTOFMEMORY => 2147942414,
	E_INVALIDARG => 2147942487,
	ERROR_FLT_NO_HANDLER_DEFINED => 2149515265,
	ERROR_FLT_CONTEXT_ALREADY_DEFINED => 2149515266,
	ERROR_FLT_INVALID_ASYNCHRONOUS_REQUEST => 2149515267,
	ERROR_FLT_DISALLOW_FAST_IO => 2149515268,
	ERROR_FLT_INVALID_NAME_REQUEST => 2149515269,
	ERROR_FLT_NOT_SAFE_TO_POST_OPERATION => 2149515270,
	ERROR_FLT_NOT_INITIALIZED => 2149515271,
	ERROR_FLT_FILTER_NOT_READY => 2149515272,
	ERROR_FLT_POST_OPERATION_CLEANUP => 2149515273,
	ERROR_FLT_INTERNAL_ERROR => 2149515274,
	ERROR_FLT_DELETING_OBJECT => 2149515275,
	ERROR_FLT_MUST_BE_NONPAGED_POOL => 2149515276,
	ERROR_FLT_DUPLICATE_ENTRY => 2149515277,
	ERROR_FLT_CBDQ_DISABLED => 2149515278,
	ERROR_FLT_DO_NOT_ATTACH => 2149515279,
	ERROR_FLT_DO_NOT_DETACH => 2149515280,
	ERROR_FLT_INSTANCE_ALTITUDE_COLLISION => 2149515281,
	ERROR_FLT_INSTANCE_NAME_COLLISION => 2149515282,
	ERROR_FLT_FILTER_NOT_FOUND => 2149515283,
	ERROR_FLT_VOLUME_NOT_FOUND => 2149515284,
	ERROR_FLT_INSTANCE_NOT_FOUND => 2149515285,
	ERROR_FLT_CONTEXT_ALLOCATION_NOT_FOUND => 2149515286,
	ERROR_FLT_INVALID_CONTEXT_REGISTRATION => 2149515287,
	ERROR_FLT_NAME_CACHE_MISS => 2149515288,
	ERROR_FLT_NO_DEVICE_OBJECT => 2149515289,
	ERROR_FLT_VOLUME_ALREADY_MOUNTED => 2149515290,
	ERROR_FLT_ALREADY_ENLISTED => 2149515291,
	ERROR_FLT_CONTEXT_ALREADY_LINKED => 2149515292,
	ERROR_FLT_NO_WAITER_FOR_REPLY => 2149515296,
	ERROR_FLT_REGISTRATION_BUSY => 2149515299,
	ERROR_SEVERITY_ERROR => 3221225472,
	EXCEPTION_ACCESS_VIOLATION => 3221225477,
	EXCEPTION_IN_PAGE_ERROR => 3221225478,
	EXCEPTION_INVALID_HANDLE => 3221225480,
	EXCEPTION_ILLEGAL_INSTRUCTION => 3221225501,
	EXCEPTION_NONCONTINUABLE_EXCEPTION => 3221225509,
	EXCEPTION_INVALID_DISPOSITION => 3221225510,
	EXCEPTION_ARRAY_BOUNDS_EXCEEDED => 3221225612,
	EXCEPTION_FLT_DENORMAL_OPERAND => 3221225613,
	EXCEPTION_FLT_DIVIDE_BY_ZERO => 3221225614,
	EXCEPTION_FLT_INEXACT_RESULT => 3221225615,
	EXCEPTION_FLT_INVALID_OPERATION => 3221225616,
	EXCEPTION_FLT_OVERFLOW => 3221225617,
	EXCEPTION_FLT_STACK_CHECK => 3221225618,
	EXCEPTION_FLT_UNDERFLOW => 3221225619,
	EXCEPTION_INT_DIVIDE_BY_ZERO => 3221225620,
	EXCEPTION_INT_OVERFLOW => 3221225621,
	EXCEPTION_PRIV_INSTRUCTION => 3221225622,
	EXCEPTION_STACK_OVERFLOW => 3221225725,
	ERROR_AUDITING_DISABLED => 3221815297,
	ERROR_ALL_SIDS_FILTERED => 3221815298,
	ENUM_ALL_CALENDARS => 4294967295,
	ERROR_UNHANDLED_ERROR => 4294967295,
    );
    # Generate proxy constant subroutines for all the values.
    # Well, almost all the values. Unfortunately we can't assume that at this
    # point that our symbol table is empty, as code such as if the parser has
    # seen code such as C<exists &Errno::EINVAL>, it will have created the
    # typeglob.
    # Doing this before defining @EXPORT_OK etc means that even if a platform is
    # crazy enough to define EXPORT_OK as an error constant, everything will
    # still work, because the parser will upgrade the PCS to a real typeglob.
    # We rely on the subroutine definitions below to update the internal caches.
    # Don't use %each, as we don't want a copy of the value.
    foreach my $name (keys %err) {
        if ($Errno::{$name}) {
            # We expect this to be reached fairly rarely, so take an approach
            # which uses the least compile time effort in the common case:
            eval "sub $name() { $err{$name} }; 1" or die $@;
        } else {
            $Errno::{$name} = \$err{$name};
        }
    }
}

our @EXPORT_OK = keys %err;

our %EXPORT_TAGS = (
    POSIX => [qw(
	E2BIG EACCES EADDRINUSE EADDRNOTAVAIL EAFNOSUPPORT EAGAIN EALREADY
	EBADF EBUSY ECHILD ECONNABORTED ECONNREFUSED ECONNRESET EDEADLK
	EDESTADDRREQ EDOM EDQUOT EEXIST EFAULT EFBIG EHOSTUNREACH EINPROGRESS
	EINTR EINVAL EIO EISCONN EISDIR ELOOP EMFILE EMLINK EMSGSIZE
	ENAMETOOLONG ENETDOWN ENETRESET ENETUNREACH ENFILE ENOBUFS ENODEV
	ENOENT ENOEXEC ENOLCK ENOMEM ENOPROTOOPT ENOSPC ENOSYS ENOTCONN
	ENOTDIR ENOTEMPTY ENOTSOCK ENOTTY ENXIO EOPNOTSUPP EPERM EPFNOSUPPORT
	EPIPE EPROCLIM EPROTONOSUPPORT EPROTOTYPE ERANGE EREMOTE EROFS
	ESHUTDOWN ESOCKTNOSUPPORT ESPIPE ESRCH ESTALE ETIMEDOUT ETOOMANYREFS
	ETXTBSY EUSERS EWOULDBLOCK EXDEV
    )],
    WINSOCK => [qw(
	WSAEWOULDBLOCK WSAEREMOTE WSAESTALE WSAEBADF WSAENOTEMPTY WSAEINVAL
	WSAECONNABORTED WSAEPFNOSUPPORT WSAEOPNOTSUPP WSAELOOP
	WSAEINVALIDPROVIDER WSAENAMETOOLONG WSAEREFUSED WSAEACCES WSAESHUTDOWN
	WSAENOTCONN WSAETIMEDOUT WSAEPROCLIM WSAEFAULT WSAENOBUFS
	WSAEADDRNOTAVAIL WSAENOMORE WSAEPROTOTYPE WSAEDISCON
	WSAEPROVIDERFAILEDINIT WSAEINPROGRESS WSAEDQUOT WSAENETRESET
	WSAEDESTADDRREQ WSAECANCELLED WSAEISCONN WSAECONNRESET
	WSAEPROTONOSUPPORT WSAEHOSTUNREACH WSAEUSERS WSAETOOMANYREFS WSAEINTR
	WSAESOCKTNOSUPPORT WSAEMSGSIZE WSAEHOSTDOWN WSAEALREADY WSAEADDRINUSE
	WSAENETUNREACH WSAEAFNOSUPPORT WSAEMFILE WSAENETDOWN WSAECONNREFUSED
	WSAENOTSOCK WSAEINVALIDPROCTABLE WSAENOPROTOOPT
    )],
);

sub TIEHASH { bless \%err }

sub FETCH {
    my (undef, $errname) = @_;
    return "" unless exists $err{$errname};
    my $errno = $err{$errname};
    return $errno == $! ? $errno : 0;
}

sub STORE {
    require Carp;
    Carp::confess("ERRNO hash is read only!");
}

# This is the true return value
*CLEAR = *DELETE = \*STORE; # Typeglob aliasing uses less space

sub NEXTKEY {
    each %err;
}

sub FIRSTKEY {
    my $s = scalar keys %err;	# initialize iterator
    each %err;
}

sub EXISTS {
    my (undef, $errname) = @_;
    exists $err{$errname};
}

sub _tie_it {
    tie %{$_[0]}, __PACKAGE__;
}

__END__

=head1 NAME

Errno - System errno constants

=head1 SYNOPSIS

    use Errno qw(EINTR EIO :POSIX);

=head1 DESCRIPTION

C<Errno> defines and conditionally exports all the error constants
defined in your system F<errno.h> include file. It has a single export
tag, C<:POSIX>, which will export all POSIX defined error numbers.

On Windows, C<Errno> also defines and conditionally exports all the
Winsock error constants defined in your system F<WinError.h> include
file. These are included in a second export tag, C<:WINSOCK>.

C<Errno> also makes C<%!> magic such that each element of C<%!> has a
non-zero value only if C<$!> is set to that value. For example:

    my $fh;
    unless (open($fh, "<", "/fangorn/spouse")) {
        if ($!{ENOENT}) {
            warn "Get a wife!\n";
        } else {
            warn "This path is barred: $!";
        } 
    } 

If a specified constant C<EFOO> does not exist on the system, C<$!{EFOO}>
returns C<"">.  You may use C<exists $!{EFOO}> to check whether the
constant is available on the system.

Perl automatically loads C<Errno> the first time you use C<%!>, so you don't
need an explicit C<use>.

=head1 CAVEATS

Importing a particular constant may not be very portable, because the
import will fail on platforms that do not have that constant.  A more
portable way to set C<$!> to a valid value is to use:

    if (exists &Errno::EFOO) {
        $! = &Errno::EFOO;
    }

=head1 AUTHOR

Graham Barr <<EMAIL>>

=head1 COPYRIGHT

Copyright (c) 1997-8 Graham Barr. All rights reserved.
This program is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=cut

# ex: set ro:
