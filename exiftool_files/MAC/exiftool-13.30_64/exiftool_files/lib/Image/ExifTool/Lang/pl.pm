#------------------------------------------------------------------------------
# File:         pl.pm
#
# Description:  ExifTool Polish language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::pl;

use strict;
use vars qw($VERSION);

$VERSION = '1.12';

%Image::ExifTool::Lang::pl::Translate = (
   'A100DataOffset' => 'Przesunięcie danych A100',
   'AEAperture' => 'Priorytet AE',
   'AEExposureTime' => 'Czas ekspozycji AE',
   'AEInfo' => 'Informacja o automatycznej ekspozycji',
   'AELock' => {
      Description => 'Blokada AE',
      PrintConv => {
        'Off' => 'Wyłączona',
        'On' => 'Włączona',
      },
    },
   'AEMeteringMode' => 'Tryb pomiaru AE',
   'AEMeteringSegments' => 'Segmenty pomiaru AE',
   'AEProgramMode' => 'Tryb programu AE',
   'AFAdjustment' => 'Korekta AF',
   'AFInfo' => 'Informacje autofocusa',
   'AFIntegrationTime' => 'Czas integracji AF',
   'AFPoint' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'AFPointMode' => 'Tryb Autofokusa',
   'AFPointSelected' => {
      Description => 'Wybrany punkt AF',
      PrintConv => {
        'Auto' => 'Automatyczny',
        'Automatic Tracking AF' => 'Śledzący AF',
        'Bottom' => 'Dolny',
        'Center' => 'Centralny',
        'Face Detect AF' => 'Wykrywanie twarzy',
        'Fixed Center' => 'Centralny',
        'Left' => 'Lewy',
        'Lower-left' => 'Dolny-lewy',
        'Lower-right' => 'Dolny-prawy',
        'Mid-left' => 'Środek-lewy',
        'Mid-right' => 'Środek-prawy',
        'Right' => 'Prawy',
        'Top' => 'Górny',
        'Upper-left' => 'Górny-lewy',
        'Upper-right' => 'Górny-prawy',
      },
    },
   'AFPointSelected2' => 'Wybrany punkt autofokusa 2',
   'AFPointsInFocus' => {
      Description => 'Punkty AF w ostrości',
      PrintConv => {
        'Bottom-center' => 'Dolny-centralny',
        'Bottom-left' => 'Dolny-lewy',
        'Bottom-right' => 'Dolny-prawy',
        'Center' => 'Centralny',
        'Fixed Center or Multiple' => 'Centralny lub wiele',
        'Left' => 'Lewy',
        'None' => 'Brak',
        'Right' => 'Prawy',
        'Top-center' => 'Górny-centralny',
        'Top-left' => 'Górny-lewy',
        'Top-right' => 'Górny-prawy',
      },
    },
   'AFPointsSelected' => 'Wybrane punkty AF',
   'AFPredictor' => 'Przewidujący AF',
   'Aperture' => 'Przysłona',
   'ApertureRingUse' => 'Użycie pierścienia przysłony',
   'ApertureValue' => 'Przysłona',
   'Artist' => 'Artysta',
   'Author' => 'Autor',
   'AuthorsPosition' => 'Pozycja autora',
   'AutoAperture' => 'Automatyczna przysłona',
   'AutoBracketing' => 'Bracketing automatyczny',
   'AutoRotate' => {
      PrintConv => {
        'None' => 'Brak',
        'Rotate 180' => '180° (dół/prawo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'AvApertureSetting' => 'Ustawienia priorytetu przysłony Av',
   'BadFaxLines' => 'Uszkodzone wiersze transmisji Fax',
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'BaseExposureCompensation' => 'Podstawowa kompensacja ekspozycji',
   'BitsPerSample' => 'Liczba bitów na składnik',
   'BlackPoint' => 'Punkt czerni',
   'BlueBalance' => 'Balans niebieskiego',
   'BlurWarning' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'BracketShotNumber' => 'Ilość zdjęć w bracketingu',
   'Brightness' => 'Jasność',
   'By-line' => 'Autor',
   'CFAPattern' => 'Wzorzec CFA',
   'CMMFlags' => 'Flagi CMM',
   'CPUFirmwareVersion' => 'Wersja firmware CPU',
   'CPUType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => 'Zachmurzone niebo',
        'Cool White Fluorescent' => 'Zimna biała jarzeniówka (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Jarzeniówka z naturalnym białym światłem (N 4600 - 5500K)',
        'Daylight' => 'Światło dzienne',
        'Daylight Fluorescent' => 'Jarzeniówka dająca światło dzienne (D 5700 - 7100K)',
        'Fine Weather' => 'Dobra pogoda',
        'Flash' => 'Lampa błyskowa',
        'Fluorescent' => 'Jarzeniowy',
        'ISO Studio Tungsten' => 'ISO dla studyjnych lamp żarowych',
        'Other' => 'Inne źródło światła',
        'Shade' => 'Cień',
        'Standard Light A' => 'Standardowe światło A',
        'Standard Light B' => 'Standardowe światło B',
        'Standard Light C' => 'Standardowe światło C',
        'Tungsten (Incandescent)' => 'Światło żarowe',
        'Unknown' => 'Nieznane',
        'White Fluorescent' => 'Biała jarzeniówka (WW3250 - 3800K)',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => 'Zachmurzone niebo',
        'Cool White Fluorescent' => 'Zimna biała jarzeniówka (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Jarzeniówka z naturalnym białym światłem (N 4600 - 5500K)',
        'Daylight' => 'Światło dzienne',
        'Daylight Fluorescent' => 'Jarzeniówka dająca światło dzienne (D 5700 - 7100K)',
        'Fine Weather' => 'Dobra pogoda',
        'Flash' => 'Lampa błyskowa',
        'Fluorescent' => 'Jarzeniowy',
        'ISO Studio Tungsten' => 'ISO dla studyjnych lamp żarowych',
        'Other' => 'Inne źródło światła',
        'Shade' => 'Cień',
        'Standard Light A' => 'Standardowe światło A',
        'Standard Light B' => 'Standardowe światło B',
        'Standard Light C' => 'Standardowe światło C',
        'Tungsten (Incandescent)' => 'Światło żarowe',
        'Unknown' => 'Nieznane',
        'White Fluorescent' => 'Biała jarzeniówka (WW3250 - 3800K)',
      },
    },
   'CameraOrientation' => {
      Description => 'Orientacja obrazu',
      PrintConv => {
        'Horizontal (normal)' => '0° (góra/lewo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'CameraSettings' => 'Ustawienia aparatu',
   'CameraTemperature' => 'Temperatura aparatu',
   'Caption-Abstract' => 'Podpis',
   'CaptionWriter' => 'Autor podpisu',
   'Categories' => 'Kategorie',
   'Category' => 'Kategorie',
   'CellLength' => 'Długość komórki',
   'CellWidth' => 'Szerokość komórki',
   'City' => 'Miasto',
   'CleanFaxData' => {
      Description => 'Poprawne wiersze transmisji Fax',
      PrintConv => {
        'Clean' => 'Dobry',
        'Regenerated' => 'Ponowiony',
        'Unclean' => 'Niedobry',
      },
    },
   'ClipPath' => 'Ścieżka obcięcia',
   'CodingMethods' => {
      Description => 'Metody kompresji',
      PrintConv => {
        'Baseline JPEG' => 'JPEG podstawowa',
        'JBIG color' => 'JBIG kolor',
        'Modified Huffman' => 'Huffmana zmodyfikowana',
        'Modified MR' => 'Zmodyfikowany MR',
        'Modified Read' => 'Zmodyfikowany odczyt',
        'Unspecified compression' => 'Nie podane',
      },
    },
   'ColorFilter' => 'Filtr kolorowy',
   'ColorInfo' => 'Informacje o kolorze',
   'ColorMap' => 'Mapa kolorów',
   'ColorMatrix1' => 'Macierz kolorów 1',
   'ColorMatrix2' => 'Macierz kolorów 2',
   'ColorSpace' => {
      Description => 'Informacja o przestrzeni barwowej',
      PrintConv => {
        'Uncalibrated' => 'Nie skalibrowany',
      },
    },
   'ColorSpaceData' => 'Dane przestrzeni barw',
   'ColorTemperature' => 'Temperatura barwowa',
   'Comment' => 'Komentarz',
   'ComponentsConfiguration' => 'Znaczenie każdego komponentu',
   'CompressedBitsPerPixel' => 'Tryb kompresji obrazu',
   'Compression' => {
      Description => 'Algorytm kompresji',
      PrintConv => {
        'Epson ERF Compressed' => 'Skompresowany Epson ERF',
        'JBIG B&W' => 'JBIG Czarno Biały',
        'JBIG Color' => 'JBIG Kolorowy',
        'JPEG' => 'Kompresja JPEG',
        'JPEG (old-style)' => 'JPEG (w starym stylu)',
        'Kodak DCR Compressed' => 'Skompresowany Kodak DCR',
        'Kodak KDC Compressed' => 'Skompresowany Kodak KDC',
        'Next' => 'Kodowanie 2-bitowe NeXT',
        'Nikon NEF Compressed' => 'Skompresowany Nikon NEF',
        'None' => 'Brak',
        'Packed RAW' => 'Spakowany RAW',
        'Pentax PEF Compressed' => 'Skompresowany Pentax PEF',
        'SGILog' => 'Kodowanie 32-bitowe SGI Log Luminance',
        'SGILog24' => 'Kodowanie 24-bitowe SGI Log Luminance',
        'Samsung SRW Compressed' => 'Skompresowany Samsung SRW',
        'Sony ARW Compressed' => 'Skompresowany Sony ARW',
        'Thunderscan' => 'Kodowanie 4-bitowe ThunderScan',
        'Uncompressed' => 'Bez kompresji',
      },
    },
   'CompressionType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'ConsecutiveBadFaxLines' => 'Sekwencja uszkodzonych wierszy transmisji Fax',
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Ostre',
        'Low' => 'Miękkie',
        'Normal' => 'Standard',
      },
    },
   'Copyright' => 'Posiadacz praw autorskich',
   'CopyrightNotice' => 'Informacja o prawach autorskich',
   'Country' => 'Kraj',
   'Country-PrimaryLocationName' => 'Kraj',
   'CreateDate' => 'Data utworzenia',
   'CreationDate' => 'Data utworzenia',
   'Credit' => 'Podziękowania',
   'CropUnit' => {
      PrintConv => {
        'inches' => 'Cal',
      },
    },
   'CropUnits' => {
      PrintConv => {
        'inches' => 'Cal',
      },
    },
   'CustomRendered' => {
      Description => 'Przetwarzanie zdjęć według ustawień własnych',
      PrintConv => {
        'Custom' => 'Proces zdefiniowany przez użytkownika',
        'Normal' => 'Normalny proces',
      },
    },
   'DNGBackwardVersion' => 'Poprzednia wersja DNG',
   'DNGVersion' => 'Wersja DNG',
   'DSPFirmwareVersion' => 'Wersja firmware DSP',
   'DataDump' => 'Zrzut danych?',
   'DataImprint' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'DataType' => 'Typ daty',
   'Date' => 'Data',
   'DateCreated' => 'Data utworzenia',
   'DateSent' => 'Wysłano datę',
   'DateTimeOriginal' => 'Pierwotna data i godzina',
   'Decode' => 'Zdekodowany',
   'DefaultImageColor' => 'Domyślny kolor w obrazie',
   'Description' => 'Opis',
   'DestinationCity' => 'Miasto przeznaczenia',
   'DestinationCityCode' => 'Kod miasta przeznaczenia',
   'DestinationDST' => {
      Description => 'Czas letni miasta przeznaczenia',
      PrintConv => {
        'No' => 'Nie',
        'Yes' => 'Tak',
      },
    },
   'DeviceAttributes' => 'Atrybuty urządzenia',
   'DeviceManufacturer' => 'Producent urządzenia',
   'DeviceModel' => 'Model urządzenia',
   'DeviceSettingDescription' => 'Opis ustawień urządzenia',
   'DigitalZoom' => 'Cyfrowy zoom',
   'DigitalZoomRatio' => 'Współczynnik cyfrowego zoomu',
   'Directory' => 'Lokalizacja pliku',
   'DisplayUnits' => {
      PrintConv => {
        'inches' => 'Cal',
      },
    },
   'DisplayedUnitsX' => {
      PrintConv => {
        'inches' => 'Cal',
      },
    },
   'DisplayedUnitsY' => {
      PrintConv => {
        'inches' => 'Cal',
      },
    },
   'DocumentName' => 'Nazwa dokumentu',
   'DriveMode' => 'Tryb przesuwu',
   'DriveMode2' => 'Tryb zdjęć 2',
   'Duration' => 'Czas',
   'DynamicRangeExpansion' => {
      Description => 'Rozszerzenie zakresu dynamiki',
      PrintConv => {
        'Off' => 'Wyłączone',
        'On' => 'Włączone',
      },
    },
   'E-DialInProgram' => 'Program E-Dial',
   'EVStepInfo' => 'Informacja o krokach EV',
   'EVSteps' => 'Krok EV',
   'EffectiveLV' => 'Efektywne LV',
   'Emphasis' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'EnvelopePriority' => {
      PrintConv => {
        '0 (reserved)' => '0 (zarezerwowane na przyszłość)',
        '1 (most urgent)' => '1 (najpilniejszy)',
        '5 (normal urgency)' => '5 (zwykły)',
        '8 (least urgent)' => '8 (niezbyt pilny)',
        '9 (user-defined priority)' => '9 (priorytet określony przez użytkownika)',
      },
    },
   'ExifImageHeight' => 'Wysokość obrazu',
   'ExifImageWidth' => 'Szerokość obrazu',
   'ExifOffset' => 'Wskaźnik Exif IFD',
   'ExifVersion' => 'Wersja Exif',
   'ExposureBracketStepSize' => 'Krok bracketingu ekspozycji',
   'ExposureCompensation' => 'Różnica ekspozycji',
   'ExposureIndex' => 'Wskaźnik ekspozycji',
   'ExposureMode' => {
      Description => 'Tryb ekspozycji',
      PrintConv => {
        'Auto' => 'Automatyczna ekspozycja',
        'Auto bracket' => 'Funkcja Autobracketing',
        'Manual' => 'Manualna ekspozycja',
      },
    },
   'ExposureProgram' => {
      Description => 'Program ekspozycji',
      PrintConv => {
        'Action (High speed)' => 'Program akcji',
        'Aperture-priority AE' => 'Priorytet przysłony',
        'Creative (Slow speed)' => 'Program kreatywny',
        'Landscape' => 'Krajobraz',
        'Manual' => 'Manualna ekspozycja',
        'Portrait' => 'Portret',
        'Program AE' => 'Normalny program',
        'Shutter speed priority AE' => 'Priorytet migawki',
      },
    },
   'ExposureTime' => 'Czas ekspozycji',
   'ExternalFlashExposureComp' => 'Kompensacja ekspozycji zewnetrznej lampy',
   'ExternalFlashGuideNumber' => 'Liczba przewodnia lampy zewnętrznej',
   'ExternalFlashMode' => 'Tryb lampy zewnętrznej',
   'FNumber' => 'Przysłona',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => '0° (góra/lewo)',
        'Rotate 180' => '180° (dół/prawo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'FaxProfile' => {
      Description => 'Profil faxu (rodzaj zawartości)',
      PrintConv => {
        'Extended B&W lossless, F' => 'Rozszerzony cz.b. bezstratny, F',
        'Lossless JBIG B&W, J' => 'Bezstratny JBIG cz.b., J',
        'Lossless color and grayscale, L' => 'Bezstratne kolor i skala szarości, L',
        'Lossy color and grayscale, C' => 'Stratne kolor i skala szarości, C',
        'Minimal B&W lossless, S' => 'Minimalny cz.b. bezstratny, S',
        'Mixed raster content, M' => 'Raster — zawartość mieszana, M',
        'Multi Profiles' => 'Wiele profili',
        'Unknown' => 'Nieznany',
      },
    },
   'FileFormat' => 'Format',
   'FileModifyDate' => 'Data aktualizacji',
   'FileName' => 'Nazwa pliku',
   'FileSize' => 'Wielkość pliku',
   'FileSource' => {
      Description => 'Źródło pliku',
      PrintConv => {
        'Digital Camera' => 'DSC',
        'Film Scanner' => 'Skaner do materiałów transparentnych',
        'Reflection Print Scanner' => 'Skaner do zdjęć',
      },
    },
   'FileType' => 'Typ pliku',
   'Filename' => 'Nazwa pliku',
   'FillOrder' => {
      Description => 'Kolejność wypełniania',
      PrintConv => {
        'Normal' => 'Normalna',
        'Reversed' => 'Zajęte',
      },
    },
   'FilterEffectMonochrome' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Flash' => {
      Description => 'Lampa',
      PrintConv => {
        'Auto, Fired' => 'Włączony (Automatyczny błysk lampy)',
        'Auto, Fired, Red-eye reduction' => '"Włączony (Błysk automatyczny, Redukcja efektu czerwonych oczu)"',
        'Auto, Fired, Red-eye reduction, Return detected' => '"Włączony (Błysk automatyczny, Redukcja efektu czerwonych oczu, rejestracja światła odbitego)"',
        'Auto, Fired, Return detected' => '"Włączony (Automatyczny błysk lampy, rejestracja światła odbitego)"',
        'Did not fire' => 'Nie nastąpił błysk lampy',
        'Fired' => 'Nastąpił błysk lampy',
        'Fired, Red-eye reduction' => 'Włączony (Redukcja efektu czerwonych oczu)',
        'Fired, Red-eye reduction, Return detected' => '"Włączony (Redukcja efektu czerwonych oczu, rejestracja światła odbitego)"',
        'Fired, Return detected' => 'Włączony (Rejestracja światła odbitego)',
        'No Flash' => 'Brak funkcji lampy błyskowej',
        'On, Fired' => 'Włączony (Błysk dopełniający)',
        'On, Red-eye reduction' => '"Włączony (Błysk dopełniający, Redukcja efektu czerwonych oczu)"',
        'On, Red-eye reduction, Return detected' => '"Włączony (Błysk dopełniający, Redukcja efektu czerwonych oczu, rejestracja światła odbitego)"',
        'On, Return detected' => '"Włączony (Błysk dopełniający, rejestracja światła odbitego)"',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'FlashEnergy' => 'Siła lampy błyskowej',
   'FlashExposureComp' => 'Kompensacja lampy',
   'FlashMeteringSegments' => 'Segmenty pomiaru błysku',
   'FlashMode' => 'Tryb lampy',
   'FlashModel' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'FlashOptions' => 'Opcje lampy',
   'FlashStatus' => 'Stan lampy',
   'FlashType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'FlashpixVersion' => 'Obsługiwana wersja Flashpix',
   'FocalLength' => 'Ogniskowa',
   'FocalLength35efl' => 'Długość ogniskowej (w wartościach dla formatu małoobrazkowego)',
   'FocalLengthIn35mmFormat' => 'Długość ogniskowej dla aparatów małoobrazkowych',
   'FocalPlaneResolutionUnit' => {
      Description => 'Jednostka rozdzielczości w płaszczyźnie ogniskowej',
      PrintConv => {
        'None' => 'Brak',
        'inches' => 'Cal',
        'um' => 'µm (mikrometr)',
      },
    },
   'FocalPlaneXResolution' => 'Rozdzielczość w płaszczyźnie ogniskowej - oś x',
   'FocalPlaneYResolution' => 'Rozdzielczość w płaszczyźnie ogniskowej - oś y',
   'FocusMode' => 'Tryb ostrzenia',
   'FocusMode2' => 'Tryb Autofokusa 2',
   'FrameNumber' => 'Numer zdjęcia',
   'FrameRate' => 'Częstotliwość zmiany kadrów',
   'FrameSize' => 'Wielkość kadru',
   'FreeByteCounts' => 'Liczba wolnych bajtów',
   'FreeOffsets' => 'Wolne offsety',
   'GDALMetadata' => 'Metadane GDAL',
   'GDALNoData' => 'Brak danych GDAL',
   'GPSAltitude' => 'Wysokość',
   'GPSAltitudeRef' => {
      Description => 'Wysokość odniesienia',
      PrintConv => {
        'Above Sea Level' => 'Poziom morza',
        'Below Sea Level' => 'Względem poziomu morza (wartość ujemna)',
      },
    },
   'GPSAreaInformation' => 'Nazwa obszaru GPS',
   'GPSDOP' => 'Precyzja pomiaru',
   'GPSDateStamp' => 'Dane GPS',
   'GPSDestBearing' => 'Azymut punktu docelowego',
   'GPSDestBearingRef' => 'Wartość odniesienia dla azymutu punktu docelowego.',
   'GPSDestDistance' => 'Odległość do punktu docelowego.',
   'GPSDestDistanceRef' => 'Wartość odniesienia dla odległości do punktu docelowego.',
   'GPSDestLatitude' => 'Szerokość geograficzna punktu docelowego',
   'GPSDestLatitudeRef' => 'Wartość odniesienia dla szerokości geograficznej punktu docelowego.',
   'GPSDestLongitude' => 'Długość geograficzna punktu docelowego',
   'GPSDestLongitudeRef' => 'Wartość odniesienia dla długości geograficznej punktu docelowego.',
   'GPSDifferential' => {
      Description => 'Różnicowa korekcja GPS',
      PrintConv => {
        'Differential Corrected' => 'Zastosowanie korekcji różnicowej',
        'No Correction' => 'Pomiar bez korekcji różnicowej',
      },
    },
   'GPSHPositioningError' => 'Błąd poziomy wyznaczenia pozycji',
   'GPSImgDirection' => 'Kierunek obrazu',
   'GPSImgDirectionRef' => 'Wartość odniesienia dla kierunku obrazu',
   'GPSInfo' => 'Wskaźnik GPS Info IFD',
   'GPSLatitude' => 'Szerokość geograficzna',
   'GPSLatitudeRef' => {
      Description => 'Szerokość geograficzna północna lub południowa',
      PrintConv => {
        'North' => 'Szerokości północnej',
        'South' => 'Szerokości południowej',
      },
    },
   'GPSLongitude' => 'Długość geograficzna',
   'GPSLongitudeRef' => {
      Description => 'Długość geograficzna wschodnia lub zachodnia',
      PrintConv => {
        'East' => 'Długości wschodniej',
        'West' => 'Długości zachodniej',
      },
    },
   'GPSMapDatum' => 'Wykorzystane dane z badań geodezyjnych',
   'GPSMeasureMode' => {
      Description => 'Tryb pomiaru GPS',
      PrintConv => {
        '3-Dimensional Measurement' => 'Pomiar 3-wymiarowy',
      },
    },
   'GPSProcessingMethod' => 'Nazwa metody przetwarzania GPS',
   'GPSSatellites' => 'Satelity GPS używane do pomiaru',
   'GPSSpeed' => 'Prędkość odbiornika GPS',
   'GPSSpeedRef' => {
      Description => 'Jednostka prędkości',
      PrintConv => {
        'km/h' => 'Kilometrów na godzinę',
        'knots' => 'Węzły',
        'mph' => 'Mil na godzinę',
      },
    },
   'GPSStatus' => {
      Description => 'Stan odbiornika GPS',
      PrintConv => {
        'Measurement Active' => 'Trwa pomiar',
        'Measurement Void' => 'Wzajemna niesprzeczność pomiarów',
      },
    },
   'GPSTimeStamp' => 'Czas GPS (zegar atomowy)',
   'GPSTrack' => 'Kierunek przesunięcia',
   'GPSTrackRef' => {
      Description => 'Wartość odniesienia dla kierunku ruchu',
      PrintConv => {
        'Magnetic North' => 'Kierunek strzałki magnetycznej',
        'True North' => 'Rzeczywisty kierunek',
      },
    },
   'GPSVersionID' => 'Wersja znacznika GPS',
   'GainControl' => {
      Description => 'Sterowanie krokiem',
      PrintConv => {
        'High gain down' => 'Z dużym krokiem do dołu',
        'High gain up' => 'Z dużym krokiem w górę',
        'Low gain down' => 'Z małym krokiem do dołu',
        'Low gain up' => 'Z małym krokiem w górę',
        'None' => 'Brak',
      },
    },
   'Genre' => 'Gatunek',
   'Gradation' => 'Gradacja',
   'GrayResponseCurve' => 'Krzywa odpowiedzi Szarości',
   'GrayResponseUnit' => 'Wielkość jednostki dla krzywej odpowiedzi szarości',
   'HalftoneHints' => 'Półtony',
   'Headline' => 'Nagłówek',
   'HighISONoiseReduction' => 'Redukcja szumu przy wysokim ISO',
   'HometownCity' => 'Miasto domowe',
   'HometownCityCode' => 'Kod miasta domowego',
   'HometownDST' => {
      Description => 'Czas letni miasta domowego',
      PrintConv => {
        'No' => 'Nie',
        'Yes' => 'Tak',
      },
    },
   'HostComputer' => 'Komputer użyty do wygenerowania obrazu',
   'Hue' => 'Barwa',
   'ICCProfile' => 'Profil ICC',
   'ISO' => 'Czułość ISO',
   'ISOFloor' => 'Minimalne ISO',
   'ISOSetting' => 'Ustawienia ISO',
   'ImageAreaOffset' => 'Przesunięcie obszaru obrazu',
   'ImageDescription' => 'Opis obrazu',
   'ImageEditCount' => 'Licznik przetworzonych zdjęć',
   'ImageHeight' => 'Wysokość obrazu',
   'ImageID' => 'Identyfikator obrazu',
   'ImageProcessing' => 'Przetwarzanie obrazu',
   'ImageSize' => 'Rozmiar zdjęcia',
   'ImageTone' => {
      Description => 'Odcień zdjęcia',
      PrintConv => {
        'Bright' => 'Jasny',
        'Landscape' => 'Krajobraz',
        'Monochrome' => 'Monochromatyczny',
        'Natural' => 'Naturalny',
        'Portrait' => 'Portret',
      },
    },
   'ImageType' => {
      Description => 'Rodzaj obrazu',
      PrintConv => {
        'Page' => 'Strona',
      },
    },
   'ImageUniqueID' => 'Unikalny kod ID zdjęcia',
   'ImageWidth' => 'Szerokość obrazu',
   'Index' => 'Indeks',
   'Indexed' => {
      Description => 'Indeksowane',
      PrintConv => {
        'Indexed' => 'Indeksowane',
        'Not indexed' => 'Nie indeksowane',
      },
    },
   'InkNames' => 'Nazwy tuszy',
   'InkSet' => {
      Description => 'Zestaw tuszy',
      PrintConv => {
        'Not CMYK' => 'Nie CMYK',
      },
    },
   'Instructions' => 'Instrukcje',
   'InternalFlashMode' => 'Tryb wewnętrznej lampy',
   'InternalFlashStrength' => 'Moc wewnętrznej lampy',
   'InteropIndex' => {
      Description => 'Identyfikacja wzajemnej zgodności',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: Plik pomocniczy DCF (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98: Plik zasadniczy DCF (sRGB)',
        'THM - DCF thumbnail file' => 'THM: Plik miniatury DCF',
      },
    },
   'InteropOffset' => 'Znacznik wzajemnej zgodności',
   'InteropVersion' => 'Wersja wzajemnej zgodności',
   'JPEGProc' => {
      PrintConv => {
        'Baseline' => 'JPEG Podstawowy',
        'Lossless' => 'Bezstratny',
      },
    },
   'JPEGQuality' => {
      Description => 'Jakość',
      PrintConv => {
        'Standard' => 'Standardowa jakość',
      },
    },
   'JPEGRestartInterval' => 'JPEG odstęp restartów',
   'Keywords' => 'Słowa kluczowe',
   'Lens' => 'Obiektyw',
   'LensID' => 'ID obiektywu',
   'LensInfo' => 'Informacja o obiektywie',
   'LensMake' => 'Producent obiektywu',
   'LensModel' => 'Model obiektywu',
   'LensSerialNumber' => 'Numer seryjny obiektywu',
   'LightReading' => 'Pomiar światła',
   'LightSource' => {
      Description => 'Źródło światła',
      PrintConv => {
        'Cloudy' => 'Zachmurzone niebo',
        'Cool White Fluorescent' => 'Zimna biała jarzeniówka (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Jarzeniówka z naturalnym białym światłem (N 4600 - 5500K)',
        'Daylight' => 'Światło dzienne',
        'Daylight Fluorescent' => 'Jarzeniówka dająca światło dzienne (D 5700 - 7100K)',
        'Fine Weather' => 'Dobra pogoda',
        'Flash' => 'Lampa błyskowa',
        'Fluorescent' => 'Jarzeniowy',
        'ISO Studio Tungsten' => 'ISO dla studyjnych lamp żarowych',
        'Other' => 'Inne źródło światła',
        'Shade' => 'Cień',
        'Standard Light A' => 'Standardowe światło A',
        'Standard Light B' => 'Standardowe światło B',
        'Standard Light C' => 'Standardowe światło C',
        'Tungsten (Incandescent)' => 'Światło żarowe',
        'Unknown' => 'Nieznane',
        'White Fluorescent' => 'Biała jarzeniówka (WW3250 - 3800K)',
      },
    },
   'Lightness' => 'Jasność',
   'LocalizedCameraModel' => 'Lokalizowany model aparatu',
   'Location' => 'Miejsce',
   'Luminance' => 'Luminancja',
   'Macro' => 'Makro',
   'Make' => 'Producent',
   'MakeAndModel' => 'Producent i model',
   'MakerNote' => 'Prywatne dane DNG',
   'MakerNotes' => 'Dane producenta',
   'MaxAperture' => 'Maks. przysłona obiektywu',
   'MaxSampleValue' => 'Maksymalny rozmiar próbki',
   'MeasurementGeometry' => {
      Description => 'Geometria pomiaru',
      PrintConv => {
        '0/45 or 45/0' => '0/45 lub 45/0',
        '0/d or d/0' => '0/d lub d/0',
      },
    },
   'MediaBlackPoint' => 'Punkt czerni materiału',
   'MediaWhitePoint' => 'Punkt bieli materiału',
   'MeteringMode' => {
      Description => 'Tryb pomiaru',
      PrintConv => {
        'Average' => 'Średni',
        'Center-weighted average' => 'Centralnie ważony uśredniony',
        'Multi-segment' => 'Wzór',
        'Multi-spot' => 'Wielopunktowy',
        'Other' => 'Inne',
        'Partial' => 'Częściowy',
        'Spot' => 'Punktowy',
        'Unknown' => 'Nieznane',
      },
    },
   'MinSampleValue' => 'Minimalny rozmiar próbki',
   'ModeNumber' => 'Numer trybu',
   'Model' => 'Aparat',
   'ModifiedPictureStyle' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'ModifyDate' => 'Data i godzina zmiany pliku',
   'MonochromeFilterEffect' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'MonochromeToningEffect' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'MultipleExposureSet' => 'Wielokrotna ekspozycja',
   'NEFCompression' => {
      PrintConv => {
        'Uncompressed' => 'Bez kompresji',
      },
    },
   'NativeDisplayInfo' => 'Informacja o natywnym(?) wyświetlaczu',
   'Noise' => 'Szumy',
   'NoiseReduction' => {
      Description => 'Redukcja szumów',
      PrintConv => {
        'Off' => 'Wyłączona',
        'On' => 'Włączona',
      },
    },
   'NumberofInks' => 'Liczba tuszy',
   'ObjectFileType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'OldSubfileType' => {
      Description => 'Stary typ podsekcji',
      PrintConv => {
        'Full-resolution image' => 'Obraz w pełnej rozdzielczości',
        'Reduced-resolution image' => 'Obraz o zredukowanej rozdzielczości',
        'Single page of multi-page image' => 'Jedna strona obrazu wielostronicowego',
      },
    },
   'Opto-ElectricConvFactor' => 'Współczynnik przekształcenia optyczno-elektrycznego',
   'Orientation' => {
      Description => 'Orientacja obrazu',
      PrintConv => {
        'Horizontal (normal)' => '0° (góra/lewo)',
        'Mirror horizontal' => '0° (góra/prawo)',
        'Mirror horizontal and rotate 270 CW' => '90° CW (lewo/góra)',
        'Mirror horizontal and rotate 90 CW' => '90° CCW (prawo/dół)',
        'Mirror vertical' => '180° (dół/lewo)',
        'Rotate 180' => '180° (dół/prawo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'OwnerName' => 'Nazwa właściciela',
   'PageName' => 'Nazwa strony',
   'PageNumber' => 'Numer strony',
   'PentaxImageSize' => {
      Description => 'Rozmiar obrazu Pentax\'a',
      PrintConv => {
        '2304x1728 or 2592x1944' => '2304 x 1728 lub 2592 x 1944',
        '2560x1920 or 2304x1728' => '2560 x 1920 lub 2304 x 1728',
        '2816x2212 or 2816x2112' => '2816 x 2212 lub 2816 x 2112',
        '3008x2008 or 3040x2024' => '3008 x 2008 lub 3040 x 2024',
        'Full' => 'Pełny',
      },
    },
   'PentaxVersion' => 'Wersja Pentax\'a',
   'PhotoEffectsType' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Schemat pikseli',
      PrintConv => {
        'BlackIsZero' => 'Czarny jest zerem',
        'Color Filter Array' => 'CFA (Filtr kolorów matrycy)',
        'Pixar LogL' => 'CIE Log2(L) (Log luminancji)',
        'Pixar LogLuv' => 'CIE Log2(l)(u\',v\') (Log luminancji i chrominancji)',
        'RGB Palette' => 'Paleta kolorów',
        'Transparency Mask' => 'Maska przeźroczystości',
        'WhiteIsZero' => 'Biały jest zerem',
      },
    },
   'PictureMode' => 'Tryb obrazu',
   'PictureMode2' => 'Tryb obrazu 2',
   'PictureStyle' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'PixelFormat' => {
      Description => 'Format zapisu koloru',
      PrintConv => {
        '128-bit PRGBA Float' => '128-bitów PRGBA (zapis zmienno przecinkowy)',
        '128-bit RGB Float' => '128-bitów RGB (zapis zmienno przecinkowy)',
        '128-bit RGBA Float' => '128-bitów RGBA (zapis zmienno przecinkowy)',
        '16-bit Gray' => '16-bitów Szarość',
        '32-bit Gray Float' => '32-bity Szarość (zapis zmienno przecinkowy)',
        '48-bit RGB Fixed Point' => '48-bitów RGB (zapis stałopozycyjny)',
        '8-bit Gray' => '8-bitów Szarość',
        '96-bit RGB Fixed Point' => '96-bitów RGB (zapis stałopozycyjny)',
        'Black & White' => 'Czarno Biały',
      },
    },
   'PlanarConfiguration' => {
      Description => 'Układ danych obrazu',
      PrintConv => {
        'Chunky' => 'Format "chunky" (z przeplotem)',
        'Planar' => 'Format "planar"',
      },
    },
   'PowerSource' => 'Zasilanie',
   'Predictor' => {
      Description => 'Przelicznik',
      PrintConv => {
        'Horizontal differencing' => 'W oparciu o różnicę w poziomie',
        'None' => 'Bez przelicznika',
      },
    },
   'Preview0' => 'Podgląd 0',
   'Preview1' => 'Podgląd 1',
   'Preview2' => 'Podgląd 2',
   'PreviewColorSpace' => {
      PrintConv => {
        'Unknown' => 'Nieznany',
      },
    },
   'PreviewImage' => 'Podgląd',
   'PreviewImageBorders' => 'Ramka podglądu',
   'PreviewImageData' => 'Dane podglądu obrazu',
   'PreviewImageLength' => 'Długość miniatury z podglądem',
   'PreviewImageSize' => 'Rozmiar podglądu',
   'PreviewImageStart' => 'Początek miniatury z podglądem',
   'PrimaryChromaticities' => 'Tonalność kolorów podstawowych',
   'ProcessingSoftware' => 'Oprogramowanie wykorzystane do przetwarzania',
   'ProductID' => 'ID produktu',
   'ProfileCMMType' => 'Typ profilu CMM',
   'ProfileClass' => {
      Description => 'Klasa profilu',
      PrintConv => {
        'Abstract Profile' => 'Profil abstrakcyjny',
        'ColorSpace Conversion Profile' => 'Profil konwersji przestrzeni barw',
        'DeviceLink Profile' => 'Profil DeviceLink',
        'Display Device Profile' => 'Profil urządzenia wyświetlającego',
        'Input Device Profile' => 'Profil urządzenia wejściowego',
        'NamedColor Profile' => 'Nazwany profil kolorów',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Profil Nikon ("nkpf")',
        'Output Device Profile' => 'Profil urządzenia wyjściowego',
      },
    },
   'ProfileConnectionSpace' => 'Profil Connection Space',
   'ProfileDateTime' => 'Data i czas profilu',
   'ProfileDescription' => 'Opis profilu',
   'ProfileDescriptionML' => 'Wielojęzyczny opis profilu.',
   'ProfileSequenceDesc' => 'Opis sekwencji profilu',
   'ProfileType' => {
      Description => 'Typ profilu',
      PrintConv => {
        'Unspecified' => 'Nie podany',
      },
    },
   'ProfileVersion' => 'Wersja profilu',
   'ProgramLine' => 'Linia programu',
   'ProgramMode' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Province-State' => 'Region',
   'Quality' => {
      Description => 'Jakość',
      PrintConv => {
        'Best' => 'Najlepsza',
        'Better' => 'Lepsza',
        'Good' => 'Dobra',
        'Low' => 'Niska jakość',
        'Normal' => 'Standardowa jakość',
      },
    },
   'Rating' => 'Ocena',
   'RatingPercent' => 'Ocena procentowo',
   'RawImageSize' => 'Rozmiar obrazu RAW',
   'RecordMode' => 'Tryb zapisu',
   'RedBalance' => 'Balans czerwonego',
   'RedMatrixColumn' => 'Kolumna matrycy czerwieni',
   'RedTRC' => 'Krzywa reprodukcji czerwieni',
   'ReferenceBlackWhite' => 'Para wartości odniesienia dla czarno-białego obrazu',
   'RelatedImageFileFormat' => 'Format pliku powiązanego zdjęcia',
   'RelatedImageHeight' => 'Długość powiązanego zdjęcia',
   'RelatedImageWidth' => 'Szerokość powiązanego zdjęcia',
   'RelatedSoundFile' => 'Powiązany plik audio',
   'ResolutionUnit' => {
      Description => 'Jednostka rozdzielczości X i Y',
      PrintConv => {
        'None' => 'Brak',
        'cm' => 'centymetry',
        'inches' => 'Cal',
      },
    },
   'RetouchHistory' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Rotation' => {
      Description => 'Obrót',
      PrintConv => {
        'Horizontal' => '0° (góra/lewo)',
        'Horizontal (Normal)' => '0° (góra/lewo)',
        'Horizontal (normal)' => '0° (góra/lewo)',
        'Rotate 180' => '180° (dół/prawo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
        'Rotated 180' => '180° (dół/prawo)',
        'Rotated 270 CW' => '90° CW (lewo/dół)',
        'Rotated 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'RowsPerStrip' => 'Liczba rzędów w pasku',
   'SRFocalLength' => 'Długość fokalna SR',
   'SRResult' => 'Stabilizacja obrazu',
   'SampleFormat' => {
      Description => 'Format próbki',
      PrintConv => {
        'Float' => 'Zmienno przecinkowa',
        'Signed' => 'Całkowita ze znakiem',
        'Undefined' => 'Nie podano',
        'Unsigned' => 'Całkowita bez znaku',
      },
    },
   'SamplesPerPixel' => 'Liczba składników',
   'Saturation' => {
      Description => 'Nasycenie',
      PrintConv => {
        'High' => 'Wysokie nasycenie',
        'Low' => 'Niskie nasycenie',
        'Normal' => 'Standardowy',
      },
    },
   'SceneCaptureType' => {
      Description => 'Ujęcie z programem tematycznym',
      PrintConv => {
        'Landscape' => 'Krajobraz',
        'Night' => 'Sceny nocne',
        'Portrait' => 'Portret',
        'Standard' => 'Standardowy',
      },
    },
   'SceneMode' => {
      Description => 'Tryby tematyczne',
      PrintConv => {
        'Sunset' => 'Zachód słońca',
      },
    },
   'SceneType' => {
      Description => 'Rodzaj sceny',
      PrintConv => {
        'Directly photographed' => 'Zdjęcie uzyskane bezpośrednio',
      },
    },
   'SensingMethod' => {
      Description => 'Metoda pomiaru',
      PrintConv => {
        'Color sequential area' => 'Sekwencyjny sensor obszaru koloru',
        'Color sequential linear' => 'Sekwencyjny liniowy sensor koloru',
        'One-chip color area' => 'Jednoprocesorowy sensor obszaru koloru',
        'Three-chip color area' => 'Trójprocesorowy sensor obszaru koloru',
        'Trilinear' => 'Trój liniowy sensor',
        'Two-chip color area' => 'Dwuprocesorowy sensor obszaru koloru',
      },
    },
   'SensitivityAdjust' => 'Regulacja czułości',
   'SensitivitySteps' => 'Krok ustawienia czułości',
   'SequentialShot' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'SerialNumber' => 'ID aparatu fotograficznego',
   'ServiceIdentifier' => 'Identyfikator usługi',
   'ShadingCompensation' => 'Kompensacja zacienienia',
   'Sharpness' => {
      Description => 'Ostrość',
      PrintConv => {
        'Hard' => 'Ostry',
        'Normal' => 'Standardowy',
        'Soft' => 'Miękki',
      },
    },
   'ShootingMode' => 'Tryb fotografowania',
   'ShutterCount' => 'Licznik migawki',
   'ShutterSpeed' => 'Czas ekspozycji',
   'ShutterSpeedValue' => 'Prędkość migawki',
   'SlaveFlashMeteringSegments' => 'Segmenty pomiarowe lampy podrzędnej',
   'SlowShutter' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Software' => 'Oprogramowanie',
   'Source' => 'Źródło',
   'SpatialFrequencyResponse' => 'Przestrzenno - częstotliwościowa charakterystyka',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'SpectralSensitivity' => 'Czułość spektralna',
   'State' => 'Region',
   'StripByteCounts' => 'Bajtów na skompresowany pasek',
   'StripOffsets' => 'Lokalizacja danych zdjęcia',
   'SubSecTime' => '"Data i godzina, subsekundy"',
   'SubSecTimeDigitized' => '"Cyfrowa data i godzina, subsekundy"',
   'SubSecTimeOriginal' => '"Pierwotna data i godzina, sub-sekundy"',
   'SubfileType' => {
      Description => 'Typ podsekcji',
      PrintConv => {
        'Full-resolution image' => 'Obraz w pełnej rozdzielczości',
        'Reduced-resolution image' => 'Obraz o zredukowanej rozdzielczości',
        'Single page of multi-page image' => 'Jedna strona obrazu wielostronicowego',
        'Single page of multi-page reduced-resolution image' => 'Jedna strona obrazu wielostronicowego o zredukowanej rozdzielczości',
        'TIFF/IT final page' => 'Ostatnia strona TIFF/IT',
        'Transparency mask' => 'Maska przezroczystości',
        'Transparency mask of multi-page image' => 'Maska przezroczystości obrazu wielostronicowego',
        'Transparency mask of reduced-resolution image' => 'Maska przezroczystości obrazu o zredukowanej rozdzielczości',
        'Transparency mask of reduced-resolution multi-page image' => 'Maska przezroczystości obrazu wielostronicowego o zredukowanej rozdzielczości',
        'invalid' => 'Błędny',
      },
    },
   'SubjectArea' => 'Obszar obiektu',
   'SubjectDistance' => 'Odległość od obiektu',
   'SubjectDistanceRange' => {
      Description => 'Zakres odległości do obiektu',
      PrintConv => {
        'Close' => 'Zbliżenie',
        'Distant' => 'Odległy plan',
        'Macro' => 'Makro',
      },
    },
   'SubjectLocation' => 'Lokalizacja obiektu',
   'SubjectProgram' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'SupplementalCategories' => 'Kategorie dodatkowe',
   'SvISOSetting' => 'Ustawienia ISO Sv',
   'T6Options' => {
      PrintConv => {
        'Uncompressed' => 'Nieskompresowany',
      },
    },
   'TargetPrinter' => 'Docelowa drukarka',
   'Technology' => {
      Description => 'Technologia',
      PrintConv => {
        'Active Matrix Display' => 'Wyświetlacz z matrycą aktywną',
        'Cathode Ray Tube Display' => 'Wyświetlacz kineskopowy',
        'Digital Camera' => 'Aparat cyfrowy',
        'Dye Sublimation Printer' => 'Drukarka termosublimacyjna',
        'Electrophotographic Printer' => 'Drukarka laserowa',
        'Film Scanner' => 'Skaner do filmów',
        'Film Writer' => 'Zapis na filmie',
        'Ink Jet Printer' => 'Drukarka atramentowa',
        'Passive Matrix Display' => 'Wyświetlacz z matrycą pasywną',
        'Photo CD' => 'Foto-CD',
        'Photographic Paper Printer' => 'Drukarka z papierem fotograficznym',
        'Projection Television' => 'Projektor telewizyjny',
        'Reflective Scanner' => 'Skaner',
        'Video Camera' => 'Kamera wideo',
        'Video Monitor' => 'Monitor wideo',
      },
    },
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Text' => 'Tekst',
   'Thresholding' => 'Progowanie',
   'ThumbnailImage' => 'Miniatura',
   'ThumbnailImageSize' => 'Rozmiar miniaturki',
   'TileByteCounts' => 'Liczba bajtów na kafelek',
   'TileLength' => 'Wysokość kafelka',
   'TileOffsets' => 'Przesunięcie kafelków',
   'TileWidth' => 'Szerokość kafelka',
   'Time' => 'Czas',
   'Title' => 'Tytuł',
   'ToneCurve' => 'Krzywa barwy',
   'ToneCurves' => 'Krzywe barwy',
   'ToningEffectMonochrome' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'Track' => 'Ścieżka',
   'TransferFunction' => 'Funkcja transferu',
   'Transformation' => {
      Description => 'Przekształcenie',
      PrintConv => {
        'Horizontal (normal)' => '0° (góra/lewo)',
        'Mirror horizontal' => '0° (góra/prawo)',
        'Mirror horizontal and rotate 270 CW' => '90° CW (lewo/góra)',
        'Mirror horizontal and rotate 90 CW' => '90° CCW (prawo/dół)',
        'Mirror vertical' => '180° (dół/lewo)',
        'Rotate 180' => '180° (dół/prawo)',
        'Rotate 270 CW' => '90° CW (lewo/dół)',
        'Rotate 90 CW' => '90° CCW (prawo/góra)',
      },
    },
   'TransmissionReference' => 'Odnośnik transmisji',
   'TvExposureTimeSetting' => 'Ustawienia czasu ekspozycji TV',
   'Uncompressed' => 'Nieskompresowany',
   'UniqueCameraModel' => 'Unikatowy model aparatu',
   'Unknown' => 'Nieznany',
   'Urgency' => 'Priorytet',
   'UserComment' => 'Komentarz użytkownika',
   'VersionYear' => 'Rok wersji',
   'VideoCardGamma' => 'Gamma karty graficznej',
   'WBAdjLighting' => {
      PrintConv => {
        'None' => 'Brak',
      },
    },
   'WB_RGGBLevelsCloudy' => 'Poziomy WB RGGB - zachmurzenie ',
   'WB_RGGBLevelsDaylight' => 'Poziomy WB RGGB - światło dzienne ',
   'WB_RGGBLevelsFlash' => 'Poziomy WB RGGB - lampa błyskowa ',
   'WB_RGGBLevelsFluorescentD' => 'Poziomy WB RGGB - światło fluorescencyjne ',
   'WB_RGGBLevelsFluorescentN' => 'Poziomy WB RGGB - światło fluorescencyjne N ',
   'WB_RGGBLevelsFluorescentW' => 'Poziomy WB RGGB - światło fluorescencyjne W ',
   'WB_RGGBLevelsShade' => 'Poziomy WB RGGB - cień ',
   'WB_RGGBLevelsTungsten' => 'Poziomy WB RGGB - światło żarowe ',
   'WhiteBalance' => {
      Description => 'Balans bieli',
      PrintConv => {
        'Auto' => 'Automatyczny balans bieli',
        'Black & White' => 'Monochromatyczny',
        'Cloudy' => 'Pochmurna pogoda',
        'Cool White Fluorescent' => 'Jarzeniówka z zimnym białym światłem',
        'Custom 1' => 'WŁASNE 1',
        'Custom 2' => 'WŁASNE 2',
        'Custom 3' => 'WŁASNE 3',
        'Custom 4' => 'WŁASNE 4',
        'Day White Fluorescent' => 'Jarzeniówka z naturalnym białym światłem',
        'Daylight' => 'Światło dzienne',
        'Daylight Fluorescent' => 'Jarzeniówka ze światłem dziennym',
        'Fluorescent' => 'Jarzeniowy',
        'Manual' => 'Manualny balans bieli',
        'Shade' => 'Cień',
        'Tungsten' => 'Światło żarówek',
      },
    },
   'WhiteBalanceMode' => {
      Description => 'Tryb balansu bieli',
      PrintConv => {
        'Auto (Cloudy)' => 'Automatyczny (zachmurzenie)',
        'Auto (Day White Fluorescent)' => 'Automatyczny (białe fluorescencyjne światło dzienne)',
        'Auto (Daylight Fluorescent)' => 'Automatyczny (fluorescencyjne światło dzienne)',
        'Auto (Daylight)' => 'Automatyczny (światło dzienne)',
        'Auto (Flash)' => 'Automatyczny (lampa błyskowa)',
        'Auto (Shade)' => 'Automatyczny (cień)',
        'Auto (Tungsten)' => 'Automatyczny (światło żarowe)',
        'Auto (White Fluorescent)' => 'Automatyczny (białe światło fluorescencyjne)',
        'Unknown' => 'Automatyczny (nie wykryty)',
        'User-Selected' => 'Użytkownika',
      },
    },
   'WhiteBalanceSet' => 'Ustawienie balansu bieli',
   'WhitePoint' => 'Chromatyczność białego punktu',
   'WorldTimeLocation' => {
      Description => 'Miejsca czasu światowego',
      PrintConv => {
        'Destination' => 'Przeznaczenie',
        'Hometown' => 'Miasto domowe',
      },
    },
   'Writer-Editor' => 'Autor podpisu',
   'XClipPathUnits' => 'Jednostki wzdłuż osi X dla ścieżki obcięcia',
   'XPosition' => 'Współrzędna X',
   'XResolution' => 'Rozdzielczość obrazu w poziomie',
   'YCbCrCoefficients' => 'Współczynniki matrycy transformacji przestrzeni barwowej',
   'YCbCrPositioning' => {
      Description => 'Pozycje Y i C',
      PrintConv => {
        'Centered' => 'Wyśrodkowane',
        'Co-sited' => 'Obok siebie (?)',
      },
    },
   'YCbCrSubSampling' => 'Współczynnik podpróbkowania(?) Y do C',
   'YClipPathUnits' => 'Jednostki wzdłuż osi Y dla ścieżki obcięcia',
   'YPosition' => 'Współrzędna Y',
   'YResolution' => 'Rozdzielczość obrazu w pionie',
   'Year' => 'Rok',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::pl.pm - ExifTool Polish language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Przemyslaw Sulek and Kacper Perschke for providing
this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
