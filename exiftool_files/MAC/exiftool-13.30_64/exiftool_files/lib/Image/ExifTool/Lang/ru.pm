#------------------------------------------------------------------------------
# File:         ru.pm
#
# Description:  ExifTool Russian language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::ru;

use strict;
use vars qw($VERSION);

$VERSION = '1.08';

%Image::ExifTool::Lang::ru::Translate = (
   'A100DataOffset' => 'Смещение данных (Sony A100)',
   'ABDate' => 'AB – Дата',
   'ABLabel' => 'AB – Метка',
   'ABRelatedNames' => 'AB – Похожие имена',
   'AB_UID' => 'AB – Уникальный ID',
   'AFAdjustment' => 'Регулировка автофокуса',
   'AIBuildNumber' => 'AI – Номер сборки',
   'AIColorModel' => 'AI – Цветовая модель',
   'AIColorUsage' => 'AI – Использование цвета',
   'AICreatorVersion' => 'AI – Версия приложения',
   'AIFileFormat' => 'AI – Формат файла',
   'AINumLayers' => 'AI – Количество слоёв',
   'AIRulerUnits' => {
      Description => 'AI – Единицы линейки',
      PrintConv => {
        'Centimeters' => 'Сантиметры',
        'Inches' => 'Дюймы',
        'Millimeters' => 'Милиметры',
        'Picas' => 'Пики',
        'Pixels' => 'Пиксели',
        'Points' => 'Точки',
      },
    },
   'AITargetResolution' => 'AI – Целевое разрешение',
   'APEVersion' => 'Версия APE',
   'ARMIdentifier' => 'Идентификатор ARM',
   'ARMVersion' => 'Версия ARM',
   'ATSCContent' => {
      Description => 'Контент ATSC',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'About' => 'Об',
   'AbsoluteAltitude' => 'Абсолютная высота',
   'Abstract' => 'Краткое описание',
   'AbstractFileName' => 'Файл с абстрактной информацией',
   'Acceleration' => 'Ускорение',
   'AccelerationVector' => 'Вектор ускорения',
   'Accelerometer' => 'Акселерометр',
   'AccelerometerTime' => 'Время акселерометра',
   'AccelerometerX' => 'Акселерометр – X',
   'AccelerometerY' => 'Акселерометр – Y',
   'AccelerometerZ' => 'Акселерометр – Z',
   'AccessDate' => 'Дата последнего доступа',
   'Acknowledged' => 'Благодарности',
   'Action' => 'Действие',
   'ActionAdvised' => {
      Description => 'Рекомендуемое действие',
      PrintConv => {
        'Object Append' => 'Добавить объект',
        'Object Kill' => 'Уничтожить объект',
        'Object Reference' => 'Сделать ссылку на объект',
        'Object Replace' => 'Заменить объект',
      },
    },
   'ActiveArea' => 'Активная область',
   'Actor' => 'Актёр',
   'Address' => 'Адресс',
   'AdoptedNeutral' => 'Принятый как нейтральный',
   'Adult' => 'Для взрослых',
   'AdultContentWarning' => {
      Description => 'Предупреждение о содержании для взрослых',
      PrintConv => {
        'Adult Content Warning Required' => 'Требуется',
        'Not Required' => 'Не требуется',
        'Unknown' => 'Не задано',
      },
    },
   'AdvancedSceneMode' => {
      Description => 'Расширенный режим сцены',
      PrintConv => {
        'Architecture' => 'Архитектура',
        'Bleach Bypass' => 'Удержание серебра',
        'Cinema' => 'Кино',
        'Color Select' => 'Выбранный цвет',
        'Creative Macro' => 'Творческий макро',
        'Creative Night Scenery' => 'Творческий ночной пейзаж',
        'Creative Portrait' => 'Творческий портрет',
        'Creative Scenery' => 'Творческий пейзаж',
        'Creative Sports' => 'Творческий спорт',
        'Cross Process' => 'Кросспроцесс',
        'Dynamic Art' => 'Динамика',
        'Dynamic Monochrome' => 'Динамичное монохромное',
        'Elegant' => 'Элегантный',
        'Expressive' => 'Выразительный',
        'Fantasy' => 'Фэнтези',
        'Flower' => 'Цветок',
        'HDR Art' => 'HDR',
        'HDR B&W' => 'Чёрно-Белый HDR',
        'Handheld Night Shot' => 'Ночная съёмка с руки',
        'High Dynamic' => 'Высокая динамика',
        'High Key' => 'В высоком ключе',
        'Illuminations' => 'Свеча',
        'Impressive Art' => 'Впечатляющий',
        'Indoor Portrait' => 'Внутренний портрет',
        'Indoor Sports' => 'Спорт в помещении',
        'Low Key' => 'В низком ключе',
        'Miniature' => 'Миниатюра',
        'Monochrome' => 'Монохромный',
        'Nature' => 'Природа',
        'Objects' => 'Объекты',
        'Old Days' => 'Старые времена',
        'Outdoor Portrait' => 'Наружный портрет',
        'Outdoor Sports' => 'Спорт на открытом воздухе',
        'Pure' => 'Чистый',
        'Retro' => 'Ретро',
        'Rough Monochrome' => 'Грубый монохромный',
        'Sepia' => 'Сепия',
        'Silhouette' => 'Силуэт',
        'Silky Monochrome' => 'Шелковистый монохромный',
        'Soft' => 'Мягкий',
        'Star' => 'Звёзды',
        'Sunshine' => 'Солнечный свет',
        'Toy Effect' => 'Эффект ломографии',
        'Toy Pop' => 'Ломография с выделением цвета',
      },
    },
   'AdventRevision' => 'Проверка появления',
   'AdventScale' => 'Масштаб появления',
   'AffineA' => 'Афинное преобразование A',
   'AffineB' => 'Афинное преобразование B',
   'AffineC' => 'Афинное преобразование C',
   'AffineD' => 'Афинное преобразование D',
   'AffineTransformMat' => 'Матрица Афинного преобразования',
   'AffineX' => 'Афинное преобразование X',
   'AffineY' => 'Афинное преобразование Y',
   'AlarmUID' => 'Уникальный ID сигнала',
   'Album' => 'Альбом',
   'AlbumArtist' => 'Исполнитель альбома',
   'AliasLayerMetadata' => 'Псевдоним метаданных слоя',
   'Alignment' => 'Выравнивание',
   'AllColorFlatField1' => 'Коррекция плоского поля – Все цвета 1',
   'AllColorFlatField2' => 'Коррекция плоского поля – Все цвета 2',
   'AllColorFlatField3' => 'Коррекция плоского поля – Все цвета 3',
   'AllDayEvent' => 'Событие на весь день',
   'Alpha' => {
      Description => 'Альфа-канал',
      PrintConv => {
        'Alpha Exists (W color component)' => 'Альфа-канал присутствует (цветной компонент W)',
        'Alpha Exists (color not premultiplied)' => 'Альфа-канал присутствует (не предварительно умноженный цвет)',
        'Alpha Exists (color premultiplied)' => 'Альфа-канал присутствует (предварительно умноженный цвет)',
        'No Alpha Plane' => 'Альфа-канал отсутствует',
      },
    },
   'AlphaByteCount' => 'Размер альфа-канала (байт)',
   'AlphaChannelsNames' => 'Названия альфа-каналов',
   'AlphaDataDiscard' => {
      Description => 'Отброшенные данные альфа-канала',
      PrintConv => {
        'Flexbits Discarded' => 'Гибкие биты отброшены',
        'Full Resolution' => 'Полное разрешение',
        'HighPass Frequency Data Discarded' => 'Данные высоких частот отброшены',
        'Highpass and LowPass Frequency Data Discarded' => 'Данные высоких и низких частот отброшены',
      },
    },
   'AlphaIdentifiers' => 'Идентификаторы альфа-канала',
   'AlphaMask' => 'Маска Альфа-канала',
   'AlphaOffset' => 'Смещение альфа-канала',
   'AlternateDuotoneColors' => 'Альтернативные цвета Duotone',
   'AlternateSpotColors' => 'Альтернативные плашечные цвета',
   'AmbientInfrared' => 'Внешний инфракрасный свет',
   'AmbientLight' => 'Внешнее освещение',
   'AmbientTemperature' => 'Температура окружающей среды',
   'AmbientTemperatureFahrenheit' => 'Температура окружающей среды по Фаренгейту',
   'AnalogBalance' => 'Аналоговый баланс белого',
   'AngleInfoRoll' => 'Угол вращения (крен)',
   'AngleInfoYaw' => 'Угол поворота (рыскание)',
   'AnimationControl' => 'Управление анимацией',
   'AnimationFrames' => 'Кадры анимации',
   'AnimationIterations' => 'Повторений анимации',
   'AnimationPlays' => 'Проигрывание анимации',
   'Anniversary' => 'Годовщина',
   'Annotation' => 'Аннотация',
   'AnnotationUsageRights' => 'Права на использование Аннотации',
   'Annotations' => 'Аннотации',
   'Announce' => 'Трекер',
   'AnnounceList1' => 'Список трекеров 1',
   'AntiAliasStrength' => 'Сила сглаживания',
   'Aperture' => 'Диафрагма',
   'ApertureValue' => 'Диафрагма',
   'AppleKeywords' => 'Apple – Ключевые слова',
   'AppleMailDateReceived' => 'Apple Mail – Дата получения',
   'AppleMailDateSent' => 'Apple Mail – Дата отправки',
   'AppleMailFlagged' => 'Apple Mail – Отмечено флажком',
   'AppleMailIsRemoteAttachment' => 'Apple Mail – Наличие удалённого вложения',
   'AppleMailMessageID' => 'Apple Mail – ID сообщения',
   'AppleMailPriority' => 'Apple Mail – Приоритет',
   'AppleMailRead' => 'Apple Mail – Прочитано',
   'AppleMailRepliedTo' => 'Apple Mail – Отвечено',
   'Application' => 'Приложение',
   'ApplicationData' => 'Данные об устройстве',
   'ApplicationNotes' => 'Примечания приложения',
   'ApplicationRecordVersion' => 'Версия Application Record',
   'AppointmentSequence' => 'Последовательность назначения',
   'ApproximateFocusDistance' => 'Приблизительное расстояние фокусировки',
   'ArchivedFileName' => 'Название заархивированного файла',
   'Artist' => 'Исполнитель',
   'ArtistLen' => 'Исполнитель Len',
   'AsShotICCProfile' => 'ICC-профиль при съёмке',
   'AsShotNeutral' => 'Нейтральный цвет при съёмке',
   'AsShotPreProfileMatrix' => 'Предварительный профиль матрицы при съёмке',
   'AsShotProfileName' => 'Название профиля при съёмке',
   'AsShotWhiteXY' => 'XY белого при съёмке',
   'Ascender' => 'Надстрочный интервал',
   'Ascent' => 'Надстрочный интервал',
   'AspectRatio' => 'Соотношение сторон',
   'AssetID' => 'ID актива',
   'Association' => 'Ассоциация',
   'AssumedDisplaySize' => 'Размер отображаемого стереоизображения',
   'AssumedDistanceView' => 'Расстояние от позиции просмотра до дисплея',
   'Attachment' => 'Вложение',
   'Attendee' => 'Участник',
   'Attitude' => 'Положение',
   'AttitudeTarget' => 'Целевое положение',
   'Audiences' => 'Аудитория',
   'AudioBitrate' => 'Битрейт аудио',
   'AudioBitsPerSample' => 'Битовая глубина аудио',
   'AudioBytes' => 'Аудио байтов',
   'AudioChannels' => 'Количество аудиоканалов',
   'AudioCodec' => 'Кодек аудио',
   'AudioCodecID' => 'ID аудиокодека',
   'AudioData' => 'Аудиоданные',
   'AudioDuration' => 'Продолжительность аудио',
   'AudioEncoding' => {
      Description => 'Кодирование звука',
      PrintConv => {
        'Device-specific sound' => 'Зависящий от устройства звук',
        'G.711 A-law logarithmic PCM' => 'G.711 A-law логарифмический PCM',
        'G.711 mu-law logarithmic PCM' => 'G.711 mu-law логарифмический PCM',
        'Nellymoser 16kHz Mono' => 'Nellymoser 16 кГц, моно',
        'Nellymoser 8kHz Mono' => 'Nellymoser 8kHz, моно',
        'PCM-BE (uncompressed)' => 'PCM-BE (без сжатия)',
        'PCM-LE (uncompressed)' => 'PCM-LE (без сжатия)',
      },
    },
   'AudioFileSize' => 'Размер аудио файла',
   'AudioFormat' => 'Формат аудио',
   'AudioFrameSize' => 'Размер аудио фрейма',
   'AudioLayer' => 'Уровень аудио',
   'AudioMimeType' => 'MIME-тип аудио',
   'AudioMode' => 'Режим аудио',
   'AudioOutcue' => 'Заключительные слова аудио',
   'AudioSampleRate' => 'Частота дискретизации звука',
   'AudioSampleSize' => 'Количество бит на канал',
   'AudioSamplingRate' => 'Частота дискретизации',
   'AudioSamplingResolution' => 'Разрядность аудио',
   'AudioSetting' => 'Настройка звука',
   'AudioSize' => 'Размер аудиофайла',
   'AudioType' => {
      Description => 'Тип аудио',
      PrintConv => {
        'Mono Actuality' => 'Событие – Моно',
        'Mono Music' => 'Музыка – Моно',
        'Mono Question and Answer Session' => 'Вопросы и ответы – Моно',
        'Mono Raw Sound' => 'Необработанная запись – Моно',
        'Mono Response to a Question' => 'Ответ на вопрос – Моно',
        'Mono Scener' => 'Обстоятельства события – Моно',
        'Mono Voicer' => 'Голосовая запись – Моно',
        'Mono Wrap' => 'Итог события – Моно',
        'Stereo Actuality' => 'Событие – Стерео',
        'Stereo Music' => 'Музыка – Стерео',
        'Stereo Question and Answer Session' => 'Вопросы и ответы – Стерео',
        'Stereo Raw Sound' => 'Необработанная запись – Стерео',
        'Stereo Response to a Question' => 'Ответ на вопрос – Стерео',
        'Stereo Scener' => 'Обстоятельства события – Стерео',
        'Stereo Voicer' => 'Голосовая запись – Стерео',
        'Stereo Wrap' => 'Итог события – Стерео',
        'Text Only' => 'Только текст',
      },
    },
   'AuthenticationTime' => 'Электронная подпись – Время аутентификации',
   'AuthenticationType' => 'Электронная подпись – Тип аутентификации',
   'Author' => 'Автор',
   'AuthorLen' => 'Автор Len',
   'AuthorsPosition' => 'Должность автора',
   'AutoFocus' => {
      Description => 'Автофокус',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'AutoISOMax' => 'Максимально значение автоматического ISO',
   'AutoISOMin' => 'Минимальное значение автоматического ISO',
   'AutoLowLightDuration' => 'Автосъёмка при низком освещении – Продолжительность',
   'AutoRotation' => {
      Description => 'Автоматический поворот',
      PrintConv => {
        'Auto' => 'Автоматически',
        'Down' => 'Вниз',
        'Up' => 'Вверх',
      },
    },
   'AutoSaveFilePath' => 'Путь к файлу автосохранения',
   'AutoSaveFormat' => 'Формат автосохранения',
   'AvgBitrate' => 'Средний битрейт',
   'AvgPacketSize' => 'Средний размер пакета',
   'AvgWidth' => 'Средняя ширина глифа',
   'AxisDistanceX' => 'Дистанция по оси X',
   'AxisDistanceY' => 'Дистанция по оси Y',
   'AxisDistanceZ' => 'Дистанция по оси Z',
   'BMPVersion' => {
      Description => 'Версия BMP',
      PrintConv => {
        'AVI BMP structure?' => 'Структура AVI BMP?',
      },
    },
   'BWFilter' => 'Чёрно-Белый фильтр',
   'BW_HalftoningInfo' => 'Сведения о тонировании – Чёрно-Белое',
   'BW_TransferFunc' => 'Функция переноса – Чёрно-Белое',
   'Background' => 'Фон',
   'BackgroundColor' => 'Цвет фона',
   'BackgroundColorIndicator' => {
      Description => 'Цвет фона',
      PrintConv => {
        'Specified Background Color' => 'Указан',
        'Unspecified Background Color' => 'Не указан',
      },
    },
   'BackgroundColorValue' => 'Значение фонового цвета',
   'BackupTime' => 'Время резервирования',
   'BadFaxLines' => 'Количество битых строк',
   'BaseViewpointNum' => 'Номер базовой точки просмотра',
   'BaselineExposure' => 'Базовая экспозиция',
   'BaselineExposureOffset' => 'Сдвиг базовой экспозиции',
   'BaselineLength' => 'Длина базовой линии',
   'BaselineNoise' => 'Базовый уровень шума',
   'BaselineSharpness' => 'Базовая чёткость',
   'BatteryCapacity' => 'Ёмкость аккумулятора',
   'BatteryCurrent' => 'Ток аккумулятора',
   'BatteryLevel' => 'Уровень заряда аккумулятора',
   'BatteryTemperature' => 'Температура аккумулятора',
   'BatteryTime' => 'Время работы от аккумулятора',
   'BatteryType' => 'Тип аккумулятора',
   'BatteryVoltage' => 'Напряжение аккумулятора',
   'BatteryVoltage1' => 'Напряжение аккумулятора 1',
   'BatteryVoltage2' => 'Напряжение аккумулятора 2',
   'BatteryVoltage3' => 'Напряжение аккумулятора 3',
   'BatteryVoltage4' => 'Напряжение аккумулятора 4',
   'BatteryVoltageAvg' => 'Напряжение аккумулятора (среднее)',
   'BayerGreenSplit' => 'Разделение зелёных каналов в матрице Байера',
   'BayerPattern' => 'Фильтр Байера',
   'BestQualityScale' => 'Оптимальный масштаб',
   'BibligraphicFileName' => 'Файл с библиографической информацией',
   'BinaryFilter' => 'Бинарный фильтр',
   'Birthday' => 'День рождения',
   'BitDepth' => {
      Description => 'Битовая глубина',
      PrintConv => {
        'Custom' => 'Заданная пользователем',
      },
    },
   'Bitrate' => 'Битрейт',
   'BitsPerComponent' => 'Количество бит на компонент',
   'BitsPerExtendedRunLength' => 'Количество бит на расширенную длину серий',
   'BitsPerPixel' => 'Количество бит на пиксель',
   'BitsPerRunLength' => 'Количество бит на длину серий',
   'BitsPerSample' => 'Количество бит на компонент',
   'BlackLevel' => 'Уровень чёрного',
   'BlackLevelBlue' => 'Уровень чёрного – Синий',
   'BlackLevelData' => 'Данные уровня черного',
   'BlackLevelDeltaH' => 'Уровень чёрного – Дельта H',
   'BlackLevelDeltaV' => 'Уровень чёрного – Дельта V',
   'BlackLevelGreen' => 'Уровень чёрного – Зелёный',
   'BlackLevelRed' => 'Уровень чёрного – Красный',
   'BlackLevelRepeatDim' => 'Уровень чёрного – Размер шаблона повторений',
   'BlacksAdj' => 'Регулировка уровня чёрного',
   'BlockSizeMax' => 'Максимальный размер блока',
   'BlockSizeMin' => 'Минимальный размер блока',
   'BlocksPerFrame' => 'Блоков на кадр',
   'BlueBalance' => 'Баланс синего',
   'BlueEndpoint' => 'Конечная точка Синего',
   'BlueMask' => 'Маска Синего',
   'BluePrimary' => 'Основной синий',
   'BlueX' => 'Синий по X',
   'BlueY' => 'Синий по Y',
   'BookName' => 'Название книги',
   'BookTitle' => 'Название книги',
   'BookType' => 'Тип книги',
   'BookVersion' => 'Версия книги',
   'BootIdentifier' => 'Идентификатор загрузки',
   'BootLoaderVersion' => 'Версия загрузчика',
   'BootSystem' => 'Система загрузки',
   'BorderColor' => 'Цвет границы',
   'BorderInformation' => 'Информация о границе',
   'BoundingBox' => 'Габаритный прямоугольник',
   'Brain' => 'Модуль Brain',
   'BrandingImageID' => 'Бренд – ID изображения',
   'BrandingName' => 'Бренд – Название',
   'BreakChar' => 'Символ определяющий границы слов',
   'Brightness' => 'Яркость',
   'BrightnessAdj' => 'Регулировка яркости',
   'BrightnessValue' => 'Значение яркости',
   'BurstID' => 'ID серии снимков',
   'BurstPrimary' => 'Первый кадр серии',
   'BurstUUID' => 'Уникальный ID для серии снимков',
   'BusyStatus' => 'Состояние занятости',
   'By-line' => 'Автор',
   'By-lineTitle' => 'Титул автора',
   'ByteLength' => 'Длина в байтах',
   'ByteOrder' => {
      Description => 'Порядок байтов',
      PrintConv => {
        'Big-endian' => 'Порядок от старшего к младшему',
        'Little-endian' => 'Порядок от младшего к старшему',
      },
    },
   'BytesPerLine' => 'Количество байт на строку',
   'BytesPerMinute' => 'Байт в минуту',
   'CDDBDiscPlayTime' => 'CDDB – Время воспроизведения диска',
   'CDDBDiscTracks' => 'CDDB – Треки на диске',
   'CDEType' => 'Тип CDE',
   'CFALayout' => {
      Description => 'Макет CFA',
      PrintConv => {
        'Even columns offset down 1/2 row' => 'Чётные столбцы смещены вниз на 1/2 строки',
        'Even columns offset up 1/2 row' => 'Чётные столбцы смещены вверх 1/2 строки',
        'Even rows offset down by 1/2 row, even columns offset left by 1/2 column' => 'Чётные строки смещены вниз на 1/2 строки, чётные столбцы смещены влево на 1/2 столбца',
        'Even rows offset down by 1/2 row, even columns offset right by 1/2 column' => 'Чётные строки смещены вниз на 1/2 строки, чётные столбцы смещены вправо на 1/2 столбца',
        'Even rows offset left 1/2 column' => 'Чётные строки смещены влево на 1/2 столбца',
        'Even rows offset right 1/2 column' => 'Чётные строки смещены вправо на 1/2 столбца',
        'Even rows offset up by 1/2 row, even columns offset left by 1/2 column' => 'Чётные строки смещены вверх на 1/2 строки, чётные столбцы смещены влево на 1/2 столбца',
        'Even rows offset up by 1/2 row, even columns offset right by 1/2 column' => 'Чётные строки смещены вверх на 1/2 строки, чётные столбцы смещены вправо на 1/2 столбца',
        'Rectangular' => 'Прямоугольная компоновка',
      },
    },
   'CFAPattern' => {
      Description => 'Шаблон CFA',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Синий, Зелёный][Зелёный, Красный]',
        '[Green,Blue][Red,Green]' => '[Зелёный, Синий][Красный, Зелёный]',
        '[Green,Red][Blue,Green]' => '[Зелёный, Красный][Синий, Зелёный]',
        '[Red,Green][Green,Blue]' => '[Красный, Зелёный][Зелёный, Синий]',
        'n/a' => 'Нет данных',
      },
    },
   'CFAPattern2' => 'Шаблон CFA 2',
   'CFAPlaneColor' => 'Цветовые плоскости CFA',
   'CFARepeatPatternDim' => 'Шаблон повторения CFA',
   'CIP3DataFile' => 'CIP3 – Файл данных',
   'CIP3Sheet' => 'CIP3 – Лист',
   'CIP3Side' => 'CIP3 – Сторона',
   'CMYKEquivalent' => 'Эквивалент CMYK',
   'CPUVersions' => 'Версии CPU',
   'CR2CFAPattern' => {
      Description => 'CR2 – Шаблон CFA',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Синий, Зеленый][Зеленый, Красный]',
        '[Green,Blue][Red,Green]' => '[Зеленый, Синий][Красный, Зеленый]',
        '[Green,Red][Blue,Green]' => '[Зеленый, Красный][Синий, Зеленый]',
        '[Red,Green][Green,Blue]' => '[Красный, Зеленый][Зеленый, Синий]',
      },
    },
   'CacheControl' => 'Контроль кеширования',
   'CacheVersion' => 'Версия кэша',
   'CalendarColor' => 'Цвет календаря',
   'CalendarDescription' => 'Описание календаря',
   'CalendarID' => 'ID календаря',
   'CalendarName' => 'Название календаря',
   'CalendarScale' => 'Календарная система',
   'CalibratedFocalLength' => 'Откалирброванное фокусное расстояние',
   'CalibratedOpticalCenterX' => 'Откалирброванный оптический центр по X',
   'CalibratedOpticalCenterY' => 'Откалирброванный оптический центр по Y',
   'CalibrationIlluminant1' => {
      Description => 'Калибровочное освещение 1',
      PrintConv => {
        'Cloudy' => 'Облачно (6500 К)',
        'Cool White Fluorescent' => 'Флуоресцентная лампа – Холодный свет (4150 К)',
        'D50' => 'D50 (5000 К)',
        'D55' => 'D55 (5500 К)',
        'D65' => 'D65 (6500 К)',
        'D75' => 'D75 (7500 К)',
        'Day White Fluorescent' => 'Флуоресцентная лампа – Дневной белый (5050 К)',
        'Daylight' => 'Дневной свет (5500 К)',
        'Daylight Fluorescent' => 'Флуоресцентная лампа дневного света (6400 К)',
        'Fine Weather' => 'Ясная погода (5500 К)',
        'Flash' => 'Вспышка (5500 К)',
        'Fluorescent' => 'Флуоресцентная лампа (4150 К)',
        'ISO Studio Tungsten' => 'Студийная лампа накаливания (3200 К)',
        'Other' => 'Другой источник света',
        'Shade' => 'Тень (7500 К)',
        'Standard Light A' => 'Стандартное освещение A (2850 К)',
        'Standard Light B' => 'Стандартное освещение B (5500 К)',
        'Standard Light C' => 'Стандартное освещение C (6500 К)',
        'Tungsten (Incandescent)' => 'Лампа накаливания (2850 К)',
        'Unknown' => 'Неизвестно',
        'Warm White Fluorescent' => 'Флуоресцентная лампа – Тёплый свет (2925 К)',
        'White Fluorescent' => 'Флуоресцентная лампа – Белый свет (3525 К)',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Калибровочное освещение 2',
      PrintConv => {
        'Cloudy' => 'Облачно (6500 К)',
        'Cool White Fluorescent' => 'Флуоресцентная лампа – Холодный свет (4150 К)',
        'D50' => 'D50 (5000 К)',
        'D55' => 'D55 (5500 К)',
        'D65' => 'D65 (6500 К)',
        'D75' => 'D75 (7500 К)',
        'Day White Fluorescent' => 'Флуоресцентная лампа – Дневной белый (5050 К)',
        'Daylight' => 'Дневной свет (5500 К)',
        'Daylight Fluorescent' => 'Флуоресцентная лампа дневного света (6400 К)',
        'Fine Weather' => 'Ясная погода (5500 К)',
        'Flash' => 'Вспышка (5500 К)',
        'Fluorescent' => 'Флуоресцентная лампа (4150 К)',
        'ISO Studio Tungsten' => 'Студийная лампа накаливания (3200 К)',
        'Other' => 'Другой источник света',
        'Shade' => 'Тень (7500 К)',
        'Standard Light A' => 'Стандартное освещение A (2850 К)',
        'Standard Light B' => 'Стандартное освещение B (5500 К)',
        'Standard Light C' => 'Стандартное освещение C (6500 К)',
        'Tungsten (Incandescent)' => 'Лампа накаливания (2850 К)',
        'Unknown' => 'Неизвестное',
        'Warm White Fluorescent' => 'Флуоресцентная лампа – Тёплый свет (2925 К)',
        'White Fluorescent' => 'Флуоресцентная лампа – Белый свет (3525 К)',
      },
    },
   'CallForImage' => 'Звонок по изображению',
   'CamReverse' => 'Камера заднего вида',
   'CameraArrangementInterval' => 'Интервал расположения камеры',
   'CameraCalibration1' => 'Калибровочная матрица фотокамеры 1',
   'CameraCalibration2' => 'Калибровочная матрица фотокамеры 2',
   'CameraCalibrationSig' => 'Сигнатура калибровки камеры',
   'CameraDateTime' => 'Дата и Время камеры',
   'CameraElevationAngle' => 'Угол обзора камеры',
   'CameraFilename' => 'Имя файла созданное камерой',
   'CameraID' => 'ID камеры',
   'CameraLabel' => 'Название камеры',
   'CameraMakeModel' => 'Модель камеры',
   'CameraModel' => 'Модель камеры',
   'CameraModelID' => 'ID модели камеры',
   'CameraOperator' => 'Кинооператор',
   'CameraOrientation' => {
      Description => 'Ориентация камеры',
      PrintConv => {
        'Horizontal (normal)' => 'Горизонтальная',
        'Rotate 180' => 'Поверот на 180°',
        'Rotate 270 CW' => 'Поворот на 270° по часовой стрелке',
        'Rotate 90 CW' => 'Поворот на 90° по часовой стрелке',
      },
    },
   'CameraPitch' => 'Тангаж камеры (Наклон)',
   'CameraRoll' => 'Крен камеры (Вращение)',
   'CameraSerialNumber' => 'Серийный номер камеры',
   'CameraTemperature' => 'Температура камеры',
   'CameraType' => 'Тип камеры',
   'CameraYaw' => 'Рыскание камеры (Поворот)',
   'CanSeekOnTime' => 'Возможность навигации по времени',
   'CanSeekToEnd' => 'Возможность перейти к концу',
   'CapHeight' => 'Высота прописных букв',
   'Caption' => 'Заголовок',
   'Caption-Abstract' => 'Подробное описание',
   'CaptionWriter' => 'Автор подписи',
   'CaptureSoftware' => 'Запись изображений выполнен в',
   'CaptureXResolution' => 'Разрешение захвата по X',
   'CaptureXResolutionUnit' => {
      Description => 'Единицы разрешения захвата по X',
      PrintConv => {
        '0.01 mm' => '0.01 мм',
        '0.1 mm' => '0.1 мм',
        '10 cm' => '10 см',
        '10 m' => '10 м',
        '100 m' => '100 м',
        'cm' => 'см',
        'km' => 'км',
        'm' => 'м',
        'mm' => 'мм',
        'um' => 'мкм',
      },
    },
   'CaptureYResolution' => 'Разрешение захвата по Y',
   'CaptureYResolutionUnit' => {
      Description => 'Единицы разрешения захвата по Y',
      PrintConv => {
        '0.01 mm' => '0.01 мм',
        '0.1 mm' => '0.1 мм',
        '10 cm' => '10 см',
        '10 m' => '10 м',
        '100 m' => '100 м',
        'cm' => 'см',
        'km' => 'км',
        'm' => 'м',
        'mm' => 'мм',
        'um' => 'мкм',
      },
    },
   'CatalogSets' => 'Наборы каталога',
   'Categories' => 'Категории',
   'Category' => 'Группа',
   'CellLength' => 'Строк в матрице дизеринга или полутонирования',
   'CellWidth' => 'Колонок в матрице дизеринга или полутонирования',
   'ChannelMode' => {
      Description => 'Режим канала',
      PrintConv => {
        'Dual Channel' => 'Двухканальное',
        'Joint Stereo' => 'Объединённое стерео',
        'Single Channel' => 'Моно',
        'Stereo' => 'Стерео',
      },
    },
   'ChannelUsage' => 'Использование канала',
   'Channels' => 'Каналов',
   'Chapter' => 'Часть',
   'ChapterCount' => 'Количество глав',
   'ChapterName' => 'Название главы',
   'ChapterNumber' => 'Номер главы',
   'CharacterSet' => 'Набор символов',
   'Characters' => 'Количество символов',
   'CharactersWithSpaces' => 'Количество символов, включая пробелы',
   'ChromaBlurRadius' => 'Радиус сглаживания цветности',
   'ChromaticAberrationCorrParams' => 'Параметры коррекции хроматической аберрации',
   'ChromaticAberrationCorrection' => {
      Description => 'Коррекция хроматической аберрации',
      PrintConv => {
        'Auto' => 'Автоматическая',
        'No correction params available' => 'Недоступна',
        'Off' => 'Не включена',
        'On' => 'Включена',
      },
    },
   'Chromaticities' => 'Основные цвета и Точка белого',
   'CircleOfConfusion' => 'Круг нерезкости',
   'City' => 'Город происхождения данных',
   'Class' => 'Класс',
   'Classification' => 'Классификации',
   'ClassifyState' => 'Класификация структуры',
   'CleanFaxData' => {
      Description => 'Наличие битых строк',
      PrintConv => {
        'Clean' => 'Без битых строк',
        'Regenerated' => 'Битые  строки восстановлены',
        'Unclean' => 'Битые строки не восстановлены',
      },
    },
   'ClipPath' => 'Обтравочный контур',
   'ClippingLimit' => 'Ограничения на копирование',
   'ClippingPathName' => 'Название обтравочного контура',
   'CodePage' => {
      Description => 'Кодовая страница',
      PrintConv => {
        'Windows Latin 1 (Western European)' => 'Windows Latin 1 (Западноевропейская)',
      },
    },
   'CodecFlavorID' => 'ID кодека Flavor',
   'CodedCharacterSet' => 'Кодовый набор символов',
   'CodedFrameSize' => 'Размер кодированного кадра',
   'CodestreamRegistration' => 'Регистрация кодового потока',
   'CodingMethods' => {
      Description => 'Метод сжатия',
      PrintConv => {
        'Baseline JPEG' => 'Базовое JPEG',
        'JBIG color' => 'Цветное JBIG',
        'Modified Huffman' => 'Одномерное – Модификация Хаффмана',
        'Modified MR' => 'Двумерное – Modified Modified READ',
        'Modified Read' => 'Двумерное – Modified READ',
        'Unspecified compression' => 'Неизвестное сжатие',
      },
    },
   'Collection' => 'Коллекция',
   'CollectionName' => 'Название коллекции',
   'CollectionURI' => 'URI коллекции',
   'Collections' => 'Коллекции',
   'ColorAdjustmentMode' => {
      Description => 'Режим регулировки цвета',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'ColorCalibrationMatrix' => 'Матрица калибровки цвета',
   'ColorCharacterization' => 'Спецификации цвета',
   'ColorClass' => {
      Description => 'Цветовой класс',
      PrintConv => {
        '0 (None)' => '0 (Не указан)',
        '1 (Winner)' => '1 (Лучшее)',
        '2 (Winner alt)' => '2 (Лучшее Alt)',
        '3 (Superior)' => '3 (Хорошее)',
        '4 (Superior alt)' => '4 (Хорошее Alt)',
        '5 (Typical)' => '5 (Обычноеl)',
        '6 (Typical alt)' => '6 (Обычное Alt)',
        '7 (Extras)' => '7 (Экстра)',
        '8 (Trash)' => '8 (Хлам)',
      },
    },
   'ColorComponents' => 'Цветовых компонентов',
   'ColorCorrection' => 'Коррекция цвета',
   'ColorEffect' => {
      PrintConv => {
        'Sepia' => 'Сепия',
      },
    },
   'ColorFilter' => 'Цветовой фильтр',
   'ColorGroup' => 'Цветовая группа',
   'ColorHalftoningInfo' => 'Сведения о тонировании – Цветное',
   'ColorMap' => 'Карта цветов',
   'ColorMatrix1' => 'Матрица преобразования цветов 1',
   'ColorMatrix2' => 'Матрица преобразования цветов 2',
   'ColorMode' => {
      Description => 'Цветовая модель',
      PrintConv => {
        'Autumn Leaves' => 'Осенние листья',
        'B&W' => 'Чёрно-белое',
        'Bitmap' => 'Монохромная',
        'Black & White' => 'Чёрно-белый',
        'Clear' => 'Прозрачный',
        'Color Palette' => 'Цветовая палитра',
        'Deep' => 'Глубокий',
        'Duotone' => 'Двуцветная',
        'Embed Adobe RGB' => 'Встроенный Adobe RGB',
        'Evening' => 'Вечер',
        'Grayscale' => 'В градациях серого',
        'Indexed' => 'Индексированные цвета',
        'Indexed Color' => 'Индексированный цвет',
        'Landscape' => 'Пейзаж',
        'Light' => 'Бледный',
        'Multichannel' => 'Многоканальная',
        'Natural' => 'Натуральный',
        'Natural color' => 'Натуральный цвет',
        'Natural sRGB' => 'Натуральный sRGB',
        'Natural+ sRGB' => 'Натуральный+ sRGB',
        'Neutral' => 'Нейтральный',
        'Night Portrait' => 'Ночной портрет',
        'Night Scene' => 'Ночная сцена',
        'Night View' => 'Ночной вид',
        'Night View/Portrait' => 'Ночной вид/портрет',
        'Portrait' => 'Портрет',
        'RGB Color' => 'Цвет RGB',
        'Sepia' => 'Сепия',
        'Solarization' => 'Соляризация',
        'Standard' => 'Стандартный',
        'Sunset' => 'Закат',
        'Vivid' => 'Яркий цвет',
        'Vivid color' => 'Яркий цвет',
        'n/a' => 'Нет данных',
      },
    },
   'ColorNoiseReduction' => 'Щумодав – Уменьшение цветных шумов',
   'ColorPalette' => 'Цветовая палитра',
   'ColorPlanes' => 'Цветовые плоскости',
   'ColorPrimaries' => 'Праймериз цвета',
   'ColorRepresentation' => {
      Description => 'Цветовое представление',
      PrintConv => {
        '3 Components, Frame Sequential in Multiple Objects' => '3 компонента. Последовательность кадров в нескольких объектах',
        '3 Components, Frame Sequential in One Object' => '3 компонента. Последовательность кадров в одном объекте',
        '3 Components, Line Sequential' => '3 компонента. Последовательная строка',
        '3 Components, Pixel Sequential' => '3 компонента. Последовательность пикселей',
        '3 Components, Single Frame' => '3 компонента. Один кадр',
        '3 Components, Special Interleaving' => '3 компонента. Специальное чередование',
        '4 Components, Frame Sequential in Multiple Objects' => '4 компонента. Последовательность кадров в нескольких объектах',
        '4 Components, Frame Sequential in One Object' => '4 компонента. Последовательность кадров в одном объекте',
        '4 Components, Line Sequential' => '4 компонента. Последовательная строка',
        '4 Components, Pixel Sequential' => '4 компонента. Последовательность пикселей',
        '4 Components, Single Frame' => '4 компонента. Один кадр',
        '4 Components, Special Interleaving' => '4 компонента. Специальное чередование',
        'Monochrome, Single Frame' => 'Монохромное. Один кадр',
        'No Image, Single Frame' => 'Нет изображения. Один кадр',
      },
    },
   'ColorResolutionDepth' => 'Глубина цвета',
   'ColorResponseUnit' => 'Единицы цветовой чувствительности',
   'ColorSamplersResource' => 'Ресурс бразцов цвета',
   'ColorSamplersResource2' => 'Ресурс бразцов цвета 2',
   'ColorSequence' => 'Последовательность цветов',
   'ColorSpace' => {
      Description => 'Цветовое пространство',
      PrintConv => {
        'BT 2020 Constant Luminance' => 'Постоянная яркость BT 2020',
        'Calibrated RGB' => 'Калиброванный RGB',
        'Device CMYK' => 'Устройство CMYK',
        'Device RGB' => 'Устройство RGB',
        'Embedded Color Profile' => 'Встроенный цветовой профиль',
        'Grayscale' => 'Оттенки серого',
        'ICC Profile' => 'ICC-профиль',
        'Linked Color Profile' => 'Связанный цветовой профиль',
        'Uncalibrated' => 'Некалиброванное',
        'Undefined' => 'Не определено',
        'Windows Color Space' => 'Цветовое пространство Windows',
      },
    },
   'ColorSpecApproximation' => {
      Description => 'Спецификация цвета – Аппроксимация',
      PrintConv => {
        'Accurate' => 'Точная',
        'Exceptional Quality' => 'Лучшее качество',
        'Not Specified' => 'Не указана',
        'Poor Quality' => 'Низкое качество',
        'Reasonable Quality' => 'Приемлемое качество',
      },
    },
   'ColorSpecData' => 'Спецификация цвета – Данные',
   'ColorSpecMethod' => {
      Description => 'Спецификация цвета – Метод',
      PrintConv => {
        'Any ICC' => 'Любой ICC',
        'Enumerated' => 'Перечислимый',
        'Restricted ICC' => 'Ограниченный ICC',
        'Vendor Color' => 'Цвет поставщика',
      },
    },
   'ColorSpecPrecedence' => 'Спецификация цвета – Precedence',
   'ColorTable' => 'Таблица цвета',
   'ColorTemperature' => 'Цветовая температура',
   'ColorTemperatureAdj' => 'Регулировка температуры цвета',
   'ColorTemperatures' => 'Цветовая температура',
   'ColorTransferFuncs' => 'Функция переноса – Цветное',
   'ColorType' => {
      Description => 'Тип цвета',
      PrintConv => {
        'Grayscale' => 'Градация серого',
        'Grayscale with Alpha' => 'Градация серого с Альфа-каналом',
        'Palette' => 'Палитра',
        'RGB with Alpha' => 'RGB с Альфа-каналом',
      },
    },
   'ColorimetricReference' => 'Колориметрический эталон',
   'Colorimetry' => 'Колориметрия',
   'Colors' => 'Цвета',
   'Command' => 'Команда',
   'CommandLineArguments' => 'Аргументы командной строки',
   'Comment' => 'Комментарий',
   'CommentLen' => 'Комментарий Len',
   'CommentTime' => 'Время комментирования',
   'Comments' => 'Комментарии',
   'CommonNetworkRelLink' => 'Общая сеть Rel Link',
   'CommonPathSuffix' => 'Суффикс общего пути',
   'Company' => 'Организация',
   'CompatibleBrands' => 'Совместимые марки',
   'CompatibleFontName' => 'Название совместимого шрифта',
   'ComponentDefinition' => 'Определение компонента',
   'ComponentMapping' => 'Сопоставление компонентов',
   'ComponentsConfiguration' => {
      Description => 'Конфигурация компонентов',
      PrintConv => {
        'Alpha (matte)' => 'Альфа-канал (маска)',
        'Alpha, B, G, R' => 'Альфа-канал, B, G, R',
        'Blue (B)' => 'Синий (B)',
        'Chrominance (Cb, Cr, subsampled by two)' => 'Цветность (Cb, Cr, субдискретизация на два)',
        'Composite video' => 'Композитное видео',
        'Depth (Z)' => 'Глубина (Z)',
        'Green (G)' => 'Зелёный (G)',
        'Luminance (Y)' => 'Яркость (Y)',
        'R, G, B, Alpha' => 'R, G, B, Альфа-канал',
        'Red (R)' => 'Красный (R)',
        'User-defined 2 component element' => '2-компонентный элемент определяемый пользователем',
        'User-defined 3 component element' => '3-компонентный элемент определяемый пользователем',
        'User-defined 4 component element' => '4-компонентный элемент определяемый пользователем',
        'User-defined 5 component element' => '5-компонентный элемент определяемый пользователем',
        'User-defined 6 component element' => '6-компонентный элемент определяемый пользователем',
        'User-defined 7 component element' => '7-компонентный элемент определяемый пользователем',
        'User-defined 8 component element' => '8-компонентный элемент определяемый пользователем',
        'User-defined single component' => 'Один компонент определяемый пользователем',
      },
    },
   'Composer' => 'Композитор',
   'CompositeImage' => {
      Description => 'Составное изображение',
      PrintConv => {
        'Composite Image Captured While Shooting' => 'Полученное во время съёмки',
        'General Composite Image' => 'Созданное в редакторе',
        'Not a Composite Image' => 'Нет',
        'Unknown' => 'Геизвестно',
      },
    },
   'CompositeImageCount' => 'Составное изображение – Количество',
   'CompositeImageExposureTimes' => 'Составное изображение – Выдержка',
   'Composition' => 'Композиция',
   'CompositionOptions' => 'Варианты композиции',
   'Compressed' => {
      Description => 'Сжатый',
      PrintConv => {
        'False' => 'Нет',
        'True' => 'Да',
      },
    },
   'CompressedBitsPerPixel' => 'Сжатых бит на пиксель',
   'CompressedSize' => 'Сжатый размер',
   'Compression' => {
      Description => 'Метод сжатия',
      PrintConv => {
        '4-Bit RLE' => '4-битное RLE',
        '8-Bit RLE' => '8-битное RLE',
        'Bitfields' => 'Битовые поля',
        'Fractal' => 'Фрактальное',
        'JPEG' => 'JPEG сжатие',
        'JPEG (old-style)' => 'JPEG (старый стиль)',
        'Modified Huffman' => 'Модифицированное кодирование Хаффмана',
        'Modified Modified READ' => 'Modified Modified READ (MMR)',
        'Modified READ' => 'Modified READ (MR)',
        'None' => 'Без сжатия',
        'RLE Encoding' => 'Кодирование RLE',
        'Uncompressed' => 'Несжатый',
        'ZIP with prediction' => 'ZIP с предсказанием',
        'ZIP without prediction' => 'ZIP без предсказания',
      },
    },
   'CompressionFactor' => 'Коэффициент сжатия',
   'CompressionLevel' => 'Уровень сжатия',
   'CompressionType' => 'Тип сжатия',
   'CompressorName' => 'Название компрессора',
   'ConditionalFEC' => 'Компенсация экспозиции при съёмке со вспышкой',
   'Confidence' => 'Конфидециальность',
   'ConfidenceLevel' => 'Интервал распознавания лица',
   'ConfidenceMime' => 'MIME-тип конфидециального изображения',
   'ConfirmedObjectSize' => 'Подтверждённый размер объекта',
   'ConsecutiveBadFaxLines' => 'Количество последовательных битых строк',
   'ConstrainedCropHeight' => 'Пропорциональная обрезка – Высота',
   'ConstrainedCropWidth' => 'Пропорциональная обрезка – Ширина',
   'Contact' => 'Контакт',
   'ContainerVersion' => 'Версия контейнера',
   'ContentDisposition' => 'Распоряжение содержимым',
   'ContentIdentifier' => 'Идентификатор контента',
   'ContentLanguage' => 'Язык контента',
   'ContentLocationCode' => 'Контент – Код страны',
   'ContentLocationName' => 'Контент – Страна',
   'ContentProtected' => {
      Description => 'Контент защищен',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'ContentProtectedPercent' => 'Контент защищен – Процент',
   'ContentRating' => {
      Description => 'Возрастной рейтинг контента',
      PrintConv => {
        'Adult Supervision Recommended' => 'Под наблюдением взрослых',
        'Adults Only' => 'Только для взрослых',
        'All Ages' => 'Для любого возраста',
        'No Rating' => 'Возрастная группа не указана',
        'Older Children' => 'Старшим детям',
        'Older Teens' => 'Старшим подросткам',
        'Younger Teens' => 'Младшим подросткам',
      },
    },
   'ContentScriptType' => 'Язык программирования сценариев',
   'ContentStyleType' => 'Язык таблицы стилей',
   'ContentType' => 'MIME-тип документа',
   'ContiguousCodestream' => 'Смежный кодовый поток',
   'Contrast' => {
      Description => 'Контрастность',
      PrintConv => {
        'High' => 'Высокая',
        'Low' => 'Низкая',
        'Normal' => 'Стандартная',
      },
    },
   'ContrastAdj' => 'Регулировка контраста',
   'Contributor' => 'Соавтор',
   'Controller' => 'Контроллер',
   'ConvergenceAngle' => 'Угол схождения',
   'ConvergenceBaseImage' => {
      Description => 'Сходимость базового изображения',
      PrintConv => {
        'Equivalent for Both Viewpoints' => 'Эквивалент для обеих точек просмотра',
        'Left Viewpoint' => 'Левая точка просмотра',
        'Right Viewpoint' => 'Правая точка просмотра',
      },
    },
   'ConvergenceDistance' => 'Расстояние сходимости',
   'Converter' => 'Конвертер',
   'Copyright' => 'Авторское право',
   'CopyrightFileName' => 'Название файла авторского права',
   'CopyrightFlag' => {
      Description => 'Флаг авторского права',
      PrintConv => {
        'False' => 'Нет',
        'True' => 'Есть',
      },
    },
   'CopyrightLen' => 'Авторское право Len',
   'CopyrightNotice' => 'Авторское право',
   'CopyrightOwner' => 'Правообладатель',
   'CopyrightOwnerID' => 'PLUS-ID правообладателя',
   'CopyrightOwnerImageID' => 'ID изображения присвоенный правообладателем',
   'CopyrightOwnerName' => 'Имя правообладателя',
   'CopyrightRegistrationNumber' => 'Регистрационный номер авторского права',
   'CopyrightStatus' => {
      Description => 'Статус изображения',
      PrintConv => {
        'Protected' => 'Защищено',
        'Public Domain' => 'Всеобщее достояние',
        'Unknown' => 'Не указано',
      },
    },
   'CountInfo' => 'Информация о подщёте',
   'Country' => 'Страна',
   'Country-PrimaryLocationCode' => 'Код страны',
   'Country-PrimaryLocationName' => 'Страна',
   'CountryCode' => 'Код страны',
   'CoverArt' => 'Обложка',
   'CoverArtMIMEType' => 'MIME тип обложки',
   'CoverArtType' => 'Тип обложки',
   'Coverage' => 'Охват',
   'CreateDate' => 'Дата оцифровки',
   'CreatedBy' => 'Создан',
   'CreationDate' => 'Дата создания',
   'CreationTime' => 'Время создания',
   'CreativeStyleWasChanged' => {
      Description => 'Творческий стиль был изменён',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'Creator' => 'Создатель',
   'CreatorAppID' => {
      Description => 'ID приложения создавшего файл',
      PrintConv => {
        'Unknown' => 'Неизвестное',
      },
    },
   'CreatorAppVersion' => 'Версия приложения',
   'CreatorBuildNumber' => 'Приложение – Номер сборки',
   'CreatorBuildNumber2' => 'Приложение – Номер сборки 2',
   'CreatorMajorVersion' => 'Приложение – Мажорная версия',
   'CreatorMinorVersion' => 'Приложение – Минорная версия',
   'CreatorOpenWithUIOptions' => 'Открыт с параметрами пользовательского интерфейса',
   'CreatorSoftware' => 'Создан в приложении',
   'CreatorTool' => 'Создано в',
   'CreatorVersion' => 'Версия исходного приложения',
   'Credit' => 'Поставщик',
   'CreditLineRequired' => {
      Description => 'Политика кредитования',
      PrintConv => {
        'Credit Adjacent To Image' => 'Кредит рядом с изображением',
        'Credit in Credits Area' => 'Кредит в области кредитов',
        'Credit on Image' => 'Кредит на изображение',
        'Not Required' => 'Кредит не требуется',
      },
    },
   'CropArea' => 'Область кадрирования',
   'CropBottom' => 'Обрезка снизу',
   'CropH' => 'Высота кадрирования',
   'CropLeft' => 'Обрезка слева',
   'CropRight' => 'Обрезка справа',
   'CropRotation' => 'Поворот рамки кадрирования',
   'CropTop' => 'Обрезка сверху',
   'CropW' => 'Ширина кадрирования',
   'CropX' => 'Координаты кадрирования по оси X',
   'CropXCommonOffset' => {
      Description => 'Обрезка по X – Общее смещение',
      PrintConv => {
        'Common Offset Setting' => 'Общая настройка смещения',
        'Individual Offset Setting' => 'Индивидуальная настройка смещения',
      },
    },
   'CropXOffset' => 'Обрезка по X – Смещение',
   'CropXOffset2' => 'Обрезка по X – Смещение 2',
   'CropXSize' => 'Размер обрезки по X',
   'CropXViewpointNumber' => 'Обрезка по X – Номер точки просмотра',
   'CropXViewpointNumber2' => 'Обрезка по X – Номер точки просмотра 2',
   'CropY' => 'Координаты кадрирования по оси Y',
   'CropYCommonOffset' => {
      Description => 'Обрезка по Y – Общее смещение',
      PrintConv => {
        'Common Offset Setting' => 'Общая настройка смещения',
        'Individual Offset Setting' => 'Индивидуальная настройка смещения',
      },
    },
   'CropYOffset' => 'Обрезка по Y – Смещение',
   'CropYOffset2' => 'Обрезка по Y – Смещение 2',
   'CropYSize' => 'Размер обрезки по Y',
   'CropYViewpointNumber' => 'Обрезка по Y – Номер точки просмотра',
   'CropYViewpointNumber2' => 'Обрезка по Y – Номер точки просмотра 2',
   'CroppedAreaImageHeightPixels' => 'Исходная высота изображения в пикселях',
   'CroppedAreaImageWidthPixels' => 'Исходная ширина изображения в пикселях',
   'CroppedAreaLeftPixels' => 'Обрезанная область слева в пикселях',
   'CroppedAreaTopPixels' => 'Обрезанная область сверху в пикселях',
   'Cropping' => {
      Description => 'Кадрирование',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'Cross-Reference' => 'Перекрестная ссылка',
   'CueSheet' => 'Файл разметки (Cue Sheet)',
   'CurrentICCProfile' => 'Текущий ICC-профиль',
   'CurrentIPTCDigest' => 'Хеш сумма текущего IPTC',
   'CurrentPreProfileMatrix' => 'Текущий предварительный профиль матрицы',
   'CurrentVersion' => 'Текущая версия',
   'CursorSize' => 'Размер курсора',
   'Curve0x' => 'Кривая 0x',
   'Curve0y' => 'Кривая 0y',
   'Curve1x' => 'Кривая 1x',
   'Curve1y' => 'Кривая 1y',
   'Curve2x' => 'Кривая 2x',
   'Curve2y' => 'Кривая 2y',
   'Curve3x' => 'Кривая 3x',
   'Curve3y' => 'Кривая 3y',
   'Curve4x' => 'Кривая 4x',
   'Curve4y' => 'Кривая 4y',
   'Custom1' => 'Дополнительное поле 1',
   'Custom10' => 'Дополнительное поле 10',
   'Custom2' => 'Дополнительное поле 2',
   'Custom3' => 'Дополнительное поле 3',
   'Custom4' => 'Дополнительное поле 4',
   'Custom5' => 'Дополнительное поле 5',
   'Custom6' => 'Дополнительное поле 6',
   'Custom7' => 'Дополнительное поле 7',
   'Custom8' => 'Дополнительное поле 8',
   'Custom9' => 'Дополнительное поле 9',
   'CustomRendered' => {
      Description => 'Пользовательский рендеринг',
      PrintConv => {
        'Custom' => 'Пользовательский',
        'Normal' => 'Обычный',
        'Panorama' => 'Панорама',
        'Portrait' => 'Портрет',
      },
    },
   'D-RangeOptimizerHighlight' => 'Оптимизация динамического диапазона – Света',
   'D-RangeOptimizerMode' => {
      Description => 'Оптимизация динамического диапазона – Режим',
      PrintConv => {
        'Auto' => 'Автоматический',
        'Manual' => 'Ручной',
        'Off' => 'Не включён',
      },
    },
   'D-RangeOptimizerShadow' => 'Оптимизация динамического диапазона – Тени',
   'D-RangeOptimizerValue' => 'Оптимизация динамического диапазона – Значение',
   'DCContinent' => 'DC – Континент',
   'DCCoordinatePrecision' => 'DC – Точность координат',
   'DCCoordinateUncertaintyInMeters' => 'DC – Погрешность определения координат (м)',
   'DCCountry' => 'DC – Страна',
   'DCCountryCode' => 'DC – Код страны',
   'DCCounty' => 'DC – Район',
   'DCDecimalLatitude' => 'DC – Географическая широта места находки',
   'DCDecimalLongitude' => 'DC – Географическая долгота места находки',
   'DCEvent' => 'Событие/Явление',
   'DCFootprintSRS' => 'DC – Footprint SRS',
   'DCFootprintSpatialFit' => 'DC – Footprint Spatial Fit',
   'DCFootprintWKT' => 'DC – Footprint WKT',
   'DCGeodeticDatum' => 'DC – Географическая система координат',
   'DCGeoreferenceProtocol' => 'DC – Описание метода определения координат',
   'DCGeoreferenceRemarks' => 'DC – Комментарии к геопривязке',
   'DCGeoreferenceSources' => 'DC – Источники геопривязки',
   'DCGeoreferenceVerificationStatus' => 'DC – Статус проверки геопривязки',
   'DCGeoreferencedBy' => 'DC – Люди создавшие геопривязку',
   'DCGeoreferencedDate' => 'DC – Дата геопривязки',
   'DCHigherGeography' => 'DC – Крупные географические объекты',
   'DCHigherGeographyID' => 'DC – ID географического региона',
   'DCIsland' => 'DC – Остров',
   'DCIslandGroup' => 'DC – Группа островов',
   'DCLocality' => 'DC – Описание места находки',
   'DCLocationAccordingTo' => 'DC – Источник данных о местоположении',
   'DCLocationID' => 'DC – ID набора данных о местоположении',
   'DCLocationRemarks' => 'DC – Комментарии к описанию местоположения',
   'DCMaximumDepthInMeters' => 'DC – Максимальная глубина (м)',
   'DCMaximumDistanceAboveSurfaceInMeters' => 'DC – Максимальное дистанция над поверхностью (м)',
   'DCMaximumElevationInMeters' => 'DC – Максимальное высота (м)',
   'DCMinimumDepthInMeters' => 'DC – Минимальная глубина (м)',
   'DCMinimumDistanceAboveSurfaceInMeters' => 'DC – Минимальное дистанция над поверхностью (м)',
   'DCMinimumElevationInMeters' => 'DC – Минимальная высота (м)',
   'DCMunicipality' => 'DC – Район',
   'DCPointRadiusSpatialFit' => 'DC – Point Radius Spatial Fit',
   'DCStateProvince' => 'DC – Область',
   'DCTermsLocation' => 'DC – Местоположение',
   'DCVerbatimCoordinateSystem' => 'DC – Система координат по первоисточнику',
   'DCVerbatimCoordinates' => 'DC – Координаты места находки по первоисточнику',
   'DCVerbatimDepth' => 'DC – Описание глубины по первоисточнику',
   'DCVerbatimElevation' => 'DC – Описание высоты по первоисточнику',
   'DCVerbatimLatitude' => 'DC – Широта местоположения по первоисточнику',
   'DCVerbatimLocality' => 'DC – Описание места по первоисточнику',
   'DCVerbatimLongitude' => 'DC – Долгота местоположения по первоисточнику',
   'DCVerbatimSRS' => 'DC – SRS по первоисточнику',
   'DCWaterBody' => 'DC – Водоём',
   'DNGAdobeData' => 'DNG – Данные Adobe',
   'DNGBackwardVersion' => 'Совместимая версия DNG',
   'DNGLensInfo' => 'DNG – Сведения об объективе',
   'DNGPrivateData' => 'DNG – Данные производителя',
   'DNGVersion' => 'Версия DNG',
   'DOF' => 'Глубина резкости',
   'DPP' => 'Digital Photo Professional (DPP)',
   'DPXFileSize' => 'Размер файла DPX',
   'DRMCommerceID' => 'DRM – Коммерческий ID',
   'DRMServerID' => 'DRM – ID сервера',
   'DRM_E-BookBaseID' => 'DRM – ID базы электронных книг',
   'DTVContent' => {
      Description => 'Контент DTV',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'DataCompressionMethod' => 'Метод сжатия данных',
   'DataDump' => 'Вывод данных',
   'DataLocation' => {
      Description => 'Расположение данных',
      PrintConv => {
        'Downloaded Separately' => 'Скачивается отдельно',
        'Local Music File' => 'Локальный музыкальный файл',
      },
    },
   'DataOffset' => 'Смещение данных',
   'DataOffsets' => 'Смещения данных',
   'DataPreparer' => 'Подготовка данных',
   'DataReference' => 'Ссылка на данные',
   'DataSign' => {
      Description => 'Подпись данных',
      PrintConv => {
        'Signed' => 'Подписан',
        'Unsigned' => 'Неподписан',
      },
    },
   'DataSize' => 'Размер данных',
   'DataType' => {
      Description => 'Тип данных',
      PrintConv => {
        'Artwork' => 'Обложка',
      },
    },
   'DataWindow' => 'Окно данных',
   'DatabaseName' => 'Название базы данных',
   'Date' => 'Дата',
   'DateAcquired' => 'Дата приобретения',
   'DateCreated' => 'Дата создания',
   'DateIdentified' => 'Дата определения',
   'DateSent' => 'Дата отправления',
   'DateTime' => 'Дата и время',
   'DateTimeCompleted' => 'Дата завершения',
   'DateTimeCreated' => 'Дата и время создания',
   'DateTimeDue' => 'Срок выполнения задачи',
   'DateTimeEnd' => 'Дата окончания события',
   'DateTimeOriginal' => 'Дата съёмки',
   'DateTimeStamp' => 'Дата создания события',
   'DateTimeStart' => 'Дата начала события',
   'DayOfWeek' => {
      Description => 'День недели',
      PrintConv => {
        'Friday' => 'Пятница',
        'Monday' => 'Понедельник',
        'Saturday' => 'Суббота',
        'Sunday' => 'Воскресенье',
        'Thursday' => 'Четверг',
        'Tuesday' => 'Вторник',
        'Wednesday' => 'Среда',
      },
    },
   'Decode' => 'Декодирование',
   'DefaultAlarm' => 'Сигнал по-умолчанию',
   'DefaultBlackRender' => {
      Description => 'Обработка чёрного по-умолчанию',
      PrintConv => {
        'Auto' => 'Автоматическая',
        'None' => 'Не обрабатывать',
      },
    },
   'DefaultChar' => 'Символ замены',
   'DefaultCropOrigin' => 'Положение обрезанного изображения по-умолчанию',
   'DefaultCropSize' => 'Размер обрезанного изображения по-умолчанию',
   'DefaultImageColor' => 'Цвет изображения по-умолчанию',
   'DefaultScale' => 'Массштаб по-умолчанию',
   'DefaultStyle' => 'Стиль по-умолчанию',
   'DefaultUserCrop' => 'Область обрезки по-умолчанию',
   'DefineQuantizationTable' => 'Определение таблиц квантования',
   'Delay' => 'Задержка',
   'DelayTime' => 'Время задержки',
   'DependentImage1EntryNumber' => 'Зависимое изображение 1 – Номер входа',
   'DependentImage2EntryNumber' => 'Зависимое изображение 2 – Номер входа',
   'Depth' => 'Глубина цвета',
   'DepthImage' => 'Глубина изображения',
   'DepthMapTiff' => 'Карта глубины Tiff',
   'Descender' => 'Подстрочный интервал',
   'Description' => 'Описание',
   'Designer' => 'Дизайнер',
   'DesignerURL' => 'URL дизайнера',
   'Destination' => 'Назначение',
   'DeviceID' => 'ID устройства',
   'DeviceName' => 'Название устройства',
   'DeviceSettingDescription' => 'Описание предустановок камеры',
   'DewarpData' => 'Компенсация оптического искажения – Данные',
   'DewarpFlag' => 'Компенсация оптического искажения – Флаг',
   'DictionaryShortName' => 'Сокращенное название словаря',
   'DigitalCreationDate' => 'Дата оцифровки файла',
   'DigitalCreationDateTime' => 'Дата и время оцифровки файла',
   'DigitalCreationTime' => 'Время оцифровки файла',
   'DigitalSignature' => 'Цифровая подпись',
   'DigitalZoom' => {
      Description => 'Цифровой зум',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'DigitalZoomOn' => {
      Description => 'Цифровой зум',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'DigitalZoomRatio' => 'Коэффициент цифрового увеличения',
   'Director' => 'Директор',
   'Directory' => 'Каталог',
   'DirectoryNumber' => 'Номер каталога',
   'DisallowCounterProposal' => 'Запретить встречное предложение',
   'Disclaimer' => 'Отказ от ответственности',
   'DisplayWindow' => 'Окно отображения',
   'DisplayXResolution' => 'Разрешение экрана по X',
   'DisplayXResolutionUnit' => {
      Description => 'Единицы разрешения экрана по X',
      PrintConv => {
        '0.01 mm' => '0.01 мм',
        '0.1 mm' => '0.1 мм',
        '10 cm' => '10 см',
        '10 m' => '10 м',
        '100 m' => '100 м',
        'cm' => 'см',
        'km' => 'км',
        'm' => 'м',
        'mm' => 'мм',
        'um' => 'мкм',
      },
    },
   'DisplayYResolution' => 'Разрешение экрана по Y',
   'DisplayYResolutionUnit' => {
      Description => 'Единицы разрешения экрана по Y',
      PrintConv => {
        '0.01 mm' => '0.01 мм',
        '0.1 mm' => '0.1 мм',
        '10 cm' => '10 см',
        '10 m' => '10 м',
        '100 m' => '100 м',
        'cm' => 'см',
        'km' => 'км',
        'm' => 'м',
        'mm' => 'мм',
        'um' => 'мкм',
      },
    },
   'DisplayedUnitsX' => {
      Description => 'Отображаемые единицы по X',
      PrintConv => {
        'cm' => 'Сантиметры',
        'inches' => 'Дюймы',
      },
    },
   'DisplayedUnitsY' => {
      Description => 'Отображаемые единицы по Y',
      PrintConv => {
        'cm' => 'Сантиметры',
        'inches' => 'Дюймы',
      },
    },
   'Dispose' => 'Управление слоями',
   'DistortionCompensation' => {
      Description => 'Компенсация дисторсии',
      PrintConv => {
        'Off' => 'Не включена',
        'On' => 'Включена',
        'n/a' => 'Нет данных',
      },
    },
   'DistortionCorrParams' => 'Параметры коррекции дисторсии',
   'DistortionCorrection' => {
      Description => 'Коррекция дисторсии',
      PrintConv => {
        'Auto' => 'Автоматическая',
        'Auto fixed by lens' => 'Автоматическая по объективу',
        'No correction params available' => 'Не доступна',
        'Off' => 'Не включена',
      },
    },
   'DistortionCorrectionAlreadyApplied' => 'Применена коррекция дисторсии',
   'Distribution' => 'Описание',
   'DittoKey' => {
      Description => 'Ключь Ditto',
      PrintConv => {
        'New' => 'Заново',
        'Same' => 'Так же',
      },
    },
   'DjVuVersion' => 'Версия DjVu',
   'DoNotForwardMeeting' => 'Не передвигать встречу',
   'DocClass' => 'Состояние документа',
   'DocRights' => 'Статус авторского права',
   'DocType' => 'Тип документа',
   'Document' => 'Документ',
   'DocumentAncestors' => 'Предки документа',
   'DocumentHistory' => 'История документа',
   'DocumentName' => 'Название документа',
   'DocumentNotes' => 'Комментарии к документу',
   'DocumentUsageRights' => 'Права на использование Документа',
   'DotRange' => 'Значения компонента соответствующих точке',
   'DriveMode' => {
      Description => 'Режим спуска',
      PrintConv => {
        'Continuous Shooting' => 'Серийная съёмка',
        'Self-timer Operation' => 'Съёмка с автоспуском',
        'Single-frame Shooting' => 'Покадровая съёмка',
      },
    },
   'DriveSerialNumber' => 'Серийный номер привода',
   'DriveType' => {
      Description => 'Тип накопителя',
      PrintConv => {
        'Fixed Disk' => 'Установленный диск',
        'Invalid Root Path' => 'Неверный корневой путь',
        'Ram Disk' => 'Ram диск',
        'Remote Drive' => 'Удаленный диск',
        'Removable Media' => 'Съемный диск',
        'Unknown' => 'Неизвестный',
      },
    },
   'DuotoneHalftoningInfo' => 'Сведения о тонировании – Двуцветное',
   'DuotoneImageInfo' => 'Сведения о двухцветном изображении',
   'DuotoneTransferFuncs' => 'Функция переноса – Двуцветное',
   'Duration' => 'Продолжительность',
   'DynamicRangeOptimizer' => {
      Description => 'Оптимизация динамического диапазона',
      PrintConv => {
        'Advanced Auto' => 'Расширенная. Автоматическая',
        'Advanced Lv1' => 'Расширенная. Уровень 1',
        'Advanced Lv2' => 'Расширенная. Уровень 2',
        'Advanced Lv3' => 'Расширенная. Уровень 3',
        'Advanced Lv4' => 'Расширенная. Уровень 4',
        'Advanced Lv5' => 'Расширенная. Уровень 5',
        'Auto' => 'Автоматическая',
        'Off' => 'Не включена',
        'Standard' => 'Стандартная',
      },
    },
   'EPSOptions' => 'Параметры EPS',
   'EXRVersion' => 'Версия EXR',
   'EarliestAgeOrLowestStage' => 'Самый ранний век/Низший ярус',
   'EarliestEonOrLowestEonothem' => 'Самый ранний эон/Низшая эонотема',
   'EarliestEpochOrLowestSeries' => 'Самая ранняя эпоха/Низший отдел',
   'EarliestEraOrLowestErathem' => 'Самая ранняя эра/Низшая эратема',
   'EarliestPeriodOrLowestSystem' => 'Самый ранний период/Низшая система',
   'EdgeNoiseReduction' => 'Щумоподавление – Уменьшение контурных шумов',
   'EditStatus' => 'Статус редактирования',
   'Edition' => 'Издание',
   'EditorialUpdate' => {
      Description => 'Тип обновления',
      PrintConv => {
        'Additional language' => 'Дополнительный язык',
      },
    },
   'EffectiveBW' => 'Эффективный Чёрно-Белый',
   'EffectsVisible' => 'Видимость эффектов',
   'ElectronicImageStabilization' => 'Электронная стабилизация изображения',
   'EmbeddedFileName' => 'Название встроенного файла',
   'EmbeddedFileUsageRights' => 'Права на использование встроенных файлов',
   'EmbeddedImage' => 'Встроенное изображение',
   'EmbeddedImageColorSpace' => 'Встроенное изображение – Цветовое пространство',
   'EmbeddedImageFilter' => 'Встроенное изображение – Фильтр',
   'EmbeddedImageHeight' => 'Встроенное изображение – Высота',
   'EmbeddedImageWidth' => 'Встроенное изображение – Ширина',
   'EmbeddedJPG' => 'Встроенный JPG',
   'EmbeddedPNG' => 'Встроенный PNG',
   'EmbeddedVideo' => 'Встроенное видео',
   'EmbeddedXMPDigest' => 'Хеш-сумма встроенного XMP',
   'Emphasis' => {
      Description => 'Акцент',
      PrintConv => {
        'None' => 'Нет',
      },
    },
   'EncodeTime' => 'Время кодирования',
   'EncodedBy' => 'Закодировал',
   'EncodedUsing' => 'Закодировано с использованием',
   'Encoder' => 'Кодер',
   'EncoderOptions' => 'Настройки кодера',
   'EncoderVersion' => 'Версия кодировщика',
   'Encoding' => 'Кодирование',
   'EncodingProcess' => {
      Description => 'Процесс кодирования',
      PrintConv => {
        'Baseline DCT, Huffman coding' => 'Базовое DCT, кодирование Хаффмана',
        'Extended sequential DCT, Huffman coding' => 'Расширенное последовательное DCT, кодирование Хаффмана',
        'Extended sequential DCT, arithmetic coding' => 'Расширенное последовательное DCT, арифметическое кодирование',
        'Lossless, Differential Huffman coding' => 'Без потерь, дифференциальное кодирование Хаффмана',
        'Lossless, Huffman coding' => 'Без потерь, кодирование Хаффмана',
        'Lossless, arithmetic coding' => 'Без потерь, арифметическое кодирование',
        'Lossless, differential arithmetic coding' => 'Без потерь, дифференциальное арифметическое кодирование',
        'Progressive DCT, Huffman coding' => 'Прогрессивное DCT, кодирование Хаффмана',
        'Progressive DCT, arithmetic coding' => 'Прогрессивное DCT, арифметическое кодирование',
        'Progressive DCT, differential Huffman coding' => 'Прогрессивное DCT, дифференциальное кодирование Хаффмана',
        'Progressive DCT, differential arithmetic coding' => 'Прогрессивное DCT, дифференциальное арифметическое кодирование',
        'Sequential DCT, differential Huffman coding' => 'Последовательное DCT, Дифференциальное кодирование Хаффмана',
        'Sequential DCT, differential arithmetic coding' => 'Последовательное DCT, дифференциальное арифметическое кодирование',
      },
    },
   'EncodingScheme' => 'Схема кодирования',
   'Encryption' => {
      Description => 'Шифрование',
      PrintConv => {
        'None' => 'Нет',
        'Old Mobipocket' => 'Старый Mobipocket',
      },
    },
   'EncryptionKey' => 'Ключ шифрования',
   'EndPoints' => 'Конечные точки',
   'EndTime' => 'Время окончания',
   'EndUser' => 'Конечный пользователь',
   'EndUserID' => 'PLUS-ID пользователя',
   'EndUserName' => 'Имя пользователя',
   'EnvelopeNumber' => 'Номер конверта',
   'EnvelopePriority' => {
      Description => 'Приоритет конверта',
      PrintConv => {
        '0 (reserved)' => '0 (В резерве)',
        '1 (most urgent)' => '1 (Неотложное)',
        '5 (normal urgency)' => '5 (Обычное)',
        '8 (least urgent)' => '8 (Не срочное)',
        '9 (user-defined priority)' => '9 (Пользовательский приоритет)',
      },
    },
   'EnvelopeRecordVersion' => 'Версия Envelope Record',
   'EnvironmentMap' => {
      Description => 'Карта окружения',
      PrintConv => {
        'Cube' => 'Куб',
        'Latitude/Longitude' => 'Широта/Долгота',
      },
    },
   'EquipmentInstitution' => 'Оборудование учреждения',
   'EquipmentManufacturer' => 'Производитель оборудования',
   'Error' => 'Ошибка',
   'EscChar' => 'Escape-символ',
   'EscapeStatus' => 'Статус пробега',
   'Event' => 'Событие',
   'EventDate' => 'Дата события',
   'EventDay' => 'День(и) события',
   'EventEarliestDate' => 'Дата начала события',
   'EventEndDayOfYear' => 'День года окончания события',
   'EventFieldNotes' => 'Полевые заметки о событии',
   'EventFieldNumber' => 'Номер полевых заметок о событии',
   'EventHabitat' => 'Ареал события',
   'EventID' => 'ID события',
   'EventLatestDate' => 'Дата окончания события',
   'EventMonth' => 'Месяц события',
   'EventNumber' => 'Номер события',
   'EventParentEventID' => 'ID основного события',
   'EventRemarks' => 'Комментарии к событию',
   'EventSampleSizeUnit' => 'Событие – Единицы размера',
   'EventSampleSizeValue' => 'Событие – Размер образца',
   'EventSamplingEffort' => 'Событие – Затраченные усилия',
   'EventSamplingProtocol' => 'Событие – Метод исследования',
   'EventStartDayOfYear' => 'День года начала события',
   'EventTime' => 'Время события',
   'EventVerbatimEventDate' => 'Дата события по первоисточнику',
   'EventYear' => 'Год события',
   'ExceptionDateTimes' => 'Дата исключения',
   'ExclusiveCoverage' => 'Эксклюзивный репортаж',
   'ExcursionTolerance' => {
      Description => 'Выход за пределы диапазона',
      PrintConv => {
        'Allowed' => 'Допускается',
        'Not Allowed' => 'Не допускается',
      },
    },
   'ExifByteOrder' => {
      Description => 'Exif – Порядок байтов',
      PrintConv => {
        'Big-endian (Motorola, MM)' => 'Порядок от старшего к младшему  (Motorola, MM)',
        'Little-endian (Intel, II)' => 'Порядок от младшего к старшему (Intel, II)',
      },
    },
   'ExifCameraInfo' => 'Сведения о камере из Exif',
   'ExifImageHeight' => 'Exif – Высота изображения',
   'ExifImageWidth' => 'Exif – Ширина изображения',
   'ExifInfo2' => 'Exif – Сведения 2',
   'ExifOffset' => 'Смещение Exif Sub IFD',
   'ExifToolVersion' => 'Версия ExifTool',
   'ExifUnicodeByteOrder' => {
      Description => 'Exif – Порядок байтов Unicode',
      PrintConv => {
        'Big-endian (Motorola, MM)' => 'Порядок от старшего к младшему (Motorola, MM)',
        'Little-endian (Intel, II)' => 'Порядок от младшего к старшему (Intel, II)',
      },
    },
   'ExifVersion' => 'Версия Exif',
   'ExpandFilm' => 'Expand – Плёнка',
   'ExpandFilterLens' => 'Expand – Фильтр объектива',
   'ExpandFlashLamp' => 'Expand – Вспышка',
   'ExpandLens' => 'Expand – Объектив',
   'ExpandScanner' => 'Expand – Сканер',
   'ExpandSoftware' => 'Expand – Приложение',
   'ExpirationDate' => 'Дата истечения срока',
   'ExpirationSpan' => 'Срок действия истечения',
   'ExpirationTime' => 'Время истечения срока',
   'Expires' => 'Дата устаревания документа',
   'Exposure' => 'Экспозиция',
   'ExposureCompensation' => 'Компенсация экспозиции',
   'ExposureIndex' => 'Индекс экспозиции',
   'ExposureLockUsed' => 'Использование блокировки параметров экспозиции',
   'ExposureMode' => {
      Description => 'Режим экспозиции',
      PrintConv => {
        'Auto' => 'Автоэкспозиция',
        'Auto bracket' => 'Автобрекетинг',
        'Manual' => 'Ручная экспозиция',
      },
    },
   'ExposureProgram' => {
      Description => 'Программа экспозиции',
      PrintConv => {
        'Action (High speed)' => 'Спорт',
        'Aperture-priority AE' => 'Автоэкспозиция с приоритетом Диафрагмы',
        'Bulb' => 'Выдержка от руки',
        'Creative (Slow speed)' => 'Творческая',
        'Landscape' => 'Пейзаж',
        'Manual' => 'Ручная',
        'Not Defined' => 'Не определена',
        'Portrait' => 'Портрет',
        'Program AE' => 'Программная автоэкспозиция',
        'Shutter speed priority AE' => 'Автоэкспозиция с приоритетом Выдержки',
      },
    },
   'ExposureTime' => 'Выдержка',
   'ExposureTimes' => 'Выдержка2',
   'ExtCache' => 'Название альтернативного кэша',
   'ExtenderStatus' => {
      Description => 'Статус экстендера',
      PrintConv => {
        'Attached' => 'Прикреплен',
        'Not attached' => 'Не прикреплен',
        'Removed' => 'Удалён',
      },
    },
   'ExternalLeading' => 'Внешний зазор',
   'ExtraFlags' => {
      Description => 'Дополнительные флаги',
      PrintConv => {
        '(none)' => 'Отсутствуют',
        'Fastest Algorithm' => 'Быстрый алгоритм',
        'Maximum Compression' => 'Максимальное сжатие',
      },
    },
   'ExtraSamples' => {
      Description => 'Описание дополнительных компонентов',
      PrintConv => {
        'Associated Alpha' => 'Связаные альфа-данные',
        'Unassociated Alpha' => 'Несвязанные альфа-данные',
        'Unspecified' => 'Данные без указания',
      },
    },
   'FNumber' => 'Диафрагменное число',
   'FOV' => 'Поле зрения',
   'FSType' => 'Тип внедрения шрифта в документ',
   'FaceBalanceOrigI' => 'Баланс лица – Исходный I',
   'FaceBalanceOrigQ' => 'Баланс лица – Исходный Q',
   'FaceBalanceStrength' => 'Баланс лица – Интенсивность',
   'FaceBalanceWarmth' => 'Баланс лица – Цветовая температура',
   'FaceDetected' => 'Обнаружение лица',
   'FaceID' => 'Идентификатор лица',
   'FaceNumbers' => 'Количество лиц',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Положительное направление',
        'Rotate 180' => 'Поворот на 180°',
        'Rotate 270 CW' => 'Поворот на 270° по часовой стрелке',
        'Rotate 90 CW' => 'Поворот на 90° по часовой стрелке',
      },
    },
   'FacePosition' => 'Положение лица',
   'FacesDetected' => 'Обнаружены лица',
   'Far' => 'Дальнее значение карты глубины',
   'FastSeek' => {
      Description => 'Быстрый поиск',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'FaxProfile' => {
      Description => 'Факс-профиль',
      PrintConv => {
        'Extended B&W lossless, F' => 'Расширенный чёрно-белый без потерь, профиль F',
        'Lossless JBIG B&W, J' => 'JBIG чёрно-белый без потерь, профиль J',
        'Lossless color and grayscale, L' => 'Цветной и с оттенками серого без потерь, профиль L',
        'Lossy color and grayscale, C' => 'Цветной и с оттенками серого с потерями, профиль C',
        'Minimal B&W lossless, S' => 'Минимальный чёрно-белый без потерь, профиль S',
        'Mixed raster content, M' => 'Смешанное содержимое растра, профиль M',
        'Multi Profiles' => 'Несколько профилей',
        'Profile T' => 'Профиль T',
        'Unknown' => 'Не соответствует профилю факсимильной связи',
      },
    },
   'FaxRecvParams' => 'Параметры получения факсов',
   'FaxRecvTime' => 'Требуемое время для получения факса',
   'FaxSubAddress' => 'Подадрес факса',
   'FieldOfView' => {
      Description => 'Угол обзора',
      PrintConv => {
        'Linear' => 'Линейный',
        'Wide' => 'Широкий',
      },
    },
   'FieldPermissions' => {
      Description => 'Разрешения на изменение Полей',
      PrintConv => {
        'Allow changes to specified form fields' => 'Разрешить изменения в указанных полях формы',
        'Disallow changes to all form fields' => 'Запретить изменения во всех полях формы',
        'Disallow changes to specified form fields' => 'Запретить изменения в указанных полях формы',
      },
    },
   'File1Duration' => 'Файл 1 – Продолжительность',
   'File1Length' => 'Файл 1 – Размер',
   'File1MD5Sum' => 'Файл 1 – Сумма MD5',
   'File1Media' => 'Файл 1 – Медиа',
   'File1Path' => 'Файл 1 – Путь',
   'File1PathUTF-8' => 'Файл 1 – Путь в кодировке UTF-8',
   'FileAccessDate' => 'Дата последнего доступа к файлу',
   'FileAttributes' => {
      Description => 'Атрибуты файла',
      PrintConv => {
        'Archive' => 'Архивный',
        'BSD Whiteout' => 'BSD – Скрытый',
        'Block' => 'Блок',
        'Character' => 'Символ',
        'Compressed' => 'Сжатый',
        'Device' => 'Устройство',
        'Directory' => 'Каталог',
        'Encrypted' => 'Зашифрованный',
        'Encrypted?' => 'Зашифрованные?',
        'FIFO' => 'FIFO (именованный канал)',
        'Hidden' => 'Скрытый',
        'Mux Block' => 'Блок Mux',
        'Mux Character' => 'Символ Mux',
        'Normal' => 'Без атрибутов',
        'Not Content Indexed' => 'Не проиндексированный',
        'Not indexed' => 'Не индексированный',
        'Offline' => 'Автономный',
        'Read Only' => 'Только для чтения',
        'Read-only' => 'Только для чтения',
        'Regular' => 'Обычный',
        'Reparse Point' => 'Точки повторной обработки',
        'Reparse point' => 'Точки повторной обработки',
        'Set Group ID' => 'Установка ID группы',
        'Set User ID' => 'Установка ID пользователя',
        'Socket' => 'Сокет',
        'Solaris Door' => 'Дверь (в Solaris)',
        'Solaris Shadow Inode' => 'Теневой индекс Solaris',
        'Sparse' => 'Разрежённый',
        'Sparse File' => 'Разрежённый файл',
        'Symbolic Link' => 'Символическая ссылка',
        'System' => 'Системный',
        'Temporary' => 'Временный',
        'Unknown' => 'Неизвестный',
        'Volume' => 'Том',
        'Volume Label' => 'Метка тома',
        'VxFS Compressed' => 'Сжатый VxFS',
        'XENIX Named' => 'Именованный (в XENIX)',
      },
    },
   'FileBlockCount' => 'Количество блоков в файле',
   'FileBlockSize' => 'Размер блока в файле',
   'FileCreateDate' => 'Дата создания файла',
   'FileDeviceID' => 'ID устройства файла',
   'FileDeviceNumber' => 'Номер устройства файла',
   'FileFormat' => {
      Description => 'Формат файла',
      PrintConv => {
        'AppleSingle (Apple Computer Inc)' => 'AppleSingle (Apple)',
        'Audio Interchange File Format AIFF (Apple Computer Inc)' => 'Аудиофайл формата AIFF (Apple)',
        'Bit Mapped Graphics File [.BMP] (Microsoft)' => 'Битовый графический файл [.BMP] (Microsoft)',
        'Compressed Binary File [.ZIP] (PKWare Inc)' => 'Сжатый двоичный файл [.ZIP] (Компания PKWare)',
        'Digital Audio File [.WAV] (Microsoft & Creative Labs)' => 'Аудиофайл формата [.WAV] (Microsoft и Creative Labs)',
        'Hypertext Markup Language [.HTML] (The Internet Society)' => 'Язык гипертекстовой разметки [.HTML] (The Internet Society)',
        'IPTC Unstructured Character Oriented File Format (UCOFF)' => 'Неструктурированный символьно-ориентированный формат файла IPTC  (UCOFF)',
        'IPTC7901 Recommended Message Format' => 'IPTC7901 Рекомендуемый формат сообщения',
        'Illustrator (Adobe Graphics data)' => 'Illustrator (Графические данные Adobe)',
        'JPEG File Interchange (JFIF)' => 'Формат обмена файлами стандарта JPEG (JFIF)',
        'MPEG 2 Audio Layer 2 (Musicom), ISO/IEC' => 'Аудио второго уровня MPEG 2  (Musicom), ISO/IEC',
        'MPEG 2 Audio Layer 3, ISO/IEC' => 'Аудио третьего уровня MPEG 2 , ISO/IEC',
        'News Industry Text Format (NITF)' => 'Текстовый формат новостной индустрии (NITF)',
        'No ObjectData' => 'Данные объекта отсутствуют',
        'PC DOS/Windows Executable Files [.COM][.EXE]' => 'Исполняемый файл PC DOS/Windows [.COM][.EXE]',
        'RIFF Wave (Microsoft Corporation)' => 'RIFF Wave (Microsoft)',
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Ritzaus Bureau вариант NITF  (RBNITF DTD)',
        'Tagged Image File Format (Adobe/Aldus Image data)' => 'Формат файла изображения с тегами (Adobe/Aldus данные изображения)',
        'Tape Archive [.TAR]' => 'Формат файла архива [.TAR]',
        'Tidningarnas Telegrambyra NITF version (TTNITF DTD)' => 'Tidningarnas Telegrambyra вариант NITF (TTNITF DTD)',
        'United Press International ANPA 1312 variant' => 'United Press International  – Вариант ANPA 1312',
        'United Press International Down-Load Message' => 'United Press International – Down-Load Message',
      },
    },
   'FileGroupID' => 'ID группы файла',
   'FileHardLinks' => 'Жёсткие ссылки файла',
   'FileID' => 'ID файла',
   'FileInfoLen' => 'Сведения о файле – Len',
   'FileInfoLen2' => 'Сведения о файле – Len 2',
   'FileInfoVersion' => 'Сведения о файле – Версия',
   'FileInodeChangeDate' => 'Дата изменения файлового индекса',
   'FileInodeNumber' => 'Номер индекса файла',
   'FileModifyDate' => 'Дата редактирования файла',
   'FileName' => 'Название файла',
   'FileNameAsDelivered' => 'Название предоставленного файла',
   'FileNameLength' => 'Длина названия файла',
   'FileNumber' => 'Номер файла',
   'FilePath' => 'Путь к файлу',
   'FilePermissions' => 'Разрешения файла',
   'FileSequence' => 'Последовательность файлов',
   'FileSize' => 'Размер файла',
   'FileSizeBytes' => 'Размер файла в байтах',
   'FileSource' => {
      Description => 'Источник файла',
      PrintConv => {
        'Digital Camera' => 'Цифровая фотокамера',
        'Film Scanner' => 'Плёночный сканер',
        'Reflection Print Scanner' => 'Планшетный сканер',
        'Sigma Digital Camera' => 'Цифровая фотокамера Sigma',
      },
    },
   'FileType' => 'Тип файла',
   'FileTypeExtension' => 'Расширение файла',
   'FileUserID' => 'ID пользователя файла',
   'FileVersion' => 'Версия формата',
   'Filename' => 'Название файла',
   'Files' => 'Файлы',
   'FillAttributes' => 'Аттрибуты заполнения',
   'FillOrder' => {
      Description => 'Порядок битов в байте',
      PrintConv => {
        'Normal' => 'Обычный',
        'Reversed' => 'Обратный',
      },
    },
   'Filter' => {
      Description => 'Фильтр',
      PrintConv => {
        'Adaptive' => 'Адаптивный',
      },
    },
   'FinalFrameBlocks' => 'Блоков в последнем кадре',
   'Firmware' => 'Прошивка',
   'FirmwareDate' => 'Дата прошивки',
   'FirmwareID' => 'ID прошивки',
   'FirmwareVersion' => 'Версия прошивки',
   'FirmwareVersions' => 'Версия прошивки',
   'FirstChar' => 'Первый символ шрифта',
   'FirstPhotoDate' => 'Дата создания первого изображения',
   'FirstPublicationDate' => 'Дата первой публикации изображения',
   'FixtureIdentifier' => 'Узнаваемый идентификатор',
   'Flags' => {
      Description => 'Флаги',
      PrintConv => {
        'Allow Download' => 'Разрешить загрузку',
        'Allow Recording' => 'Разрешить запись',
        'Animation' => 'Анимация',
        'Comment' => 'Комментарий',
        'Description' => 'Описание',
        'Extension Present' => 'Настоящее время',
        'ExtraFields' => 'Дополнительное поле',
        'FileName' => 'Название файлов',
        'IDList' => 'Список ID',
        'IconFile' => 'Иконка файла',
        'Limited Range' => 'Ограниченный диапазон',
        'LinkInfo' => 'Информация о ссылке',
        'LinkToLink' => 'Ссылка на ссылку',
        'Live' => 'В прямом эфире',
        'NoLinkInfo' => 'Информация о ссылке отсутствует',
        'NoLinkPathTracking' => 'Отслеживание пути без ссылки',
        'NoLinkTrack' => 'Без ссылки на трек',
        'Perfect Play' => 'Воспроизводить после загрузки',
        'RelativePath' => 'Относительный путь',
        'RunAsUser' => 'Запуск от имени пользователя',
        'TargetMetadata' => 'Целевые метаданные',
        'Text' => 'Текст',
        'Unicode' => 'Юникод',
        'WorkingDir' => 'Рабочая папка',
      },
    },
   'Flash' => {
      Description => 'Состояние вспышки при съёмке',
      PrintConv => {
        'Auto, Did not fire' => 'Автоматический режим. Вспышка не сработала',
        'Auto, Did not fire, Red-eye reduction' => 'Автоматический режим. Вспышка не сработала. Включён режим уменьшения эффекта «красных глаз»',
        'Auto, Fired' => 'Автоматический режим. Вспышка сработала',
        'Auto, Fired, Red-eye reduction' => 'Автоматический режим. Вспышка сработала',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Автоматический режим. Вспышка сработала. Включён режим уменьшения эффекта «красных глаз». Отражённый свет обнаружен',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Автоматический режим. Вспышка сработала. Включён режим уменьшения эффекта «красных глаз». Отражённый свет не обнаружен',
        'Auto, Fired, Return detected' => 'Автоматический режим. Вспышка сработала. Отражённый свет обнаружен',
        'Auto, Fired, Return not detected' => 'Автоматический режим. Вспышка сработала. Отражённый свет не обнаружен',
        'Did not fire' => 'Вспышка не сработала',
        'Fired' => 'Вспышка сработала',
        'Fired, Red-eye reduction' => 'Вспышка сработала. Включён режим уменьшения эффекта «красных глаз»',
        'Fired, Red-eye reduction, Return detected' => 'Вспышка сработала. Включён режим уменьшения эффекта «красных глаз». Отражённый свет обнаружен',
        'Fired, Red-eye reduction, Return not detected' => 'Вспышка сработала. Включён режим уменьшения эффекта «красных глаз». Отражённый свет не обнаружен',
        'Fired, Return detected' => 'Вспышка сработала. Отражённый свет обнаружен',
        'Fired, Return not detected' => 'Вспышка сработала. Отражённый свет не обнаружен',
        'No Flash' => 'Без вспышки',
        'No flash function' => 'Функция вспышки не поддерживается',
        'Off' => 'Не включена',
        'Off, Did not fire' => 'Не включена. Вспышка не сработала',
        'Off, Did not fire, Return not detected' => 'Не включена. Вспышка не сработала. Отражённый свет не обнаружен',
        'Off, No flash function' => 'Не включена. Функция вспышки не поддерживается',
        'Off, Red-eye reduction' => 'Не включена. Включён режим уменьшения эффекта «красных глаз»',
        'On' => 'Включена',
        'On, Did not fire' => 'Принудительный режим. Вспышка не сработала',
        'On, Fired' => 'Принудительный режим. Вспышка сработала',
        'On, Red-eye reduction' => 'Принудительный режим. Включён режим уменьшения эффекта «красных глаз»',
        'On, Red-eye reduction, Return detected' => 'Принудительный режим. Включён режим уменьшения эффекта «красных глаз». Отражённый свет обнаружен',
        'On, Red-eye reduction, Return not detected' => 'Принудительный режим. Включён режим уменьшения эффекта «красных глаз». Отражённый свет не обнаружен',
        'On, Return detected' => 'Принудительный режим. Отражённый свет обнаружен',
        'On, Return not detected' => 'Принудительный режим. Отражённый свет не обнаружен',
      },
    },
   'FlashAttributes' => {
      Description => 'Flash аттрибуты',
      PrintConv => {
        'UseNetwork' => 'Использовать сеть',
      },
    },
   'FlashCompensation' => 'Компенсация вспышки',
   'FlashEnergy' => 'Мощность вспышки',
   'FlashExposureComp' => 'Коррекция вспышки',
   'FlashManufacturer' => 'Производитель вспышки',
   'FlashMode' => {
      Description => 'Режим вспышки',
      PrintConv => {
        'Auto' => 'Автоматический',
        'Disabled' => 'Вспышка не включена',
        'Force' => 'Принудительный',
        'Red eye' => 'Красные глаза',
      },
    },
   'FlashModel' => 'Модель вспышки',
   'FlashType' => {
      Description => 'Тип вспышки',
      PrintConv => {
        'Built-In Flash' => 'Встроенная',
        'External' => 'Внешняя',
      },
    },
   'FlashVersion' => 'Версия Flash',
   'FlashpixVersion' => 'Версия Flashpix',
   'FlickerReduce' => {
      Description => 'Уменьшение мерцания',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'FlightPitchDegree' => 'Тангаж полета (наклон)',
   'FlightRollDegree' => 'Крен полета (вращение)',
   'FlightXSpeed' => 'Скорость полёта по оси X',
   'FlightYSpeed' => 'Скорость полёта по оси Y',
   'FlightYawDegree' => 'Рыскание полета (поворот)',
   'FlightZSpeed' => 'Скорость полёта по оси Z',
   'FocalDistance' => 'Фокусное расстояние',
   'FocalLength' => 'Фокусное расстояние',
   'FocalLength35efl' => 'Фокусное расстояние',
   'FocalLengthIn35mmFormat' => 'Фокусное расстояние для 35-мм формата',
   'FocalPlaneResolutionUnit' => {
      Description => 'Единицы разрешения в фокальной плоскости',
      PrintConv => {
        'None' => 'Не указано',
        'cm' => 'см',
        'inches' => 'дюймы',
        'mm' => 'мм',
        'um' => 'мкм',
      },
    },
   'FocalPlaneXResolution' => 'Разрешение в фокальной плоскости по X',
   'FocalPlaneYResolution' => 'Разрешение в фокальной плоскости по Y',
   'FocalPointX' => 'Точка фокуса по оси X',
   'FocalPointY' => 'Точка фокуса по оси Y',
   'FocusDistance' => 'Фокусное расстояние',
   'FocusDistance2' => 'Фокусное расстояние 2',
   'FocusMode' => 'Режим фокусировки',
   'FocusPos' => 'Позиция фокуса',
   'FontFamily' => {
      Description => 'Семейство шрифта',
      PrintConv => {
        'Decorative' => 'Декоративный',
        'Don\'t Care' => 'Без разницы',
        'Modern' => 'Модерн',
        'Script' => 'Скрипт',
      },
    },
   'FontName' => 'Название шрифта',
   'FontSize' => 'Размер шрифта',
   'FontSubfamily' => 'Стиль шрифта',
   'FontSubfamilyID' => 'ID стиля шрифта',
   'FontType' => 'Тип шрифта',
   'FontWeight' => 'Толщина шрифта',
   'Footnotes' => 'Сноски',
   'For' => 'Для',
   'FormExtraUsageRights' => 'Права на использование Форм Extra',
   'FormFields' => 'Поля форм',
   'FormUsageRights' => 'Права на использование Форм',
   'Format' => {
      Description => 'Формат',
      PrintConv => {
        'RangeInverse' => 'Диапазон инверсии',
        'RangeLinear' => 'Диапазон линейности',
      },
    },
   'FormatVersionTime' => 'Время версии формата',
   'FormattedName' => 'Отформатированное имя',
   'Formatter' => 'Форматировщик',
   'ForwardMatrix1' => 'Прямая матрица 1',
   'ForwardMatrix2' => 'Прямая матрица 2',
   'FossilSpecimen' => 'Ископаемый образец',
   'FossilSpecimenMaterialSampleID' => 'ID материала ископаемого образца',
   'FovCot' => 'Угол обзора и Котангенс',
   'FractalParameters' => 'Фрактальные параметры',
   'FragmentList' => 'Список фрагментов',
   'FragmentTable' => 'Таблица фрагментов',
   'FrameCount' => 'Количество кадров',
   'FrameExposureTime' => 'Время экспозиции кадра',
   'FrameID' => 'ID кадра',
   'FrameNum' => 'Номер кадра',
   'FrameRate' => 'Частота кадров',
   'FrameSize' => 'Размер кадра',
   'FrameSizeMax' => 'Максимальный размер кадра',
   'FrameSizeMin' => 'Минимальный размер кадра',
   'FramesPerSecond' => 'Кадров в секунду',
   'Free' => 'Свободный',
   'FreeBusyTime' => 'Свободное/Занятое время',
   'FreeByteCounts' => 'Количество байтов в строке неиспользуемых байтов',
   'FreeOffsets' => 'Смещение к строке неиспользуемых байтов',
   'FullName' => 'Полное название шрифта',
   'FullPanoHeightPixels' => 'Исходная полная высота до обрезки изображения',
   'FullPanoWidthPixels' => 'Исходная полная ширина до обрезки изображения',
   'FullScreen' => 'Полноэкранный',
   'GDALMetadata' => 'GDAL – Метаданные',
   'GDALNoData' => 'GDAL – Прозрачность',
   'GEMake' => 'GE – Сделан',
   'GEModel' => 'GE – Модель',
   'GIFApplicationExtension' => 'Расширение GIF-приложения',
   'GIFGraphicControlExtension' => 'Расширение управления графикой GIF',
   'GIFPlainTextExtension' => 'Расширение обычного текста GIF',
   'GIFTFtpPriority' => 'Приоритет GIFT Ftp',
   'GIFVersion' => 'Версия GIF',
   'GLPI_Unknown4' => 'GLPI  – Неизвестный 4',
   'GPRI_Unknown4' => 'GPRI – Неизвестный – 4',
   'GPRI_Unknown5' => 'GPRI – Неизвестный – 5',
   'GPRI_Unknown8' => 'GPRI – Неизвестный – 8',
   'GPRI_Unknown9' => 'GPRI – Неизвестный – 9',
   'GPSAltitude' => 'GPS – Высота',
   'GPSAltitudeRaw' => 'GPS – Высота – Raw',
   'GPSAltitudeRef' => {
      Description => 'GPS – Индекс высоты',
      PrintConv => {
        'Above Sea Level' => 'Над уровнем моря',
        'Below Sea Level' => 'Ниже уровня моря',
      },
    },
   'GPSAreaInformation' => 'GPS – Название области',
   'GPSDOP' => 'GPS – Точность измерения',
   'GPSDateStamp' => 'GPS – Дата и время',
   'GPSDateTime' => 'GPS – Дата/Время',
   'GPSDateTimeRaw' => 'GPS – Дата/Время – Raw',
   'GPSDestBearing' => 'GPS – Пеленг объекта съёмки',
   'GPSDestBearingRef' => {
      Description => 'GPS – Ориентир пеленга объекта съёмки',
      PrintConv => {
        'Magnetic North' => 'На магнитный северный полюс',
        'True North' => 'На географический северный полюс',
      },
    },
   'GPSDestDistance' => 'GPS – Расстояние до объекта съёмки',
   'GPSDestDistanceRef' => {
      Description => 'GPS – Единицы измерения расстояния',
      PrintConv => {
        'Kilometers' => 'Километры',
        'Miles' => 'Мили',
        'Nautical Miles' => 'Морские мили',
      },
    },
   'GPSDestLatitude' => 'GPS – Широта объекта съёмки',
   'GPSDestLatitudeRef' => {
      Description => 'GPS – Индекс широты объекта съёмки',
      PrintConv => {
        'North' => 'Северная широта',
        'South' => 'Южная широта',
      },
    },
   'GPSDestLongitude' => 'GPS – Долгота объекта съёмки',
   'GPSDestLongitudeRef' => {
      Description => 'GPS – Индекс долготы объекта съёмки',
      PrintConv => {
        'East' => 'Восточная долгота',
        'West' => 'Западная долгота',
      },
    },
   'GPSDifferential' => {
      Description => 'GPS – Дифференциальная поправка',
      PrintConv => {
        'Differential Corrected' => 'Дифференциальная поправка применена',
        'No Correction' => 'Измерение без дифференциальной поправки',
      },
    },
   'GPSHPositioningError' => 'GPS – Ошибка горизонтального позиционирования',
   'GPSImgDirection' => 'GPS – Направления камеры при съёмке',
   'GPSImgDirectionRef' => {
      Description => 'GPS – Ориентир направления камеры',
      PrintConv => {
        'Magnetic North' => 'На магнитный северный полюс',
        'True North' => 'На географический северный полюс',
      },
    },
   'GPSInfo' => 'IFD указатель информации GPS',
   'GPSLatitude' => 'GPS – Широта',
   'GPSLatitudeRaw' => 'GPS – Широта – Raw',
   'GPSLatitudeRef' => {
      Description => 'GPS – Индекс широты',
      PrintConv => {
        'North' => 'Северная широта',
        'South' => 'Южная широта',
      },
    },
   'GPSLongitude' => 'GPS – Долгота',
   'GPSLongitudeRaw' => 'GPS – Долгота – Raw',
   'GPSLongitudeRef' => {
      Description => 'GPS – Индекс долготы',
      PrintConv => {
        'East' => 'Восточная долгота',
        'West' => 'Западная долгота',
      },
    },
   'GPSLongtitude' => 'GPS – Долгота',
   'GPSMapDatum' => 'GPS – Система координат',
   'GPSMeasureMode' => {
      Description => 'GPS – Режим измерения GPS',
      PrintConv => {
        '2-Dimensional Measurement' => '2-мерная навигация',
        '3-Dimensional Measurement' => '3-мерная навигация',
      },
    },
   'GPSPosition' => 'GPS – Местоположение',
   'GPSProcessingMethod' => 'GPS – Метод вычисления положения',
   'GPSSatellites' => 'GPS – Используемые спутники',
   'GPSSpeed' => 'GPS – Скорость передвижения',
   'GPSSpeed3D' => 'GPS – Скорость 3D',
   'GPSSpeedRaw' => 'GPS – скорость – Raw',
   'GPSSpeedRef' => {
      Description => 'GPS – Единицы измерения скорости',
      PrintConv => {
        'km/h' => 'Км/ч',
        'knots' => 'Узлы',
        'mph' => 'Миль/ч',
      },
    },
   'GPSSpeedX' => 'GPS – Скорость по X',
   'GPSSpeedY' => 'GPS – Скорость по Y',
   'GPSSpeedZ' => 'GPS – Скорость по Z',
   'GPSStatus' => {
      Description => 'GPS – Состояние приёмника во время съёмки',
      PrintConv => {
        'Measurement Active' => 'Координаты актуальные',
        'Measurement Void' => 'Актуальных координат нету',
      },
    },
   'GPSTimeStamp' => 'GPS – Время записанных координат',
   'GPSTrack' => 'GPS – Трек',
   'GPSTrackRaw' => 'GPS – Трек – Raw',
   'GPSTrackRef' => {
      Description => 'GPS – Ориентир направления',
      PrintConv => {
        'Magnetic North' => 'На магнитный северный полюс',
        'True North' => 'На географический северный полюс',
      },
    },
   'GPSVersionID' => 'GPS – Версия тегов',
   'GainControl' => {
      Description => 'Управление усилением',
      PrintConv => {
        'High gain down' => 'С большим шагом вниз',
        'High gain up' => 'С большим шагом вверх',
        'Low gain down' => 'С малым шагом вниз',
        'Low gain up' => 'С малым шагом вверх',
        'None' => 'Отсутствует',
      },
    },
   'Gamma' => 'Гамма',
   'GammaBlue' => 'Гамма Синего',
   'GammaCompensatedValue' => 'Значение гамма-компенсации',
   'GammaGreen' => 'Гамма Зелёного',
   'GammaRed' => 'Гамма Красного',
   'Gapless' => {
      Description => 'Без зазора',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'Gender' => 'Пол',
   'Generator' => 'Сгенерирован в',
   'GeneratorVersion' => 'Версия приложения',
   'Genr' => 'Жанр',
   'Genre' => 'Жанр',
   'GeoTiffAsciiParams' => 'Geo Tiff – Параметры Ascii',
   'GeoTiffDirectory' => 'Geo Tiff – Каталог',
   'GeoTiffDoubleParams' => 'Geo Tiff – Параметры дублирования',
   'Geolocation' => 'Геолокация',
   'GeologicalContext' => 'Геологический контекст',
   'GeologicalContextBed' => 'Название литостратиграфического слоя',
   'GeologicalContextFormation' => 'Название литостратиграфической свиты',
   'GeologicalContextGroup' => 'Название литостратиграфической серии',
   'GeologicalContextID' => 'ID геологического контекста',
   'GeologicalContextMember' => 'Название литостратиграфической пачки',
   'Geosync' => 'Синхронизация времени местоположения',
   'Geotag' => 'Журнал пройденного маршрута',
   'Geotime' => 'Дата/Время местоположения',
   'GimbalPitchDegree' => 'Тангаж шарнира (наклон)',
   'GimbalReverse' => 'Оборот шарнира',
   'GimbalRollDegree' => 'Крен шарнира (вращение)',
   'GimbalYawDegree' => 'Рыскание шарнира (поворот)',
   'GlobalAltitude' => 'Глобальная высота',
   'GlobalAngle' => 'Глобальный угол',
   'GoogleBot' => 'Поисковый робот Google',
   'GooglePlusUploadCode' => 'Код загрузки Google Plus',
   'Gradation' => 'Впечатляющ.режим',
   'GrayPoint' => 'Точка серого',
   'GrayResponseCurve' => 'Оптическая плотность серого',
   'GrayResponseUnit' => 'Единицы плотности серого',
   'GreenEndpoint' => 'Конечная точка Зелёного',
   'GreenMask' => 'Маска Зелёного',
   'GreenPrimary' => 'Основной зелёный',
   'GreenX' => 'Зелёный по X',
   'GreenY' => 'Зелёный по Y',
   'GridGuidesInfo' => 'Сведения о сетке и направляющих',
   'Gyroscope' => 'Гироскоп',
   'HCUsage' => 'Тип информации HC-файла',
   'HDContent' => {
      Description => 'Контент HD',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'HDR' => {
      Description => 'Автоматический HDR',
      PrintConv => {
        'Off' => 'Не включён',
      },
    },
   'HDRImageType' => {
      Description => 'Тип HDR изображения',
      PrintConv => {
        'HDR Image' => 'HDR изображение',
        'Original Image' => 'Оригинальное изображение',
      },
    },
   'HDRSetting' => 'Праметры HDR',
   'HDRToningInfo' => 'Сведения о HDR-тонировании',
   'HTTPHostHeader' => 'Заголовок HTTP-хоста',
   'HalftoneHints' => 'Указания для полутонирования',
   'HardLink' => 'Жёсткая ссылка',
   'HasAudio' => 'Имеет аудио',
   'HasColorMap' => {
      Description => 'Имеет цветовую карту',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'HasCuePoints' => 'Имеет ключевые точки',
   'HasKeyFrames' => 'Имеет ключевые кадры',
   'HasMetadata' => 'Имеет метаданные',
   'HasRealMergedData' => {
      Description => 'Имеет совмещённые данные',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'HasVideo' => 'Имеет видео',
   'HasXFA' => {
      Description => 'XFA',
      PrintConv => {
        'No' => 'Отсутствует',
        'Yes' => 'Есть',
      },
    },
   'HeaderSize' => 'Размер заголовка',
   'HeaderVersion' => 'Версия заголовков',
   'Headline' => 'Заголовок',
   'HeightResolution' => 'Разрешение по высоте (PPI)',
   'HierarchicalKeywords' => 'Иерархия ключевых слов',
   'HierarchicalKeywords1' => 'Иерархия ключевых слов 1',
   'HierarchicalKeywords1Applied' => 'Иерархия ключевых слов 1 – Применена',
   'HierarchicalKeywords1Children' => 'Иерархия ключевых слов 1 – Дочерняя',
   'HierarchicalKeywords2' => 'Иерархия ключевых слов 2',
   'HierarchicalKeywords2Applied' => 'Иерархия ключевых слов 2 – Применена',
   'HierarchicalKeywords2Children' => 'Иерархия ключевых слов 2 – Дочерняя',
   'HierarchicalKeywords3' => 'Иерархия ключевых слов 3',
   'HierarchicalKeywords3Applied' => 'Иерархия ключевых слов 3 – Применена',
   'HierarchicalKeywords3Children' => 'Иерархия ключевых слов 3 – Дочерняя',
   'HierarchicalKeywords4' => 'Иерархия ключевых слов 4',
   'HierarchicalKeywords4Applied' => 'Иерархия ключевых слов 4 – Применена',
   'HierarchicalKeywords4Children' => 'Иерархия ключевых слов 4 – Дочерняя',
   'HierarchicalKeywords5' => 'Иерархия ключевых слов 5',
   'HierarchicalKeywords5Applied' => 'Иерархия ключевых слов 5 – Применена',
   'HierarchicalKeywords5Children' => 'Иерархия ключевых слов 5 – Дочерняя',
   'HierarchicalKeywords6' => 'Иерархия ключевых слов 6',
   'HierarchicalKeywords6Applied' => 'Иерархия ключевых слов 6 – Применена',
   'HierarchicalSubject' => 'Иерархия тем',
   'HighISOMultiplierBlue' => 'Множитель ISO – Синий',
   'HighISOMultiplierGreen' => 'Множитель ISO – Зелёный',
   'HighISOMultiplierRed' => 'Множитель ISO – Красный',
   'HighISONoiseReduction' => {
      Description => 'Шумоподавление при высоком ISO',
      PrintConv => {
        'Auto' => 'Автоматическое',
        'High' => 'Сильное',
        'Low' => 'Слабое',
        'Normal' => 'Стандартное',
        'Off' => 'Не включено',
        'Standard' => 'Стандартное',
        'Strong' => 'Сильное',
      },
    },
   'HighestBiostratigraphicZone' => 'Высшая биостратиграфическая зона',
   'HighlightColorDistortReduct' => {
      Description => 'Уменьшение искажений светлых цветов',
      PrintConv => {
        'Advanced' => 'Расширенный',
        'Standard' => 'Стандартный',
      },
    },
   'Highlights' => 'Света',
   'HighlightsAdj' => 'Регулировка светов',
   'History' => 'История',
   'HistoryBufferSize' => 'Размер буфера истории',
   'HostComputer' => 'Компьютер/Система',
   'HotKey' => {
      Description => 'Горячая клавиша',
      PrintConv => {
        '(none)' => 'Отсутствует',
      },
    },
   'HowPublished' => 'Метод публикации',
   'Hue' => 'Оттенок',
   'HueAdj' => 'Регулировка оттенка',
   'HumanObservation' => 'Визуальное наблюдение',
   'HumanObservationDay' => 'День месяца визуального наблюдения',
   'HumanObservationEarliestDate' => 'Дата начала визуального наблюдения',
   'HumanObservationEndDayOfYear' => 'День года окончания визуального наблюдения',
   'HumanObservationEventDate' => 'Дата визуального наблюдения',
   'HumanObservationEventID' => 'ID визуального наблюдения',
   'HumanObservationEventRemarks' => 'Комментарии к визуальному наблюдению',
   'HumanObservationEventTime' => 'Время визуального наблюдения',
   'HumanObservationFieldNotes' => 'Полевые заметки о визуальном наблюдении',
   'HumanObservationFieldNumber' => 'Номер полевых заметок о визуальном наблюдении',
   'HumanObservationHabitat' => 'Ареал визуального наблюдения',
   'HumanObservationLatestDate' => 'Дата окончания визуального наблюдения',
   'HumanObservationMonth' => 'Месяц визуального наблюдения',
   'HumanObservationParentEventID' => 'ID основного визуального наблюдения',
   'HumanObservationSampleSizeUnit' => 'Визуальное наблюдение – Единицы размера',
   'HumanObservationSampleSizeValue' => 'Визуальное наблюдение – Размер образца',
   'HumanObservationSamplingEffort' => 'Визуальное наблюдение – Затраченные усилия',
   'HumanObservationSamplingProtocol' => 'Визуальное наблюдение – Метод исследования',
   'HumanObservationStartDayOfYear' => 'День года начала визуального наблюдения',
   'HumanObservationVerbatimEventDate' => 'Дата визуального наблюдения дословно',
   'HumanObservationYear' => 'Год визуального наблюдения',
   'Humidity' => 'Влажность',
   'HyperfocalDistance' => 'Гиперфокальное расстояние',
   'HyperlinkBase' => 'База гиперссылок',
   'ICCProfileName' => 'Название ICC-профиля',
   'ICC_Profile' => 'ICC-профиль',
   'ICC_Untagged' => 'ICC без метки',
   'ID3Size' => 'Размер блока данных ID3',
   'IDCCreativeStyle' => {
      Description => 'IDC – Творческий стиль',
      PrintConv => {
        'A100 Standard' => 'Стандарт A100',
        'Autumn Leaves' => 'Осенние листья',
        'B&W' => 'Чёрно-Белое',
        'Camera Setting' => 'Настройка камеры',
        'Clear' => 'Чистый',
        'Deep' => 'Глубокий',
        'Landscape' => 'Пейзаж',
        'Light' => 'Светлый',
        'Neutral' => 'Нейтральный',
        'Night View' => 'Ночной вид',
        'Portrait' => 'Портрет',
        'Real' => 'Реальный',
        'Sepia' => 'Сепия',
        'Standard' => 'Стандарт',
        'Sunset' => 'Закат',
        'Vivid' => 'Яркий',
      },
    },
   'IDCPreviewImage' => 'IDC – файл предпросмотра',
   'IDCPreviewLength' => 'IDC – Строк в файле предпросмотра',
   'IDCPreviewStart' => 'IDC – Начало файла предпросмотра',
   'IDsBaseValue' => 'Основные значения индикаторов',
   'INGRReserved' => 'Intergraph – Резерв',
   'IPTCBitsPerSample' => 'IPTC – Количество бит на компонент',
   'IPTCDigest' => 'Хеш IPTC',
   'IPTCImageHeight' => 'IPTC – Высота изображения',
   'IPTCImageRotation' => {
      Description => 'IPTC – Вращение изображения',
      PrintConv => {
        '0' => 'Не вращать',
        '180' => 'Повернуть вправо на 180°',
        '270' => 'Повернуть вправо на 270°',
        '90' => 'Повернуть вправо на 90°',
      },
    },
   'IPTCImageWidth' => 'IPTC – Ширина изображения',
   'IPTCPictureNumber' => 'IPTC – Номер изображения',
   'IPTCPixelHeight' => 'IPTC – Высота (в пикселях)',
   'IPTCPixelWidth' => 'IPTC – Ширина (в пикселях)',
   'ISOSetting' => {
      Description => 'ISO2',
      PrintConv => {
        '200 (Zone Matching High)' => '200 (Согласование зон. В высоком ключе)',
        '80 (Zone Matching Low)' => '80 (Согласование зон. В низком ключе)',
        'Auto' => 'Автоматически',
      },
    },
   'ISOSpeed' => 'Чувствительность ISO',
   'ISOSpeedLatitudeyyy' => 'ISO – Фотографическая широта yyy',
   'ISOSpeedLatitudezzz' => 'ISO – Фотографическая широта zzz',
   'ISOSpeeds' => 'ISO4',
   'ISRCNumber' => 'Номер ISRC',
   'IT8Header' => 'IT8-заголовок',
   'IconFileName' => 'Название файла значка',
   'IconIndex' => 'Индекс иконки',
   'Identification' => 'Определение',
   'IdentificationID' => 'ID определения',
   'IdentificationQualifier' => 'Уточнение к определению',
   'IdentificationReferences' => 'Источники использованные для определения',
   'IdentificationRemarks' => 'Комментарии к определению',
   'IdentificationVerificationStatus' => 'Код подтверждения определения',
   'IdentifiedBy' => 'Люди определившие образец',
   'Identifier' => 'Идентификатор',
   'Illumination' => {
      Description => 'Подсветка',
      PrintConv => {
        'Off' => 'Не включена',
        'On' => 'Включена',
      },
    },
   'Image2Description' => 'Изображение 2 – Описание',
   'Image3Description' => 'Изображение 3 – Описание',
   'Image4Description' => 'Изображение 4 – Описание',
   'Image5Description' => 'Изображение 5 – Описание',
   'Image6Description' => 'Изображение 6 – Описание',
   'Image7Description' => 'Изображение 7 – Описание',
   'Image8Description' => 'Изображение 8 – Описание',
   'Image::ExifTool::AIFF::Comment' => 'Комментарии AIFF',
   'Image::ExifTool::AIFF::Common' => 'Общее AIFF',
   'Image::ExifTool::AIFF::FormatVers' => 'Версия формата AIFF',
   'Image::ExifTool::APE::NewHeader' => 'Новый заголовок APE',
   'Image::ExifTool::APE::OldHeader' => 'Старый заголовок APE',
   'Image::ExifTool::APP12::PictureInfo' => 'Сведения APP12',
   'Image::ExifTool::Apple::RunTime' => 'Apple – Время выполнения',
   'Image::ExifTool::Audible::cvrx' => 'Обложка Audible',
   'Image::ExifTool::Audible::meta' => 'Метаданные Audible',
   'Image::ExifTool::BPG::Extensions' => 'Расширения BPG',
   'Image::ExifTool::DjVu::Form' => 'Форма DjVu',
   'Image::ExifTool::DjVu::Info' => 'Сведения о DjVu',
   'Image::ExifTool::DjVu::Meta' => 'DjVu метаданные',
   'Image::ExifTool::FLAC::Picture' => 'Изображение FLAC',
   'Image::ExifTool::FLAC::StreamInfo' => 'Сведения о потоке FLAC',
   'Image::ExifTool::Flash::Audio' => 'Flash аудио',
   'Image::ExifTool::Flash::Meta' => 'Flash метаданные',
   'Image::ExifTool::Flash::Video' => 'Flash видео',
   'Image::ExifTool::Font::Main' => 'Шрифт',
   'Image::ExifTool::Font::Name' => 'Название шрифта',
   'Image::ExifTool::Font::PFM' => 'Шрифт PFM',
   'Image::ExifTool::Font::PSInfo' => 'Сведения о PostScript шрифте',
   'Image::ExifTool::FotoStation::SoftEdit' => 'FotoStation – Редактор',
   'Image::ExifTool::GE::Main' => 'General Imaging (GE)',
   'Image::ExifTool::GIF::Animation' => 'GIF анимация',
   'Image::ExifTool::GIF::Extensions' => 'GIF Расширения',
   'Image::ExifTool::GIF::MIDIControl' => 'GIF MIDIControl',
   'Image::ExifTool::GIF::Main' => 'GIF',
   'Image::ExifTool::GIF::Screen' => 'GIF Screen',
   'Image::ExifTool::GIMP::Header' => 'Заголовок GIMP',
   'Image::ExifTool::GIMP::Parasite' => 'GIMP – Паразитный элемент',
   'Image::ExifTool::GIMP::Resolution' => 'GIMP – Разешение',
   'Image::ExifTool::HP::Type2' => 'HP – Тип 2',
   'Image::ExifTool::HP::Type4' => 'HP – Тип 4',
   'Image::ExifTool::HP::Type6' => 'HP – Тип 6',
   'Image::ExifTool::IPTC::ObjectData' => 'IPTC – Данные объекта',
   'Image::ExifTool::IPTC::PostObjectData' => 'IPTC – Данные Post объекта',
   'Image::ExifTool::IPTC::PreObjectData' => 'IPTC – Данные Pre объекта',
   'Image::ExifTool::ITC::Header' => 'Заголовок ITC',
   'Image::ExifTool::ITC::Item' => 'Элемент ITC',
   'Image::ExifTool::JFIF::Extension' => 'JFIF Дополнение',
   'Image::ExifTool::JVC::Text' => 'JVC – Текст',
   'Image::ExifTool::Jpeg2000::ColorSpec' => 'Jpeg2000 – Спецификация цвета',
   'Image::ExifTool::Jpeg2000::DisplayResolution' => 'Jpeg2000 – Разрешение экрана',
   'Image::ExifTool::Jpeg2000::FileType' => 'Jpeg2000 – Тип файла',
   'Image::ExifTool::Jpeg2000::ImageHeader' => 'Jpeg2000 – Заголовок изображения',
   'Image::ExifTool::MPEG::Audio' => 'MPEG аудио',
   'Image::ExifTool::MPEG::Video' => 'MPEG видео',
   'Image::ExifTool::Microsoft::MP' => 'Сведения о Microsoft Photo',
   'Image::ExifTool::PDF::Encrypt' => 'PDF зашифрован',
   'Image::ExifTool::PDF::Info' => 'Сведения о PDF-файле',
   'Image::ExifTool::PDF::Pages' => 'Страниц в PDF',
   'Image::ExifTool::PDF::Root' => 'Основные параметры PDF',
   'Image::ExifTool::PDF::Signature' => 'Электронная подпись PDF',
   'Image::ExifTool::PDF::TransformParams' => 'Параметры изменения PDF-файла',
   'Image::ExifTool::PNG::ImageHeader' => 'Заголовки PNG-изображения',
   'Image::ExifTool::PNG::PrimaryChromaticities' => 'Основные цвета PNG',
   'Image::ExifTool::PNG::StereoImage' => 'Стереоизображние PNG',
   'Image::ExifTool::PSP::Creator' => 'Создатель PSP',
   'Image::ExifTool::PSP::Image' => 'Изображение PSP',
   'Image::ExifTool::PhaseOne::SensorCalibration' => 'PhaseOne – Коррекция сенсора',
   'Image::ExifTool::PhotoMechanic::SoftEdit' => 'PhotoMechanic',
   'Image::ExifTool::Photoshop::Header' => 'Заголовок Photoshop',
   'Image::ExifTool::Photoshop::JPEG_Quality' => 'Качество JPEG в Photoshop',
   'Image::ExifTool::Photoshop::Layers' => 'Слои Photoshop',
   'Image::ExifTool::Photoshop::PrintScaleInfo' => 'Photoshop – Масштабирование при печати',
   'Image::ExifTool::Photoshop::Resolution' => 'Photoshop – Разрешение',
   'Image::ExifTool::PrintIM::Main' => 'Print Image Matching (PrintIM)',
   'Image::ExifTool::RTF::Main' => 'RTF',
   'Image::ExifTool::Rawzor::Main' => 'Rawzor',
   'Image::ExifTool::Real::Metadata' => 'Метаданные Real',
   'Image::ExifTool::Real::Metafile' => 'Метафайл Real',
   'Image::ExifTool::Real::Properties' => 'Свойства Real',
   'Image::ExifTool::Sanyo::Thumbnail' => 'Миниатюра Sanyo',
   'Image::ExifTool::Stim::CropX' => 'Stim – Обрезка по X',
   'Image::ExifTool::Stim::CropY' => 'Stim – Обрезка по Y',
   'Image::ExifTool::Theora::Identification' => 'Идентификация Theora',
   'Image::ExifTool::Torrent::Files' => 'Торрент-файлы',
   'Image::ExifTool::Torrent::Info' => 'Сведения о торренте',
   'Image::ExifTool::Torrent::Main' => 'Торрент',
   'Image::ExifTool::Torrent::Profiles' => 'Торрент профили',
   'Image::ExifTool::Vorbis::Comments' => 'Комментарии Vorbis',
   'Image::ExifTool::Vorbis::Identification' => 'Идентификатор Vorbis',
   'Image::ExifTool::ZIP::GZIP' => 'ZIP GZIP',
   'Image::ExifTool::ZIP::Main' => 'ZIP',
   'Image::ExifTool::ZIP::RAR' => 'ZIP RAR',
   'ImageAlterationConstraints' => {
      Description => 'Ограничение на редактирование',
      PrintConv => {
        'No Colorization' => 'Не раскрашивать',
        'No Cropping' => 'Не кадрировать',
        'No De-Colorization' => 'Не обесцвечивать',
        'No Flipping' => 'Не отражать',
        'No Merging' => 'Не объединять',
        'No Retouching' => 'Не ретушировать',
      },
    },
   'ImageArrangement' => {
      Description => 'Расположение стереоизображений',
      PrintConv => {
        'Cross View Alignment' => 'Пересечное расположение',
        'Parallel View Alignment' => 'Параллельное расположение',
      },
    },
   'ImageByteCount' => 'Размер изображения (байт)',
   'ImageColorIndicator' => {
      Description => 'Цвет переднего плана или прозрачности',
      PrintConv => {
        'Specified Image Color' => 'Указан',
        'Unspecified Image Color' => 'Не указан',
      },
    },
   'ImageColorValue' => 'Значение цвета переднего плана или прозрачности',
   'ImageCreator' => 'Создатель изображеения',
   'ImageCreatorID' => 'PLUS-ID создателя изображения',
   'ImageCreatorImageID' => 'ID изображения присвоенный создателем',
   'ImageCreatorName' => 'Имя создателя изображения',
   'ImageData' => 'Данные изображения',
   'ImageDataDiscard' => {
      Description => 'Отброшенные данные изображения',
      PrintConv => {
        'Flexbits Discarded' => 'Гибкие биты отброшены',
        'Full Resolution' => 'Полное разрешение',
        'HighPass Frequency Data Discarded' => 'Данные высоких частот отброшены',
        'Highpass and LowPass Frequency Data Discarded' => 'Данные высоких и низких частот отброшены',
      },
    },
   'ImageDepth' => 'Глубина изображения',
   'ImageDescription' => 'Описание изображения',
   'ImageDuplicationConstraints' => {
      Description => 'Ограничение на дублирование',
      PrintConv => {
        'Duplication Only as Necessary Under License' => 'Дублировать только при необходимости в соответствии с лицензией',
        'No Duplication' => 'Дублирование запрещено',
        'No Duplication Constraints' => 'Ограничения на дублирование отсутствуют',
      },
    },
   'ImageElements' => 'Элементы изображения',
   'ImageFileConstraints' => {
      Description => 'Ограничения на файл',
      PrintConv => {
        'Maintain File Name' => 'Запрещается переименование файла',
        'Maintain File Type' => 'Запрещается изменять тип файла',
        'Maintain ID in File Name' => 'Запрещается изменять ID в имени файла',
        'Maintain Metadata' => 'Запрещается изменение метаданных',
      },
    },
   'ImageFileFormatAsDelivered' => {
      Description => 'Исходный формат файла',
      PrintConv => {
        'Other' => 'Другой формат',
      },
    },
   'ImageFileName' => 'Название файла изображения',
   'ImageFileSizeAsDelivered' => {
      Description => 'Исходный размер файла',
      PrintConv => {
        'Greater than 50 MB' => 'Более 50 МБ',
        'Up to 1 MB' => 'До 1 МБ',
        'Up to 10 MB' => 'До 10 МБ',
        'Up to 30 MB' => 'До 30 МБ',
        'Up to 50 MB' => 'До 50 МБ',
      },
    },
   'ImageFullHeight' => 'Полная высота изображения',
   'ImageFullWidth' => 'Полная ширина изображения',
   'ImageHeight' => 'Высота изображения',
   'ImageHistory' => 'История изображения',
   'ImageID' => 'ID изображения',
   'ImageLayer' => 'Слой изображения',
   'ImageLength' => 'Строк в изображении',
   'ImageLimitExposureBias' => 'Лимит поправки экспозиции',
   'ImageMimeType' => 'MIME-тип изображения',
   'ImageModulationExposureBias' => 'Модуляция поправки экспозиции',
   'ImageNumber' => 'Номер изображения',
   'ImageOffset' => 'Смещение изображения',
   'ImageOrientation' => {
      Description => 'Ориентация изображения',
      PrintConv => {
        'Landscape' => 'Альбомная',
        'Portrait' => 'Портретная',
        'Square' => 'Квадратная',
      },
    },
   'ImageRank' => 'Рейтинг изображения',
   'ImageReadyDataSets' => 'Наборы данных',
   'ImageReadyVariables' => 'Переменные',
   'ImageReferencePoints' => 'Опорные точки изображения',
   'ImageResolution' => 'Разрешение изображения',
   'ImageRotation' => {
      Description => 'Поворот/Реверс точек просмотра изображения',
      PrintConv => {
        'None' => 'Нет',
      },
    },
   'ImageSensorGain' => 'Коэффициент усиления датчика изображения',
   'ImageSize' => 'Размер изображения',
   'ImageSourceData' => 'Исходные данные изображения',
   'ImageStabilization' => {
      PrintConv => {
        'CCD Shift' => 'Оптический стабилизатор',
        'High Sensitivity' => 'Движение объекта съёмки',
        'Off' => 'Выкл',
        'Off (1)' => 'Выкл (1)',
      },
    },
   'ImageSupplier' => 'Поставщик изображения',
   'ImageSupplierID' => 'PLUS-ID поставщика изображения',
   'ImageSupplierImageID' => 'ID изображения присвоенный поставщиком',
   'ImageSupplierName' => 'Имя поставщика изображения',
   'ImageToolbar' => 'Панель команд для изображения',
   'ImageType' => {
      Description => 'Тип изображения',
      PrintConv => {
        'Grayscale (interlaced)' => 'Градация серого (чересстрочная)',
        'Grayscale (non-interlaced)' => 'Градация серого (без чередования)',
        'Grayscale Animation (interlaced)' => 'Анимация в градациях серого (чересстрочная)',
        'Grayscale Animation (non-interlaced)' => 'Анимация в градациях серого (без чередования)',
        'Illustrated Image' => 'Графическое изображение',
        'Multimedia or Composited Image' => 'Мультимедийное или композитное изображение',
        'Other' => 'Другой тип',
        'Page' => 'Страница',
        'Photographic Image' => 'Фотография',
        'Preview' => 'Предпросмотр',
        'RGB (interlaced)' => 'RGB (чересстрочная)',
        'RGB (non-interlaced)' => 'RGB (без чередования)',
        'RGB Animation (interlaced)' => 'Анимация RGB (чересстрочная)',
        'RGB Animation (non-interlaced)' => 'Анимация RGB (без чередования)',
        'RGBA (interlaced)' => 'RGBA (чересстрочная)',
        'RGBA (non-interlaced)' => 'RGBA (без чередования)',
        'RGBA Animation (interlaced)' => 'Анимация RGBA (чересстрочная)',
        'RGBA Animation (non-interlaced)' => 'Анимация RGBA  (без чередования)',
        'Video' => 'Видео',
      },
    },
   'ImageUIDList' => 'Список уникальных ID изображений',
   'ImageUniqueID' => 'Уникальный ID изображения',
   'ImageWidth' => 'Ширина изображения',
   'Importance' => {
      Description => 'Важность',
      PrintConv => {
        'High' => 'Высокая',
        'Low' => 'Низкая',
        'Normal' => 'Обычная',
      },
    },
   'Imprint' => 'Издательство',
   'InclinationAngle' => 'Угол наклона',
   'InclinationCorrection' => {
      Description => 'Коррекция наклона',
      PrintConv => {
        'Off' => 'Не включена',
        'On' => 'Включена',
      },
    },
   'IncludedFileID' => 'ID встроенного файла',
   'Index' => 'Индекс',
   'IndexOffset' => 'Индекс смещения',
   'Indexable' => {
      Description => 'Индексированный',
      PrintConv => {
        'False' => 'Нет',
        'True' => 'Да',
      },
    },
   'Indexed' => {
      Description => 'Индексированный',
      PrintConv => {
        'Indexed' => 'Да',
        'Not indexed' => 'Нет',
      },
    },
   'IndexedColorTableCount' => 'Количество индексированных цветов',
   'InfraredIlluminator' => {
      Description => 'Инфракрасная подсветка',
      PrintConv => {
        'Off' => 'Не включена',
        'On' => 'Включена',
      },
    },
   'InitialCameraDolly' => 'Исходное положение вдоль линии взгляда',
   'InitialDisplayEffect' => {
      Description => 'Начальный эффект отображения',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'InitialHorizontalFOVDegrees' => 'Исходное горизонтальное поле зрения (°)',
   'InitialViewHeadingDegrees' => 'Курсовой угол для исходного представления (°)',
   'InitialViewPitchDegrees' => 'Угол продольного наклона для исходного представления (°)',
   'InitialViewRollDegrees' => 'Угол поперечного наклона для исходного представления (°)',
   'InkNames' => 'Названия чернил',
   'InkSet' => {
      Description => 'Набор чернил',
      PrintConv => {
        'Not CMYK' => 'Не CMYK',
      },
    },
   'InputDeviceName' => 'Название устройства ввода',
   'InputDeviceSerialNumber' => 'Серийный номер устройства ввода',
   'InsertMode' => 'Режим вставки',
   'InstanceType' => {
      Description => 'Тип события',
      PrintConv => {
        'Exception to Recurring Appointment' => 'Исключение для повторяющегося события',
        'Non-recurring Appointment' => 'Неповторяющиеся событие',
        'Recurring Appointment' => 'Повторяющиеся событие',
        'Single Instance of Recurring Appointment' => 'Событие с единократным повторением',
      },
    },
   'Institution' => 'Институт',
   'InstructionSet' => 'Набор инструкций',
   'Instructions' => 'Инструкции',
   'Instrument' => 'Инструмент',
   'IntendedBusyStatus' => 'Предполагаемый статус занятости',
   'IntensityStereo' => 'Интенсивное стерео',
   'InterchangeColorSpace' => {
      Description => 'Смена цветового пространства',
      PrintConv => {
        'CMY (K) Device Dependent' => 'Аппаратно-зависимый CMY (K)',
        'RGB Device Dependent' => 'Аппаратно-зависимый RGB',
      },
    },
   'IntergraphFlagRegisters' => 'Intergraph – Флаг регистров',
   'IntergraphMatrix' => 'Intergraph – Матрица',
   'IntergraphPacketData' => 'Intergraph – Данные пакета',
   'Interlace' => {
      Description => 'Чересстрочная развёртка',
      PrintConv => {
        'Adam7 Interlace' => 'Чересстрочная Adam7',
        'Noninterlaced' => 'Прогрессивная',
      },
    },
   'InternalIDNumber' => 'Внутренний ID-номер',
   'InternalLeading' => 'Внутренний зазор',
   'InternalVersionNumber' => 'Внутренняя версия файла',
   'InteropIndex' => {
      Description => 'Индекс файловой совместимости',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03 – Дополнительный файл DCF (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98 – Основной файл DCF (sRGB)',
        'THM - DCF thumbnail file' => 'THM – Файл с миниатюрами DCF',
      },
    },
   'InteropOffset' => 'Тег взаимной совместимости',
   'InteropVersion' => 'Версия файловой совместимости',
   'IsBaseFont' => 'Базовый шрифт',
   'IsFixedPitch' => 'Моноширинный шрифт',
   'IsMergedHDR' => 'Объединённый HDR',
   'IsMergedPanorama' => 'Объединённая панорама',
   'Italic' => 'Курсив',
   'ItalicAngle' => 'Угол наклона шрифта',
   'ItemSubType' => 'Подтип элемента',
   'Iterations' => 'Повторов',
   'JBIGOptions' => 'Параметры JBIG',
   'JFIFVersion' => 'Версия JFIF',
   'JP2Signature' => 'JP2 Подпись',
   'JPEGACTables' => 'Таблицы JPEGAC',
   'JPEGDCTables' => 'Таблицы JPEGDC',
   'JPEGDigest' => 'JPEG – Хеш-сумма',
   'JPEGImageLength' => 'JPEG – Строк в изображении',
   'JPEGLosslessPredictors' => 'Значения предиктора JPEG без потерь',
   'JPEGPointTransforms' => 'Преобразование точек JPEG',
   'JPEGProc' => {
      Description => 'JPEG-сжатие в старом стиле',
      PrintConv => {
        'Baseline' => 'Базовое последовательное',
        'Lossless' => 'Без потерь с кодированием Хаффмана',
      },
    },
   'JPEGQTables' => 'Таблицы JPEGQ',
   'JPEGQuality' => {
      Description => 'Качество изображения',
      PrintConv => {
        'Extra Fine' => 'Сверхвысокое',
        'Fine' => 'Высокое',
        'Standard' => 'Стандартное качество',
      },
    },
   'JPEGQualityEstimate' => 'JPEG – Оценка качества',
   'JPEGRestartInterval' => 'Интервал перезапуска JPEG',
   'JPEGTables' => 'JPEG-таблицы',
   'JSONMetadata' => 'Метаданные JSON',
   'JobID' => 'ID задания',
   'JobTitle' => 'Должность',
   'Journal' => 'Журнал',
   'JpgFromRaw' => 'Jpg-файл, встроенный в Raw-файл',
   'JpgFromRawLength' => 'Строк в JPG-файле, встроенного в RAW-файл',
   'JpgFromRawStart' => 'Смещение JPG-файла, встроенного в RAW-файл',
   'JumpToXPEP' => 'Переход к XPEP',
   'KBAT_Unknown10' => 'KBAT – Неизвестный 10',
   'KBAT_Unknown11' => 'KBAT – Неизвестный 11',
   'KBAT_Unknown12' => 'KBAT – Неизвестный 12',
   'KBAT_Unknown13' => 'KBAT – Неизвестный 13',
   'KBAT_Unknown2' => 'KBAT – Неизвестный 2',
   'KBAT_Unknown9' => 'KBAT – Неизвестный 9',
   'KByteSize' => 'Общий размер в килобайтах',
   'KF8CoverURI' => 'KF8 – URI обложки',
   'Key' => 'Код',
   'KeyCode' => 'Код ключа',
   'KeyFramePositions' => 'Положение ключевых кадров',
   'KeyFramesTimes' => 'Время ключевых кадров',
   'KeywordInfo' => 'Сведения о ключевых словах',
   'Keywords' => 'Ключевые слова',
   'Label' => 'Метка',
   'LameBitrate' => 'Lame – Битрейт',
   'LameLowPassFilter' => 'Lame – Фильтр нижних частот',
   'LameMethod' => {
      Description => 'Lame – Метод кодирования',
      PrintConv => {
        'ABR' => 'ABR – Средний битрейт',
        'ABR (2-pass)' => 'ABR – Средний битрейт (2 прохода)',
        'CBR' => 'CBR – Постоянный битрейт',
        'CBR (2-pass)' => 'CBR – Постоянный битрейт (2 прохода)',
        'VBR' => 'VBR – переменный битрейт',
        'VBR (new/mtrh)' => 'VBR – переменный битрейт (новый/mtrh)',
        'VBR (old/rh)' => 'VBR – переменный битрейт (старый/rh)',
      },
    },
   'LameQuality' => 'Lame – Качество',
   'LameStereoMode' => {
      Description => 'Lame – Режим стерео',
      PrintConv => {
        'Auto' => 'Автоматический',
        'Dual Channels' => 'Двуканальный режим',
        'Forced Joint Stereo' => 'Принудительное объединённое стерео',
        'Intensity Stereo' => 'Интенсивное стерео',
        'Joint Stereo' => 'Объединённое стерео',
        'Mono' => 'Моно',
        'Stereo' => 'Стерео',
      },
    },
   'LameVBRQuality' => 'Lame – Качество переменного битрейта',
   'Language' => 'Язык',
   'LanguageIdentifier' => 'Идентификатор языка',
   'LargestValidInteriorRectHeight' => 'Максимально возможная высота прямоугольника',
   'LargestValidInteriorRectLeft' => 'Отступ слева для максимально возможного прямоугольника',
   'LargestValidInteriorRectTop' => 'Отступ сверху для максимально возможного прямоугольника',
   'LargestValidInteriorRectWidth' => 'Максимально возможная ширина прямоугольника',
   'LastAuthor' => 'Последнее редактирование сделал',
   'LastBackupDate' => 'Дата последнего резервного копирования',
   'LastChar' => 'Последний символ шрифта',
   'LastKeyFrameTime' => 'Время последнего ключевого кадра',
   'LastKeywordIPTC' => 'Последнее ключевое слово IPTC',
   'LastKeywordXMP' => 'Последнее ключевое слово XMP',
   'LastModifiedBy' => 'Последнее изменение сделал',
   'LastPhotoDate' => 'Дата создания последнего изображения',
   'LastPrinted' => 'Дата последней печати',
   'LastTimeStamp' => 'Последняя временна́я метка',
   'LastUpdateTime' => 'Время последнего обновления',
   'LateralChromaticAberrationCorrectionAlreadyApplied' => 'Применена коррекция хроматической аберрации',
   'LatestAgeOrHighestStage' => 'Последний век/Высший ярус',
   'LatestEonOrHighestEonothem' => 'Последний эон/Высшая эонотема',
   'LatestEpochOrHighestSeries' => 'Последняя эпоха/Высший отдел',
   'LatestEraOrHighestErathem' => 'Последняя эра/Высшая эратема',
   'LatestPeriodOrHighestSystem' => 'Последний период/Высшая система',
   'Latitude' => 'Широта',
   'LayerBlendModes' => {
      Description => 'Режим наложения слоёв',
      PrintConv => {
        'Color' => 'Цветность',
        'Color Burn' => 'Затемнение основы',
        'Color Dodge' => 'Осветление основы',
        'Darken' => 'Затемнение',
        'Darker Color' => 'Темнее',
        'Difference' => 'Разница',
        'Dissolve' => 'Затухание',
        'Divide' => 'Разделить',
        'Exclusion' => 'Исключение',
        'Hard Light' => 'Жёсткий свет',
        'Hard Mix' => 'Жёсткое смешение',
        'Hue' => 'Цветовой тон',
        'Lighten' => 'Замена светлым',
        'Lighter Color' => 'Светлее',
        'Linear Burn' => 'Линейный затемнитель',
        'Linear Dodge' => 'Линейный осветлитель',
        'Linear Light' => 'Линейный свет',
        'Luminosity' => 'Яркость',
        'Multiply' => 'Умножение',
        'Normal' => 'Обычный',
        'Overlay' => 'Перекрытие',
        'Pass Through' => 'Пропустить',
        'Pin Light' => 'Точечный свет',
        'Saturation' => 'Насыщенность',
        'Screen' => 'Экран',
        'Soft Light' => 'Мягкий свет',
        'Subtract' => 'Вычитание',
        'Vivid Light' => 'Яркий свет',
      },
    },
   'LayerComps' => 'Композиции слоёв',
   'LayerCount' => 'Количество слоёв',
   'LayerGroupsEnabledID' => 'ID включенных групп слоёв',
   'LayerIDs' => 'ID слоёв',
   'LayerModifyDates' => 'Дата изменения слоя',
   'LayerNames' => 'Названия слоёв',
   'LayerOpacities' => 'Непрозрачность слоя',
   'LayerRectangles' => 'Прямоугольники слоя',
   'LayerSelectionIDs' => 'Идентификаторы выборанного слоя',
   'LayerUnicodeNames' => 'Юникодные названия слоёв',
   'LayersGroupInfo' => 'Информация о группе слоёв',
   'Layout' => {
      Description => 'Макет',
      PrintConv => {
        'Scan Lines' => 'Линии сканирования',
        'Tiles' => 'Тайлы',
      },
    },
   'LeftMargin' => 'Отступ слева',
   'LegacyIPTCDigest' => 'Хеш-сумма прежней версии IPTC',
   'Length' => 'Общий размер в байтах',
   'Lens' => 'Объектив',
   'Lens35efl' => 'Объектив',
   'LensDistortInfo' => 'Дисторсия объектива',
   'LensID' => 'ID объектива',
   'LensInfo' => 'Сведения об объективе',
   'LensMake' => 'Производитель объектива',
   'LensManufacturer' => 'Производитель объектива',
   'LensModel' => 'Модель объектива',
   'LensNumber' => 'Серийный номер объектива',
   'LensSerialNumber' => 'Серийный номер объектива',
   'LensShading' => 'Виньетирование объектива',
   'LensSpec' => 'Параметры объектива',
   'LensTemperature' => 'Температура объектива',
   'LibraryID' => 'ID библиотеки',
   'License' => 'Лицензия',
   'LicenseEndDate' => 'Дата окончания лицензии',
   'LicenseID' => 'PLUS-ID лицензии',
   'LicenseInfoURL' => 'URL лицензии',
   'LicenseStartDate' => 'Дата вступления лицензии в силу',
   'LicenseTransactionDate' => 'Дата лицензионной сделки',
   'Licensee' => 'Лицензия предоставлена для',
   'LicenseeID' => 'PLUS-ID лицензиата',
   'LicenseeImageID' => 'ID изображения присвоенный лицензиатом',
   'LicenseeImageNotes' => 'Примечания лицензиата',
   'LicenseeName' => 'Имя лицензиата',
   'LicenseeProjectReference' => 'Название проека присвоенное лицензиатом',
   'LicenseeTransactionID' => 'ID транзакции присвоенное лицензиатом',
   'Licensor' => 'Лицензор',
   'LicensorCity' => 'Город проживания лицензора',
   'LicensorCountry' => 'Страна проживания лицензора',
   'LicensorEmail' => 'Адрес электронной почты лицензора',
   'LicensorExtendedAddress' => 'Дополнительные адреса электронной почты лицензора',
   'LicensorID' => 'PLUS-ID лицензора',
   'LicensorImageID' => 'ID изображения данное лицензором',
   'LicensorName' => 'Имя лицензора',
   'LicensorNotes' => 'Коментарии лицензора',
   'LicensorPostalCode' => 'Почтовый индекс лицензиара',
   'LicensorRegion' => 'Область проживания лицензора',
   'LicensorStreetAddress' => 'Улица проживания лицензора',
   'LicensorTelephone1' => 'Телефон 1 лицензора',
   'LicensorTelephone2' => 'Телефон 2 лицензора',
   'LicensorTelephoneType1' => {
      Description => 'Тип телефона 1',
      PrintConv => {
        'Cell' => 'Мобильный',
        'FAX' => 'ФАКС',
        'Home' => 'Домашний',
        'Pager' => 'Пейджер',
        'Work' => 'Рабочий',
      },
    },
   'LicensorTelephoneType2' => {
      Description => 'Тип телефона 2',
      PrintConv => {
        'Cell' => 'Мобильный',
        'FAX' => 'ФАКС',
        'Home' => 'Домашний',
        'Pager' => 'Пейджер',
        'Work' => 'Рабочий',
      },
    },
   'LicensorTransactionID' => 'ID транзакции присвоенный лицензором',
   'LicensorURL' => 'URL лицензора',
   'LightSource' => {
      Description => 'Тип освещения',
      PrintConv => {
        'Cloudy' => 'Облачно (6500 К)',
        'Cool White Fluorescent' => 'Флуоресцентная лампа – Холодный свет (4150 К)',
        'Day White Fluorescent' => 'Флуоресцентная лампа – Дневной белый (5050 К)',
        'Daylight' => 'Дневной свет (5500 К)',
        'Daylight Fluorescent' => 'Флуоресцентная лампа дневного света (6400 К)',
        'Fine Weather' => 'Ясная погода (5500 К)',
        'Flash' => 'Вспышка (5500 К)',
        'Fluorescent' => 'Флуоресцентная лампа (4150 К)',
        'ISO Studio Tungsten' => 'ISO для студийных ламп накаливания',
        'Other' => 'Другой источник света',
        'Shade' => 'Тень (7500 К)',
        'Standard Light A' => 'Стандартный свет A',
        'Standard Light B' => 'Стандартный свет B',
        'Standard Light C' => 'Стандартный свет C',
        'Tungsten (Incandescent)' => 'Лампа накаливания (2850 К)',
        'Unknown' => 'Неизвестный',
        'Warm White Fluorescent' => 'Флуоресцентная лампа – Тёплый свет (2925 К)',
        'White Fluorescent' => 'Флуоресцентная лампа – Белый свет (3525 К)',
      },
    },
   'LightSourceSpecial' => {
      Description => 'Специальный источник света',
      PrintConv => {
        'Off' => 'Не использовался',
        'On' => 'Использовался',
      },
    },
   'LightValue' => 'Световое число',
   'Lightness' => 'Освещенность',
   'LightroomWorkflow' => 'Рабочий процесс Lightroom',
   'LineOrder' => {
      Description => 'Порядок строк',
      PrintConv => {
        'Decreasing Y' => 'По убыванию Y',
        'Increasing Y' => 'По возрастанию Y',
        'Random Y' => 'Случайный Y',
      },
    },
   'LinearResponseLimit' => 'Предел линейного отклика',
   'LinearityLimitBlue' => 'Предел линейности – Синий',
   'LinearityLimitGreen' => 'Предел линейности – Зелёный',
   'LinearityLimitRed' => 'Предел линейности – Красный',
   'LinearizationCoefficients1' => 'Коэффициенты линеаризации 1',
   'LinearizationCoefficients2' => 'Коэффициенты линеаризации 2',
   'LinearizationTable' => 'Таблица линеаризации',
   'Linearized' => {
      Description => 'Линеаризация',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'Lines' => 'Строк',
   'LinkedProfileName' => 'Название связанного профиля',
   'LithostratigraphicTerms' => 'Литостратиграфические названия пород',
   'LivingSpecimen' => 'Живой образец',
   'LivingSpecimenMaterialSampleID' => 'Живой образец – ID образца материала',
   'LocalBasePath' => 'Локальный базовый путь',
   'LocalCaption' => 'Описание места',
   'LocalDefaultAlarm' => 'Локальный сигнал по-умолчанию',
   'LocalPositionNED' => 'Локальная позиция NED',
   'LocalizedCameraModel' => 'Локализованное название модели камеры',
   'Location' => 'Местоположение',
   'Logo' => 'Логотип',
   'LongExposureNoiseReduction' => {
      Description => 'Шумоподавление при длительной выдержке',
      PrintConv => {
        'Auto' => 'Автоматическое',
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'Longitude' => 'Долгота',
   'LookModTransform' => 'Преобразование вида ACES-ACES',
   'LookupTable' => 'Таблица подстановки',
   'LowestBiostratigraphicZone' => 'Низшая биостратиграфическая зона',
   'MD5Signature' => 'MD5',
   'MD5Sum' => 'Сумма MD5',
   'MDColorTable' => 'MD – Таблица цветов',
   'MDFileTag' => 'MD – Формат исходных данных',
   'MDFileUnits' => 'MD – Единицы данных файла',
   'MDItemAccountHandles' => 'MD Item – Дескрипторы аккаунта',
   'MDItemAccountIdentifier' => 'MD Item – Идентификатор аккаунта',
   'MDItemAcquisitionMake' => 'MD Item – Производитель устройства',
   'MDItemAcquisitionModel' => 'MD Item – Модель устройства',
   'MDItemAltitude' => 'MD Item – Высота',
   'MDItemAperture' => 'MD Item – Диафрагма',
   'MDItemAudioBitRate' => 'MD Item – Битрейт аудио',
   'MDItemAudioChannelCount' => 'MD Item – Количество аудиоканалов',
   'MDItemAuthorEmailAddresses' => 'MD Item – Адрес электронной почты автора',
   'MDItemAuthors' => 'MD Item – Авторы',
   'MDItemBitsPerSample' => 'MD Item – Количество бит на образец',
   'MDItemBundleIdentifier' => 'MD Item – Идентификатор комплекта',
   'MDItemCity' => 'MD Item – Город',
   'MDItemCodecs' => 'MD Item – Кодеки',
   'MDItemColorSpace' => 'MD Item – Цветовое пространство',
   'MDItemComment' => 'MD Item – Комментарий',
   'MDItemContentCreationDate' => 'MD Item – Дата создания',
   'MDItemContentCreationDateRanking' => 'MD Item – Дата создания – Рейтинг',
   'MDItemContentCreationDate_Ranking' => 'MD Item – Дата создания – Рейтинг',
   'MDItemContentModificationDate' => 'MD Item – Дата редактирования',
   'MDItemContentType' => 'MD Item – Тип контента',
   'MDItemContentTypeTree' => 'MD Item – Тип контента – Древо',
   'MDItemContributors' => 'MD Item – Внесшие вклад',
   'MDItemCopyright' => 'MD Item – Авторское право',
   'MDItemCountry' => 'MD Item – Страна',
   'MDItemCreator' => 'MD Item – Создано в',
   'MDItemDateAdded' => 'MD Item – Дата добавления',
   'MDItemDateAdded_Ranking' => 'MD Item – Дата добавления –  Рейтинг',
   'MDItemDescription' => 'MD Item – Описание',
   'MDItemDisplayName' => 'MD Item – Локализованное название файла',
   'MDItemDownloadedDate' => 'MD Item – Дата загрузки',
   'MDItemDurationSeconds' => 'MD Item – Продолжительность в секундах',
   'MDItemEXIFGPSVersion' => 'MD Item – EXIF – Версия GPS',
   'MDItemEXIFVersion' => 'MD Item – Версия EXIF',
   'MDItemEmailConversationID' => 'MD Item – ID переписки по электронной почте',
   'MDItemEncodingApplications' => 'MD Item – Кодировано в приложении',
   'MDItemExposureMode' => 'MD Item – Режим экспозиции',
   'MDItemExposureProgram' => 'MD Item – Программа экспозиции',
   'MDItemExposureTimeSeconds' => 'MD Item – Выдержка',
   'MDItemFNumber' => 'MD Item – Диафрагменное число',
   'MDItemFSContentChangeDate' => 'MD Item – FS – Дата редактирования',
   'MDItemFSCreationDate' => 'MD Item – FS – Дата создания',
   'MDItemFSCreatorCode' => 'MD Item – FS – Код программы, создавшей файл',
   'MDItemFSFinderFlags' => 'MD Item – FS – Атрибуты файла',
   'MDItemFSHasCustomIcon' => 'MD Item – FS – Наличие иконки файла',
   'MDItemFSInvisible' => 'MD Item – FS – Скрытый',
   'MDItemFSIsExtensionHidden' => 'MD Item – FS – Расширение файла скрыто',
   'MDItemFSIsStationery' => 'MD Item – FS – Шаблон',
   'MDItemFSLabel' => {
      Description => 'MD Item – FS – Цветная метка файла',
      PrintConv => {
        '0 (none)' => '0 (Без метки)',
        '1 (Gray)' => '1 (Серая)',
        '2 (Green)' => '2 (Зелёная)',
        '3 (Purple)' => '3 (Пурпурная)',
        '4 (Blue)' => '4 (Синяя)',
        '5 (Yellow)' => '5 (Жёлтая)',
        '6 (Red)' => '6 (Красная)',
        '7 (Orange)' => '7 (Оранжевая)',
      },
    },
   'MDItemFSName' => 'MD Item – FS – Название файла',
   'MDItemFSNodeCount' => 'MD Item – FS – Количество файлов в каталоге',
   'MDItemFSOwnerGroupID' => 'MD Item – FS – ID группы владельца файла',
   'MDItemFSOwnerUserID' => 'MD Item – FS – ID пользователя владельца файла',
   'MDItemFSSize' => 'MD Item – FS – Размер файла',
   'MDItemFSTypeCode' => 'MD Item – FS – Код типа файла',
   'MDItemFinderComment' => 'MD Item – Комментарий к файлу',
   'MDItemFlashOnOff' => 'MD Item – Срабатывание вспышки',
   'MDItemFocalLength' => 'MD Item – Фокусное расстояние',
   'MDItemGPSDateStamp' => 'MD Item –  GPS – Дата и время местоположения',
   'MDItemGPSStatus' => 'MD Item –  GPS – Состояние  приемника во время съёмки',
   'MDItemGPSTrack' => 'MD Item –  GPS – Трек маршрута',
   'MDItemHasAlphaChannel' => 'MD Item – Наличие Альфа-канала',
   'MDItemISOSpeed' => 'MD Item  – ISO',
   'MDItemIdentifier' => 'MD Item – Идентификатор',
   'MDItemImageDirection' => 'MD Item – Направления камеры при съёмке',
   'MDItemInterestingDateRanking' => 'MD Item – Дата интересных мест – Рейтинг',
   'MDItemInterestingDate_Ranking' => 'MD Item – Дата интересных мест – Рейтинг',
   'MDItemIsApplicationManaged' => 'MD Item – Управляется приложением',
   'MDItemIsExistingThread' => 'MD Item – Цепочка сообщений',
   'MDItemIsLikelyJunk' => 'MD Item – Спам',
   'MDItemKeywords' => 'MD Item – Ключевые слова',
   'MDItemKind' => 'MD Item – Тип файла',
   'MDItemLastUsedDate' => 'MD Item – Дата последнего использования',
   'MDItemLastUsedDate_Ranking' => 'MD Item – Дата последнего использования – Рейтинг',
   'MDItemLatitude' => 'MD Item – Широта',
   'MDItemLensModel' => 'MD Item – Модель объектива',
   'MDItemLogicalSize' => 'MD Item – Логический размер',
   'MDItemLongitude' => 'MD Item – Долгота',
   'MDItemMailDateReceived_Ranking' => 'MD Item – Дата получения почты – Рейтинг',
   'MDItemMailboxes' => 'MD Item – Почтовые ящики',
   'MDItemMediaTypes' => 'MD Item – Типы медиафайлов',
   'MDItemNumberOfPages' => 'MD Item – Количество страниц',
   'MDItemOrientation' => 'MD Item – Ориентация',
   'MDItemOriginApplicationIdentifier' => 'MD Item – Идентификатор приложения',
   'MDItemOriginMessageID' => 'MD Item – ID сообщения',
   'MDItemOriginSenderDisplayName' => 'MD Item – Имя отправителя',
   'MDItemOriginSenderHandle' => 'MD Item – Дескриптор отправителя',
   'MDItemOriginSubject' => 'MD Item – Тема сообщения',
   'MDItemPageHeight' => 'MD Item – Высота страницы',
   'MDItemPageWidth' => 'MD Item – Ширина страницы',
   'MDItemPhysicalSize' => 'MD Item – Физический размер',
   'MDItemPixelCount' => 'MD Item – Количество пикселей',
   'MDItemPixelHeight' => 'MD Item – Высота в пикселях',
   'MDItemPixelWidth' => 'MD Item – Ширина в пикселях',
   'MDItemPrimaryRecipientEmailAddresses' => 'MD Item – Адрес электронной почты основного получателя',
   'MDItemProfileName' => 'MD Item – Название цветового профиля',
   'MDItemRecipients' => 'MD Item – Получатели',
   'MDItemRedEyeOnOff' => 'MD Item – Уменьшение эффекта "красных глаз"',
   'MDItemResolutionHeightDPI' => 'MD Item – Разрешение по высоте в DPI',
   'MDItemResolutionWidthDPI' => 'MD Item – Разрешение по ширине в DPI',
   'MDItemSecurityMethod' => 'MD Item – Метод шифрования',
   'MDItemSpeed' => 'MD Item – Скорость передвижения',
   'MDItemStateOrProvince' => 'MD Item – Область/Район',
   'MDItemStreamable' => 'MD Item – Потоковый',
   'MDItemSubject' => 'MD Item – Тема',
   'MDItemTimestamp' => 'MD Item – Временна́я метка',
   'MDItemTitle' => 'MD Item – Название файла',
   'MDItemTotalBitRate' => 'MD Item – Общий битрейт',
   'MDItemUseCount' => 'MD Item – Счётчик использования',
   'MDItemUsedDates' => 'MD Item – Используемые даты',
   'MDItemUserDownloadedDate' => 'MD Item – Пользователь – Дата загрузки',
   'MDItemUserDownloadedUserHandle' => 'MD Item – Пользователь – Дескриптор загрузки пользователя',
   'MDItemUserSharedReceivedDate' => 'MD Item – User Shared Received Date',
   'MDItemUserSharedReceivedRecipient' => 'MD Item – User Shared Received Recipient',
   'MDItemUserSharedReceivedRecipientHandle' => 'MD Item – User Shared Received Recipient Handle',
   'MDItemUserSharedReceivedSender' => 'MD Item – User Shared Received Sender',
   'MDItemUserSharedReceivedSenderHandle' => 'MD Item – User Shared Received Sender Handle',
   'MDItemUserSharedReceivedTransport' => 'MD Item – Общая папка пользователя – Received Transport',
   'MDItemUserTags' => 'MD Item – Пользовательские теги',
   'MDItemVersion' => 'MD Item – Версия файла',
   'MDItemVideoBitRate' => 'MD Item – Битрейт видео',
   'MDItemWhereFroms' => 'MD Item – Источник файла',
   'MDItemWhiteBalance' => 'MD Item – Баланс белого',
   'MDLabName' => 'MD – Создатель файла',
   'MDPrepDate' => 'MD – Дата создания',
   'MDPrepTime' => 'MD – Время создания',
   'MDSampleInfo' => 'MD – Описание файла',
   'MDScalePixel' => 'MD – Коефициент масштабирования пикселя',
   'MIDIControlVersion' => 'Версия MIDI Control',
   'MIDISong' => 'MIDI композиция',
   'MIMEType' => 'MIME тип',
   'MOIVersion' => 'Версия MOI',
   'MPEG7Binary' => 'Бинарный MPEG7',
   'MPEGAudioVersion' => 'Версия MPEG аудио',
   'MPFVersion' => 'Версия MPF',
   'MPImage' => 'Изображение MP',
   'MPImageFlags' => {
      Description => 'MP изображение – Флаги',
      PrintConv => {
        'Dependent child image' => 'Зависимое вложенное изображение',
        'Dependent parent image' => 'Зависимое родительское изображение',
        'Representative image' => 'Репрезентативное изображение',
      },
    },
   'MPImageFormat' => 'MP изображение – Формат',
   'MPImageLength' => 'MP изображение – Количество строк',
   'MPImageStart' => 'MP изображение – Начало',
   'MPImageType' => {
      Description => 'MP изображение – Тип',
      PrintConv => {
        'Baseline MP Primary Image' => 'Базовое первичное изображение MP',
        'Large Thumbnail (VGA equivalent)' => 'Большой эскиз (эквивалент VGA)',
        'Large Thumbnail (full HD equivalent)' => 'Большой эскиз (эквивалент Full HD)',
        'Multi-angle' => 'Многоракурсное',
        'Multi-frame Disparity' => 'Многокадровое смещённое (Disparity)',
        'Multi-frame Panorama' => 'Многокадровая панорама',
        'Undefined' => 'Неопределённый',
      },
    },
   'MPIndividualNum' => 'Индивидуальный номер MP',
   'MSDocumentText' => 'MS – Текстовый документ',
   'MSDocumentTextPosition' => 'MS – Текстовый документ – Позиция',
   'MSPropertySetStorage' => 'MS – Хранилище свойств',
   'MSStereo' => {
      Description => 'MS стерео',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'MachineID' => 'ID машины',
   'MachineObservation' => 'Машинное наблюдение',
   'MachineObservationDay' => 'День месяца машинного наблюдения',
   'MachineObservationEarliestDate' => 'Дата начала машинного наблюдения',
   'MachineObservationEndDayOfYear' => 'День года окончания машинного наблюдения',
   'MachineObservationEventDate' => 'Дата машинного наблюдения',
   'MachineObservationEventID' => 'ID машинного наблюдения',
   'MachineObservationEventRemarks' => 'Комментарии к машинному наблюдению',
   'MachineObservationEventTime' => 'Время машинного наблюдения',
   'MachineObservationFieldNotes' => 'Полевые заметки о машинном наблюдении',
   'MachineObservationFieldNumber' => 'Номер полевых заметок о машинном наблюдении',
   'MachineObservationHabitat' => 'Ареал машинного наблюдения',
   'MachineObservationLatestDate' => 'Дата окончания машинного наблюдения',
   'MachineObservationMonth' => 'Месяц машинного наблюдения',
   'MachineObservationParentEventID' => 'ID основного машинного наблюдения',
   'MachineObservationSampleSizeUnit' => 'Машинное наблюдение – Единицы размера',
   'MachineObservationSampleSizeValue' => 'Машинное наблюдение – Размер образца',
   'MachineObservationSamplingEffort' => 'Машинное наблюдение – Затраченные усилия',
   'MachineObservationSamplingProtocol' => 'Машинное наблюдение – Метод исследования',
   'MachineObservationStartDayOfYear' => 'День года начала машинного наблюдения',
   'MachineObservationVerbatimEventDate' => 'Дата машинного наблюдения дословно',
   'MachineObservationYear' => 'Год машинного наблюдения',
   'MacintoshNSPrintInfo' => 'Macintosh NS – Сведения о печати',
   'MacintoshPrintInfo' => 'Macintosh – Сведения о печати',
   'Macro' => {
      Description => 'Макро',
      PrintConv => {
        'Macro' => 'Макро',
        'Manual' => 'Ручной',
        'Normal' => 'Обычный',
        'Off' => 'Выключен',
        'On' => 'Включен',
      },
    },
   'Magnetometer' => 'Магнитометр',
   'MajorBrand' => {
      Description => 'Основная торговая марка',
      PrintConv => {
        'JPEG 2000 Compound Image (.JPM)' => 'Составное изображение JPEG 2000 (.JPM)',
        'JPEG 2000 Image (.JP2)' => 'Изображение JPEG 2000 (.JP2)',
        'JPEG 2000 with extensions (.JPX)' => 'Улучшенный JPEG 2000 (.JPX)',
      },
    },
   'MajorVersion' => 'Мажорная версия',
   'Make' => 'Производитель',
   'MakerNote' => 'Приватные данные DNG',
   'MakerNoteApple' => 'Комментарии Apple',
   'MakerNoteCanon' => 'Комментарии Canon',
   'MakerNoteCasio' => 'Комментарии Casio',
   'MakerNoteCasio2' => 'Комментарии Casio 2',
   'MakerNoteDJI' => 'Комментарии DJI',
   'MakerNoteFLIR' => 'Комментарии FLIR',
   'MakerNoteFujiFilm' => 'Комментарии Fuji Film',
   'MakerNoteGE' => 'Комментарии GE',
   'MakerNoteGE2' => 'Комментарии GE2',
   'MakerNoteHP' => 'Комментарии HP',
   'MakerNoteHP2' => 'Комментарии HP2',
   'MakerNoteHP4' => 'Комментарии HP4',
   'MakerNoteHP6' => 'Комментарии HP6',
   'MakerNoteHasselblad' => 'Комментарии Hasselblad',
   'MakerNoteISL' => 'Комментарии ISL',
   'MakerNoteJVC' => 'Комментарии JVC',
   'MakerNoteJVCText' => 'Комментарии JVC – Текст',
   'MakerNoteKodak10' => 'Комментарии Kodak 10',
   'MakerNoteKodak11' => 'Комментарии Kodak 11',
   'MakerNoteKodak12' => 'Комментарии Kodak 12',
   'MakerNoteKodak1a' => 'Комментарии Kodak 1a',
   'MakerNoteKodak1b' => 'Комментарии Kodak 1b',
   'MakerNoteKodak2' => 'Комментарии Kodak 2',
   'MakerNoteKodak3' => 'Комментарии Kodak 3',
   'MakerNoteKodak4' => 'Комментарии Kodak 4',
   'MakerNoteKodak5' => 'Комментарии Kodak 5',
   'MakerNoteKodak6a' => 'Комментарии Kodak 6a',
   'MakerNoteKodak6b' => 'Комментарии Kodak 6b',
   'MakerNoteKodak7' => 'Комментарии Kodak 7',
   'MakerNoteKodak8a' => 'Комментарии Kodak 8a',
   'MakerNoteKodak8b' => 'Комментарии Kodak 8b',
   'MakerNoteKodak8c' => 'Комментарии Kodak 8c',
   'MakerNoteKodak9' => 'Комментарии Kodak 9',
   'MakerNoteKodakUnknown' => 'Комментарии Kodak – Неизвестно',
   'MakerNoteKyocera' => 'Комментарии Kyocera',
   'MakerNoteLeica' => 'Комментарии Leica',
   'MakerNoteLeica10' => 'Комментарии Leica 10',
   'MakerNoteLeica2' => 'Комментарии Leica 2',
   'MakerNoteLeica3' => 'Комментарии Leica 3',
   'MakerNoteLeica4' => 'Комментарии Leica 4',
   'MakerNoteLeica5' => 'Комментарии Leica 5',
   'MakerNoteLeica6' => 'Комментарии Leica 6',
   'MakerNoteLeica7' => 'Комментарии Leica 7',
   'MakerNoteLeica8' => 'Комментарии Leica 8',
   'MakerNoteLeica9' => 'Комментарии Leica 9',
   'MakerNoteMinolta' => 'Комментарии Minolta',
   'MakerNoteMinolta2' => 'Комментарии Minolta 2',
   'MakerNoteMinolta3' => 'Комментарии Minolta 3',
   'MakerNoteMotorola' => 'Комментарии Motorola',
   'MakerNoteNikon' => 'Комментарии Nikon',
   'MakerNoteNikon2' => 'Комментарии Nikon 2',
   'MakerNoteNikon3' => 'Комментарии Nikon 3',
   'MakerNoteNintendo' => 'Комментарии Nintendo',
   'MakerNoteOffset' => 'Смещение заметки производителя',
   'MakerNoteOlympus' => 'Комментарии Olympus',
   'MakerNoteOlympus2' => 'Комментарии Olympus 2',
   'MakerNotePanasonic' => 'Комментарии Panasonic',
   'MakerNotePanasonic2' => 'Комментарии Panasonic 2',
   'MakerNotePanasonic3' => 'Комментарии Panasonic 3',
   'MakerNotePentax' => 'Комментарии Pentax',
   'MakerNotePentax2' => 'Комментарии Pentax 2',
   'MakerNotePentax3' => 'Комментарии Pentax 3',
   'MakerNotePentax4' => 'Комментарии Pentax 4',
   'MakerNotePentax5' => 'Комментарии Pentax 5',
   'MakerNotePentax6' => 'Комментарии Pentax 6',
   'MakerNotePhaseOne' => 'Комментарии Phase One',
   'MakerNoteReconyx' => 'Комментарии Reconyx',
   'MakerNoteReconyx2' => 'Комментарии Reconyx 2',
   'MakerNoteReconyx3' => 'Комментарии Reconyx 3',
   'MakerNoteRicoh' => 'Комментарии Ricoh',
   'MakerNoteRicoh2' => 'Комментарии Ricoh 2',
   'MakerNoteRicohPentax' => 'Комментарии Ricoh Pentax',
   'MakerNoteRicohText' => 'Комментарии Ricoh – Текст',
   'MakerNoteSafety' => {
      Description => 'Примечания безопасности',
      PrintConv => {
        'Safe' => 'Безопасно',
        'Unsafe' => 'Небезопасно',
      },
    },
   'MakerNoteSamsung1a' => 'Комментарии Samsung 1a',
   'MakerNoteSamsung1b' => 'Комментарии Samsung 1b',
   'MakerNoteSamsung2' => 'Комментарии Samsung 2',
   'MakerNoteSanyo' => 'Комментарии Sanyo',
   'MakerNoteSanyoC4' => 'Комментарии Sanyo C4',
   'MakerNoteSanyoPatch' => 'Комментарии Sanyo Patch',
   'MakerNoteSigma' => 'Комментарии Sigma',
   'MakerNoteSigma3' => 'Комментарии Sigma 3',
   'MakerNoteSony' => 'Комментарии Sony',
   'MakerNoteSony2' => 'Комментарии Sony 2',
   'MakerNoteSony3' => 'Комментарии Sony 3',
   'MakerNoteSony4' => 'Комментарии Sony 4',
   'MakerNoteSony5' => 'Комментарии Sony 5',
   'MakerNoteSonyEricsson' => 'Комментарии Sony Ericsson',
   'MakerNoteSonySRF' => 'Комментарии Sony SRF',
   'MakerNoteUnknown' => 'Комментарии неизвестного производителя',
   'MakerNoteUnknownBinary' => 'Комментарии неизвестного производителя – Бинарный',
   'MakerNoteUnknownText' => 'Комментарии неизвестного производителя – Текстовый',
   'MakerNoteVersion' => 'Заметка производителя – Версия',
   'MakerNotes' => 'Данные производителей',
   'Manager' => 'Руководитель',
   'ManualFocusDistance' => 'Расстояние ручной фокусировки',
   'Manufacturer' => 'Разработчик',
   'MappingScheme' => 'Схема подстановки отсутствующего шрифта',
   'MarkerID' => 'ID маркера',
   'MaskedAreas' => 'Маскированные области',
   'MasterDocumentID' => 'Основной ID документа',
   'MaterialSample' => 'Материал образца',
   'MaterialSampleID' => 'ID материала образца',
   'MatrixWorldToCamera' => 'Матрица мира на камеру',
   'MatrixWorldToScreen' => 'Матрица мира на экран',
   'MattColor' => 'Matt цвет',
   'Matte' => 'Matte (Маска?)',
   'Matteing' => 'Матирование',
   'MaxAperture' => 'Максимальная диафрагма объектива',
   'MaxApertureValue' => 'Максимальное значение диафрагмы',
   'MaxBand' => 'Максимальный диапазон',
   'MaxBitrate' => 'Максимальный битрейт',
   'MaxPacketSize' => 'Размер максимального пакета',
   'MaxSampleValue' => 'Максимальное значение компонента',
   'MaxSubfileSize' => 'Максимальный размер подфайла',
   'MaxVal' => 'Максимальное значение пикселя',
   'MaxWidth' => 'Максимальная ширина глифа',
   'MaximumBitrate' => 'Максимальный битрейт',
   'MaximumDensityRange' => 'Максимальный диапазон плотности',
   'MaximumObjectSize' => 'Максимальный размер объекта',
   'MeasureType' => {
      Description => 'Тип измерения глубины',
      PrintConv => {
        'OpticalAxis' => 'Оптическая ось',
        'OpticalRay' => 'Оптический луч',
      },
    },
   'MeasurementAccuracy' => 'Потенциальная погрешность измерения',
   'MeasurementDeterminedBy' => 'Люди определившие значение измерения',
   'MeasurementDeterminedDate' => 'Дата определения измерения',
   'MeasurementID' => 'ID измерения',
   'MeasurementMethod' => 'Метод измерения',
   'MeasurementOrFact' => 'Сведения об измерении',
   'MeasurementRemarks' => 'Заметки измерения',
   'MeasurementScale' => 'Шкала измерений',
   'MeasurementType' => 'Тип измерения',
   'MeasurementUnit' => 'Единицы измерения',
   'MeasurementValue' => 'Значение измерения',
   'MediaConstraints' => 'Ограничения на медиаресурсы',
   'MediaData' => 'Медиа-данные',
   'MediaEventIdDate' => 'Дата события',
   'MediaIsDelay' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsFinale' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsLive' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsMovie' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsPremiere' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsRepeat' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsSAP' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsSport' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsStereo' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsSubtitled' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaIsTape' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'MediaSummaryCode' => 'Сводный код медиаресурсов',
   'MeetingLocations' => 'Место встречи',
   'Megapixels' => 'Мегапикселей',
   'MelodicPolyphony' => 'Полифоническая мелодия',
   'Message' => 'Сообщение',
   'MetadataCreator' => 'Метаданные – Создатель',
   'MetadataDate' => 'Метаданные – Дата',
   'MeteringMode' => {
      Description => 'Экспозамер',
      PrintConv => {
        'Average' => 'Усреднённый',
        'Center-weighted average' => 'Центрально-взвешенный',
        'Multi-segment' => 'Оценочный/Многозонный',
        'Multi-spot' => 'Многоточечный',
        'Other' => 'Другой',
        'Partial' => 'Частичный',
        'Spot' => 'Точечный',
        'Unknown' => 'Неизвестный',
      },
    },
   'Method' => 'Метод',
   'Micro1Version' => 'Micro 1 – Версия',
   'Micro2Version' => 'Micro 2 – Версия',
   'Mime' => 'MIME-тип',
   'MinApertureValue' => 'Минимальное число диафрагмы объектива',
   'MinSampleValue' => 'Минимальное значение компонента',
   'MinimumBitrate' => 'Минимальный битрейт',
   'MinimumVersion' => 'Минимальная версия',
   'MinorModelAgeDisclosure' => {
      Description => 'Возраст самой молодой модели на снимке',
      PrintConv => {
        'Age 14 or Under' => '14 лет и меньше',
        'Age 15' => '15 лет',
        'Age 16' => '16 лет',
        'Age 17' => '17 лет',
        'Age 18' => '18 лет',
        'Age 19' => '19 лет',
        'Age 20' => '20 лет',
        'Age 21' => '21 год',
        'Age 22' => '22 года',
        'Age 23' => '23 года',
        'Age 24' => '24 года',
        'Age 25 or Over' => '25 лет и выше',
        'Age Unknown' => 'Возраст неизвестен',
      },
    },
   'MinorVersion' => 'Минорная версия',
   'MobiType' => {
      Description => 'Тип Mobi',
      PrintConv => {
        'Audio' => 'Аудио',
        'KF8: generated by kindlegen2' => 'KF8: сгенерированный kindlegen 2',
        'Mobipocket Book' => 'Книга Mobipocket',
        'News' => 'Новости',
        'News_Feed' => 'Новостная лента',
        'News_Magazine' => 'Журнал новостей',
        'PalmDoc Book' => 'Книга PalmDoc',
        'mobipocket? generated by kindlegen1.2' => 'Mobipocket? Сгенерированный kindlegen 1.2',
      },
    },
   'MobiVersion' => 'Версия Mobi',
   'ModDate' => 'Дата редактирования',
   'ModeExtension' => {
      Description => 'Расширение режима канала',
      PrintConv => {
        'Bands 12-31' => 'Полосы 12-32',
        'Bands 16-31' => 'Полосы 16-31',
        'Bands 4-31' => 'Полосы 4-31',
        'Bands 8-31' => 'Полосы 8-31',
      },
    },
   'ModeNumber' => 'Режим стандарта факс-профиля',
   'Model' => 'Модель камеры',
   'Model2' => 'Модель 2',
   'ModelReleaseID' => 'ID релиза модели',
   'ModelReleaseStatus' => {
      Description => 'Статус релиза модели',
      PrintConv => {
        'Limited or Incomplete Model Releases' => 'Ограниченные или неполные версии моделей',
        'None' => 'Не обозначено',
        'Not Applicable' => 'Не действующий',
        'Unlimited Model Releases' => 'Неограниченное количество релизов модели',
      },
    },
   'ModelTiePoint' => 'Пространственная привязка',
   'ModelTransform' => 'Модель преобразования',
   'ModificationNumber' => 'Номер модификации',
   'ModificationPermissions' => {
      Description => 'Разрешения на Редактирование',
      PrintConv => {
        'Do not restrict applications to reader permissions' => 'Не ограничивать всем приложениям права на чтение',
        'Fill forms, Create page templates, Sign' => 'Разрешается: Заполнение форм, Создание шаблонов страниц, Электронная подпись',
        'Fill forms, Create page templates, Sign, Create/Delete/Edit annotations' => 'Разрешается: Заполнение форм, Создание шаблонов страниц, Электронная подпись, Создание/Удаление/Редактирование аннотаций',
        'No changes permitted' => 'Редактирование запрещено',
        'Restrict all applications to reader permissions' => 'Ограничивать всем приложениям права на чтение',
      },
    },
   'ModifyDate' => 'Дата редактирования',
   'MoireFilter' => 'Фильтр муара',
   'Montage' => 'Монтаж',
   'Month' => 'Месяц',
   'MoonPhase' => {
      Description => 'Фаза луны',
      PrintConv => {
        'First Quarter' => 'Первая четверть',
        'Full' => 'Полнолуние',
        'Last Quarter' => 'Последняя четверть',
        'New' => 'Новолуние',
        'New Crescent' => 'Молодая луна',
        'Old Crescent' => 'Старая луна',
        'Waning Gibbous' => 'Убывающая луна',
        'Waxing Gibbous' => 'Растущая луна',
      },
    },
   'MotionSensitivity' => 'Обнаружение движения',
   'MultiFrameNoiseReduction' => {
      Description => 'Многокадровое шумоподавление',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'MultiProfiles' => {
      Description => 'Мультипрофили',
      PrintConv => {
        'JBIG2 Profile M' => 'JBIG2 профиль M',
        'N Layer Profile M' => 'N слой Профиль M',
        'Profile C' => 'Профиль C',
        'Profile F' => 'Профиль F',
        'Profile J' => 'Профиль J',
        'Profile L' => 'Профиль L',
        'Profile M' => 'Профиль M',
        'Profile S' => 'Профиль S',
        'Profile T' => 'Профиль T',
        'Resolution/Image Width' => 'Разрешение/Ширина изображения',
        'Shared Data' => 'Общие данные',
      },
    },
   'MultimediaType' => 'Тип мультимедиа',
   'Multishot' => {
      Description => 'Мультисъёмка',
      PrintConv => {
        'Off' => 'Не включена',
        'Pixel Shift' => 'Сдвиг пикселей',
      },
    },
   'Name' => 'Название файла',
   'NameTableVersion' => 'Таблица имён – Версия',
   'NameUTF-8' => 'Название в кодировке UTF-8',
   'Narrator' => 'Имя диктора',
   'Near' => 'Ближайшее значение карты глубины',
   'NetName' => 'Сетевое имя',
   'NetProviderType' => 'Тип сетевого провайдера',
   'NewGUID' => 'Новый GUID',
   'NewRawImageDigest' => 'Хеш-сумма RAW-изображения в новом формате',
   'NewsPhotoVersion' => 'Версия News Photo',
   'Nickname' => 'Прозвище',
   'NoMSSmartTags' => 'Смарт-теги MS отсутствуют',
   'Noise' => 'Уровень шума',
   'NoiseProfile' => 'Профиль – Уровень шума',
   'NoiseReduction' => 'Шумоподавление',
   'NoiseReductionApplied' => 'Применённое шумоподавление',
   'NoiseReductionMode' => {
      Description => 'Режим шумоподавления',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включён',
      },
    },
   'NoiseReductionParams' => 'Параметры шумоподавления',
   'NoiseReductionValue' => 'Значение шумоподавления',
   'NominalBitrate' => 'Номинальный битрейт',
   'NominalVideoBitrate' => {
      Description => 'Номинальный битрейт видео',
      PrintConv => {
        'Unspecified' => 'Неопределённый',
      },
    },
   'Note' => 'Заметка',
   'Notes' => 'Комментарии',
   'Notice' => 'Заметка',
   'Now' => 'Текущие Дата и Время',
   'NumChannels' => 'Количество каналов',
   'NumColors' => 'Количество цветов',
   'NumFonts' => 'Количество шрифтов в коллекции',
   'NumImportantColors' => 'Количество важных цветов',
   'NumIndexEntries' => 'Количество индексов',
   'NumPackets' => 'Количество пакетов',
   'NumProperties' => 'Количество свойств',
   'NumRules' => 'Количество правил',
   'NumSampleFrames' => 'Количество кадров',
   'NumSlices' => 'Количество фрагментов',
   'NumStreams' => 'Количество потоков',
   'Number' => 'Номер',
   'NumberList' => 'Нумерованный список',
   'NumberOfComponents' => 'Количество компонентов',
   'NumberOfImages' => 'Количество изображений',
   'NumberofInks' => 'Количество чернил',
   'OPIProxy' => {
      Description => 'Подстановка изображения',
      PrintConv => {
        'Higher resolution image does not exist' => 'Изображение большего разрешения отсутствует',
        'Higher resolution image exists' => 'Присутствует изображение с большим разрешением',
      },
    },
   'Object' => 'Объект',
   'ObjectAttributeReference' => 'Ссылка на атрибут объекта',
   'ObjectCycle' => {
      Description => 'Суточный цикл',
      PrintConv => {
        'Both Morning and Evening' => 'Утро и вечер',
        'Evening' => 'Вечер',
        'Morning' => 'Утро',
      },
    },
   'ObjectName' => 'Название объекта',
   'ObjectPreviewData' => 'Предпросмотр объекта – Данные',
   'ObjectPreviewFileFormat' => {
      Description => 'Предпросмотр объекта – Формат файла',
      PrintConv => {
        'AppleSingle (Apple Computer Inc)' => 'AppleSingle (Apple)',
        'Audio Interchange File Format AIFF (Apple Computer Inc)' => 'Аудиофайл формата AIFF (Apple)',
        'Bit Mapped Graphics File [.BMP] (Microsoft)' => 'Битовый графический файл [.BMP] (Microsoft)',
        'Compressed Binary File [.ZIP] (PKWare Inc)' => 'Сжатый двоичный файл [.ZIP] (Компания PKWare)',
        'Digital Audio File [.WAV] (Microsoft & Creative Labs)' => 'Аудиофайл формата [.WAV] (Microsoft и Creative Labs)',
        'Hypertext Markup Language [.HTML] (The Internet Society)' => 'Язык гипертекстовой разметки [.HTML] (The Internet Society)',
        'IPTC Unstructured Character Oriented File Format (UCOFF)' => 'Неструктурированный символьно-ориентированный формат файла IPTC  (UCOFF)',
        'IPTC7901 Recommended Message Format' => 'IPTC7901 Рекомендуемый формат сообщения',
        'Illustrator (Adobe Graphics data)' => 'Illustrator (Графические данные Adobe)',
        'JPEG File Interchange (JFIF)' => 'Формат обмена файлами стандарта JPEG (JFIF)',
        'MPEG 2 Audio Layer 2 (Musicom), ISO/IEC' => 'Аудио второго уровня MPEG 2  (Musicom), ISO/IEC',
        'MPEG 2 Audio Layer 3, ISO/IEC' => 'Аудио третьего уровня MPEG 2 , ISO/IEC',
        'News Industry Text Format (NITF)' => 'Текстовый формат новостной индустрии (NITF)',
        'No ObjectData' => 'Данные объекта отсутствуют',
        'PC DOS/Windows Executable Files [.COM][.EXE]' => 'Исполняемый файл PC DOS/Windows [.COM][.EXE]',
        'RIFF Wave (Microsoft Corporation)' => 'RIFF Wave (Microsoft)',
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Ritzaus Bureau вариант NITF  (RBNITF DTD)',
        'Tagged Image File Format (Adobe/Aldus Image data)' => 'Формат файла изображения с тегами (Adobe/Aldus данные изображения)',
        'Tape Archive [.TAR]' => 'Формат файла архива [.TAR]',
        'Tidningarnas Telegrambyra NITF version (TTNITF DTD)' => 'Tidningarnas Telegrambyra вариант NITF (TTNITF DTD)',
        'United Press International ANPA 1312 variant' => 'United Press International  – Вариант ANPA 1312',
        'United Press International Down-Load Message' => 'United Press International – Down-Load Message',
      },
    },
   'ObjectPreviewFileVersion' => 'Предпросмотр объекта – Версия',
   'ObjectSizeAnnounced' => 'Заявленный размер объекта',
   'ObjectType' => 'Тип объекта',
   'ObjectTypeReference' => 'Ссылка на тип объекта',
   'ObservationDate' => 'Дата наблюдения',
   'ObservationDateEnd' => 'Дата окончания наблюдения',
   'ObservationTime' => 'Время наблюдения',
   'ObservationTimeEnd' => 'Время окончания наблюдения',
   'Observer' => 'Наблюдатель',
   'ObsoletePhotoshopTag1' => 'Photoshop – Устаревший тег 1',
   'ObsoletePhotoshopTag2' => 'Photoshop – Устаревший тег 2',
   'ObsoletePhotoshopTag3' => 'Photoshop – Устаревший тег 3',
   'Occurrence' => 'Находка',
   'OccurrenceAssociatedMedia' => 'Медиаресурсы связанные с находкой',
   'OccurrenceAssociatedOccurrences' => 'Связанные находки',
   'OccurrenceAssociatedReferences' => 'Ссылки связанные с находкой',
   'OccurrenceAssociatedSequences' => 'Генетические последовательности связанные с находкой',
   'OccurrenceAssociatedTaxa' => 'Таксоны связанные с находкой',
   'OccurrenceBehavior' => 'Поведения особи во время находки',
   'OccurrenceCatalogNumber' => 'Номер набора данных',
   'OccurrenceDetails' => 'Подробности о находке',
   'OccurrenceDisposition' => 'Состояние образца по отношению к коллекции',
   'OccurrenceEstablishmentMeans' => 'Способ основания на местности',
   'OccurrenceID' => 'ID находки',
   'OccurrenceIndividualCount' => 'Количество найденных особей',
   'OccurrenceIndividualID' => 'ID найденных особей',
   'OccurrenceLifeStage' => 'Жизненные циклы найденных особей',
   'OccurrenceOrganismQuantity' => 'Число учётных единиц или обилие вида',
   'OccurrenceOrganismQuantityType' => 'Тип системы количественного определения',
   'OccurrenceOtherCatalogNumbers' => 'Другие номера каталога для находки',
   'OccurrencePreparations' => 'Методы препарирования и консервирования образца',
   'OccurrencePreviousIdentifications' => 'Предыдущие определения находки',
   'OccurrenceRecordNumber' => 'Номер присвоенный объекту в момент находки',
   'OccurrenceRecordedBy' => 'Люди ответственные за запись о находке',
   'OccurrenceRemarks' => 'Комментарии к находке',
   'OccurrenceReproductiveCondition' => 'Репродуктивное состояние особей во время находки',
   'OccurrenceSex' => 'Пол найденных особей',
   'OccurrenceStatus' => 'Присутствие вида в данной местности',
   'OceApplicationSelector' => 'Задатчик приложения Oce',
   'OceIDNumber' => 'ID-номер Oce',
   'OceImageLogic' => 'Логика изображения Oce',
   'OceScanjobDesc' => 'Описание задания сканирования Oce',
   'OffsetSchema' => 'Схема смещения',
   'OffsetTime' => 'Смещение времени',
   'OffsetTimeDigitized' => 'Смещения времени создания файла',
   'OffsetTimeOriginal' => 'Смещение времени съёмки',
   'OldSubfileType' => {
      Description => 'Тип подфайла (старый)',
      PrintConv => {
        'Full-resolution image' => 'Изображение с полным разрешением',
        'Reduced-resolution image' => 'Изображение с пониженным разрешением',
        'Single page of multi-page image' => 'Одна страница из многостраничного изображения',
      },
    },
   'OnionSkins' => 'Обрамление сцены',
   'Opacity' => 'Непрозрачность',
   'OpcodeList1' => 'Список кодов операций обработки изображений 1',
   'OpcodeList2' => 'Список кодов операций обработки изображений 2',
   'OpcodeList3' => 'Список кодов операций обработки изображений 3',
   'OperatingSystem' => {
      Description => 'Операционная система',
      PrintConv => {
        'FAT filesystem (MS-DOS, OS/2, NT/Win32)' => 'Файловая система FAT (MS-DOS, OS/2, NT/Win32)',
        'HPFS filesystem (OS/2, NT)' => 'Файловая система HPFS (OS/2, NT)',
        'NTFS filesystem (NT)' => 'Файловая система NTFS (NT)',
        'VMS (or OpenVMS)' => 'VMS (или OpenVMS)',
        'Z-System' => 'Z/OS',
        'unknown' => 'Неизвестная',
      },
    },
   'OpticalZoomOn' => {
      Description => 'Оптический зум',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'Opto-ElectricConvFactor' => 'Фактор оптико-электрического преобразования',
   'Organism' => 'Организм',
   'OrganismAssociatedOccurrences' => 'Связанные находки с этим организмом',
   'OrganismAssociatedOrganisms' => 'Другие организмы связанные с этим организмом',
   'OrganismID' => 'ID организма',
   'OrganismName' => 'Название присвоенное организму',
   'OrganismPreviousIdentifications' => 'Предыдущие определения организма',
   'OrganismRemarks' => 'Комментарии к экземпляру организма',
   'OrganismScope' => 'Вид экземпляра организма',
   'Organization' => 'Организация',
   'Organizer' => 'Организатор',
   'Orientation' => {
      Description => 'Ориентация',
      PrintConv => {
        'Horizontal (normal)' => 'Горизонтальная',
        'Mirror horizontal' => 'Отражение по горизонтали',
        'Mirror horizontal and rotate 270 CW' => 'Отражение по горизонтали и поворот на 270° по часовой стрелке',
        'Mirror horizontal and rotate 90 CW' => 'Отражение по горизонтали и поворот на 90° по часовой стрелке',
        'Mirror vertical' => 'Отражение по вертикали',
        'Rotate 180' => 'Поворот на 180°',
        'Rotate 270 CW' => 'Поворот на 270° по часовой стрелке',
        'Rotate 90 CW' => 'Поворот на 90° по часовой стрелке',
      },
    },
   'OriginPathInfo' => 'Сведения об исходном обтравочном контуре',
   'OriginalBestQualitySize' => 'Размер более качественного изображения',
   'OriginalCreateDateTime' => 'Исходные дата и время создания',
   'OriginalDecisionData' => 'Цифровая подпись оригинальности снимка (ODD)',
   'OriginalDefaultCropSize' => 'Исходный размер кадрированного изображения',
   'OriginalDefaultFinalSize' => 'Полный размер исходного изображения',
   'OriginalFileName' => 'Исходное название файла',
   'OriginalFileSize' => 'Размер исходного файла',
   'OriginalFileType' => 'Тип исходного файла',
   'OriginalFrameRate' => 'Исходная частота кадров',
   'OriginalImageHeight' => 'Исходная высотаи зображения',
   'OriginalImageWidth' => 'Исходная ширина изображения',
   'OriginalMedia' => {
      Description => 'Оригинальный носитель',
      PrintConv => {
        'False' => 'Нет',
        'True' => 'Да',
      },
    },
   'OriginalRawFileData' => 'Данные исходного RAW-файла',
   'OriginalRawFileDigest' => 'Хеш-сумма исходного RAW-файла',
   'OriginalRawFileName' => 'Название исходного RAW-файла',
   'OriginalReleaseTime' => 'Время выхода оригинала',
   'OriginalTransmissionReference' => 'Исходная ссылка на передачу',
   'OriginatingProgram' => 'Создано в риложении',
   'Originator' => 'Контент создан в',
   'OtherConditions' => 'Другие условия',
   'OtherConstraints' => 'Другие ограничения',
   'OtherDate1' => 'Другое – Дата 1',
   'OtherDate2' => 'Другое – Дата 2',
   'OtherDate3' => 'Другое – Дата 3',
   'OtherFirmware' => 'Другая прошивка',
   'OtherImage' => 'Другое изображение',
   'OtherImageInfo' => 'Другие сведения об изображении',
   'OtherImageLength' => 'Строк в файле предпросмотра',
   'OtherImageStart' => 'Смещение другого изображения',
   'OtherLicenseDocuments' => 'Другие документы лицензии',
   'OtherLicenseInfo' => 'Дополнительные сведения о лицензии',
   'OtherLicenseRequirements' => 'Другие лицензионные требования',
   'OtherSerialNumber' => 'Другой Серийный номер',
   'Owner' => 'Владелец',
   'OwnerAppointmentID' => 'ID владельца события',
   'OwnerID' => 'ID владельца',
   'OwnerName' => 'Имя владельца камеры',
   'PDFVersion' => 'Версия PDF',
   'PFMVersion' => 'Версия PFM',
   'PGFVersion' => 'Версия PGF',
   'PLUSVersion' => 'Версия PLUS',
   'PMVersion' => 'Версия PhotoMechanic',
   'PNGWarning' => 'Предупреждение PNG',
   'PStringCaption' => 'P – Строка заголовка',
   'PXC Viewer Info' => 'Сведения от PDF-XChange Viewer',
   'Packets' => 'Пакетов',
   'PackingMethod' => {
      Description => 'Метод сжатия',
      PrintConv => {
        'Best Compression' => 'Максимальный',
        'Fast' => 'Быстрый',
        'Fastest' => 'Скоростной',
        'Good Compression' => 'Хороший',
        'Normal' => 'Обычный',
        'Stored' => 'Без сжатия',
      },
    },
   'Padding' => 'Внутренние отступы',
   'Page' => 'Размер и расположение холста',
   'PageCount' => 'Количество страниц',
   'PageEnter' => 'Эффект при заходе на страницу',
   'PageExit' => 'Эффект при уходе со страницы',
   'PageFront' => 'Количество фронтальных страниц',
   'PageLayout' => 'Макет страницы',
   'PageMode' => 'Режим страницы',
   'PageName' => 'Название страницы',
   'PageNormal' => 'Количество обычных страниц',
   'PageNumber' => 'Номер страницы',
   'PageSpecial' => 'Количество специальных страниц',
   'Pages' => 'Страниц',
   'Palette' => 'Палитра',
   'PaletteHistogram' => 'Гистограмма палитры',
   'PalmFileType' => 'Тип файла Palm',
   'PanOrientation' => {
      Description => 'Ориентация панорамы',
      PrintConv => {
        'Bottom to top' => 'Снизу вверх',
        'Clockwise' => 'По часовой стрелке',
        'Counter clockwise' => 'Против часовой стрелки',
        'Left to right' => 'Слева направо',
        'Right to left' => 'Справа налево',
        'Start at bottom left' => 'Начало внизу слева',
        'Start at bottom right' => 'Начало внизу справа',
        'Start at top left' => 'Начало сверху слева',
        'Start at top right' => 'Начало сверху справа',
        'Top to bottom' => 'Сверху вниз',
        'Zigzag (column start)' => 'Зигзаг (начало колонки)',
        'Zigzag (row start)' => 'Зигзаг (начало строки)',
        '[unused]' => 'Неиспользуется',
      },
    },
   'PanOverlapH' => 'Горизонтальное перекрытие панорамы',
   'PanOverlapV' => 'Вертикальное перекрытие панорамы',
   'PanasonicRawVersion' => 'Panasonic – Версия RAW',
   'PanasonicTitle' => 'Panasonic – Название',
   'PanasonicTitle2' => 'Panasonic – Название 2',
   'PanoramicStitchCameraMotion' => {
      Description => 'Метод сшивания панорамы',
      PrintConv => {
        '3D Rotation' => '3D-вращение',
        'Affine' => 'Аффинное преобразование',
        'Homography' => 'Гомография',
        'Rigid Scale' => 'Жёсткая шкала',
      },
    },
   'PanoramicStitchMapType' => {
      Description => 'Тип карты',
      PrintConv => {
        'Horizontal Cylindrical' => 'Горизонтальная цилиндрическая',
        'Horizontal Spherical' => 'Горизонтальная сферическая',
        'Perspective' => 'Перспектива',
        'Vertical Cylindrical' => 'Вертикальная цилиндрическая',
        'Vertical Spherical' => 'Вертикальная сферическая',
      },
    },
   'PanoramicStitchPhi0' => 'Угол Phi 0',
   'PanoramicStitchPhi1' => 'Угол Phi 1',
   'PanoramicStitchTheta0' => 'Угол Theta 0',
   'PanoramicStitchTheta1' => 'Угол Theta 1',
   'Paragraphs' => 'Абзацев',
   'ParentMEID' => 'Родительский MEID',
   'ParentMediaEventID' => 'ID события',
   'PathSelectionState' => 'Состояние выбранного обтравочного контура',
   'PathTableLocation' => 'Расположение таблицы путей',
   'PathTableSize' => 'Размер таблицы путей',
   'PatientBirthDate' => 'Дата рождения пациента',
   'PatientID' => 'ID пациента',
   'PatientName' => 'Имя пациента',
   'PatientSex' => 'Пол пациента',
   'PeakSpectralSensitivity' => 'Пиковая спектральная чувствительность',
   'People' => 'Люди',
   'PercentComplete' => 'Процент завершения',
   'PercussivePolyphony' => 'Полифоническая перкуссия',
   'Performer' => 'Исполнитель',
   'PeripheralIllumCentralRadius' => 'Компенсация паразитного сигнала – Центральный радиус',
   'PeripheralIllumCentralValue' => 'Компенсация паразитного сигнала – Центральная насыщенность',
   'PeripheralIllumPeriphValue' => 'Компенсация паразитного сигнала – Периферийная насыщенность',
   'Personality' => 'Личности',
   'Photo' => 'Фотография',
   'PhotoResolution' => 'Разрешение снимка',
   'PhotometricInterpretation' => {
      Description => 'Цветовая модель',
      PrintConv => {
        'BlackIsZero' => 'Чёрный равен нулю',
        'Color Filter Array' => 'Массив цветных фильтров (CFA)',
        'RGB Palette' => 'Палитра RGB',
        'Transparency Mask' => 'Маска прозрачности',
        'WhiteIsZero' => 'Белый равен нулю',
      },
    },
   'Photoshop2ColorTable' => 'Photoshop 2 – Таблица цветов',
   'Photoshop2Info' => 'Photoshop 2 – Сведения',
   'PhotoshopBGRThumbnail' => 'Photoshop – BGR миниатюра',
   'PhotoshopFormat' => {
      Description => 'Photoshop – Формат',
      PrintConv => {
        'Optimized' => 'Оптимизированный',
        'Progressive' => 'Прогрессивный',
        'Standard' => 'Стандартный',
      },
    },
   'PhotoshopQuality' => 'Photoshop – Качество',
   'PhotoshopThumbnail' => 'Photoshop – Миниатюра',
   'PhysicalStreamNumberMap' => 'Физический поток – Number Map',
   'PhysicalStreamNumbers' => 'Номера физических потоков',
   'PhysicalStreams' => 'Физических потоков',
   'PicsLabel' => 'Метка взрослости контента',
   'PictInfo' => 'Информация об изображении',
   'Picture' => 'Картина',
   'PictureBitsPerPixel' => 'Количество бит на пиксель картинки',
   'PictureDescription' => 'Описание изображения',
   'PictureHeight' => 'Высота картинки',
   'PictureIndexedColors' => 'Индексированные цвета изображения',
   'PictureLength' => 'Строк в картинке',
   'PictureMIMEType' => 'MIME тип изображения',
   'PictureType' => {
      Description => 'Тип изображения',
      PrintConv => {
        '32x32 PNG Icon' => '32x32 иконка PNG',
        'Artist' => 'Исполнитель',
        'Back Cover' => 'Задняя обложка',
        'Band' => 'Группа',
        'Band Logo' => 'Логотип группы/исполнителя',
        'Bright(ly) Colored Fish' => 'Ярко окрашенная рыба',
        'Capture from Movie or Video' => 'Захват из фильма или видео',
        'Composer' => 'Композитор',
        'Conductor' => 'Дирижер',
        'Front Cover' => 'Передняя обложка',
        'Illustration' => 'Иллюстрация',
        'Lead Artist' => 'Ведущий Артист/Исполнитель/Солист',
        'Leaflet' => 'Листовка',
        'Lyricist' => 'Автор текста',
        'Media' => 'Носитель',
        'Other' => 'Другое',
        'Other Icon' => 'Другая иконка',
        'Performance' => 'Представление',
        'Publisher Logo' => 'Логотип издателя/студии',
        'Recording Session' => 'Запись сессии',
        'Recording Studio or Location' => 'Студия звукозаписи или местоположение',
      },
    },
   'PictureWidth' => 'Ширина картинки',
   'PieceLength' => 'Размер блока в байтах',
   'Pieces' => 'Блоков',
   'PipelineVersion' => 'Версия Pipeline',
   'Pitch' => 'Тангаж (Наклон)',
   'PitchAndFamily' => 'Шаг и семейство шрифта',
   'PitchAngle' => 'Угол наклона (Тангаж)',
   'PixHeight' => 'Высота в пикселях',
   'PixWidth' => 'Ширина в пикселях',
   'PixelAspectRatio' => 'Соотношение сторон пикселей',
   'PixelCalibration' => 'Калибровка по пикселям',
   'PixelFormat' => {
      Description => 'Формат пикселя',
      PrintConv => {
        '112-bit 6 Channels Alpha' => '112-битный, 6 каналов, альфа-канал',
        '112-bit 7 Channels' => '112-битный, 7 каналов',
        '128-bit 7 Channels Alpha' => '128-битный, 7 каналов, альфа-канал',
        '128-bit 8 Channels' => '128-битный, 8 каналов',
        '128-bit PRGBA Float' => '128-битный, PRGBA с плавающей точкой',
        '128-bit RGB Float' => '128-битный, RGB с плавающей точкой',
        '128-bit RGBA Fixed Point' => '128-битный, RGBA с фиксированной точкой',
        '128-bit RGBA Float' => '128-битный, RGBA с плавающей точкой',
        '144-bit 8 Channels Alpha' => '144-битный, 8 каналов, альфа-канал',
        '16-bit BGR555' => '16-битный, BGR555',
        '16-bit BGR565' => '16-битный, BGR565',
        '16-bit Gray' => '16-битный, Серый',
        '16-bit Gray Half' => '16-битный, половина серого',
        '24-bit 3 Channels' => '24-битный, 3 канала',
        '24-bit BGR' => '24-битный, BGR',
        '24-bit RGB' => '24-битный, RGB',
        '32-bit 3 Channels Alpha' => '32-битный, 3 канала, альфа-канал',
        '32-bit 4 Channels' => '32-битный, 4 канала',
        '32-bit BGR' => '32-битный, BGR',
        '32-bit BGR101010' => '32-битный, BGR101010',
        '32-bit BGRA' => '32-битный, BGRA',
        '32-bit CMYK' => '32-битный, CMYK',
        '32-bit Gray Fixed Point' => '32-битный, серый с фиксированной точкой',
        '32-bit Gray Float' => '32-битный, Серый с плавающей точкой',
        '32-bit PBGRA' => '32-битный, PBGRA',
        '32-bit RGBE' => '32-битный, RGBE',
        '40-bit 4 Channels Alpha' => '40-битный, 4 канала, альфа-канал',
        '40-bit 5 Channels' => '40-битный, 5 каналов',
        '40-bit CMYK Alpha' => '40-битный, CMYK, альфа-канал',
        '48-bit 3 Channels' => '48-битный, 3 канала',
        '48-bit 5 Channels Alpha' => '48-битный, 5 каналов, альфа-канал',
        '48-bit 6 Channels' => '48-битный, 6 каналов',
        '48-bit RGB' => '48-битный, RGB',
        '48-bit RGB Fixed Point' => '48-битный, RGB с фиксированной точкой',
        '48-bit RGB Half' => '48-битный, RGB Half',
        '4:2:0 (chroma at 0, 0.5)' => '4:2:0 (цветность в 0, 0.5)',
        '4:2:0 (chroma at 0.5, 0.5)' => '4:2:0 (цветность в 0.5, 0.5)',
        '4:2:2 (chroma at 0, 0)' => '4:2:2 (цветность в 0, 0)',
        '4:2:2 (chroma at 0.5, 0)' => '4:2:2 (цветность в 0.5, 0)',
        '56-bit 6 Channels Alpha' => '56-битный, 6 каналов, альфа-канал',
        '56-bit 7 Channels' => '56-битный, 7 каналов',
        '64-bit 3 Channels Alpha' => '64-битный, 3 канала, альфа-канал',
        '64-bit 4 Channels' => '64-битный, 4 канала',
        '64-bit 7 Channels Alpha' => '64-битный, 7 каналов, альфа-канал',
        '64-bit 8 Channels' => '64-битный, 8 каналов',
        '64-bit CMYK' => '64-битный, CMYK',
        '64-bit PRGBA' => '64-битный, PRGBA',
        '64-bit RGBA' => '64-битный, RGBA',
        '64-bit RGBA Fixed Point' => '64-битный, RGBA с фиксированной точкой',
        '64-bit RGBA Half' => '64-битный, RGBA Half',
        '72-bit 8 Channels Alpha' => '72-битный, 8 каналов, альфа-канал',
        '8-bit Gray' => '8-битный, Серый',
        '80-bit 4 Channels Alpha' => '80-битный, 4 канала, альфа-канал',
        '80-bit 5 Channels' => '80-битный, 5 канаов',
        '80-bit CMYK Alpha' => '80-битный, CMYK, альфа-канал',
        '96-bit 5 Channels Alpha' => '96-битный, 5 каналов, альфа-канал',
        '96-bit 6 Channels' => '96-битный, 6 канаов',
        '96-bit RGB Fixed Point' => '96-битный, RGB с фиксированной точкой',
        'Black & White' => 'Чёрно-белый',
        'Grayscale' => 'Оттенки серого',
      },
    },
   'PixelIntensityRange' => 'Диапазон интенсивности пикселей  (%)',
   'PixelMagicJBIGOptions' => 'Pixel Magic – Параметры JBIG',
   'PixelScale' => 'Масштаб пикселя',
   'PixelUnits' => {
      Description => 'Пиксельные единицы',
      PrintConv => {
        'Unknown' => 'Неизвестно',
        'meters' => 'Метры',
      },
    },
   'PixelsPerMeterX' => 'Пикселей на метр по X',
   'PixelsPerMeterY' => 'Пикселей на метр по Y',
   'PixelsPerUnitX' => 'Пикселей на единицу по X',
   'PixelsPerUnitY' => 'Пикселей на единицу по Y',
   'PlanarConfiguration' => {
      Description => 'Принцип организации данных',
      PrintConv => {
        'Chunky' => 'Непрерывный (RGBRGB)',
        'Planar' => 'По каналам (RRGGBB)',
      },
    },
   'Planes' => 'Плоскости',
   'PointSize' => 'Размер точки',
   'PopupFillAttributes' => 'Аттрибуты заполнения всплывающего окна',
   'PortraitVersion' => 'Портретная ориентация',
   'PoseHeadingDegrees' => 'Направление по компасу для центра изображения (°)',
   'PosePitchDegrees' => 'Продольный наклон для центра изображения (°)',
   'PoseRollDegrees' => 'Поперечный наклон изображения (°)',
   'PostScriptFontName' => 'PostScript название шрифта',
   'Pragma' => 'Контроль кэширования',
   'Predictor' => {
      Description => 'Предиктор',
      PrintConv => {
        'Horizontal differencing' => 'Горизонтальное дифференцирование',
        'None' => 'Не использовался',
      },
    },
   'PreferredFamily' => 'Предпочитаемое семейство шрифта',
   'PreferredSubfamily' => 'Предпочитаемый стиль шрифта',
   'Prefs' => 'Настройки',
   'PreservedSpecimen' => 'Сохраненный образец',
   'PreservedSpecimenMaterialSampleID' => 'ID материала сохранённого образца',
   'PresetWhiteBalance' => {
      Description => 'Предустановка баланса белого',
      PrintConv => {
        'Auto' => 'Автоматический',
        'Camera Setting' => 'Настройка камеры',
        'Cloudy' => 'Облачно',
        'Color Temperature' => 'Цветовая температура',
        'Cool White Fluorescent' => 'Холодный белый (флуоресцентная лампа)',
        'Day Light Fluorescent' => 'Дневной свет (флуоресцентная лампа)',
        'Day White Fluorescent' => 'Дневной белый (флуоресцентная лампа)',
        'Daylight' => 'Дневной свет',
        'Flash' => 'Вспышка',
        'Shade' => 'Тень',
        'Specify Gray Point' => 'Указанная точка серого',
        'Tungsten' => 'Лампа накаливания',
        'Warm White Fluorescent' => 'Тёплый белый (флуоресцентная лампа)',
      },
    },
   'PresetWhiteBalanceAdj' => 'Предустановка баланса белого',
   'Pressure' => 'Давление',
   'Preview' => 'Предпросмотр',
   'PreviewApplicationName' => 'Файл предпросмотра – Название приложения',
   'PreviewApplicationVersion' => 'Файл предпросмотра – Версия приложения',
   'PreviewColorSpace' => {
      Description => 'Файл предпросмотра – Цветовое пространство',
      PrintConv => {
        'Unknown' => 'Неизвестно',
      },
    },
   'PreviewCropBottom' => 'Предпросмотр – Обрезка снизу',
   'PreviewCropLeft' => 'Предпросмотр – Обрезка слева',
   'PreviewCropRight' => 'Предпросмотр – Обрезка справа',
   'PreviewCropTop' => 'Предпросмотр – Обрезка сверху',
   'PreviewDateTime' => 'Файл предпросмотра – Дата и Время',
   'PreviewImage' => 'Файл предпросмотра',
   'PreviewImageLength' => 'Строк в файле предпросмотра',
   'PreviewImageSize' => 'Размер файла предпросмотра',
   'PreviewImageStart' => 'Смещение файла предпросмотра',
   'PreviewPDF' => 'Файл предпросмотра PDF',
   'PreviewPNG' => 'Файл предпросмотра PNG',
   'PreviewSettingsDigest' => 'Файл предпросмотра – Идентификатор параметров преобразования',
   'PreviewSettingsName' => 'Файл предпросмотра – Название  параметров преобразования',
   'PreviewTIFF' => 'Файл предпросмотра TIFF',
   'PreviewWMF' => 'Файл предпросмотра WMF',
   'PrimaryChromaticities' => 'Цветность основных цветов',
   'PrimaryFTP' => 'Основной FTP',
   'PrintFlags' => 'Флаги печати',
   'PrintFlagsInfo' => 'Сведения о флагах печати',
   'PrintIMVersion' => 'Версия PrintIM',
   'PrintInfo' => 'Сведения о печати',
   'PrintInfo2' => 'Сведения о печати 2',
   'PrintPosition' => 'Положение при печати',
   'PrintScale' => 'Масштаб при печати',
   'PrintStyle' => {
      Description => 'Стиль печати',
      PrintConv => {
        'Centered' => 'По центру',
        'Size to Fit' => 'Подогнать под формат листа',
        'User Defined' => 'Определённый пользователем',
      },
    },
   'Priority' => 'Приоритет',
   'Private' => 'Частный',
   'PrivateRTKInfo' => 'Частная нформация о RTK',
   'ProTune' => {
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'ProcessingSoftware' => 'Редактировано в приложении',
   'ProcessingTime' => 'Время обработки',
   'ProdNotes' => 'Количество заметок производителя',
   'ProducedDate' => 'Дата производства',
   'Producer' => 'Производитель',
   'ProductID' => 'ID продукта',
   'ProductOrServiceConstraints' => 'Ограничения на продукты или услуги',
   'Profile' => 'Профиль',
   'Profile1AudioCodec' => 'Профиль 1 – Кодек аудио',
   'Profile1Height' => 'Профиль 1 – Высота',
   'Profile1VideoCodec' => 'Профиль 1 – Кодек видео',
   'Profile1Width' => 'Профиль 1 – Ширина',
   'ProfileCalibrationSig' => 'Сигнатура калибровки профиля',
   'ProfileCopyright' => 'Авторское право профиля',
   'ProfileDataOffset' => 'Смещение данных профиля',
   'ProfileEmbedPolicy' => {
      Description => 'Политика внедрения профиля',
      PrintConv => {
        'Allow Copying' => 'Разрешено копирование',
        'Embed if Used' => 'Внедрить если используется',
        'Never Embed' => 'Никогда не внедрять',
        'No Restrictions' => 'Без ограничений',
      },
    },
   'ProfileHueSatMapData1' => 'Профиль – Карта оттенков/насыщенности – Данные 1',
   'ProfileHueSatMapData2' => 'Профиль – Карта оттенков/насыщенности – Данные 2',
   'ProfileHueSatMapDims' => 'Профиль – Карта оттенков/насыщенности – Затемнение',
   'ProfileHueSatMapEncoding' => {
      Description => 'Профиль – Карта оттенков/насыщенности – Кодирование',
      PrintConv => {
        'Linear' => 'Линейное',
      },
    },
   'ProfileLookTableData' => 'Профиль – Таблица подстановки – Данные',
   'ProfileLookTableDims' => 'Профиль – Таблица подстановки – Затемнение',
   'ProfileLookTableEncoding' => {
      Description => 'Профиль – Таблица подстановки – Кодирование',
      PrintConv => {
        'Linear' => 'Линейное',
      },
    },
   'ProfileName' => 'Название профиля',
   'ProfileSize' => 'Размер профиля',
   'ProfileToneCurve' => 'Профиль – Тоновая кривая',
   'ProfileType' => {
      Description => 'Тип профиля',
      PrintConv => {
        'Unspecified' => 'Не указан',
      },
    },
   'ProgID' => 'ID приложения',
   'ProgramID' => 'ID программы',
   'ProgramMode' => {
      Description => 'Режим программы',
      PrintConv => {
        'Night Portrait' => 'Ночной портрет',
        'None' => 'Отсутствует',
        'Portrait' => 'Портрет',
        'Sports' => 'Спорт',
        'Sunset' => 'Закат',
        'Text' => 'Текст',
      },
    },
   'ProgramVersion' => 'Версия приложения',
   'ProgressiveScans' => {
      Description => 'Прогрессивное сканирование',
      PrintConv => {
        '3 Scans' => '3 прохода',
        '4 Scans' => '4 прохода',
        '5 Scans' => '5 проходов',
      },
    },
   'Project' => 'Проект',
   'ProjectionType' => 'Тип проекции',
   'PropertyReleaseID' => 'ID свойств релиза',
   'PropertyReleaseStatus' => {
      Description => 'Статус свойств релиза',
      PrintConv => {
        'Limited or Incomplete Property Releases' => 'Ограниченные или неполные релизы',
        'None' => 'Не обозначено',
        'Not Applicable' => 'Не используется',
        'Unlimited Property Releases' => 'Неограниченные релизы свойств',
      },
    },
   'Protect' => 'Защита',
   'Provider' => 'Поставщик',
   'ProviderCopyright' => 'Поставщик – Авторское право',
   'ProviderRating' => 'Провайдер – Рейтинг',
   'Province-State' => 'Область/район',
   'PublishDate' => 'Дата публикации',
   'PublishDateStart' => 'Дата начала публикации',
   'Publisher' => 'Издатель',
   'PublisherLimit' => 'Ограничения издателя',
   'PxShiftPeriphEdgeNR' => {
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'PyramidLevels' => 'Уровни пирамиды',
   'Quality' => {
      Description => 'Качество',
      PrintConv => {
        '2 (Telephone)' => '2 (Телефон)',
        '3 (Thumb)' => '3 (Эскиз)',
        '4 (Radio)' => '4 (Радио)',
        '5 (Standard)' => '5 (Стандартное)',
        '6 (Xtreme)' => '6 (Экстремальное)',
        '7 (Insane)' => '7 (Сумасшедшее)',
        '8 (BrainDead)' => '8 (Полный улёт)',
        'Compressed RAW' => 'cRAW',
        'Compressed RAW + JPEG' => 'cRAW+JPEG',
        'Extra Fine' => 'Сверхвысокое',
        'Fine' => 'Высокое',
        'Low' => 'Низкое качество',
        'Normal' => 'Стандартное качество',
        'RAW + JPEG' => 'RAW+JPEG',
        'Standard' => 'Стандартное',
        'Unstable/Experimental' => 'Нестабильное/Экспериментальное',
      },
    },
   'QuantizationMethod' => {
      Description => 'Метод квантования',
      PrintConv => {
        'Color Space Specific' => 'Специфичное цветовое пространство',
        'Compression Method Specific' => 'Специфичный метод сжатия',
        'Gamma Compensated' => 'Компенсация гаммы',
        'Linear Density' => 'Линейная плотность',
        'Linear Reflectance/Transmittance' => 'Линейное Отражение/Пропускание',
      },
    },
   'QuickEdit' => 'Быстрое редактирование',
   'QuickMaskInfo' => 'Сведения о быстрой маске',
   'QuickShot' => {
      Description => 'Быстрый снимок',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'RAFVersion' => 'Версия RAF',
   'RGBCurves' => 'Кривые RGB',
   'ROIDescription' => 'Описание ROI',
   'RPP' => 'Raw Photo Processor (RPP)',
   'RasterPadding' => {
      Description => 'Растровое заполнение',
      PrintConv => {
        'Byte' => 'Байт',
        'Long Sector' => 'Длинный сектор',
        'Long Word' => 'Длинное слово',
        'Sector' => 'Сектор',
        'Word' => 'Слово',
      },
    },
   'RasterizedCaption' => 'Растрированное описание',
   'Rate' => 'Темп',
   'Rating' => 'Рейтинг',
   'RatingPercent' => 'Рейтинг (%)',
   'RawCropBottom' => 'Raw – Обрезка снизу',
   'RawCropLeft' => 'Raw – Обрезка слева',
   'RawCropRight' => 'Raw – Обрезка справа',
   'RawCropTop' => 'Raw – Обрезка сверху',
   'RawData' => 'Raw-данные',
   'RawDataLength' => 'Строк в Raw-данных',
   'RawDataOffset' => 'Смещение RAW-данных',
   'RawDataUniqueID' => 'Уникальный ID RAW-данных',
   'RawDepth' => 'Глубина Raw-изображения',
   'RawFile' => 'RAW-файл',
   'RawFormat' => 'RAW-формат',
   'RawImageDigest' => 'Хеш-сумма RAW-изображения',
   'RawImageMode' => 'Режим Raw-изображения',
   'RawImageSegmentation' => 'Сегментация RAW-изображения',
   'RawToPreviewGain' => 'Коэффициент увеличения между Raw и файлом предпросмотра',
   'Rawrppused' => 'Использовался RPP',
   'RawzorCreatorVersion' => 'Версия приложения',
   'RawzorRequiredVersion' => 'Требуемая версия',
   'ReaderName' => 'Название просмотрщика',
   'ReaderRequirements' => 'Требования к ридеру',
   'RecEngineer' => 'Инженер',
   'RecLocation' => 'Локация',
   'RecommendedExposureIndex' => 'Рекомендуемый индекс экспозиции',
   'Record' => 'Запись',
   'RecordBasisOfRecord' => 'Основание записи',
   'RecordCollectionCode' => 'Код/Название коллекции записи',
   'RecordCollectionID' => 'ID коллекции записи',
   'RecordDataGeneralizations' => 'Обобщение данных записи',
   'RecordDatasetID' => 'ID базы данных записи',
   'RecordDatasetName' => 'Название базы данных записи',
   'RecordDynamicProperties' => 'Динамические свойства записи',
   'RecordInformationWithheld' => 'Информация отсутствующая в записи',
   'RecordInstitutionCode' => 'Организация хранения образца или информации',
   'RecordInstitutionID' => 'ID организации хранения образца или информации',
   'RecordMode' => 'Режим записи',
   'RecordOwnerInstitutionCode' => 'Организация-владелец образца или информации',
   'RecordShutterRelease' => {
      Description => 'Спуск затвора при записи',
      PrintConv => {
        'Press start, press stop' => 'Нажать «Старт», нажать «Стоп»',
        'Record while down' => 'Запись пока нажата кнопка',
      },
    },
   'RecurrenceDateTimes' => 'Дата/Время повторения события',
   'RecurrenceID' => 'ID повторяющегося события',
   'RecurrenceRule' => 'Правило повторения',
   'RedBalance' => 'Баланс красного',
   'RedBlueFlatField' => 'Коррекция плоского поля – Красный и Синий',
   'RedEndpoint' => 'Конечная точка Красного',
   'RedEyeReduction' => {
      Description => 'Уменьшение эффекта красных глаз',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'RedMask' => 'Маска Красного',
   'RedPrimary' => 'Основной красный',
   'RedX' => 'Красный по X',
   'RedY' => 'Красный по Y',
   'RedcodeVersion' => 'Версия Redcode',
   'ReductionMatrix1' => 'Матрица редукции 1',
   'ReductionMatrix2' => 'Матрица редукции 2',
   'ReelName' => 'Название носителя информации',
   'ReelNumber' => 'Номер записи',
   'ReelTimecode' => 'Таймкод записи',
   'Reference' => 'Ссылка',
   'ReferenceBlackWhite' => 'Исходные значения чёрного и белого',
   'ReferenceDate' => 'Дата предыдущего конверта',
   'ReferenceNumber' => 'Номер предыдущего конверта',
   'ReferenceService' => 'Ссылка на предыдущий конверт',
   'Refresh' => 'Задержка обновления страницы',
   'RegionAppliedToDimensions' => 'Размеры изображения при которых применялся регион',
   'RegionAppliedToDimensionsH' => 'Высота изображения при которой применяля регион',
   'RegionAppliedToDimensionsUnit' => 'Единицы размеров изображения',
   'RegionAppliedToDimensionsW' => 'Ширина изображения при которой применяля регион',
   'RegionArea' => 'Область региона',
   'RegionAreaD' => 'Диаметр круглой области',
   'RegionAreaH' => 'Высота прямоугольной обласи',
   'RegionAreaUnit' => 'Единицы измерения области',
   'RegionAreaW' => 'Ширина прямоугольной области',
   'RegionAreaX' => 'Координаты центра области по X',
   'RegionAreaY' => 'Координаты центра области по Y',
   'RegionBarCodeValue' => 'Значение штрихкода региона',
   'RegionConstraints' => 'Региональные ограничения',
   'RegionDescription' => 'Описание региона',
   'RegionExtensions' => 'Дополнительное свойство региона',
   'RegionFocusUsage' => {
      Description => 'Использование фокуса',
      PrintConv => {
        'Evaluated, Not Used' => 'Учитывался, но не использовался',
        'Evaluated, Used' => 'Учитывался и использовался',
        'Not Evaluated, Not Used' => 'Не учитывался и не использовался',
      },
    },
   'RegionInfo' => 'Сведения об регионе',
   'RegionInfoDateRegionsValid' => 'Дата создания последнего региона',
   'RegionInfoMP' => 'Сведения о регионе в Microsoft Photo',
   'RegionInfoRegions' => 'Сведения о регионах',
   'RegionList' => 'Список структуры регионов',
   'RegionName' => 'Название региона',
   'RegionPersonDisplayName' => 'Имя человека отмеченное регионом',
   'RegionPersonEmailDigest' => 'Хеш-сумма электронной почты Windows Live пользователя',
   'RegionPersonLiveIdCID' => 'ID пользователя Windows Live CID',
   'RegionPersonSourceID' => 'Исходный ID пользователя',
   'RegionRectangle' => 'Прямоугольник региона',
   'RegionRotation' => 'Вращение региона',
   'RegionSeeAlso' => 'Смотрите также',
   'RegionType' => {
      Description => 'Тип региона',
      PrintConv => {
        'BarCode' => 'Штрихкод',
        'Face' => 'Лицо',
        'Focus' => 'Фокус',
        'Pet' => 'Животное',
      },
    },
   'RegionXformTackPoint' => 'Точки-модификаторы области преобразования Xform',
   'RelatedImageFileFormat' => 'Формат файла связанного изображения',
   'RelatedImageHeight' => 'Высота связанного изображения',
   'RelatedImageWidth' => 'Ширина связанного изображения',
   'RelatedResourceID' => 'ID связанного ресурса',
   'RelatedSoundFile' => 'Связанный аудиофайл',
   'RelatedTo' => 'Относится к',
   'Relation' => 'Ссылка на связанный ресурс',
   'RelationshipAccordingTo' => 'Связь между ресурсами создал',
   'RelationshipEstablishedDate' => 'Дата связи между ресурсами',
   'RelationshipOfResource' => 'Связь ресурсов',
   'RelationshipRemarks' => 'Комментарии к связи ресурсов',
   'RelativeAltitude' => 'Относительная высота',
   'RelativePath' => 'Относительный путь',
   'ReleaseDate' => 'Дата публикации',
   'ReleaseTime' => 'Время публикации',
   'RemoveHistoryDuplicates' => 'Удалить историю дубликатов',
   'RenderingIntent' => {
      Description => 'Методы цветового преобразования',
      PrintConv => {
        'Absolute Colorimetric (LCS_GM_ABS_COLORIMETRIC)' => 'Абсолютный колориметрический (LCS_GM_ABS_COLORIMETRIC)',
        'Graphic (LCS_GM_BUSINESS)' => 'По насыщенности  (LCS_GM_BUSINESS)',
        'Picture (LCS_GM_IMAGES)' => 'Перцепционный (LCS_GM_IMAGES)',
        'Proof (LCS_GM_GRAPHICS)' => 'Относительный колориметрический (LCS_GM_GRAPHICS)',
      },
    },
   'RenderingTransform' => 'Преобразование цвета',
   'RentalExpirationDate' => 'Дата окончания аренды',
   'RentalFlag' => 'Флаг аренды',
   'Repeat' => 'Количество повторов',
   'ReplayGainAlbumGain' => 'Replay Gain – Коррекция громкости альбома',
   'ReplayGainAlbumPeak' => 'Replay Gain – Пиковый уровень альбома',
   'ReplayGainTrackGain' => 'Replay Gain – Коррекция громкости трека',
   'ReplayGainTrackPeak' => 'Replay Gain – Пиковый уровень трека',
   'ReplyTo' => 'E-Mail вебмастера',
   'RepresentativeDisparityFar' => 'Репрезентативная несогласованность – Вдали',
   'RepresentativeDisparityNear' => 'Репрезентативная несогласованность – Рядом',
   'RepresentativeImage' => {
      Description => 'Репрезентативное изображение',
      PrintConv => {
        'Left Viewpoint' => 'Левая точка просмотра',
        'Right Viewpoint' => 'Правая точка просмотра',
      },
    },
   'RequestID' => 'ID запрса',
   'RequestStatus' => 'Статус запроса',
   'Resaved' => {
      Description => 'Повторное сохранение',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'Reserved5' => 'Зарезервировано 5',
   'Resolution' => 'Разрешение изображения',
   'ResolutionUnit' => {
      Description => 'Единицы разрешения по X и Y',
      PrintConv => {
        'None' => 'Не указаны',
        'cm' => 'см',
        'inches' => 'дюймы',
      },
    },
   'ResourceCount' => 'Количество ресурсов',
   'ResourceForkSize' => 'Размер разветвления ресурса',
   'ResourceID' => 'ID ресурса',
   'ResourceRelationship' => 'Связи ресурсов',
   'ResourceRelationshipID' => 'ID связи между ресурсами',
   'ResourceType' => 'Тип ресурса',
   'Resources' => 'Ресурсы',
   'RetailPrice' => 'Розничная цена',
   'RetailPriceCurrency' => 'Валюта розничной цены',
   'Reuse' => {
      Description => 'Повторное использование',
      PrintConv => {
        'Not Applicable' => 'Запрещено',
        'Repeat Use' => 'Разрешено',
      },
    },
   'Review' => 'Обзор',
   'Revision' => 'Дата обновления vCard',
   'RevisionDate' => 'Дата ревизии',
   'RevisionNumber' => 'Количество редакций файла',
   'RevisitAfter' => 'Повторно посетить через',
   'RicohPitch' => 'Ricoh – Тангаж (Наклон)',
   'RicohRoll' => 'Ricoh – Крен (Вращение)',
   'RightAlbedo' => 'Правильное стереоскопическое изображение',
   'Rights' => 'Права',
   'Robots' => 'Политика обработки страницы роботами',
   'Roll' => 'Крен (Вращение)',
   'RollAngle' => 'Угол вращения (Крен)',
   'RootDirectoryCreateDate' => 'Дата создания корневого каталога',
   'Rotation' => 'Вращение',
   'RoundTripVersion' => 'Версия Round Trip',
   'Routing' => 'Роутинг',
   'RoutingDestinations' => 'Назначение маршрутизации',
   'RoutingExclusions' => 'Исключения маршрутизации',
   'RowInterleaveFactor' => 'Коэффициент чередования строк',
   'RowsPerStrip' => 'Количество строк на полосу',
   'RtkFlag' => 'Rtk – Флаг',
   'RtkStdHgt' => 'Rtk – Стандартное отклонение высоты',
   'RtkStdLat' => 'Rtk – Стандартное отклонение широты',
   'RtkStdLon' => 'Rtk – Стандартное отклонение долготы',
   'RunTimeEpoch' => 'Время выполнения – Номер эпохи',
   'RunTimeFlags' => {
      Description => 'Время выполнения – Флаги',
      PrintConv => {
        'Has been rounded' => 'Округленное время',
        'Indefinite' => 'Неопределённое время',
        'Negative infinity' => 'Минус бесконечность',
        'Positive infinity' => 'Плюс бесконечность',
        'Valid' => 'Действительное время',
      },
    },
   'RunTimeScale' => 'Время выполнения – Шкала времени',
   'RunTimeSincePowerUp' => 'Время работы с момента включения',
   'RunTimeValue' => 'Время выполнения – Значение',
   'RunWindow' => {
      Description => 'Запустить окно',
      PrintConv => {
        'Hide' => 'Скрытый',
        'Minimized' => 'Минимизировать',
        'Normal' => 'Обычный',
        'Restore' => 'Восстановить',
        'Show' => 'Показывать',
        'Show Default' => 'Отображать по-умолчанию',
        'Show Maximized' => 'Отображать развёрнутым',
        'Show Minimized' => 'Отображать свернутым',
        'Show Minimized No Activate' => 'Отображать свернутым и не активвным',
        'Show No Activate' => 'Отображать',
      },
    },
   'SEMInfo' => 'SEM – Описание',
   'SIUnits' => 'Единицы SI',
   'SMaxSampleValue' => 'Максимальное значение компонента',
   'SMinSampleValue' => 'Минимальное значение компонента',
   'SRGBRendering' => {
      Description => 'SRGB-рендеринг',
      PrintConv => {
        'Absolute Colorimetric' => 'Абсолютно колориметрический',
        'Perceptual' => 'Перцепционный',
        'Relative Colorimetric' => 'Относительно колориметрический',
        'Saturation' => 'Насыщенный',
      },
    },
   'SRawType' => 'Тип SRaw',
   'SampleFlag' => 'Флаг образца',
   'SampleFormat' => {
      Description => 'Формат компонента',
      PrintConv => {
        'Complex float' => 'Комплексные данные с плавающей точкой IEEE',
        'Complex int' => 'Комплексные целые данные',
        'Float' => 'Данные с плавающей точкой IEEE',
        'Signed' => 'Целочисленные данные со знаком',
        'Undefined' => 'Неопределенный формат данных',
        'Unsigned' => 'Беззнаковые целочисленные данные',
      },
    },
   'SampleRate' => 'Частота дискретизации',
   'SampleRate2' => 'Частота дискретизации 2',
   'SampleSize' => 'Размер',
   'SampleStructure' => {
      Description => 'Структура компонента',
      PrintConv => {
        'CompressionDependent' => 'Зависимая от сжатия',
        'Orthogonal4-2-2Sampling' => 'Ортогональная с частотами дискретизации в соотношении 4: 2: 2: (4)',
        'OrthogonalConstangSampling' => 'Ортогональная с теми же относительными частотами дискретизации в каждом компоненте',
      },
    },
   'SampleText' => 'Пример текста',
   'SamplesPerPixel' => 'Количество компонентов на пиксель',
   'SamsungRawByteOrder' => 'Samsung RAW – Порядок байтов',
   'SamsungRawPointersLength' => 'Samsung RAW – Длина указателей',
   'SamsungRawPointersOffset' => 'Samsung RAW – Смещение указателей',
   'SamsungRawUnknown' => 'Samsung RAW – Неизвестно',
   'SanyoQuality' => {
      Description => 'Sanyo – Качество',
      PrintConv => {
        'Fine/High' => 'Хорошее/Высокое',
        'Fine/Low' => 'Хорошее/Низкое',
        'Fine/Medium' => 'Хорошее/Среднее',
        'Fine/Medium High' => 'Хорошее/Выше среднего',
        'Fine/Medium Low' => 'Хорошее/Ниже среднего',
        'Fine/Super High' => 'Хорошее/Лучшее',
        'Fine/Very High' => 'Хорошее/Очень высокое',
        'Fine/Very Low' => 'Хорошее/Очень низкое',
        'Normal/High' => 'Нормальное/Высокое',
        'Normal/Low' => 'Нормальное/Низкое',
        'Normal/Medium' => 'Нормальное/Среднее',
        'Normal/Medium High' => 'Нормальное/Выше среднего',
        'Normal/Medium Low' => 'Нормальное/Ниже среднего',
        'Normal/Super High' => 'Нормальное/Лучшее',
        'Normal/Very High' => 'Нормальное/Очень высокое',
        'Normal/Very Low' => 'Нормальное/Очень низкое',
        'Super Fine/High' => 'Лучшее/Высокое',
        'Super Fine/Low' => 'Лучшее/Низкое',
        'Super Fine/Medium' => 'Лучшее/Среднее',
        'Super Fine/Medium High' => 'Лучшее/Выше среднего',
        'Super Fine/Medium Low' => 'Лучшее/Ниже среднего',
        'Super Fine/Super High' => 'Лучшее/Лучшее',
        'Super Fine/Very High' => 'Лучшее/Очень высокое',
        'Super Fine/Very Low' => 'Лучшее/Очень низкое',
      },
    },
   'SanyoThumbnail' => 'Sanyo – Миниатюра',
   'Saturation' => {
      Description => 'Насыщенность',
      PrintConv => {
        'High' => 'Высокая',
        'Low' => 'Низкая',
        'Normal' => 'Стандартная',
      },
    },
   'SaturationAdj' => 'Регулировка насыщенности',
   'ScaleFactor' => 'Коэффициент масштабирования',
   'ScaleFactor35efl' => 'Коэффициент масштабирования к 35-мм формату',
   'ScaledIMU' => 'Масштабируемый IMU',
   'ScaledPressure' => 'Вычисленное давление',
   'ScalingFactor' => 'Коэффициент масштабирования',
   'ScanningDirection' => {
      Description => 'Направление сканирования',
      PrintConv => {
        'Bottom-Top, L-R' => 'Снизу вверх, слева направо',
        'Bottom-Top, R-L' => 'Снизу вверх, справа налево',
        'L-R, Bottom-Top' => 'Слева направо, снизу вверх',
        'L-R, Top-Bottom' => 'Слева направо, сверху вниз',
        'R-L, Bottom-Top' => 'Справа налево, снизу вверх',
        'R-L, Top-Bottom' => 'Справа налево, сверху вниз',
        'Top-Bottom, L-R' => 'Сверху вниз, слева направо',
        'Top-Bottom, R-L' => 'Сверху вниз, справа налево',
      },
    },
   'Scene' => 'Номер изображения',
   'SceneCaptureType' => {
      Description => 'Тип снимаемой сцены',
      PrintConv => {
        'Landscape' => 'Пейзаж',
        'Night' => 'Ночная съёмка',
        'Other' => 'Другой',
        'Portrait' => 'Портрет',
        'Standard' => 'Стандартный режим',
      },
    },
   'SceneMode' => {
      Description => 'Выбор сцены',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => 'Устранение смазывания',
        'Auto' => 'Авто',
        'Cont. Priority AE' => 'Автоэкспозиция с приоритетом серийной съёмки',
        'Handheld Night Shot' => 'Ночная съёмка с рук',
        'Landscape' => 'Пейзаж',
        'Macro' => 'Макросъёмка',
        'Night Portrait' => 'Ночной портрет',
        'Night Scene' => 'Ночной вид',
        'Night View/Portrait' => 'Ночной вид/портрет',
        'Portrait' => 'Портрет',
        'Sports' => 'Спортивные сцены',
        'Sunset' => 'Закат',
        'Sweep Panorama' => 'Панорамный обзор',
      },
    },
   'SceneSelect' => {
      Description => 'Выбор сцены',
      PrintConv => {
        'Lamp' => 'Лампа',
        'Night' => 'Ночь',
        'Off' => 'Не задействован',
        'Sport' => 'Спорт',
        'User 1' => 'Пользовательская 1',
        'User 2' => 'Пользовательская 2',
      },
    },
   'SceneType' => {
      Description => 'Тип сцены',
      PrintConv => {
        'Directly photographed' => 'Сфотографировано цифровой камерой',
      },
    },
   'School' => 'Школа',
   'ScreenBufferSize' => 'Размер буфера экрана',
   'ScreenHeight' => 'Высота экрана',
   'ScreenWidth' => 'Ширина экрана',
   'ScreenWindowCenter' => 'Окно по центру экрана',
   'ScreenWindowWidth' => 'Окно по ширине экрана',
   'SecondaryFTP' => 'Дополнительный FTP',
   'SecurityClassification' => {
      Description => 'Классификация безопасности',
      PrintConv => {
        'Confidential' => 'Личное',
        'Restricted' => 'Для узкого круга',
        'Secret' => 'Секретно',
        'Top Secret' => 'Совершенно секретно',
        'Unclassified' => 'Без классификации',
      },
    },
   'SeekTable' => 'Таблица поиска',
   'SelfData' => 'Собственные данные',
   'SelfTimer' => {
      Description => 'Режим автоспуска затвора',
      PrintConv => {
        'Off' => 'Не задействован',
        'On' => 'Включен',
      },
    },
   'SelfTimerMode' => 'Режим таймера спуска затвора',
   'SensingMethod' => {
      Description => 'Тип датчика',
      PrintConv => {
        'Color sequential area' => 'Цветной последовательный сенсор',
        'Color sequential linear' => 'Цветной последовательный линейный сенсор',
        'Monochrome area' => 'Монохромный сенсор',
        'Monochrome linear' => 'Монохромный линейный сенсор',
        'Not defined' => 'Не опредеен',
        'One-chip color area' => 'Одночиповый цветной сенсор',
        'Three-chip color area' => 'Трехчиповый цветной сенсор',
        'Trilinear' => 'Трехлинейный цветной сенсор',
        'Two-chip color area' => 'Двухчиповый цветной сенсор',
      },
    },
   'SensitivityType' => {
      Description => 'Тип чувствительности',
      PrintConv => {
        'ISO Speed' => 'Чувствительность ISO',
        'Recommended Exposure Index' => 'Рекомендуемый индекс экспозиции',
        'Recommended Exposure Index and ISO Speed' => 'Рекомендуемый индекс экспозиции и ISO',
        'Standard Output Sensitivity' => 'Стандартная выходная чувствительность',
        'Standard Output Sensitivity and ISO Speed' => 'Стандартная выходная чувствительность и ISO',
        'Standard Output Sensitivity and Recommended Exposure Index' => 'Стандартная выходная чувствительность и рекомендуемый индекс экспозиции',
        'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed' => 'Стандартная выходная чувствительность и рекомендуемый индекс экспозиции и ISO',
        'Unknown' => 'Неизвестно',
      },
    },
   'Sensor' => 'Сенсор',
   'SensorBottomBorder' => 'Нижняя граница сенсора',
   'SensorCalibration_0x0404' => 'Коррекция сенсора 0x0404',
   'SensorCalibration_0x0405' => 'Коррекция сенсора 0x0405',
   'SensorCalibration_0x0406' => 'Коррекция сенсора 0x0406',
   'SensorCalibration_0x0408' => 'Коррекция сенсора 0x0408',
   'SensorCalibration_0x040f' => 'Коррекция сенсора 0x040f',
   'SensorCalibration_0x0413' => 'Коррекция сенсора 0x0413',
   'SensorCalibration_0x0414' => 'Коррекция сенсора 0x0414',
   'SensorCalibration_0x0418' => 'Коррекция сенсора 0x0418',
   'SensorCalibration_0x041c' => 'Коррекция сенсора 0x041c',
   'SensorCalibration_0x041e' => 'Коррекция сенсора 0x041e',
   'SensorDefects' => 'Дефекты сенсора',
   'SensorHeight' => 'Высота сенсора',
   'SensorLeftBorder' => 'Левая граница сенсора',
   'SensorLeftMargin' => 'Матрица – Отступ слева',
   'SensorRightBorder' => 'Правая граница сенсора',
   'SensorSerialNumber' => 'Серийный номер сенсора',
   'SensorTemperature' => 'Датчик температуры',
   'SensorTemperature2' => 'Датчик температуры 2',
   'SensorTopBorder' => 'Верхняя граница сенсора',
   'SensorTopMargin' => 'Матрица – Отступ справа',
   'SensorWidth' => 'Ширина сенсора',
   'Sequence' => 'Последовательность',
   'SequenceNumber' => 'Порядковый номер',
   'SequenceShotInterval' => {
      Description => 'Интервал последовательных снимков',
      PrintConv => {
        '10 frames/s' => '10 кадров/с',
        '15 frames/s' => '15 кадров/с',
        '20 frames/s' => '20 кадров/с',
        '5 frames/s' => '5 кадров/с',
      },
    },
   'SequentialShot' => {
      Description => 'Последовательность снимков',
      PrintConv => {
        'Adjust Exposure' => 'По сдвигу экспозиции',
        'Best' => 'Лучший снимок',
        'None' => 'Нет',
        'Standard' => 'Стандартная',
      },
    },
   'SerialNumber' => 'Серийный номер камеры',
   'Series' => 'Серия',
   'SeriesDateTime' => 'Дата серии',
   'SeriesDescription' => 'Описание срии',
   'SeriesModality' => 'Модальность серии',
   'SeriesNumber' => 'Номер серии',
   'ServiceID' => 'ID сервиса',
   'ServiceIdentifier' => 'Идентификатор поставщика и продукта',
   'SetCookie' => 'Настройки cookie',
   'ShadingCompensation' => 'Устранение теней',
   'ShadowScale' => 'Диапазон теней',
   'Shadows' => 'Тени',
   'ShadowsAdj' => 'Регулировка теней',
   'SharedData' => 'Общие данные',
   'Sharpness' => {
      Description => 'Резкость',
      PrintConv => {
        'Hard' => 'Сильная',
        'Normal' => 'Стандартная',
        'Soft' => 'Слабая',
      },
    },
   'SharpnessAdj' => 'Регулировка резкостти',
   'SharpnessOvershoot' => 'Резкость – Положительное отклонение',
   'SharpnessThreshold' => 'Резкость – Порог',
   'SharpnessUndershoot' => 'Резкость – Отрицательное отклонение',
   'ShootingCount' => 'Количество снимков с обеих точек просмотра',
   'ShootingMode' => 'Режим съёмки',
   'ShortDocumentID' => 'Краткий ID документа',
   'ShutterCurtainHack' => {
      Description => 'Синхронизация шторки затвора',
      PrintConv => {
        '1st-curtain sync' => 'Синхронизация по 1-й шторке',
        '2nd-curtain sync' => 'Синхронизация по 2-й шторке',
      },
    },
   'ShutterSpeed' => 'Скорость срабатывания затвора',
   'ShutterSpeedValue' => 'Скорость срабатывания затвора',
   'Sidebars' => 'Количество боковых панелей',
   'SidecarForExtension' => 'Расширение файла',
   'Signature' => 'Подпись',
   'SignatureUsageRights' => 'Права на использование Подписи',
   'SignerContactInfo' => 'Электронная подпись – Контактная информация',
   'SignificantBits' => 'Старшие биты',
   'SigningAuthority' => 'Электронная подпись – Орган',
   'SigningDate' => 'Электронная подпись – Дата подписи',
   'SigningLocation' => 'Электронная подпись – Местоположение',
   'SigningReason' => 'Электронная подпись – Причина подписания',
   'SimilarityIndex' => 'Индекс сходства',
   'Site' => 'Сайт',
   'SiteEnter' => 'Эффект при заходе на сайт',
   'SiteExit' => 'Эффект при уходе с сайта',
   'SizeMode' => {
      Description => 'Режим размера',
      PrintConv => {
        'Size Known' => 'Размер известный',
        'Size Not Known' => 'Размер не известен',
      },
    },
   'SlateInformation' => 'Сведения о Slate',
   'SlicesGroupName' => 'Название группы фрагментов',
   'Smoothness' => 'Сглаживание',
   'Snapshots' => 'Скриншоты',
   'SocTemperature' => 'Температура на чипе (Soc)',
   'SocialProfile' => 'Социальный профиль',
   'Software' => {
      Description => 'Приложение',
      PrintConv => {
        'PC Paintbrush 2.8 (with palette)' => 'PC Paintbrush 2.8 (с палитрой)',
        'PC Paintbrush 2.8 (without palette)' => 'PC Paintbrush 2.8 (без палитры)',
        'PC Paintbrush for Windows' => 'PC Paintbrush для Windows',
      },
    },
   'SoftwareVersion' => 'Версия приложения',
   'SonyCropSize' => 'Sony – Размер рамки кадрирования',
   'SonyCropTopLeft' => 'Sony – Верхний левый угол рамки кадрирования',
   'SonyRawFileType' => {
      Description => 'Sony – Тип RAW-файла',
      PrintConv => {
        'Sony Compressed RAW' => 'Sony – сжатый RAW',
        'Sony Lossless Compressed RAW' => 'Sony – сжатый без потерь RAW',
        'Sony Uncompressed 12-bit RAW' => 'Sony – 12-битный несжатый RAW',
        'Sony Uncompressed 14-bit RAW' => 'Sony – 14-битный несжатый RAW',
      },
    },
   'SonyToneCurve' => 'Sony – Тоновая кривая',
   'Sound' => 'Звукозапись',
   'Source' => 'Источник',
   'SourceCount' => 'Количество использованых камер',
   'SourceCreateDate' => 'Исходная дата создания',
   'SourceData' => 'Исходные данные',
   'SourceDate' => 'Дата публикации',
   'SourceEdition' => 'Печатное издание',
   'SourceFileName' => 'Название исходного файла',
   'SourcePhotosCount' => 'Количество исходных изображений',
   'SourcePublisher' => 'Издательство',
   'SourceRights' => 'Права печатного издания',
   'SourceTitle' => 'Название издательства',
   'SpatialFrequencyResponse' => 'Отклик пространственной частоты',
   'SpatialResolution' => 'Пространственное разрешение',
   'SpecialInstructions' => 'Специальные указания',
   'SpecialMode' => 'Специальный режим',
   'SpecialTypeID' => 'ID специального типа',
   'SpectralSensitivity' => 'Спектральная чувствительность',
   'SpeedX' => 'Скорость по оси X',
   'SpeedY' => 'Скорость по оси Y',
   'SpeedZ' => 'Скорость по оси Z',
   'Spherical' => 'Сферическое видео',
   'SplitColumn' => 'Разделенная колонка',
   'SpotHalftone' => 'Точка полутона',
   'StandardOutputSensitivity' => 'Стандартная выходная чувствительность',
   'StartEdgeCode' => 'Edge код старта',
   'StartReading' => 'Начало чтения',
   'StartTime' => 'Время начала',
   'StartTimecode' => 'Таймкод старта',
   'State' => 'Область',
   'Status' => 'Статус',
   'Stereo' => 'Стерео',
   'StereoMode' => 'Режим стерео',
   'StimVersion' => 'Версия Stim',
   'Stitched' => 'Сшитая видеопанорама',
   'StitchingSoftware' => 'Сшито в программе',
   'StoNits' => 'Освещенность (коэффициент пересчета кд/м2)',
   'StorageFormatDate' => 'Память – Дата форматирования',
   'StorageFormatTime' => 'Память – Время форматирования',
   'StorageMethod' => 'Способ хранения',
   'StorageModel' => 'Память – Модель',
   'StorageSerialNumber' => 'Память – Серийный номер',
   'StorageType' => 'Память – Тип',
   'StreamAvgBitrate' => 'Средний битрейт потока',
   'StreamAvgPacketSize' => 'Размер среднего битрейта потока',
   'StreamDuration' => 'Продолжительность потока',
   'StreamMaxBitrate' => 'Максимальный битрейт потока',
   'StreamMaxPacketSize' => 'Размер максимального битрейта потока',
   'StreamMimeLen' => 'Mime потока Len',
   'StreamMimeType' => 'Mime-тип потока',
   'StreamName' => 'Название потока',
   'StreamNameLen' => 'Название потока Len',
   'StreamNumber' => 'Номер потока',
   'StreamPreroll' => 'Предварительный просмотр потока',
   'StreamStartTime' => 'Время начала потока',
   'StreamType' => 'Тип потока',
   'Strikeout' => 'Перечёркнутый',
   'StripByteCounts' => 'Количество байт на полосу',
   'StripOffsets' => 'Смещение для каждой полосы изображения',
   'StripRowCounts' => 'Количество полос',
   'StrobeTime' => 'Время строба',
   'StructureType' => 'Тип конструкции',
   'StudyDateTime' => 'Дата обследования',
   'StudyDescription' => 'Описание обследования',
   'StudyID' => 'ID исследования',
   'StudyPhysician' => 'Врач проводивший обследование',
   'Sub-location' => 'Местоположение в городе',
   'SubFile' => 'Подфайл',
   'SubPacketH' => 'Субпакет H',
   'SubPacketSize' => 'Размер субпакета',
   'SubSecCreateDate' => 'Дата, время и милисекунды создания',
   'SubSecDateTimeOriginal' => 'Исходные дата, время и милисекунды создания',
   'SubSecModifyDate' => 'Дата, время и милисекунды редактирования',
   'SubSecTime' => 'Время редактирования в милисекундах',
   'SubSecTimeDigitized' => 'Время создания файла в милисекундах',
   'SubSecTimeOriginal' => 'Время съёмки в милисекундах',
   'SubTileBlockSize' => 'Размер блока подзаголовка',
   'SubfileType' => {
      Description => 'Тип подфайла',
      PrintConv => {
        'Alternate reduced-resolution image' => 'Альтернативное изображение с пониженным разрешением',
        'Color IW44' => 'Цветной IW44',
        'Full-resolution image' => 'Изображение с полным разрешением',
        'Grayscale IW44' => 'Градация серого IW44',
        'Multi-page document' => 'Многостраничный документ',
        'Reduced-resolution image' => 'Изображение с пониженным разрешением',
        'Shared component' => 'Общий компонент',
        'Single page of multi-page image' => 'Одна страница из многостраничного изображения',
        'Single page of multi-page reduced-resolution image' => 'Одна страница из многостраничного изображения с пониженным разрешением',
        'Single-page image' => 'Одностраничное изображение',
        'TIFF-FX mixed raster content' => 'Смешанное растровое содержимое TIFF-FX',
        'TIFF/IT final page' => 'Последняя страница TIFF/IT',
        'Thumbnail image' => 'Миниатюра',
        'Transparency mask' => 'Маска прозрачности изображения',
        'Transparency mask of multi-page image' => 'Маска прозрачности многостраничного изображения',
        'Transparency mask of reduced-resolution image' => 'Маска прозрачности изображения с пониженным разрешением',
        'Transparency mask of reduced-resolution multi-page image' => 'Маска прозрачности многостраничного изображения с пониженным разрешением',
        'invalid' => 'Некорректный',
      },
    },
   'Subject' => 'Тема',
   'SubjectArea' => 'Область объекта',
   'SubjectCode' => 'Предметный код',
   'SubjectDistance' => 'Дистанция к фокусируемому объекту',
   'SubjectDistanceRange' => {
      Description => 'Расстояние до снимаемого объекта',
      PrintConv => {
        'Close' => 'Съёмка с близкого расстояния',
        'Distant' => 'Съёмка с дальнего  расстояния',
        'Macro' => 'Макро съёмка',
        'Unknown' => 'Неизвестно',
      },
    },
   'SubjectLocation' => 'Расположение объекта',
   'SubjectPixelHeight' => 'Высота объекта в пикселях',
   'SubjectPixelWidth' => 'Ширина объекта в пикселях',
   'SubjectReference' => 'Ссылка на предмет',
   'SubjectUnits' => {
      Description => 'Единицы объекта',
      PrintConv => {
        'meters' => 'Метры',
        'radians' => 'Радианы',
      },
    },
   'Subtitle' => 'Субтитры',
   'SubtitleDescription' => 'Субтитры – Описание',
   'SuggestedPalette' => 'Рекомендованная палитра',
   'Summary' => 'Резюме',
   'SupplementalCategories' => 'Дополнительные категории',
   'SupplementalType' => {
      Description => 'Тип дополнения',
      PrintConv => {
        'Logo' => 'Логотип',
        'Main Image' => 'Основное изображение',
        'Rasterized Caption' => 'Растрированный заголовок',
        'Reduced Resolution Image' => 'Изображение с пониженным разрешением',
      },
    },
   'SymLink' => 'Символическая ссылка',
   'System' => 'Система',
   'SystemTime' => 'Системное время',
   'T4Options' => {
      Description => 'Параметры кодирования T4',
      PrintConv => {
        '2-Dimensional encoding' => 'Двумерное кодирование',
        'Fill bits added' => 'Добавление заполняющих битов',
        'Uncompressed' => 'Без сжатия',
      },
    },
   'T6Options' => {
      Description => 'Параметры кодирования T6',
      PrintConv => {
        'Uncompressed' => 'Без сжатия',
      },
    },
   'T82Options' => 'Параметры T82',
   'T88Options' => 'Параметры T88',
   'TIFF-EPStandardID' => 'ID стандарта TIFF-EP',
   'TIFFPreview' => 'Предпросмотр TIFF',
   'TIFF_FXExtensions' => {
      Description => 'Разширения TIFF_FX',
      PrintConv => {
        'JBIG2 Profile M' => 'JBIG2 профиль M',
        'N Layer Profile M' => 'N слой Профиль M',
        'Resolution/Image Width' => 'Разрешение/Ширина изображения',
        'Shared Data' => 'Общие данные',
      },
    },
   'TOCItems' => 'Количество точек навигации',
   'TStop' => 'Индекс светопропускания объектива (T-Stop)',
   'Tagged' => {
      Description => 'Тегированный',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'TaggedPDF' => {
      Description => 'Тегированный PDF',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'Take' => 'Дубль',
   'Tamper-proofKeys' => 'Ключи от взлома',
   'TargetAudiences' => 'Целевая аудитория',
   'TargetFileDOSName' => 'DOS название целевого файла',
   'TargetFileSize' => 'Размер целевого файла',
   'TargetLayerID' => 'ID целевого слоя',
   'TargetPrinter' => 'Среда печати',
   'Tattoo' => 'Татуировка',
   'Taxon' => 'Таксон',
   'TaxonAcceptedNameUsage' => 'Общепринятое название таксона',
   'TaxonAcceptedNameUsageID' => 'ID общепринятого названия таксона',
   'TaxonClass' => 'Класс таксона',
   'TaxonConceptID' => 'ID таксономической концепции',
   'TaxonFamily' => 'Семейство таксона',
   'TaxonGenus' => 'Род таксона',
   'TaxonHigherClassification' => 'Таксоны рангом выше',
   'TaxonID' => 'ID таксона',
   'TaxonInfraspecificEpithet' => 'Низший внутривидовой эпитет',
   'TaxonKingdom' => 'Царство таксона',
   'TaxonNameAccordingTo' => 'Источник описания границ таксона',
   'TaxonNameAccordingToID' => 'ID источника описания границ таксона',
   'TaxonNamePublishedIn' => 'Название таксона впервые опубликовано в',
   'TaxonNamePublishedInID' => 'ID первой публикации названия таксона',
   'TaxonNamePublishedInYear' => 'Год первой публикации названия таксона',
   'TaxonNomenclaturalCode' => 'Код номенклатуры таксона',
   'TaxonNomenclaturalStatus' => 'Номенклатурный статус таксона',
   'TaxonOrder' => 'Порядок таксона',
   'TaxonOriginalNameUsage' => 'Первоисточник названия таксона',
   'TaxonOriginalNameUsageID' => 'ID первоисточника названия таксона',
   'TaxonParentNameUsage' => 'Родительский таксон',
   'TaxonParentNameUsageID' => 'ID родительского таксона',
   'TaxonPhylum' => 'Тип таксона',
   'TaxonRank' => 'Ранг таксона',
   'TaxonRemarks' => 'Комментарии к таксону',
   'TaxonScientificName' => 'Научное название таксона',
   'TaxonScientificNameAuthorship' => 'Автор научного названия таксона',
   'TaxonScientificNameID' => 'ID научного названия таксона',
   'TaxonSpecificEpithet' => 'Видовой эпитет таксона',
   'TaxonSubgenus' => 'Подрод таксона',
   'TaxonTaxonomicStatus' => 'Статус таксона',
   'TaxonVerbatimTaxonRank' => 'Ранг таксона по первоисточнику',
   'TaxonVernacularName' => 'Народное название таксона',
   'Telephone' => 'Номер телефона',
   'Telescope' => 'Телескоп',
   'Template' => 'Шаблон',
   'TermsAndConditionsText' => 'Текст лицензионного соглашения',
   'TermsAndConditionsURL' => 'Ссылка на лицензионное соглашение',
   'TestName' => 'Тестовое название файла',
   'Text' => 'Текст',
   'TextLayerName' => 'Названия текстовых слоёв',
   'TextLayerText' => 'Текст текстового слоя',
   'TextLayers' => 'Текстовые слои',
   'TextToSpeech' => {
      Description => 'Текст в речь',
      PrintConv => {
        'Disabled' => 'Не включеён',
        'Enabled' => 'Включен',
      },
    },
   'TextureFormat' => 'Формат текстуры',
   'TheoraVersion' => 'Версия Theora',
   'Thresholding' => {
      Description => 'Пороговая обработка',
      PrintConv => {
        'No dithering or halftoning' => 'Дизеринг или полутонирование не применялось',
        'Ordered dither or halftone' => 'Заказной дизеринг или полутонирование',
        'Randomized dither' => 'Случайный дизеринг',
      },
    },
   'ThumbnailBPG' => 'Миниатюра BPG',
   'ThumbnailHeight' => 'Высота миниатюры',
   'ThumbnailImage' => 'Миниатюра изображения',
   'ThumbnailImageSize' => 'Размер миниатюри',
   'ThumbnailLength' => 'Строк в миниатюре',
   'ThumbnailOffset' => 'Смещение миниатюры',
   'ThumbnailTIFF' => 'TIFF миниатюра',
   'ThumbnailWidth' => 'Ширина миниатюры',
   'TileByteCounts' => 'Количество байт в тайле',
   'TileDepth' => 'Глубина тайла',
   'TileLength' => 'Строк в тайле',
   'TileOffsets' => 'Смещение тайла',
   'TileWidth' => 'Колонок в тайле',
   'Tiles' => 'Тайлы',
   'Time' => 'Время',
   'TimeCode' => 'Код времени',
   'TimeCodes' => 'Таймкоды',
   'TimeCreated' => 'Время создания',
   'TimeSent' => 'Время отправления',
   'TimeShot' => 'Время съёмки',
   'TimeStamp' => 'Временна́я метка',
   'TimeTransparency' => 'Прозрачность времени',
   'TimeZone' => 'Часовой пояс',
   'TimeZone2' => 'Часовой пояс 2',
   'TimeZoneOffset' => 'Смещение часового пояса',
   'TimeZoneURL' => 'URL Часового пояса',
   'TimelineInfo' => 'Сведения о шкале времени',
   'TimezoneID' => 'ID часового пояса',
   'TimezoneName' => 'Название часового пояса',
   'TimezoneOffsetFrom' => 'Смещение часового пояса с',
   'TimezoneOffsetTo' => 'Смещение часового пояса к',
   'Title' => 'Название',
   'TitleLen' => 'Название Len',
   'ToneCurveBlueX' => 'Тоновая кривая – Синий X',
   'ToneCurveBlueY' => 'Тоновая кривая – Синий Y',
   'ToneCurveBrightnessX' => 'Тоновая кривая – Яркость X',
   'ToneCurveBrightnessY' => 'Тоновая кривая – Яркость Y',
   'ToneCurveGreenX' => 'Тоновая кривая – Зелёный X',
   'ToneCurveGreenY' => 'Тоновая кривая – Зелёный Y',
   'ToneCurveRedX' => 'Тоновая кривая – Красный X',
   'ToneCurveRedY' => 'Тоновая кривая – Красный Y',
   'ToolName' => 'Приложение',
   'ToolVersion' => 'Версия приложения',
   'TopMargin' => 'Отступ сверху',
   'TotalBitrate' => 'Общий битрейт',
   'TotalDataRate' => 'Общая скорость передачи данных',
   'TotalDuration' => 'Общая продолжительность',
   'TotalEditTime' => 'Общее время редактирования',
   'TotalFrames' => 'Всего кадров',
   'TotalSamples' => 'Всего образцов',
   'Track' => 'Трек',
   'TrackCategory' => 'Категория трека',
   'TrackComments' => 'Комментарии к треку',
   'TrackID' => 'ID трека',
   'TrackLyrics' => 'Текст песни',
   'TrackNumber' => 'Номер трека',
   'Trademark' => 'Торговая марка',
   'TransferFunction' => 'Функция переноса',
   'TransferRange' => 'Диапазон функции переноса',
   'Transformation' => {
      Description => 'Трансформация',
      PrintConv => {
        'Horizontal (normal)' => 'Горизонтально',
        'Mirror horizontal' => 'Отразить по горизонтали',
        'Mirror horizontal and rotate 270 CW' => 'Отражение по горизонтали и поворот на 270° по часовой стрелке',
        'Mirror horizontal and rotate 90 CW' => 'Отражение по горизонтали и поворот на 90° по часовой стрелке',
        'Mirror vertical' => 'Отразить по вертикали',
        'Rotate 180' => 'Повернуть на 180°',
        'Rotate 270 CW' => 'Поворот на 270° по часовой стрелке',
        'Rotate 90 CW' => 'Поворот на 90° по часовой стрелке',
      },
    },
   'TransmissionReference' => 'Ссылка на источник',
   'Transparency' => 'Прозрачность',
   'TransparencyIndicator' => 'Наличие прозрачности',
   'TransparentIndex' => 'Индекс прозрачности',
   'TrapIndicator' => 'Применен ли треппинг к файлу',
   'Trapped' => 'Треппинг',
   'Trigger' => 'Время срабатывания сигнала',
   'TriggerMode' => {
      Description => 'Режим триггера',
      PrintConv => {
        'CodeLoc Not Entered' => 'Код блокировки не задан',
        'External Sensor' => 'Внешний датчик',
        'Motion Detection' => 'Обнаружение движения',
        'Point and Shoot' => 'Наведи и снимай',
        'Time Lapse' => 'Таймлапс',
      },
    },
   'Type' => 'Тип',
   'TypeStatus' => 'Список номенклатурных типов',
   'UIC1Tag' => 'UIC – Тег 1',
   'UIC2Tag' => 'UIC – Тег 2',
   'UIC3Tag' => 'UIC – Тег 3',
   'UIC4Tag' => 'UIC – Тег 4',
   'UID' => 'Уникальный ID',
   'URLList1' => 'Список URL 1',
   'URL_List' => 'Список URL',
   'USPTOOriginalContentType' => {
      Description => 'USPTO – Исходный тип контента',
      PrintConv => {
        'Color' => 'Цвет',
        'Grayscale' => 'Оттенки серого',
        'Text or Drawing' => 'Текст или рисунок',
      },
    },
   'UUID-Unknown' => 'Неизвестный UUID',
   'UUIDList' => 'Список UUID',
   'Uncompressed' => {
      Description => 'Не сжато',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'UncompressedSize' => 'Несжатый размер',
   'UncompressedTextLength' => 'Длина несжатого текста',
   'Underline' => 'Подчёркнутый',
   'UnderlinePosition' => 'Позиция подчёркивания',
   'UnderlineThickness' => 'Толщина подчёркивания',
   'UnicodeAlphaNames' => 'Юникодное название альфа-канала',
   'UniqueCameraModel' => 'Уникальное название модели камеры',
   'UniqueDocumentID' => 'Уникальный ID документа',
   'UniqueObjectName' => 'Уникальное название объекта',
   'Units' => {
      Description => 'Единицы измерения',
      PrintConv => {
        'Inches' => 'Дюймы',
        'Picas' => 'Пики',
        'Points' => 'Точки',
        'mm' => 'Милиметры',
      },
    },
   'Unknown' => 'Неизвестный',
   'UnknownDate' => 'Неизвестная дата',
   'UpdatedTitle' => 'Обновленное название',
   'Urgency' => {
      Description => 'Приоритет обработки',
      PrintConv => {
        '0 (reserved)' => '0 (Зарезервированно)',
        '1 (most urgent)' => '1 (Срочно)',
        '5 (normal urgency)' => '5 (Обычный)',
        '8 (least urgent)' => '8 (Не срочно)',
        '9 (user-defined priority)' => '9 (Пользовательский приоритет)',
      },
    },
   'UsageRightsMessage' => 'Сообщение о правах использования',
   'UsePanoramaViewer' => 'Использовать просмотрщик панорам',
   'UserAccess' => {
      Description => 'Разрешается',
      PrintConv => {
        'Annotate' => 'Аннотирование',
        'Assemble' => 'Сборка',
        'Copy' => 'Копирование',
        'Extract' => 'Извлечение',
        'Fill forms' => 'Заполнение форм',
        'Modify' => 'Редактирование',
        'Print' => 'Печать',
        'Print high-res' => 'Печать в высоком разрешении',
      },
    },
   'UserComment' => 'Комментарии пользователя',
   'UserFields' => 'Дополнительное поле',
   'UserID' => 'ID пользователя',
   'UserLabel' => 'Ярлык пользователя',
   'VBRBytes' => 'Переменный битрейт – Бит',
   'VBRFrames' => 'Переменный битрейт – Количество фреймов',
   'VBRScale' => 'Переменный битрейт – Шкала',
   'VCalendarVersion' => 'Версия VCalendar',
   'VCardVersion' => 'Версия VCard',
   'Vary' => 'Альтернативный HTTP-заголовок',
   'Vendor' => 'Поставщик',
   'VendorURL' => 'URL поставщика',
   'Version' => 'Версия',
   'Version2' => 'Версия 2',
   'VersionCreateDate' => 'Версия – Дата создания',
   'VersionModifyDate' => 'Версия – Дата редактирования',
   'VersionYear' => 'Год стандарта факс-профиля',
   'VerticalDivergence' => 'Угол вертикального расхождения',
   'Vibrance' => 'Красочность',
   'VideoBitrate' => 'Битрейт видео',
   'VideoClosedCaptioning' => {
      Description => 'Видео с субтитрами',
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'VideoCodecID' => 'ID видеокодека',
   'VideoEncoding' => 'Видеокодирование',
   'VideoFormat' => 'Формат видео',
   'VideoMode' => 'Режим видео',
   'VideoQuality' => 'Качество видео',
   'VideoScanType' => 'Тип сканирования видео',
   'VideoSize' => 'Размер видео',
   'View' => 'Просмотр',
   'ViewType' => {
      Description => 'Тип просмотра',
      PrintConv => {
        'No Pop-up Effect' => 'Без всплывающего эффекта',
        'Pop-up Effect' => 'С всплывающим эффектом',
      },
    },
   'Viewfinder' => 'Видоискатель',
   'VignetteCorrectionAlreadyApplied' => 'Применена коррекция виньетирования',
   'VignettingCorrParams' => 'Параметры коррекции виньетирования',
   'VignettingCorrection' => {
      Description => 'Коррекция виньетирования',
      PrintConv => {
        'Auto' => 'Автоматическая',
        'No correction params available' => 'Недоступна',
        'Off' => 'Не включена',
      },
    },
   'VirtualImageHeight' => 'Высота виртуального изображения',
   'VirtualImageWidth' => 'Ширина виртуального изображения',
   'VirtualPageUnits' => 'Единицы виртуальной страницы',
   'VisualFlightRulesHUD' => 'Визуальные правила полетов HUD',
   'VoiceMemo' => {
      Description => 'Голосовые заметки',
      PrintConv => {
        'Off' => 'Не включено',
        'On' => 'Включено',
      },
    },
   'Volume' => 'Том',
   'VolumeBlockCount' => 'Количество блоков в томе',
   'VolumeBlockSize' => 'Размер блока в томе',
   'VolumeCreateDate' => 'Дата создания тома',
   'VolumeEffectiveDate' => 'Дата начала использования тома',
   'VolumeExpirationDate' => 'Дата устаревания тома',
   'VolumeID' => 'ID тома',
   'VolumeLabel' => 'Метка тома',
   'VolumeModifyDate' => 'Дата редактирования тома',
   'VolumeName' => 'Название тома',
   'VolumeSetDiskCount' => 'Количество дисков в наборе томов',
   'VolumeSetDiskNumber' => 'Номер диска в наборе томов',
   'VolumeSetName' => 'Название набора томов',
   'VolumeSize' => 'Размер тома',
   'VorbisVersion' => 'Версия Vorbis',
   'WBBlueLevel' => 'Баланс Белого – Уровень синего',
   'WBGreenLevel' => 'Баланс Белого – Уровень зелёного',
   'WBMode' => 'Режим баланса белого',
   'WBRedLevel' => 'Баланс Белого – Уровень красного',
   'WBScale' => 'Чёрно-Белое – Шкала',
   'WB_GBRGLevels' => 'Чёрно-Белое – Уровни GBRG',
   'WB_GRGBLevels' => 'Уровни WB_GRGB',
   'WB_RBLevelsCloudy' => 'Баланс белого – Уровни RB – Облачно',
   'WB_RBLevelsCoolWhiteF' => 'Баланс белого – Уровни RB – Холодный белый F',
   'WB_RBLevelsDayWhiteF' => 'Баланс белого – Уровни RB – Дневной белый F',
   'WB_RBLevelsDaylight' => 'Баланс белого – Уровни RB – Дневной свет',
   'WB_RBLevelsDaylightF' => 'Баланс белого – Уровни RB – Дневной свет F',
   'WB_RBLevelsFlash' => 'Баланс белого – Уровни RB – Вспышка',
   'WB_RBLevelsShade' => 'Баланс белого – Уровни RB – Тень',
   'WB_RBLevelsTungsten' => 'Баланс белого – Уровни RB – Лампа накаливания',
   'WB_RBLevelsUnknown' => 'Баланс белого – Уровни RB – Неизвестный источник света',
   'WB_RBLevelsWhiteF' => 'Баланс белого – Уровни RB – Белый',
   'WB_RGBLevels' => 'Уровни WB RGB',
   'WB_RGGBLevels' => 'Чёрно-Белое – Уровни RGGB',
   'WWSFamilyName' => 'Название семейства WWS',
   'WWSSubfamilyName' => 'Название стиля WWS',
   'WangAnnotation' => 'Wang Imaging – Аннотация',
   'WangTag1' => 'Wang Imaging – Тег 1',
   'WangTag3' => 'Wang Imaging – Тег 3',
   'WangTag4' => 'Wang Imaging – Тег 4',
   'Warning' => 'Уведомление',
   'WarpQuadrilateral' => 'Деформация четырёхугольника',
   'Watched' => {
      PrintConv => {
        'No' => 'Нет',
        'Yes' => 'Да',
      },
    },
   'WaterDepth' => 'Глубина воды',
   'Watermark' => 'Водяной знак',
   'Weight' => 'Толщина шрифта',
   'WhiteBalance' => {
      Description => 'Баланс белого',
      PrintConv => {
        'Auto' => 'Автоматический',
        'Black & White' => 'Монохром',
        'Cloudy' => 'Облачность',
        'Color Temperature/Color Filter' => 'Цветовая температура/Цветовой фильтр',
        'Cool White Fluorescent' => 'Флуоресцентный белый холодный',
        'Custom' => 'Пользователь',
        'Custom 1' => 'ПЕРСОНАЛЬНЫЙ 1',
        'Custom 2' => 'ПЕРСОНАЛЬНЫЙ 2',
        'Custom 3' => 'ПЕРСОНАЛЬНЫЙ 3',
        'Custom 4' => 'ПЕРСОНАЛЬНЫЙ 4',
        'Day White Fluorescent' => 'Флуоресцентный белый дневной',
        'Daylight' => 'Дневной свет',
        'Daylight Fluorescent' => 'Флуоресцентный дневной',
        'Flash' => 'Вспышка',
        'Fluorescent' => 'Флуоресцентный',
        'Manual' => 'Ручной',
        'Shade' => 'Тень',
        'Tungsten' => 'Лампа накаливания',
        'Unknown' => 'неизвестно',
        'Warm White Fluorescent' => 'Флуоресцентный теплый белый',
        'White Fluorescent' => 'Флуоресцентный белый',
      },
    },
   'WhiteBalance0' => 'Баланс белого 0',
   'WhiteBalance1' => 'Баланс белого 1',
   'WhiteBalance2' => 'Баланс белого 2',
   'WhiteBalanceRGB' => 'Баланс белого RGB',
   'WhiteLevel' => 'Уровень белого',
   'WhiteLuminance' => 'Яркость белого',
   'WhitePoint' => 'Цветность белой точки',
   'WhitePointX' => 'Белая точка по X',
   'WhitePointY' => 'Белая точка по Y',
   'WhitesAdj' => 'Регулировка уровня белого',
   'WideRange' => {
      Description => 'Широкий диапазон',
      PrintConv => {
        'Off' => 'Не включён',
        'On' => 'Включен',
      },
    },
   'WidthBytes' => 'Ширина в байтах',
   'WidthResolution' => 'Разрешение по ширине (PPI)',
   'WindowOrigin' => 'Начало координат окна',
   'WindowOriginAuto' => 'Начало координат окна – Автоматически',
   'WindowSize' => 'Размер окна',
   'WindowTarget' => 'Целевое окно',
   'Words' => 'Слов',
   'WorkflowURL' => 'URL рабочего процесса',
   'WorkingDirectory' => 'Рабочий каталог',
   'WorkingPath' => 'Рабочий контур',
   'WorldToCamera' => 'Мир на камеру',
   'WorldToNDC' => 'Мир на NDC',
   'WrapModes' => 'Режим обёртывания',
   'Writer-Editor' => 'Писатель/Редактор',
   'WriterName' => 'Название редактора',
   'XAttrAppleMailDateReceived' => 'X Attr – Почта Apple – Дата получения',
   'XAttrAppleMailDateSent' => 'X Attr – Почта Apple – Дата отправки',
   'XAttrAppleMailIsRemoteAttachment' => 'X Attr – Почта Apple – Наличие удалённого вложения',
   'XAttrFinderInfo' => 'X Attr – Свойства файла',
   'XAttrLastUsedDate' => 'X Attr – Дата последнего использования',
   'XAttrMDItemDownloadedDate' => 'X Attr – MD Item – Дата загрузки',
   'XAttrMDItemFinderComment' => 'X Attr – MD Item – Комментарий к файлу',
   'XAttrMDItemWhereFroms' => 'X Attr – MD Item – Источник файла',
   'XAttrMDLabel' => 'X Attr – MD – Цветная метка файла',
   'XAttrQuarantine' => 'X Attr – Карантинный',
   'XAttrResourceFork' => 'X Attr – Вилка ресурсов',
   'XCFVersion' => 'Версия XCF',
   'XClipPathUnits' => 'Единицы обтравочного контура по X',
   'XHeight' => 'Высота строчных букв',
   'XMLData' => 'Данные XML',
   'XOffset' => 'Смещение по X',
   'XPAuthor' => 'XP – Автор',
   'XPComment' => 'XP – Комментарии',
   'XPKeywords' => 'XP – Ключевые слова',
   'XPSubject' => 'XP – Тема',
   'XPTitle' => 'XP – Название',
   'XPosition' => 'Положение изображения по X',
   'XResolution' => 'Разрешение по X',
   'XYResolution' => 'Разрешение по X и Y',
   'YCbCrCoefficients' => 'Коэффициенты преобразования из RGB в Y Cb Cr',
   'YCbCrPositioning' => {
      Description => 'Положение точки, определяющей цвет в Y Cb Cr',
      PrintConv => {
        'Centered' => 'Центрованный',
        'Co-sited' => 'Совместимый',
      },
    },
   'YCbCrSubSampling' => 'Коэффициент субдискретизации Y Cb Cr',
   'YClipPathUnits' => 'Единицы обтравочного контура по Y',
   'YLevel' => 'Уровень – Y',
   'YOffset' => 'Смещение по Y',
   'YPosition' => 'Положение изображения по Y',
   'YResolution' => 'Разрешение по Y',
   'YTarget' => 'Цель – Y',
   'Yaw' => 'Рыскание  (Поворот)',
   'YawAngle' => 'Угол поворота (Рыскание)',
   'Year' => 'Год',
   'ZipBitFlag' => 'Zip – Битовый флаг',
   'ZipCRC' => 'Zip – CRC',
   'ZipCompressedSize' => 'Zip – Сжатый размер',
   'ZipCompression' => {
      Description => 'Zip – Сжатие',
      PrintConv => {
        'Enhanced Deflate using Deflate64(tm)' => 'Улучшенный Deflate с использованием Deflate64(tm)',
        'IBM LZ77 z Architecture (PFS)' => 'IBM LZ77 z/Architecture (PFS)',
        'IBM TERSE (new)' => 'IBM TERSE (новый)',
        'Imploded (old IBM TERSE)' => 'Imploded (старый IBM TERSE)',
        'None' => 'Без сжатия',
        'PPMd version I, Rev 1' => 'PPMd версия I, Ревизия 1',
        'Reduced with compression factor 1' => 'Сжатый с коэффициентом сжатия 1',
        'Reduced with compression factor 2' => 'Сжатый с коэффициентом сжатия 2',
        'Reduced with compression factor 3' => 'Сжатый с коэффициентом сжатия 3',
        'Reduced with compression factor 4' => 'Сжатый с коэффициентом сжатия 4',
        'WavPack compressed' => 'WavPack',
      },
    },
   'ZipFileName' => 'Zip – Название файла',
   'ZipFileNameLength' => 'Zip – Длина названия файла',
   'ZipModifyDate' => 'Zip – Дата редактирования',
   'ZipRequiredVersion' => 'Zip – Требуемая версия',
   'ZipUncompressedSize' => 'Zip – Несжатый размер',
   'ZoneMatching' => {
      Description => 'Согласование зон',
      PrintConv => {
        'High Key' => 'В высоком ключе',
        'ISO Setting Used' => 'Используются параметры ISO',
        'Low Key' => 'В низком ключе',
      },
    },
   'Zoom' => 'Зум',
   'ZoomPos' => 'Позиция зума',
   'ZoomedPreviewImage' => 'Увеличенный предпросмотр изображения',
   'iTunesMediaType' => 'Тип iTunes медиа',
);

1;  # end

__END__

=head1 NAME

Image::ExifTool::Lang::ru.pm - ExifTool Russian language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Sergey Shemetov, Dmitry Yerokhin, Anton Sukhinov and
Alexander for providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
