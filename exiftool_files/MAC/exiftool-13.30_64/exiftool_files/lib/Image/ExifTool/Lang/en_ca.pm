#------------------------------------------------------------------------------
# File:         en_ca.pm
#
# Description:  ExifTool Canadian English language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::en_ca;

use strict;
use vars qw($VERSION);

$VERSION = '1.12';

%Image::ExifTool::Lang::en_ca::Translate = (
   'AboveColor' => 'Above Colour',
   'AdvancedFilter' => {
      PrintConv => {
        'Partial Color Blue' => 'Partial Colour Blue',
        'Partial Color Green' => 'Partial Colour Green',
        'Partial Color Orange' => 'Partial Colour Orange',
        'Partial Color Purple' => 'Partial Colour Purple',
        'Partial Color Red' => 'Partial Colour Red',
        'Partial Color Yellow' => 'Partial Colour Yellow',
        'Pop Color' => 'Pop Colour',
      },
    },
   'AdvancedSceneMode' => {
      PrintConv => {
        'Color Select' => 'Colour Select',
      },
    },
   'AllColorFlatField1' => 'All Colour Flat Field 1',
   'AllColorFlatField2' => 'All Colour Flat Field 2',
   'AllColorFlatField3' => 'All Colour Flat Field 3',
   'Alpha' => {
      PrintConv => {
        'Alpha Exists (W color component)' => 'Alpha Exists (W colour component)',
        'Alpha Exists (color not premultiplied)' => 'Alpha Exists (colour not premultiplied)',
        'Alpha Exists (color premultiplied)' => 'Alpha Exists (colour premultiplied)',
      },
    },
   'AlternateDuotoneColors' => 'Alternate Duotone Colours',
   'AlternateSpotColors' => 'Alternate Spot Colours',
   'ArtFilter' => {
      PrintConv => {
        'Pale & Light Color' => 'Pale & Light Colour',
        'Pale & Light Color II' => 'Pale & Light Colour II',
        'Partial Color' => 'Partial Colour',
        'Partial Color II' => 'Partial Colour II',
        'Partial Color III' => 'Partial Colour III',
        'Watercolor' => 'Watercolour',
        'Watercolor I' => 'Watercolour I',
        'Watercolor II' => 'Watercolour II',
      },
    },
   'ArtFilterEffect' => {
      PrintConv => {
        'Green Color Filter' => 'Green Colour Filter',
        'No Color Filter' => 'No Colour Filter',
        'Orange Color Filter' => 'Orange Colour Filter',
        'Pale & Light Color' => 'Pale & Light Colour',
        'Pale & Light Color II' => 'Pale & Light Colour II',
        'Partial Color' => 'Partial Colour',
        'Partial Color II' => 'Partial Colour II',
        'Partial Color III' => 'Partial Colour III',
        'Red Color Filter' => 'Red Colour Filter',
        'Watercolor' => 'Watercolour',
        'Watercolor I' => 'Watercolour I',
        'Watercolor II' => 'Watercolour II',
        'Yellow Color Filter' => 'Yellow Colour Filter',
      },
    },
   'AutoAFPointColorTracking' => 'Auto AF Point Colour Tracking',
   'BackgroundColor' => 'Background Colour',
   'BackgroundColorIndicator' => {
      Description => 'Background Colour Indicator',
      PrintConv => {
        'Specified Background Color' => 'Specified Background Colour',
        'Unspecified Background Color' => 'Unspecified Background Colour',
      },
    },
   'BackgroundColorValue' => 'Background Colour Value',
   'BasicColorImageSequence' => 'Basic Colour Image Sequence',
   'BelowColor' => 'Below Colour',
   'BestShotMode' => {
      PrintConv => {
        'Water Color' => 'Water Colour',
      },
    },
   'BkColor' => 'Background Colour',
   'BluePaletteColorTableData' => 'Blue Palette Colour Table Data',
   'BluePaletteColorTableDescriptor' => 'Blue Palette Colour Table Descriptor',
   'BorderColor' => 'Border Colour',
   'CFAPlaneColor' => 'CFA Plane Colour',
   'CameraColorCalibration01' => 'Camera Colour Calibration 01',
   'CameraColorCalibration02' => 'Camera Colour Calibration 02',
   'CameraColorCalibration03' => 'Camera Colour Calibration 03',
   'CameraColorCalibration04' => 'Camera Colour Calibration 04',
   'CameraColorCalibration05' => 'Camera Colour Calibration 05',
   'CameraColorCalibration06' => 'Camera Colour Calibration 06',
   'CameraColorCalibration07' => 'Camera Colour Calibration 07',
   'CameraColorCalibration08' => 'Camera Colour Calibration 08',
   'CameraColorCalibration09' => 'Camera Colour Calibration 09',
   'CameraColorCalibration10' => 'Camera Colour Calibration 10',
   'CameraColorCalibration11' => 'Camera Colour Calibration 11',
   'CameraColorCalibration12' => 'Camera Colour Calibration 12',
   'CameraColorCalibration13' => 'Camera Colour Calibration 13',
   'CameraColorCalibration14' => 'Camera Colour Calibration 14',
   'CameraColorCalibration15' => 'Camera Colour Calibration 15',
   'CameraRawColorTone' => 'Camera Raw Colour Tone',
   'CanonColorInfo1' => 'Canon Colour Info 1',
   'CanonColorInfo2' => 'Canon Colour Info 2',
   'ChromaticityColorant' => 'Chromaticity Colourant',
   'CodingMethods' => {
      PrintConv => {
        'JBIG color' => 'JBIG colour',
      },
    },
   'Color' => {
      Description => 'Colour',
      PrintConv => {
        'Color' => 'Colour',
      },
    },
   'ColorAberrationControl' => 'Colour Aberration Control',
   'ColorAdjustment' => 'Colour Adjustment',
   'ColorAdjustmentMode' => 'Colour Adjustment Mode',
   'ColorAverages' => 'Colour Averages',
   'ColorBW' => 'Colour BW',
   'ColorBalance' => 'Colour Balance',
   'ColorBalanceAdj' => 'Colour Balance Adj',
   'ColorBalanceBlue' => 'Colour Balance Blue',
   'ColorBalanceGreen' => 'Colour Balance Green',
   'ColorBalanceRed' => 'Colour Balance Red',
   'ColorBalanceUnknown' => 'Colour Balance Unknown',
   'ColorBalanceVersion' => 'Colour Balance Version',
   'ColorBitDepth' => 'Colour Bit Depth',
   'ColorBlur' => 'Colour Blur',
   'ColorBlurOn' => 'Colour Blur On',
   'ColorBoostLevel' => 'Colour Boost Level',
   'ColorBoostType' => 'Colour Boost Type',
   'ColorBooster' => 'Colour Booster',
   'ColorCalibrationMatrix' => 'Colour Calibration Matrix',
   'ColorCasts' => 'Colour Casts',
   'ColorCharacterization' => 'Colour Characterization',
   'ColorClass' => 'Colour Class',
   'ColorCompensationFilter' => 'Colour Compensation Filter',
   'ColorCompensationFilterCustom' => 'Colour Compensation Filter Custom',
   'ColorCompensationFilterSet' => 'Colour Compensation Filter Set',
   'ColorComponents' => 'Colour Components',
   'ColorControl' => 'Colour Control',
   'ColorCorrection' => 'Colour Correction',
   'ColorCreatorEffect' => 'Colour Creator Effect',
   'ColorDataVersion' => 'Colour Data Version',
   'ColorDescriptor' => 'Colour Descriptor',
   'ColorEffect' => 'Colour Effect',
   'ColorFieldCode' => 'Colour Field Code',
   'ColorFilter' => 'Colour Filter',
   'ColorGain' => 'Colour Gain',
   'ColorGroup' => 'Colour Group',
   'ColorHalftoningInfo' => 'Colour Halftoning Info',
   'ColorHue' => 'Colour Hue',
   'ColorImagePrintingFlag' => 'Colour Image Printing Flag',
   'ColorLabel' => 'Colour Label',
   'ColorMap' => 'Colour Map',
   'ColorMatrix' => 'Colour Matrix',
   'ColorMatrix1' => 'Colour Matrix 1',
   'ColorMatrix2' => 'Colour Matrix 2',
   'ColorMatrixA' => 'Colour Matrix A',
   'ColorMatrixAdobeRGB' => 'Colour Matrix Adobe RGB',
   'ColorMatrixB' => 'Colour Matrix B',
   'ColorMatrixNumber' => 'Colour Matrix Number',
   'ColorMatrixSRGB' => 'Colour Matrix sRGB',
   'ColorMode' => {
      Description => 'Colour Mode',
      PrintConv => {
        'Indexed Color' => 'Indexed Colour',
        'Natural color' => 'Natural colour',
        'Neutral Color' => 'Neutral Colour',
        'RGB Color' => 'RGB Colour',
        'Saturated Color' => 'Saturated Colour',
        'Vivid color' => 'Vivid colour',
      },
    },
   'ColorMoireReduction' => 'Colour Moire Reduction',
   'ColorMoireReductionMode' => 'Colour Moire Reduction Mode',
   'ColorNoiseReduction' => 'Colour Noise Reduction',
   'ColorNoiseReductionDetail' => 'Colour Noise Reduction Detail',
   'ColorNoiseReductionIntensity' => 'Colour Noise Reduction Intensity',
   'ColorNoiseReductionSharpness' => 'Colour Noise Reduction Sharpness',
   'ColorNoiseReductionSmoothness' => 'Colour Noise Reduction Smoothness',
   'ColorObjBackType' => 'Colour Obj Back Type',
   'ColorObjName' => 'Colour Obj Name',
   'ColorObjType' => 'Colour Obj Type',
   'ColorObjVersion' => 'Colour Obj Version',
   'ColorPalette' => 'Colour Palette',
   'ColorPlanes' => 'Colour Planes',
   'ColorPrimaries' => 'Colour Primaries',
   'ColorProfile' => 'Colour Profile',
   'ColorProfileSettings' => 'Colour Profile Settings',
   'ColorRangeLevels' => 'Colour Range Levels',
   'ColorRepresentation' => 'Colour Representation',
   'ColorReproduction' => 'Colour Reproduction',
   'ColorResolutionDepth' => 'Colour Resolution Depth',
   'ColorResponseUnit' => 'Colour Response Unit',
   'ColorSamplersResource' => 'Colour Samplers Resource',
   'ColorSamplersResource2' => 'Colour Samplers Resource 2',
   'ColorSaturationAdj' => 'Colour Saturation Adj',
   'ColorSequence' => 'Colour Sequence',
   'ColorSiting' => 'Colour Siting',
   'ColorSpace' => {
      Description => 'Colour Space',
      PrintConv => {
        'Embedded Color Profile' => 'Embedded Colour Profile',
        'Linked Color Profile' => 'Linked Colour Profile',
        'No color space specified' => 'No colour space specified',
        'Windows Color Space' => 'Windows Colour Space',
      },
    },
   'ColorSpaceData' => 'Colour Space Data',
   'ColorSpecApproximation' => 'Colour Spec Approximation',
   'ColorSpecData' => 'Colour Spec Data',
   'ColorSpecMethod' => {
      Description => 'Colour Spec Method',
      PrintConv => {
        'Vendor Color' => 'Vendor Colour',
      },
    },
   'ColorSpecPrecedence' => 'Colour Spec Precedence',
   'ColorSpecification' => 'Colour Specification',
   'ColorTable' => 'Colour Table',
   'ColorTempAsShot' => 'Colour Temp As Shot',
   'ColorTempAuto' => 'Colour Temp Auto',
   'ColorTempCloudy' => 'Colour Temp Cloudy',
   'ColorTempCustom' => 'Colour Temp Custom',
   'ColorTempCustom1' => 'Colour Temp Custom 1',
   'ColorTempCustom2' => 'Colour Temp Custom 2',
   'ColorTempDaylight' => 'Colour Temp Daylight',
   'ColorTempFlash' => 'Colour Temp Flash',
   'ColorTempFlashData' => 'Colour Temp Flash Data',
   'ColorTempFluorescent' => 'Colour Temp Fluorescent',
   'ColorTempFluorescentD' => 'Colour Temp Fluorescent D',
   'ColorTempFluorescentN' => 'Colour Temp Fluorescent N',
   'ColorTempFluorescentW' => 'Colour Temp Fluorescent W',
   'ColorTempKelvin' => 'Colour Temp Kelvin',
   'ColorTempMeasured' => 'Colour Temp Measured',
   'ColorTempPC1' => 'Colour Temp PC1',
   'ColorTempPC2' => 'Colour Temp PC2',
   'ColorTempPC3' => 'Colour Temp PC3',
   'ColorTempShade' => 'Colour Temp Shade',
   'ColorTempTungsten' => 'Colour Temp Tungsten',
   'ColorTempUnknown' => 'Colour Temp Unknown',
   'ColorTempUnknown10' => 'Colour Temp Unknown 10',
   'ColorTempUnknown11' => 'Colour Temp Unknown 11',
   'ColorTempUnknown12' => 'Colour Temp Unknown 12',
   'ColorTempUnknown13' => 'Colour Temp Unknown 13',
   'ColorTempUnknown14' => 'Colour Temp Unknown 14',
   'ColorTempUnknown15' => 'Colour Temp Unknown 15',
   'ColorTempUnknown16' => 'Colour Temp Unknown 16',
   'ColorTempUnknown17' => 'Colour Temp Unknown 17',
   'ColorTempUnknown18' => 'Colour Temp Unknown 18',
   'ColorTempUnknown19' => 'Colour Temp Unknown 19',
   'ColorTempUnknown2' => 'Colour Temp Unknown 2',
   'ColorTempUnknown20' => 'Colour Temp Unknown 20',
   'ColorTempUnknown21' => 'Colour Temp Unknown 21',
   'ColorTempUnknown22' => 'Colour Temp Unknown 22',
   'ColorTempUnknown23' => 'Colour Temp Unknown 23',
   'ColorTempUnknown24' => 'Colour Temp Unknown 24',
   'ColorTempUnknown25' => 'Colour Temp Unknown 25',
   'ColorTempUnknown26' => 'Colour Temp Unknown 26',
   'ColorTempUnknown27' => 'Colour Temp Unknown 27',
   'ColorTempUnknown28' => 'Colour Temp Unknown 28',
   'ColorTempUnknown29' => 'Colour Temp Unknown 29',
   'ColorTempUnknown3' => 'Colour Temp Unknown 3',
   'ColorTempUnknown30' => 'Colour Temp Unknown 30',
   'ColorTempUnknown4' => 'Colour Temp Unknown 4',
   'ColorTempUnknown5' => 'Colour Temp Unknown 5',
   'ColorTempUnknown6' => 'Colour Temp Unknown 6',
   'ColorTempUnknown7' => 'Colour Temp Unknown 7',
   'ColorTempUnknown8' => 'Colour Temp Unknown 8',
   'ColorTempUnknown9' => 'Colour Temp Unknown 9',
   'ColorTemperature' => 'Colour Temperature',
   'ColorTemperatureAdj' => 'Colour Temperature Adj',
   'ColorTemperatureAuto' => 'Colour Temperature Auto',
   'ColorTemperatureBG' => 'Colour Temperature BG',
   'ColorTemperatureCustom' => 'Colour Temperature Custom',
   'ColorTemperatureRG' => 'Colour Temperature RG',
   'ColorTemperatureSet' => 'Colour Temperature Set',
   'ColorTemperatureSetting' => {
      Description => 'Colour Temperature Setting',
      PrintConv => {
        'Color Filter' => 'Colour Filter',
      },
    },
   'ColorTone' => 'Colour Tone',
   'ColorToneAdj' => 'Colour Tone Adj',
   'ColorToneAuto' => 'Colour Tone Auto',
   'ColorToneFaithful' => 'Colour Tone Faithful',
   'ColorToneLandscape' => 'Colour Tone Landscape',
   'ColorToneMonochrome' => 'Colour Tone Monochrome',
   'ColorToneNeutral' => 'Colour Tone Neutral',
   'ColorTonePortrait' => 'Colour Tone Portrait',
   'ColorToneStandard' => 'Colour Tone Standard',
   'ColorToneUserDef1' => 'Colour Tone User Def 1',
   'ColorToneUserDef2' => 'Colour Tone User Def 2',
   'ColorToneUserDef3' => 'Colour Tone User Def 3',
   'ColorTransferFuncs' => 'Colour Transfer Funcs',
   'ColorTransform' => 'Colour Transform',
   'ColorTwistMatrix' => 'Colour Twist Matrix',
   'ColorType' => {
      Description => 'Colour Type',
      PrintConv => {
        'Color' => 'Colour',
        'Color Alpha' => 'Colour Alpha',
      },
    },
   'Colorant1Coordinates' => 'Colourant 1 Coordinates',
   'Colorant1Name' => 'Colourant 1 Name',
   'Colorant2Coordinates' => 'Colourant 2 Coordinates',
   'Colorant2Name' => 'Colourant 2 Name',
   'Colorant3Coordinates' => 'Colourant 3 Coordinates',
   'Colorant3Name' => 'Colourant 3 Name',
   'ColorantA' => 'Colourant A',
   'ColorantB' => 'Colourant B',
   'ColorantBlack' => 'Colourant Black',
   'ColorantBlue' => 'Colourant Blue',
   'ColorantCount' => 'Colourant Count',
   'ColorantCyan' => 'Colourant Cyan',
   'ColorantGray' => 'Colourant Gray',
   'ColorantGreen' => 'Colourant Green',
   'ColorantL' => 'Colourant L',
   'ColorantMagenta' => 'Colourant Magenta',
   'ColorantMode' => 'Colourant Mode',
   'ColorantOrder' => 'Colourant Order',
   'ColorantRed' => 'Colourant Red',
   'ColorantSwatchName' => 'Colourant Swatch Name',
   'ColorantTableOut' => 'Colourant Table Out',
   'ColorantTint' => 'Colourant Tint',
   'ColorantType' => 'Colourant Type',
   'ColorantYellow' => 'Colourant Yellow',
   'Colorants' => 'Colourants',
   'ColorimetricIntentImageState' => 'Colourimetric Intent Image State',
   'ColorimetricReference' => 'Colourimetric Reference',
   'Colorimetry' => 'Colourimetry',
   'ColorimetryCode' => 'Colourimetry Code',
   'Colors' => 'Colours',
   'Compression' => {
      PrintConv => {
        'JBIG Color' => 'JBIG Colour',
      },
    },
   'ContrastMode' => {
      PrintConv => {
        'Dynamic (Color Film)' => 'Dynamic (Colour Film)',
        'Dynamic Art (My Color)' => 'Dynamic Art (My Colour)',
        'Elegant (My Color)' => 'Elegant (My Colour)',
        'Nature (Color Film)' => 'Nature (Colour Film)',
        'Nostalgic (Color Film)' => 'Nostalgic (Colour Film)',
        'Retro (My Color)' => 'Retro (My Colour)',
        'Smooth (Color Film) or Pure (My Color)' => 'Smooth (Colour Film) or Pure (My Colour)',
        'Vibrant (Color Film) or Expressive (My Color)' => 'Vibrant (Colour Film) or Expressive (My Colour)',
      },
    },
   'CustomColorTone' => 'Custom Colour Tone',
   'D-LightingHQColorBoost' => 'D-Lighting HQ Colour Boost',
   'D-LightingHSColorBoost' => 'D-Lighting HS Colour Boost',
   'DefHilite' => 'Use Default Highlight Colour',
   'DefaultImageColor' => 'Default Image Colour',
   'DeltaType' => {
      PrintConv => {
        'Color Addition' => 'Colour Addition',
        'Color Replacement' => 'Colour Replacement',
      },
    },
   'DigitalFilter01' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Replace Color' => 'Replace Colour',
        'Unicolor Bold' => 'Unicolour Bold',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter02' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter03' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter04' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter05' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter06' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter07' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter08' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter09' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter10' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter11' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter12' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter13' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter14' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter15' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter16' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter17' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter18' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter19' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DigitalFilter20' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Extract Color' => 'Extract Colour',
        'Invert Color' => 'Invert Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'DisplayUnits' => {
      PrintConv => {
        'meters' => 'Metres',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Color Accent' => 'Colour Accent',
        'Color Swap' => 'Colour Swap',
        'My Colors' => 'My Colours',
      },
    },
   'EmbeddedImageColorSpace' => 'Embedded Image Colour Space',
   'ExposureProgram' => {
      PrintConv => {
        'Partial Color Blue' => 'Partial Colour Blue',
        'Partial Color Green' => 'Partial Colour Green',
        'Partial Color Red' => 'Partial Colour Red',
        'Partial Color Yellow' => 'Partial Colour Yellow',
        'Pop Color' => 'Pop Colour',
      },
    },
   'FaithfulRawColorTone' => 'Faithful Raw Colour Tone',
   'FaxProfile' => {
      PrintConv => {
        'Lossless color and grayscale, L' => 'Lossless colour and grayscale, L',
        'Lossy color and grayscale, C' => 'Lossy colour and grayscale, C',
      },
    },
   'FgColor' => 'Foreground Colour',
   'FilmColorProcess' => 'Film Colour Process',
   'FilmMode' => {
      PrintConv => {
        'Dynamic (color)' => 'Dynamic (colour)',
        'Nature (color)' => 'Nature (colour)',
        'Smooth (color)' => 'Smooth (colour)',
        'Standard (color)' => 'Standard (colour)',
      },
    },
   'FlagColor' => 'Flag Colour',
   'FlashColorFilter' => 'Flash Colour Filter',
   'FocalPlaneColorimetryEstimates' => 'Focal Plane Colourimetry Estimates',
   'GammaColorTone' => 'Gamma Colour Tone',
   'GenOpColor' => 'Gen Op Colour',
   'GenreID' => {
      PrintConv => {
        'Books|Kids|Basic Concepts|Colors' => 'Books|Kids|Basic Concepts|Colours',
      },
    },
   'GreenPaletteColorTableData' => 'Green Palette Colour Table Data',
   'GreenPaletteColorTableDescriptor' => 'Green Palette Colour Table Descriptor',
   'HasColorMap' => 'Has Colour Map',
   'HighlightColorDistortReduct' => 'Highlight Colour Distort Reduct',
   'HiliteColor' => 'Highlight Colour',
   'ImageAlterationConstraints' => {
      PrintConv => {
        'No Colorization' => 'No Colourization',
        'No De-Colorization' => 'No De-Colourization',
      },
    },
   'ImageColor' => 'Image Colour',
   'ImageColorIndicator' => {
      Description => 'Image Colour Indicator',
      PrintConv => {
        'Specified Image Color' => 'Specified Image Colour',
        'Unspecified Image Color' => 'Unspecified Image Colour',
      },
    },
   'ImageColorValue' => 'Image Colour Value',
   'ImageMedium' => {
      PrintConv => {
        'Color hard copy' => 'Colour hard copy',
        'Color negative' => 'Colour negative',
        'Color reversal' => 'Colour reversal',
      },
    },
   'IndexedColorTableCount' => 'Indexed Colour Table Count',
   'InterchangeColorSpace' => 'Interchange Colour Space',
   'Isotherm1Color' => 'Isotherm 1 Colour',
   'Isotherm2Color' => 'Isotherm 2 Colour',
   'LandscapeRawColorTone' => 'Landscape Raw Colour Tone',
   'LargeBluePaletteColorTableData' => 'Large Blue Palette Colour Table Data',
   'LargeBluePaletteColorTableDescr' => 'Large Blue Palette Colour Table Descr',
   'LargeGreenPaletteColorTableData' => 'Large Green Palette Colour Table Data',
   'LargeGreenPaletteColorTableDescr' => 'Large Green Palette Colour Table Descr',
   'LargePaletteColorLookupTableUID' => 'Large Palette Colour Lookup Table UID',
   'LargeRedPaletteColorTableData' => 'Large Red Palette Colour Table Data',
   'LargeRedPaletteColorTableDescr' => 'Large Red Palette Colour Table Descr',
   'LayerBlendModes' => {
      PrintConv => {
        'Color' => 'Colour',
        'Color Burn' => 'Colour Burn',
        'Color Dodge' => 'Colour Dodge',
        'Darker Color' => 'Darker Colour',
        'Lighter Color' => 'Lighter Colour',
      },
    },
   'MDColorTable' => 'MD Colour Table',
   'MDItemColorSpace' => 'MD Item Colour Space',
   'MagicFilter' => {
      PrintConv => {
        'Pale & Light Color' => 'Pale & Light Colour',
        'Pale & Light Color II' => 'Pale & Light Colour II',
        'Partial Color' => 'Partial Colour',
        'Partial Color II' => 'Partial Colour II',
        'Partial Color III' => 'Partial Colour III',
        'Watercolor' => 'Watercolour',
        'Watercolor I' => 'Watercolour I',
        'Watercolor II' => 'Watercolour II',
      },
    },
   'MandatoryBackground' => {
      PrintConv => {
        'Color Advisory, Image Mandatory' => 'Colour Advisory, Image Mandatory',
        'Color Mandatory, Image Advisory' => 'Colour Mandatory, Image Advisory',
        'Color and Image Advisory' => 'Colour and Image Advisory',
        'Color and Image Mandatory' => 'Colour and Image Mandatory',
      },
    },
   'MattColor' => 'Matt Colour',
   'MediaColor' => 'Media Colour',
   'ModifiedColorTemp' => 'Modified Colour Temp',
   'MonochromeColor' => 'Monochrome Colour',
   'MyColorMode' => {
      Description => 'My Colour Mode',
      PrintConv => {
        'Color Accent' => 'Colour Accent',
        'Color Swap' => 'Colour Swap',
      },
    },
   'NamedColor' => 'Named Colour',
   'NamedColor2' => 'Named Colour 2',
   'NeutralRawColorTone' => 'Neutral Raw Colour Tone',
   'NewColorType' => 'New Colour Type',
   'NumColors' => 'Num Colours',
   'NumImportantColors' => 'Num Important Colours',
   'OpColor' => 'Op Colour',
   'OverflowColor' => 'Overflow Colour',
   'PF25ColorMatrix' => 'PF25 Colour Matrix',
   'PaletteColorTableUID' => 'Palette Colour Table UID',
   'PaletteColors' => 'Palette Colours',
   'PhotoEffect' => {
      PrintConv => {
        'My Color Data' => 'My Colour Data',
      },
    },
   'PhotometricInterpretation' => {
      PrintConv => {
        'Color Filter Array' => 'Colour Filter Array',
      },
    },
   'Photoshop2ColorTable' => 'Photoshop 2 Colour Table',
   'PictureEffect' => {
      PrintConv => {
        'Partial Color (blue)' => 'Partial Colour (blue)',
        'Partial Color (green)' => 'Partial Colour (green)',
        'Partial Color (red)' => 'Partial Colour (red)',
        'Partial Color (yellow)' => 'Partial Colour (yellow)',
        'Pop Color' => 'Pop Colour',
        'Water Color' => 'Water Colour',
        'Water Color 2' => 'Water Colour 2',
      },
    },
   'PictureEffect2' => {
      PrintConv => {
        'Partial Color' => 'Partial Colour',
        'Pop Color' => 'Pop Colour',
        'Water Color' => 'Water Colour',
      },
    },
   'PictureIndexedColors' => 'Picture Indexed Colours',
   'PictureMode' => {
      PrintConv => {
        'Color Creator' => 'Colour Creator',
        'Color Profile 1' => 'Colour Profile 1',
        'Color Profile 2' => 'Colour Profile 2',
        'Color Profile 3' => 'Colour Profile 3',
      },
    },
   'PictureType' => {
      PrintConv => {
        'Bright(ly) Colored Fish' => 'Bright(ly) Coloured Fish',
      },
    },
   'PictureWizardColor' => 'Picture Wizard Colour',
   'PortraitRawColorTone' => 'Portrait Raw Colour Tone',
   'PresetWhiteBalance' => {
      PrintConv => {
        'Color Temperature' => 'Colour Temperature',
      },
    },
   'PreviewColorSpace' => 'Preview Colour Space',
   'ProfileClass' => {
      PrintConv => {
        'ColorSpace Conversion Profile' => 'ColourSpace Conversion Profile',
        'NamedColor Profile' => 'Named Colour Profile',
      },
    },
   'PseudoColorType' => 'Pseudo Colour Type',
   'QuantizationMethod' => {
      PrintConv => {
        'Color Space Specific' => 'Colour Space Specific',
      },
    },
   'RGBBkCol' => 'Background Colour',
   'RGBFgCol' => 'Foreground Colour',
   'ROIDisplayColor' => 'ROI Display Colour',
   'RawColorAdj' => 'Raw Colour Adj',
   'RawDevArtFilter' => {
      PrintConv => {
        'Pale & Light Color' => 'Pale & Light Colour',
        'Pale & Light Color II' => 'Pale & Light Colour II',
        'Partial Color' => 'Partial Colour',
        'Partial Color II' => 'Partial Colour II',
        'Partial Color III' => 'Partial Colour III',
        'Watercolor' => 'Watercolour',
        'Watercolor I' => 'Watercolour I',
        'Watercolor II' => 'Watercolour II',
      },
    },
   'RawDevColorSpace' => 'Raw Dev Colour Space',
   'RawDevMemoryColorEmphasis' => 'Raw Dev Memory Colour Emphasis',
   'RawDevSettings' => {
      PrintConv => {
        'Color Space' => 'Colour Space',
        'WB Color Temp' => 'WB Colour Temp',
      },
    },
   'RawDevWhiteBalance' => {
      PrintConv => {
        'Color Temperature' => 'Colour Temperature',
      },
    },
   'RedPaletteColorTableData' => 'Red Palette Colour Table Data',
   'RedPaletteColorTableDescriptor' => 'Red Palette Colour Table Descriptor',
   'ReflectionHardcopyOrigColorimetry' => 'Reflection Hardcopy Orig Colourimetry',
   'ReflectionPrintOutputColorimetry' => 'Reflection Print Output Colourimetry',
   'RenderingIntent' => {
      PrintConv => {
        'Absolute Colorimetric (LCS_GM_ABS_COLORIMETRIC)' => 'Absolute Colourimetric (LCS_GM_ABS_COLORIMETRIC)',
        'ICC-Absolute Colorimetric' => 'ICC-Absolute Colourimetric',
        'Media-Relative Colorimetric' => 'Media-Relative Colourimetric',
      },
    },
   'RetouchHistory' => {
      PrintConv => {
        'Color Custom' => 'Colour Custom',
        'Color Outline' => 'Colour Outline',
        'Color Sketch' => 'Colour Sketch',
        'Selective Color' => 'Selective Colour',
      },
    },
   'SBAInputImageColorspace' => 'SBA Input Image Colourspace',
   'SRGBRendering' => {
      PrintConv => {
        'Absolute Colorimetric' => 'Absolute Colourimetric',
        'Relative Colorimetric' => 'Relative Colourimetric',
      },
    },
   'SceneBalanceAlgorithmCommand' => {
      PrintConv => {
        'Neutral SBA Off, Color SBA Off' => 'Neutral SBA Off, Colour SBA Off',
        'Neutral SBA Off, Color SBA On' => 'Neutral SBA Off, Colour SBA On',
        'Neutral SBA On, Color SBA Off' => 'Neutral SBA On, Colour SBA Off',
        'Neutral SBA On, Color SBA On' => 'Neutral SBA On, Colour SBA On',
      },
    },
   'SceneColorimetryEstimates' => 'Scene Colourimetry Estimates',
   'SceneMode' => {
      PrintConv => {
        'Color Effects' => 'Colour Effects',
        'My Color' => 'My Colour',
      },
    },
   'ScreenMinimumColorBitDepth' => 'Screen Minimum Colour Bit Depth',
   'SegmentedBlueColorTableData' => 'Segmented Blue Colour Table Data',
   'SegmentedGreenColorTableData' => 'Segmented Green Colour Table Data',
   'SegmentedRedColorTableData' => 'Segmented Red Colour Table Data',
   'SensingMethod' => {
      PrintConv => {
        'Color sequential area' => 'Colour sequential area',
        'Color sequential linear' => 'Colour sequential linear',
        'One-chip color area' => 'One-chip colour area',
        'Three-chip color area' => 'Three-chip colour area',
        'Two-chip color area' => 'Two-chip colour area',
      },
    },
   'ShootingMode' => {
      PrintConv => {
        'Color Effects' => 'Colour Effects',
        'My Color' => 'My Colour',
      },
    },
   'ShutterPresentationColorCIELabVal' => 'Shutter Presentation Colour CIE Lab Val',
   'SmartAlbumColor' => 'Smart Album Colour',
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'Colored' => 'Coloured',
      },
    },
   'StandardRawColorTone' => 'Standard Raw Colour Tone',
   'StdOutputColorMode' => 'Std Output Colour Mode',
   'StreamColor' => 'Stream Colour',
   'SubfileType' => {
      PrintConv => {
        'Color IW44' => 'Colour IW44',
      },
    },
   'SubimageColor' => 'Subimage Colour',
   'SwatchColorantA' => 'Swatch Colourant A',
   'SwatchColorantB' => 'Swatch Colourant B',
   'SwatchColorantBlack' => 'Swatch Colourant Black',
   'SwatchColorantBlue' => 'Swatch Colourant Blue',
   'SwatchColorantCyan' => 'Swatch Colourant Cyan',
   'SwatchColorantGray' => 'Swatch Colourant Gray',
   'SwatchColorantGreen' => 'Swatch Colourant Green',
   'SwatchColorantL' => 'Swatch Colourant L',
   'SwatchColorantMagenta' => 'Swatch Colourant Magenta',
   'SwatchColorantMode' => 'Swatch Colourant Mode',
   'SwatchColorantRed' => 'Swatch Colourant Red',
   'SwatchColorantSwatchName' => 'Swatch Colourant Swatch Name',
   'SwatchColorantTint' => 'Swatch Colourant Tint',
   'SwatchColorantType' => 'Swatch Colourant Type',
   'SwatchColorantYellow' => 'Swatch Colourant Yellow',
   'SwatchGroupsColorants' => 'Swatch Groups Colourants',
   'TestTarget' => {
      PrintConv => {
        'Color Chart' => 'Colour Chart',
      },
    },
   'TextColor' => 'Text Colour',
   'ToneCurveColorSpace' => 'Tone Curve Colour Space',
   'ToningEffect' => {
      PrintConv => {
        'Color' => 'Colour',
      },
    },
   'TypeOfOriginal' => {
      PrintConv => {
        'Color Document' => 'Colour Document',
        'Color Print' => 'Colour Print',
      },
    },
   'UCRBG' => 'Under Colour Removal & Black Gen.',
   'USPTOOriginalContentType' => {
      PrintConv => {
        'Color' => 'Colour',
      },
    },
   'UltrasoundColorDataPresent' => 'Ultrasound Colour Data Present',
   'UnderflowColor' => 'Underflow Colour',
   'Unsharp1Color' => 'Unsharp 1 Colour',
   'Unsharp2Color' => 'Unsharp 2 Colour',
   'Unsharp3Color' => 'Unsharp 3 Colour',
   'Unsharp4Color' => 'Unsharp 4 Colour',
   'VideoAlphaPremultipleColor' => 'Video Alpha Premultiple Colour',
   'VideoAlphaPremultipleColorA' => 'Video Alpha Premultiple Colour A',
   'VideoAlphaPremultipleColorB' => 'Video Alpha Premultiple Colour B',
   'VideoAlphaPremultipleColorBlack' => 'Video Alpha Premultiple Colour Black',
   'VideoAlphaPremultipleColorBlue' => 'Video Alpha Premultiple Colour Blue',
   'VideoAlphaPremultipleColorCyan' => 'Video Alpha Premultiple Colour Cyan',
   'VideoAlphaPremultipleColorGray' => 'Video Alpha Premultiple Colour Gray',
   'VideoAlphaPremultipleColorGreen' => 'Video Alpha Premultiple Colour Green',
   'VideoAlphaPremultipleColorL' => 'Video Alpha Premultiple Colour L',
   'VideoAlphaPremultipleColorMagenta' => 'Video Alpha Premultiple Colour Magenta',
   'VideoAlphaPremultipleColorMode' => 'Video Alpha Premultiple Colour Mode',
   'VideoAlphaPremultipleColorRed' => 'Video Alpha Premultiple Colour Red',
   'VideoAlphaPremultipleColorSwatchName' => 'Video Alpha Premultiple Colour Swatch Name',
   'VideoAlphaPremultipleColorTint' => 'Video Alpha Premultiple Colour Tint',
   'VideoAlphaPremultipleColorType' => 'Video Alpha Premultiple Colour Type',
   'VideoAlphaPremultipleColorYellow' => 'Video Alpha Premultiple Colour Yellow',
   'VideoColorKind' => 'Video Colour Kind',
   'VideoColorSpace' => 'Video Colour Space',
   'VisualColor' => {
      Description => 'Visual Colour',
      PrintConv => {
        'Color' => 'Colour',
      },
    },
   'WBAdjColorTemp' => 'WB Adj Colour Temp',
   'WBAdjLighting' => {
      PrintConv => {
        'High Color Rendering Fluorescent (3700K)' => 'High Colour Rendering Fluorescent (1)',
        'High Color Rendering Fluorescent (5000K)' => 'High Colour Rendering Fluorescent (3)',
        'High Color Rendering Fluorescent (cool white)' => 'High Colour Rendering Fluorescent (2)',
        'High Color Rendering Fluorescent (daylight)' => 'High Colour Rendering Fluorescent (4)',
        'High Color Rendering Fluorescent (warm white)' => 'High Colour Rendering Fluorescent (0)',
        'Sodium Vapor Lamps' => 'Sodium Vapour Lamps',
        'Standard Fluorescent (high temperature mercury vapor)' => 'Standard Fluorescent (high temperature mercury vapour)',
      },
    },
   'WatercolorFilter' => 'Watercolour Filter',
   'WhiteBalance' => {
      PrintConv => {
        'Color Filter' => 'Colour Filter',
        'Color Temperature' => 'Colour Temperature',
        'Color Temperature Enhancement' => 'Colour Temperature Enhancement',
        'Color Temperature/Color Filter' => 'Colour Temperature/Colour Filter',
      },
    },
   'WhiteBalance2' => {
      PrintConv => {
        'Auto (Keep Warm Color Off)' => 'Auto (Keep Warm Colour Off)',
      },
    },
   'WhiteBalanceSet' => {
      PrintConv => {
        'Set Color Temperature 1' => 'Set Colour Temperature 1',
        'Set Color Temperature 2' => 'Set Colour Temperature 2',
        'Set Color Temperature 3' => 'Set Colour Temperature 3',
      },
    },
   'WhiteBalanceSetting' => {
      PrintConv => {
        'Color Temperature/Color Filter' => 'Colour Temperature/Colour Filter',
      },
    },
   'WorkColorSpace' => {
      Description => 'Work Colour Space',
      PrintConv => {
        'ColorMatch RGB' => 'ColourMatch RGB',
      },
    },
   'XMethod' => {
      PrintConv => {
        'Color Closest Pixel and Alpha Linear Interpolation' => 'Colour Closest Pixel and Alpha Linear Interpolation',
        'Color Linear Interpolation and Alpha Closest Pixel' => 'Colour Linear Interpolation and Alpha Closest Pixel',
      },
    },
   'YMethod' => {
      PrintConv => {
        'Color Closest Pixel and Alpha Linear Interpolation' => 'Colour Closest Pixel and Alpha Linear Interpolation',
        'Color Linear Interpolation and Alpha Closest Pixel' => 'Colour Linear Interpolation and Alpha Closest Pixel',
      },
    },
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::en_ca.pm - ExifTool Canadian English language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
