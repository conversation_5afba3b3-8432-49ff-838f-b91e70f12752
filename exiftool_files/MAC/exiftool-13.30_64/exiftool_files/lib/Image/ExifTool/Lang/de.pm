#------------------------------------------------------------------------------
# File:         de.pm
#
# Description:  ExifTool German language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::de;

use strict;
use vars qw($VERSION);

$VERSION = '1.37';

%Image::ExifTool::Lang::de::Translate = (
   'AEAperture' => 'AE-Blende',
   'AEBAutoCancel' => {
      Description => 'Automatisches Bracketingende',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AEBBracketValue' => 'AEB-Korrekturwert',
   'AEBSequence' => 'Bracketing-Sequenz',
   'AEBSequenceAutoCancel' => {
      Description => 'WB-Sequenz/autom. Abschaltung',
      PrintConv => {
        '-,0,+/Disabled' => '-,0,+/Aus',
        '-,0,+/Enabled' => '-,0,+/Ein',
        '0,-,+/Disabled' => '0,-,+/Aus',
        '0,-,+/Enabled' => '0,-,+/Ein',
      },
    },
   'AEBShotCount' => 'Anzahl Belichtungsreihenaufnahmen',
   'AEBXv' => 'AEB-Belichtungskorrektur',
   'AEExposureTime' => 'AE-Belichtungszeit',
   'AEExtra' => 'AE-Extra?',
   'AEFlags' => {
      PrintConv => {
        'AE lock' => 'AE Speicherung',
        'Aperture wide open' => 'Offene Blende',
        'Flash recommended?' => 'Blitz erforderlich',
      },
    },
   'AEInfo' => 'Automatikbelichtungs-Informationen',
   'AELock' => {
      Description => 'Belichtungsspeicher',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AELockButton' => {
      Description => 'AE-L/AF-L-Taste',
      PrintConv => {
        'AE Lock (hold)' => 'Nur Belichtung (halten)',
        'AE Lock Only' => 'Nur Belichtung',
        'AE-L/AF Area' => 'Belichtung & Messfeld',
        'AE-L/AF-L/AF Area' => 'Bel. & Fokus & Messfeld',
        'AE/AF Lock' => 'Belichtung & Fokus',
        'AF Lock Only' => 'Nur Fokus',
        'AF-L/AF Area' => 'Fokus & Messfeld',
        'AF-ON' => 'AF-Aktivierung',
        'AF-ON/AF Area' => 'AF-Aktiv. & Messfeld',
        'FV Lock' => 'FV-Messwertspeicher',
        'Focus Area Selection' => 'AF-Messfeldauswahl',
      },
    },
   'AEMaxAperture' => 'Größte AE-Blende',
   'AEMaxAperture2' => 'Größte AE-Blende (2)',
   'AEMeteringMode' => {
      Description => 'AE Belichtungs-Messmethode',
      PrintConv => {
        'Center-weighted average' => 'Mittenbetont',
        'Multi-segment' => 'Multi-Segment',
      },
    },
   'AEMeteringSegments' => 'AE-Messfelder',
   'AEMicroadjustment' => {
      Description => 'AE Feinabstimmung',
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'AEMinAperture' => 'Kleinste AE-Blende',
   'AEMinExposureTime' => 'Kürzeste AE Belichtungszeit',
   'AEProgramMode' => {
      Description => 'AE-Programm-Modus',
      PrintConv => {
        'Av, B or X' => 'Av, B oder X',
        'Candlelight' => 'Kerzenlicht',
        'DOF Program' => 'Schärfentiefe-Priorität',
        'DOF Program (P-Shift)' => 'Schärfentiefe-Priorität (P Shift)',
        'Hi-speed Program' => 'HS-Priorität',
        'Hi-speed Program (P-Shift)' => 'HS-Priorität (P Shift)',
        'Kids' => 'Kinder',
        'Landscape' => 'Landschaft',
        'M, P or TAv' => 'M, P oder TAv',
        'MTF Program' => 'MTF-Priorität',
        'MTF Program (P-Shift)' => 'MTF-Priorität (P Shift)',
        'Macro' => 'Makro',
        'Night Scene' => 'Nachtszene',
        'Night Scene Portrait' => 'Nacht-Porträt',
        'No Flash' => 'Kein Blitz',
        'Pet' => 'Haustiere',
        'Portrait' => 'Porträt',
        'Sunset' => 'Sonnenuntergang',
        'Surf & Snow' => 'Surf & Schnee',
        'Sv or Green Mode' => 'Sv oder "Grünes" AE-Programm',
      },
    },
   'AESetting' => {
      Description => 'AE-Einstellung',
      PrintConv => {
        'AE Lock' => 'AE-Speicherung',
        'AE Lock + Exposure Comp.' => 'AE-Speicherung + Belichtungskorrektur',
        'Exposure Compensation' => 'Belichtungskorrektur',
        'No AE' => 'Kein AE',
      },
    },
   'AEXv' => 'AE-Belichtungskorrektur',
   'AE_ISO' => 'AE-ISO-Empfindlichkeit',
   'AF-CPrioritySelection' => {
      Description => 'Priorität bei AF-C',
      PrintConv => {
        'Focus' => 'Schärfepriorität',
        'Release' => 'Auslösepriorität',
        'Release + Focus' => 'Auslösepriorität & AF',
      },
    },
   'AF-OnForMB-D10' => {
      Description => 'AF-ON-Taste (MB-D10)',
      PrintConv => {
        'AE Lock (hold)' => 'Belichtung speichern ein/aus',
        'AE Lock (reset on release)' => 'Bel. speichern ein/aus (Reset)',
        'AE Lock Only' => 'Belichtung speichern',
        'AE/AF Lock' => 'Belichtung & Fokus speichern',
        'AF Lock Only' => 'Fokus speichern',
        'AF-On' => 'Autofokus aktiviert',
        'Same as FUNC Button' => 'Wie Funktionstaste',
      },
    },
   'AF-SPrioritySelection' => {
      Description => 'Priorität bei AF-S (Einzel-AF)',
      PrintConv => {
        'Focus' => 'Schärfepriorität',
        'Release' => 'Auslösepriorität',
      },
    },
   'AFActivation' => {
      Description => 'AF-Aktivierung',
      PrintConv => {
        'AF-On Only' => 'Nur AF-ON-Taste',
        'Shutter/AF-On' => 'AF-On-Taste & Auslöser',
      },
    },
   'AFAdjustment' => 'AF-Korrektur',
   'AFAndMeteringButtons' => {
      Description => 'AF And Mess-Tasten',
      PrintConv => {
        'AE lock' => 'AE Speicherung',
        'AF stop' => 'AE Stopp',
        'Metering + AF start' => 'Messung + AF Start',
        'Metering start' => 'Messung Start',
        'No function' => 'Keine Funktion',
      },
    },
   'AFAperture' => 'AF-Blende',
   'AFArea' => 'AF Bereich',
   'AFAreaHeight' => 'AF-Bereichshöhe',
   'AFAreaHeights' => 'AF-Bereichshöhe',
   'AFAreaIllumination' => {
      Description => 'Messfeld-LED',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AFAreaMode' => {
      Description => 'Messfeldsteuerung',
      PrintConv => {
        '1-area' => '1 Bereich',
        '1-area (high speed)' => '1 Bereich (kurze Verschlußzeit)',
        '23-area' => '23 Bereiche',
        '3-area (center)?' => '3 Bereiche (mitte)',
        '3-area (high speed)' => '3 Bereiche (kurze Verschlußzeit)',
        '3-area (left)?' => '3 Bereiche (links)',
        '3-area (right)?' => '3 Bereiche (rechts)',
        '5-area' => '5 Bereiche',
        '9-area' => '9 Bereiche',
        'AF Point Expansion' => 'AF Punkt-Erweiterung',
        'Auto (41 points)' => 'Automatisch (41 Punkte)',
        'Auto-area' => 'Autom. Messfeldgr.',
        'Center' => 'Mitte',
        'Contrast-detect' => 'Kontrasterkennung',
        'Contrast-detect (face priority)' => 'Kontrasterkennung (Gesichtserkennung)',
        'Contrast-detect (normal area)' => 'Kontrasterkennung (Standardbereich)',
        'Contrast-detect (subject tracking)' => 'Kontrasterkennung (Objektverfolgung)',
        'Contrast-detect (wide area)' => 'Kontrasterkennung (großer Bereich)',
        'Default' => 'Standard',
        'Dynamic Area' => 'Dynamisch',
        'Dynamic Area (21 points)' => 'Dynamischer Bereich (21 Punkte)',
        'Dynamic Area (3D-tracking)' => 'Dynamischer Bereich (3D-Nachführung)',
        'Dynamic Area (51 points)' => 'Dynamischer Bereich (51 Punkte)',
        'Dynamic Area (51 points, 3D-tracking)' => 'Dynamischer Bereich (51 Punkte, 3D-Nachführung)',
        'Dynamic Area (9 points)' => 'Dynamischer Bereich (9 Punkte)',
        'Dynamic Area (closest subject)' => 'Dynamic Messfeldgruppensteuerung (priorität der kürzesten Aufnahmedistanz)',
        'Dynamic Area (wide)' => 'Dynamische Messfeldsteuerung (groß)',
        'Dynamic Area (wide, 3D-tracking)' => 'Dynamischer Bereich (groß, 3D-Nachführung)',
        'Face + Tracking' => 'Gesichtserkennung + Nachführung',
        'Face Detect' => 'Gesichtserkennung',
        'Face Detect AF' => 'Gesichtserkennung AF',
        'Face Priority (41 points)' => 'Gesichtserkennung (41 Punkte)',
        'Face Tracking' => 'Gesichtserkennung',
        'Flexible' => 'Angepasst',
        'Flexible Spot' => 'Gesetzter Punkt',
        'Flexizone Multi' => 'Mehrpunkt-Bereich',
        'Flexizone Single' => 'Einpunkt-Bereich',
        'Group Dynamic' => 'Dynamische Messfeldgruppensteuerung',
        'Local' => 'Lokal',
        'Manual' => 'Manuell',
        'Multi' => 'Mehrpunkt',
        'Multi-point AF or AI AF' => 'Mehrpunkt AF oder AI AF',
        'Normal?' => 'Normal',
        'Off (Manual Focus)' => 'Aus (Manueller Fokus)',
        'Selective (for Miniature effect)' => 'Selektiv (für Vorschau-Effekt)',
        'Single (135 points)' => 'Einpunkt (135 Punkte)',
        'Single Area' => 'Einzelfeld',
        'Single Area (wide)' => 'Einzelfeldmessung (groß)',
        'Single-point AF' => 'Einpunkt AF',
        'Spot Focusing' => 'Spotfokussierung',
        'Spot Focusing 2' => 'Spotfokussierung 2',
        'Spot Mode Off' => 'Spot-Modus Aus',
        'Spot Mode On' => 'Spot-Modus Ein',
        'Subject Tracking (41 points)' => 'Objektverfolgung (41 Punkte)',
        'Touch' => 'Berührungspunkt',
        'Tracking' => 'Nachführung',
        'Wide' => 'Weit',
        'Zone' => 'Bereich',
        'Zone AF' => 'Zonen AF',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AFAreaModeSetting' => {
      Description => 'Messfeldsteuerung',
      PrintConv => {
        '3D-tracking (11 points)' => '3D Nachführung (11 Punkte)',
        'Auto-area' => 'Automatischer Bereich',
        'Center' => 'Mitte',
        'Closest Subject' => 'Nächstes Objekt',
        'Dynamic Area' => 'Dynamisch',
        'Flexible Spot' => 'Gesetzter Punkt',
        'Local' => 'Lokal',
        'Multi' => 'Mehrpunkt',
        'Single Area' => 'Einzelfeld',
        'Wide' => 'Weit',
        'Zone' => 'Bereich',
      },
    },
   'AFAreaWidth' => 'AF-Bereichsbreite',
   'AFAreaWidths' => 'AF-Bereichsbreite',
   'AFAreaXPosition' => 'AF-Bereich X Position',
   'AFAreaXPosition1' => 'AF-Bereich X Position 1',
   'AFAreaXPositions' => 'AF Bereich X Positionen',
   'AFAreaYPosition' => 'AF-Bereich Y Position',
   'AFAreaYPosition1' => 'AF-Bereich Y Position 1',
   'AFAreaYPositions' => 'AF Bereich Y Positionen',
   'AFAreas' => 'AF-Bereiche',
   'AFAssist' => {
      Description => 'AF-Hilfslicht',
      PrintConv => {
        'Does not emit/Fires' => 'Kein Messlicht/Zündung',
        'Emits/Does not fire' => 'Messlicht/keine Zündung',
        'Emits/Fires' => 'Messlicht/Zündung',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Only ext. flash emits/Fires' => 'Nur ext. Messl./Zündung',
      },
    },
   'AFAssistBeam' => {
      Description => 'AF-Hilfslicht Aussendung',
      PrintConv => {
        'Does not emit' => 'Deaktiv',
        'Emits' => 'Aktiv',
        'Only ext. flash emits' => 'Nur bei ext. Blitz aktiv',
      },
    },
   'AFAssistLamp' => {
      Description => 'AF Hilfslicht',
      PrintConv => {
        'Disabled and Not Required' => 'Nicht eingestellt, war nicht nötig',
        'Disabled but Required' => 'Nicht eingestellt, war aber nötig',
        'Enabled but Not Used' => 'Eingestellt, aber nicht verwendet',
        'Fired' => 'Blitz wurde ausgelöst',
      },
    },
   'AFDefocus' => 'AF-Defocus',
   'AFDuringLiveView' => {
      Description => 'AF bei Live View-Aufnahmen',
      PrintConv => {
        'Disable' => 'Inaktiv',
        'Enable' => 'Aktiv',
        'Live mode' => 'LiveModus',
        'Quick mode' => 'QuickModus',
      },
    },
   'AFFineTune' => {
      Description => 'AF-Feinabstimmung',
      PrintConv => {
        'Off' => 'Aus',
        'On (1)' => 'Ein (1)',
        'On (2)' => 'Ein (2)',
      },
    },
   'AFFineTuneAdj' => 'AF-Feinabstimmung',
   'AFIlluminator' => {
      PrintConv => {
        'Off' => 'Aus',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AFImageHeight' => 'AF-Bildhöhe',
   'AFImageWidth' => 'AF-Bildbreite',
   'AFInFocus' => 'AF in Fokus',
   'AFInfo' => 'AF-Modus',
   'AFInfo2' => 'AF-Informationen',
   'AFInfo2Version' => 'AF-Info-Version',
   'AFIntegrationTime' => 'AF-Messzeit',
   'AFMicroAdj' => 'AF Feinabstimmung',
   'AFMicroAdjMode' => {
      Description => 'AF Feinabstimmung Modus',
      PrintConv => {
        'Adjust all by the same amount' => 'Korrektur immer um gleichen Wert',
        'Adjust by lens' => 'Korrektur objektivabhängig',
        'Disable' => 'Deaktiviert',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AFMicroAdjRegisteredLenses' => 'AF Feinabstimmung bekannte Objektive',
   'AFMicroAdjValue' => 'AF Feinabstimmung Wert',
   'AFMicroadjustment' => {
      Description => 'AF Feinabstimmung',
      PrintConv => {
        'Adjust all by same amount' => 'Alle auf gleichen Wert',
        'Adjust by lens' => 'Abstimmung pro Objektiv',
        'Disable' => 'Deaktivieren',
      },
    },
   'AFMode' => {
      Description => 'AF-Modus',
      PrintConv => {
        'Face Detection' => 'Gesichtserkennung',
        'Off' => 'Aus',
        'Tracking' => 'Nachführung',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AFOnAELockButtonSwitch' => {
      Description => 'AF-ON/AELocktaste- Schalter',
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'AFPoint' => {
      Description => 'Gewählter AF-Punkt',
      PrintConv => {
        '(none)' => '(keiner)',
        'Auto AF point selection' => 'Automatische Wahl des AF-Punktes',
        'Bottom' => 'Unten',
        'Bottom (horizontal)' => 'Unten (horizontal)',
        'Bottom (vertical)' => 'Unten (vertikal)',
        'Bottom Center' => 'Unten Mitte',
        'Bottom Left' => 'Unten Links',
        'Bottom Right' => 'Unten Rechts',
        'Bottom-center (horizontal)' => 'Unten Mitte (horizontal)',
        'Bottom-center (vertical)' => 'Unten Mitte (vertikal)',
        'Bottom-left' => 'Unten Links',
        'Bottom-left (horizontal)' => 'Unten Links (horizontal)',
        'Bottom-left (vertical)' => 'Unten Links (vertikal)',
        'Bottom-right' => 'Unten Rechts',
        'Bottom-right (horizontal)' => 'Unten Rechts (horizontal)',
        'Bottom-right (vertical)' => 'Unten Rechts (vertikal)',
        'Center' => 'Mitte',
        'Center (horizontal)' => 'Mitte (horizontal)',
        'Center (vertical)' => 'Mitte (vertikal)',
        'Center Left' => 'Mitte Links',
        'Center Right' => 'Mitte Rechts',
        'Face Detect' => 'Gesichtserkennung',
        'Far Left' => 'Weit links',
        'Far Left (horizontal)' => 'Weit links (horizontal)',
        'Far Left (vertical)' => 'Weit links (vertikal)',
        'Far Left/Right of Center' => 'Weit Links/Rechts der Mitte',
        'Far Left/Right of Center/Bottom' => 'Weit Links/Rechts der Mitte/Unten',
        'Far Right' => 'Weit rechts',
        'Far Right (horizontal)' => 'Weit rechts (horizontal)',
        'Far Right (vertical)' => 'Weit rechts (vertikal)',
        'Left' => 'Links',
        'Left (horizontal)' => 'Links (horizontal)',
        'Left (or n/a)' => 'Links (oder Nicht gesetzt)',
        'Left (vertical)' => 'Links (vertikal)',
        'Lower Far Left' => 'Unten ganz links',
        'Lower Far Right' => 'Unten ganz rechts',
        'Lower-left' => 'Links unten',
        'Lower-left (horizontal)' => 'Links unten (horizontal)',
        'Lower-left (vertical)' => 'Links unten (vertikal)',
        'Lower-middle' => 'Untere Mitte',
        'Lower-right' => 'Rechts unten',
        'Lower-right (horizontal)' => 'Rechts unten (horizontal)',
        'Lower-right (vertical)' => 'Unten rechts (vertikal)',
        'Manual AF point selection' => 'Manuell gewählter AF-Punkt',
        'Mid-left' => 'Links mitte',
        'Mid-left (horizontal)' => 'Links mitte (horizontal)',
        'Mid-left (vertical)' => 'Links mitte (vertikal)',
        'Mid-right' => 'Rechts mitte',
        'Mid-right (horizontal)' => 'Rechts mitte (horizontal)',
        'Mid-right (vertical)' => 'Rechts mitte (vertikal)',
        'Near Left' => 'Nahe Links',
        'Near Left/Right of Center' => 'Nahe Links/Rechts der Mitte',
        'Near Right' => 'Nahe Rechts',
        'Near Upper/Left' => 'Nahe Links oben',
        'None' => 'Keiner',
        'None (MF)' => 'Keiner (MF)',
        'Right' => 'Rechts',
        'Right (horizontal)' => 'Rechts (horizontal)',
        'Right (vertical)' => 'Rechts (vertikal)',
        'Top' => 'Oben',
        'Top (horizontal)' => 'Oben (horizontal)',
        'Top (vertical)' => 'Oben (vertikal)',
        'Top Center' => 'Oben Mitte',
        'Top Left' => 'Oben Links',
        'Top Near-left' => 'Oben nahe-Links',
        'Top Near-right' => 'Nahe Rechts-oben',
        'Top Right' => 'Oben Rechts',
        'Top-center (horizontal)' => 'Oben Mitte (horizontal)',
        'Top-center (vertical)' => 'Oben Mitte (vertikal)',
        'Top-left' => 'Oben Links',
        'Top-left (horizontal)' => 'Oben Links (horizontal)',
        'Top-left (vertical)' => 'Oben Links (vertikal)',
        'Top-right' => 'Oben Rechts',
        'Top-right (horizontal)' => 'Oben Rechts (horizontal)',
        'Top-right (vertical)' => 'Oben Rechts (vertikal)',
        'Upper Far Left' => 'Oben ganz links',
        'Upper Far Right' => 'Oben ganz rechts',
        'Upper Left' => 'Links oben',
        'Upper Right' => 'Rechts oben',
        'Upper-left' => 'Links oben',
        'Upper-left (horizontal)' => 'Links oben (horizontal)',
        'Upper-left (vertical)' => 'Links oben (vertikal)',
        'Upper-middle' => 'Obere Mitte',
        'Upper-right' => 'Rechts oben',
        'Upper-right (horizontal)' => 'Rechts oben (horizontal)',
        'Upper-right (vertical)' => 'Oben rechts (vertikal)',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AFPointActivationArea' => {
      Description => 'AF-Messfeld-Aktivierungsbereich',
      PrintConv => {
        'Expanded' => 'Erweitert',
      },
    },
   'AFPointAreaExpansion' => {
      Description => 'AF-Messbereich Ausweitung',
      PrintConv => {
        'All 45 points area' => 'Alle 45 Punkte',
        'Disable' => 'Aus',
        'Enable' => 'Ein',
        'Left/right AF points' => 'Möglich (linkes/rechtes zusätzliches AF-Messfeld)',
        'Surrounding AF points' => 'Möglich (entsprechendes zusätzliches AF-Messfeld)',
      },
    },
   'AFPointAtShutterRelease' => {
      Description => 'AF-Punkt beim Auslösen',
      PrintConv => {
        '(out of focus)' => '(ausserhalb Fokus)',
        'Bottom (horizontal)' => 'Unten (horizontal)',
        'Bottom (vertical)' => 'Unten (vertikal)',
        'Center (horizontal)' => 'Mitte (horizontal)',
        'Center (vertical)' => 'Mitte (vertikal)',
        'Far Left' => 'Weit links',
        'Far Left (horizontal)' => 'Weit links (horizontal)',
        'Far Left (vertical)' => 'Weit links (vertikal)',
        'Far Right' => 'Weit rechts',
        'Far Right (horizontal)' => 'Weit rechts (horizontal)',
        'Far Right (vertical)' => 'Weit rechts (vertikal)',
        'Left' => 'Links',
        'Left (horizontal)' => 'Links (horizontal)',
        'Left (vertical)' => 'Links (vertikal)',
        'Lower Far Left' => 'Unten ganz links',
        'Lower Far Right' => 'Unten ganz rechts',
        'Lower-left' => 'Links unten',
        'Lower-left (horizontal)' => 'Links unten (horizontal)',
        'Lower-left (vertical)' => 'Links unten (vertikal)',
        'Lower-middle' => 'Unten mitte',
        'Lower-right' => 'Rechts unten',
        'Lower-right (horizontal)' => 'Rechts unten (horizontal)',
        'Lower-right (vertical)' => 'Rechts unten (vertikal)',
        'Near Left' => 'Nahe links',
        'Near Right' => 'Nahe rechts',
        'Right' => 'Rechts',
        'Right (horizontal)' => 'Rechts (horizontal)',
        'Right (vertical)' => 'Rechts (vertikal)',
        'Top (horizontal)' => 'Oben (horizontal)',
        'Top (vertical)' => 'Oben (vertikal)',
        'Upper Far Left' => 'Oben ganz links',
        'Upper Far Right' => 'Oben ganz rechts',
        'Upper-left' => 'Links oben',
        'Upper-left (horizontal)' => 'Links oben (horizontal)',
        'Upper-left (vertical)' => 'Links oben (vertikal)',
        'Upper-middle' => 'Oben mitte',
        'Upper-right' => 'Rechts oben',
        'Upper-right (horizontal)' => 'Rechts oben (horizontal)',
        'Upper-right (vertical)' => 'Rechts oben (vertikal)',
      },
    },
   'AFPointAutoSelection' => {
      Description => 'Automatische AF-Feldwahl',
      PrintConv => {
        'Control-direct:disable/Main:disable' => 'Schnelleinstellrad-Direkt:nicht möglich/Haupt-Wahlrad:nein',
        'Control-direct:disable/Main:enable' => 'Schnelleinstellrad-Direkt:nicht möglich/Haupt-Wahlrad:möglich',
        'Control-direct:enable/Main:enable' => 'Schnelleinstellrad-Direkt:möglich/Haupt-Wahlrad:möglich',
      },
    },
   'AFPointBrightness' => {
      Description => 'AF-Feld Helligkeit',
      PrintConv => {
        'Brighter' => 'Heller',
        'Extra High' => 'Extra Hoch',
        'High' => 'Hoch',
        'Low' => 'Niedrig',
      },
    },
   'AFPointDisplayDuringFocus' => {
      Description => 'AF-Feld Anzeige während Fokus',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (when focus achieved)' => 'Ein (nach Scharfeinstellung)',
      },
    },
   'AFPointIllumination' => {
      Description => 'Messfeld-LED',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AFPointInFocus' => {
      Description => 'AF-Punkt im Fokus',
      PrintConv => {
        '(none)' => '(keiner)',
        'Bottom (horizontal)' => 'Unten (horizontal)',
        'Bottom (vertical)' => 'Unten (vertikal)',
        'Center (horizontal)' => 'Mitte (horizontal)',
        'Center (vertical)' => 'Mitte (vertikal)',
        'Far Left' => 'Weit links',
        'Far Left (horizontal)' => 'Weit links(horizontal)',
        'Far Left (vertical)' => 'Weit links (vertikal)',
        'Far Right' => 'Weit rechts',
        'Far Right (horizontal)' => 'Weit rechts (horizontal)',
        'Far Right (vertical)' => 'Weit rechts (vertikal)',
        'Left' => 'Links',
        'Left (horizontal)' => 'Links (horizontal)',
        'Left (vertical)' => 'Links (vertikal)',
        'Lower Far Left' => 'Unten ganz Links',
        'Lower Far Right' => 'Unten ganz rechts',
        'Lower-left' => 'Links unten',
        'Lower-left (horizontal)' => 'Links unten (horizontal)',
        'Lower-left (vertical)' => 'Links unten (vertikal)',
        'Lower-middle' => 'Unten mitte',
        'Lower-right' => 'Rechts unten',
        'Lower-right (horizontal)' => 'Rechts unten (horizontal)',
        'Lower-right (vertical)' => 'Rechts unten (vertikal)',
        'Near Left' => 'Nahe links',
        'Near Right' => 'Nahe rechts',
        'Right' => 'Rechts',
        'Right (horizontal)' => 'Rechts (horizontal)',
        'Right (vertical)' => 'Rechts (vertikal)',
        'Top (horizontal)' => 'Oben (horizontal)',
        'Top (vertical)' => 'Oben (vertikal)',
        'Upper Far Left' => 'Oben ganz links',
        'Upper Far Right' => 'Oben ganz rechts',
        'Upper-left' => 'Links oben',
        'Upper-left (horizontal)' => 'Links oben (horizontal)',
        'Upper-left (vertical)' => 'Links oben (vertikal)',
        'Upper-middle' => 'Oben mitte',
        'Upper-right' => 'Rechts oben',
        'Upper-right (horizontal)' => 'Rechts oben (horizontal)',
        'Upper-right (vertical)' => 'Rechts oben (vertikal)',
      },
    },
   'AFPointMode' => {
      Description => 'AF-Punkt-Modus',
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'AFPointPosition' => 'AF-Punkt Position',
   'AFPointRegistration' => {
      Description => 'AF-Feld Speicherung',
      PrintConv => {
        'Automatic' => 'Automatisch',
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Extreme Left' => 'Ganz links',
        'Extreme Right' => 'Ganz rechts',
        'Left' => 'Links',
        'Right' => 'Rechts',
        'Top' => 'Oben',
      },
    },
   'AFPointSelected' => {
      Description => 'AF gewählter Punkt',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Automatic Tracking AF' => 'Nachführ AF',
        'Bottom' => 'Unten',
        'Bottom-left' => 'Unten-links',
        'Bottom-right' => 'Unten-rechts',
        'Center' => 'Mitte',
        'Face Detect AF' => 'Gesichtserkennungs-AF',
        'Far Left' => 'Weit links',
        'Far Right' => 'Weit rechts',
        'Fixed Center' => 'Auf Mitte fixiert',
        'Left' => 'Links',
        'Lower Far Left' => 'Unten weit Links',
        'Lower Far Right' => 'Unten weit Rechts',
        'Lower-left' => 'Links unten',
        'Lower-middle' => 'Untere Mitte',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Near Left' => 'Nahe Links',
        'Near Right' => 'Nahe Rechts',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Top-left' => 'Oben-links',
        'Top-right' => 'Oben-rechts',
        'Upper Far Left' => 'Oben weit Links',
        'Upper Far Right' => 'Oben weit Rechts',
        'Upper-left' => 'Links oben',
        'Upper-middle' => 'Obere Mitte',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPointSelected2' => {
      Description => 'AF gewählter Punkt 2',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Left' => 'Links',
        'Lower-left' => 'Links unten',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPointSelection' => {
      Description => 'AF-Messfeldauswahl',
      PrintConv => {
        '11 Points' => '11 Messfelder',
        '51 Points' => '51 Messfelder (3D-Tracking)',
      },
    },
   'AFPointSelectionMethod' => {
      Description => 'Wahlmethode für AF-Messfeld',
      PrintConv => {
        'Multi-controller direct' => 'Multicontroller',
        'Quick Control Dial direct' => 'Schnelleinstellrad',
      },
    },
   'AFPointSet' => {
      Description => 'AF-Punkt Einstellung',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'AFPointSpotMetering' => 'Anzahl AF-Messff./Spotmsg.',
   'AFPoints' => 'AF-Punkte',
   'AFPointsInFocus' => {
      Description => 'AF-Punkte im Fokus',
      PrintConv => {
        '(none)' => '(Keine)',
        'All' => 'Alle',
        'All 11 Points' => 'Alle 11 Punkte',
        'Bottom' => 'Unten',
        'Bottom, Center' => 'Unten, Mitte',
        'Bottom-center' => 'Unten-mitte',
        'Bottom-left' => 'Unten-links',
        'Bottom-right' => 'Unten-rechts',
        'Center' => 'Mitte',
        'Center (horizontal)' => 'Mitte (horizontal)',
        'Center (vertical)' => 'Mitte (vertikal)',
        'Center+Right' => 'Mitte Rechts',
        'Far Left' => 'Weit links',
        'Far Right' => 'Weit rechts',
        'Fixed Center or Multiple' => 'Auf Mitte fixiert oder mehrere',
        'Left' => 'Links',
        'Left+Center' => 'Links Mitte',
        'Left+Right' => 'Links Rechts',
        'Lower-left' => 'Links unten',
        'Lower-left, Bottom' => 'Links unten, Unten',
        'Lower-left, Mid-left' => 'Links unten, Links mitte',
        'Lower-right' => 'Rechts unten',
        'Lower-right, Bottom' => 'Rechts unten, Unten',
        'Lower-right, Mid-right' => 'Rechts unten, Rechts mitte',
        'Mid-left' => 'Links mitte',
        'Mid-left, Center' => 'Links mitte, Mitte',
        'Mid-right' => 'Rechts mitte',
        'Mid-right, Center' => 'Rechts mitte, Mitte',
        'None' => 'Keiner',
        'None (MF)' => 'Keiner (MF)',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Top, Center' => 'Oben, Mitte',
        'Top-center' => 'Oben-Mitte',
        'Top-left' => 'Oben-links',
        'Top-right' => 'Oben-rechts',
        'Upper-left' => 'Links oben',
        'Upper-left, Mid-left' => 'Links oben, Links mitte',
        'Upper-left, Top' => 'Links oben, Oben',
        'Upper-right' => 'Rechts oben',
        'Upper-right, Mid-right' => 'Rechts oben, Rechts mitte',
        'Upper-right, Top' => 'Rechts oben, Oben',
      },
    },
   'AFPointsInFocus1D' => 'AF-Punkte im Fokus 1D',
   'AFPointsInFocus5D' => {
      Description => 'AF-Punkte im Fokus',
      PrintConv => {
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Left' => 'Links',
        'Lower-left' => 'Unten links',
        'Lower-right' => 'Rechts unten',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPointsSelected' => 'AF gewählte Punkte',
   'AFPointsUnknown1' => {
      Description => 'AF-Punkte Unbekannt 1',
      PrintConv => {
        'All' => 'Alle',
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Central 9 points' => 'Alle mittleren 9 Punkte',
        'Left' => 'Links',
        'Lower-left' => 'Links unten',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPointsUnknown2' => {
      Description => 'AF-Punkte Unbekannt 2?',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Left' => 'Links',
        'Lower-left' => 'Links unten',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Right' => 'Rechts',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPointsUsed' => {
      Description => 'Verwendete AF-Punkte',
      PrintConv => {
        'All 11 Points' => 'Alle 11 Punkte',
        'Bottom' => 'Unten',
        'Center' => 'Mitte',
        'Far Left' => 'Weit links',
        'Far Right' => 'Weit rechts',
        'Lower-left' => 'Links unten',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'AFPredictor' => 'AF-Prädiktor',
   'AFResponse' => 'Verwendeter AF',
   'AFSearch' => {
      Description => 'AF Fokussierung',
      PrintConv => {
        'Not Ready' => 'Nicht bereit',
        'Ready' => 'Bereit',
      },
    },
   'AFSensorActive' => {
      Description => 'Aktiver AF Sensor',
      PrintConv => {
        'Bottom' => 'Unten',
        'Bottom-left' => 'Unten Links',
        'Bottom-right' => 'Unten Rechts',
        'Center Vertical' => 'Mitte vertikal',
        'Middle Horizontal' => 'Mitte horizontal',
        'Top' => 'Oben',
        'Top-left' => 'Oben Links',
        'Top-right' => 'Rechts Oben',
      },
    },
   'AFStatusActiveSensor' => {
      Description => 'Status aktiver AF Sensor',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusBottom' => {
      Description => 'AF Status Unten',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusBottom-left' => {
      Description => 'AF Status Unten Links',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusBottom-right' => {
      Description => 'AF Status Unten Rechts',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusCenterHorizontal' => {
      Description => 'AF Status Mitte horizontal',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusCenterVertical' => {
      Description => 'AF Status Mitte vertikal',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusLeft' => {
      Description => 'AF Status Links',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusMiddleHorizontal' => {
      Description => 'AF Status Mitte horizontal',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusRight' => {
      Description => 'AF Status Rechts',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusTop' => {
      Description => 'AF Status Oben',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusTop-left' => {
      Description => 'AF Status Oben Links',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFStatusTop-right' => {
      Description => 'AF Status Oben Rechts',
      PrintConv => {
        'In Focus' => 'Im Fokus',
        'Out of Focus' => 'Nicht im Fokus',
      },
    },
   'AFType' => {
      Description => 'AF-Typ',
      PrintConv => {
        '15-point' => '15 Punkte',
        '19-point' => '19 Punkte',
      },
    },
   'AIServoContinuousShooting' => 'Auslösepriorität',
   'AIServoImagePriority' => {
      Description => 'AI Servo Priorität 1./2. Bild',
      PrintConv => {
        '1: AF, 2: Drive speed' => 'AF-Priorität/Transportgeschwindigkeit',
        '1: AF, 2: Tracking' => 'AF-Priorität/Nachführpriorität',
        '1: Release, 2: Drive speed' => 'Auslösung/Transportgeschwindigkeit',
        '1: Release, 2: Tracking' => '1: Auslösung, 2: Nachführung',
      },
    },
   'AIServoTrackingMethod' => {
      Description => 'AI Servo AF Nachführung',
      PrintConv => {
        'Continuous AF track priority' => 'AF Nachführ-Priorität',
        'Main focus point priority' => 'Hauptfokussierungsfeld',
      },
    },
   'AIServoTrackingSensitivity' => {
      Description => 'AI Servo Empfindlichkeit',
      PrintConv => {
        'Fast' => 'Schnell',
        'Medium Fast' => 'Mittel-Schnell',
        'Medium Slow' => 'Mittel',
        'Slow' => 'Langsam',
      },
    },
   'APEVersion' => 'APE-Version',
   'ARMVersion' => 'ARM-Version',
   'AccessDate' => 'Zugriffsdatum',
   'AccessoryType' => 'Zubehör-Typ',
   'ActionAdvised' => 'Aktion empfohlen',
   'ActiveArea' => 'Aktiver Bereich',
   'ActiveD-Lighting' => {
      Description => 'Aktives D-Lighting',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ActiveD-LightingMode' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
      },
    },
   'AddAspectRatioInfo' => {
      Description => 'Seitenverhältnisinfo zufügen',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'AddOriginalDecisionData' => {
      Description => 'Originaldaten zufügen',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Address' => 'Adresse',
   'AdjustmentMode' => 'Korrekturmodus',
   'AdultContentWarning' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'AdvancedRaw' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AdvancedSceneMode' => {
      Description => 'Erweiteter Szenenmodus',
      PrintConv => {
        'Off' => 'Aus',
        'Creative Macro' => 'Makro kreativ',
        'Flower' => 'Blumen',
        'HDR B&W' => 'HDR Schwarz-Weiß',
        'High Key' => 'High-Key',
        'Indoor Sports' => 'Hallensport',
        'Low Key' => 'Low-Key',
        'Outdoor Sports' => 'Freiluftsport',
      },
    },
   'AdvancedSceneType' => 'Erweiterter Szenentyp',
   'AlphaByteCount' => 'Anzahl Bytes der Alpha-Kanal-Daten',
   'AlphaChannelsNames' => 'Alpha-Kanal-Namen',
   'AlphaDataDiscard' => {
      Description => 'Verworfene Alpha-Kanal-Daten',
      PrintConv => {
        'Flexbits Discarded' => 'FlexBits verworfen',
        'Full Resolution' => 'Volle Auflösung',
        'HighPass Frequency Data Discarded' => 'Hochpass-Frequenz-Daten verworfen',
        'Highpass and LowPass Frequency Data Discarded' => 'Hochpass- und Tiefpass-Frequenz-Daten verworfen',
      },
    },
   'AlphaOffset' => 'Alpha-Kanal-Datenposition',
   'AlreadyApplied' => 'Bereits zugewiesen',
   'AnalogBalance' => 'Analog-Balance',
   'Annotation' => 'Anmerkung',
   'Annotations' => 'Anmerkungen',
   'Anti-Blur' => {
      Description => 'Verwacklungsschutz',
      PrintConv => {
        'Off' => 'Aus',
        'On (Continuous)' => 'Ein (Kontinuierlich)',
        'On (Shooting)' => 'Ein (Aufnahme)',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AntiAliasStrength' => 'Anti-Aliasing Stärke',
   'Aperture' => 'Blende',
   'ApertureDisplayed' => 'Angezeigte Blende',
   'ApertureRange' => {
      Description => 'Einstellung Verschlusszeitenbereich',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ApertureRingUse' => {
      Description => 'Blendenring-Verwendung',
      PrintConv => {
        'Permitted' => 'Erlaubt',
        'Prohibited' => 'Nicht erlaubt',
      },
    },
   'ApertureSetting' => 'Blendeneinstellung',
   'ApertureValue' => 'Blende',
   'ApplicationRecordVersion' => 'IPTC-Modell-2-Version',
   'ApplyShootingMeteringMode' => {
      Description => 'Angewandter Belichtungs-/Messmodus',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ApproximateFNumber' => 'F-Wert angenähert',
   'ApproximateFocusDistance' => 'Fokus-Distanz angenähert',
   'ArtFilter' => {
      Description => 'Kunst Filter',
      PrintConv => {
        'Drawing' => 'Zeichnung',
        'Fish Eye' => 'Fischauge',
        'Off' => 'Aus',
        'Reflection' => 'Reflektierung',
        'Soft Focus' => 'Weichzeichner',
        'Soft Focus 2' => 'Weichzeichner 2',
        'Sparkle' => 'Perleffekt',
        'Watercolor' => 'Wasserfarbe',
        'Watercolor II' => 'Wasserfarbe II',
      },
    },
   'ArtFilterEffect' => {
      Description => 'Filtereffekt Kunst',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Drawing' => 'Zeichnung',
        'Fish Eye' => 'Fischauge',
        'No Effect' => 'Ohne Effekt',
        'Off' => 'Aus',
        'Reflection' => 'Reflektierung',
        'Soft Focus' => 'Weichzeichner',
        'Soft Focus 2' => 'Weichzeichner 2',
        'Sparkle' => 'Perleffekt',
        'Watercolor' => 'Wasserfarbe',
        'Watercolor II' => 'Wasserfarbe II',
      },
    },
   'ArtMode' => {
      PrintConv => {
        'Silent Movie' => 'Stillleben',
      },
    },
   'Artist' => 'Künstler',
   'ArtworkCreator' => 'Artwork Ersteller',
   'AsShotICCProfile' => 'Aufnahme Farbprofil',
   'AsShotNeutral' => 'Aufnahme Neutral',
   'AsShotPreProfileMatrix' => 'Aufnahme Pre Profil Matrix',
   'AsShotProfileName' => 'Aufnahme Pre Profilname',
   'AsShotWhiteXY' => 'Aufnahme Weiß XY',
   'AspectRatio' => 'Bildformat',
   'AssignBktButton' => {
      Description => 'Zugeordnete Belichtungsreihen-Taste',
      PrintConv => {
        'Auto Bracketing' => 'Automatische Belichtungsreihe',
        'Multiple Exposure' => 'Mehrfachbelichtung',
      },
    },
   'AssignFuncButton' => {
      Description => 'FUNC.-Taste zuordnen',
      PrintConv => {
        'Exposure comp./AEB setting' => 'Belichtungskorrektur/AEB-Einstellung',
        'Image jump with main dial' => 'Bildsprung mit Haupt-Wahlrad',
        'Image quality' => 'Qualität ändern',
        'LCD brightness' => 'LCD-Helligkeit',
        'Live view function settings' => 'Livebild Funktionseinstellung',
      },
    },
   'AssistButtonFunction' => {
      Description => 'Funktion Assist-Taste',
      PrintConv => {
        'Av+/- (AF point by QCD)' => 'Av+/- (AF-Feld mit Daumenrad)',
        'FE lock' => 'FE Blitzmesswertspeicherung',
        'Select HP (while pressing)' => 'Ausw.G.pos.(Ass-Taste gedr.)',
        'Select Home Position' => 'Auswahl Grundposition',
      },
    },
   'Audio' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'AudioCodecID' => {
      PrintConv => {
        'Unknown -' => 'Unbekannt -',
      },
    },
   'AudioDuration' => 'Audiodauer',
   'AudioOutcue' => 'Audio-Outcue',
   'AudioSamplingRate' => 'Audio-Samplingrate',
   'AudioSamplingResolution' => 'Audio-Samplingauflösung',
   'AudioType' => 'Audiotyp',
   'Author' => 'Autor',
   'AuthorURL' => 'Autor URL',
   'AuthorsPosition' => 'Autorenposition',
   'AutoAperture' => {
      Description => 'Blendenring auf A',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AutoBracket' => 'Automatische Belichtungsreihe',
   'AutoBracketModeM' => {
      Description => 'Belichtungsreihen bei M',
      PrintConv => {
        'Flash Only' => 'Nur Blitz',
        'Flash/Aperture' => 'Blitz & Blende',
        'Flash/Speed' => 'Blitz & Zeit',
        'Flash/Speed/Aperture' => 'Blitz, Zeit & Blende',
      },
    },
   'AutoBracketOrder' => 'BKT-Reihenfolge',
   'AutoBracketSet' => {
      Description => 'Belichtungsreihen',
      PrintConv => {
        'AE & Flash' => 'Belichtung & Blitz',
        'AE Only' => 'Nur Belichtung',
        'Exposure' => 'Belichtung',
        'Flash Only' => 'Nur Blitz',
        'WB Bracketing' => 'Weißabgleichs-Belichtungsreihe',
      },
    },
   'AutoBracketing' => {
      Description => 'Automatische Belichtungsreihe',
      PrintConv => {
        'AE' => 'Belichtung',
        'Contrast' => 'Kontrast',
        'Effect' => 'Effekt',
        'No flash & flash' => 'Kein Blitz & Blitz',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Pre-shot' => 'Vorauslösung',
        'WB' => 'Weißabgleich',
        'WB2' => 'Weißabgleich 2',
      },
    },
   'AutoBracketingSet' => {
      Description => 'Automatische Belichtungsreihen-Einstellung',
      PrintConv => {
        'AE & Flash' => 'Belichtung & Blitz',
        'AE Only' => 'Nur Belichtung',
        'Flash Only' => 'Nur Blitz',
        'WB Bracketing' => 'Weißabgleich-Belichtungsreihe',
      },
    },
   'AutoBrightness' => 'Helligkeit Auto',
   'AutoContrast' => 'Kontrast Auto',
   'AutoDistortionControl' => {
      Description => 'Automatische Verzeichnungskontrolle',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AutoExposure' => 'Belichtung Auto',
   'AutoExposureBracketing' => {
      Description => 'Auto-Belichtungsreihe',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AutoFP' => {
      Description => 'FP-Kurzzeitsynchr.',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AutoFocus' => {
      Description => 'Auto-Fokus',
      PrintConv => {
        'Off' => 'Deaktiviert',
        'On' => 'Aktiviert',
      },
    },
   'AutoISO' => {
      Description => 'ISO-Automatik',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (high sensitivity)' => 'Ein (Hohe Empfindlichkeit)',
      },
    },
   'AutoISOMax' => 'ISO-Automatik Max',
   'AutoISOMinShutterSpeed' => 'ISO-Automatik Längste Belichtungszeit',
   'AutoLightingOptimizer' => {
      Description => 'Autom. Belichtungsoptimierung',
      PrintConv => {
        'Disable' => 'Inaktiv',
        'Enable' => 'Aktiv',
        'Low' => 'Gering',
        'Off' => 'Aus',
        'Strong' => 'Stark',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AutoLightingOptimizerOn' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'AutoPortraitFramed' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'AutoRedEye' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'AutoRotate' => {
      Description => 'Automatische Bilddrehung',
      PrintConv => {
        'None' => 'Keine',
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'AutoShadows' => 'Schatten Auto',
   'AuxiliaryLens' => 'Vorsatzlinse',
   'AvApertureSetting' => 'Av Blenden-Einstellung',
   'AvSettingWithoutLens' => {
      Description => 'Blendeneinstellung ohne Objektiv',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'BWFilter' => 'S/W-Filter',
   'BWMode' => {
      Description => 'Schwarz-Weiß Modus',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'BabyAge' => 'Kindesalter',
   'BabyName' => 'Kindesname',
   'BackLight' => 'Hintergrundbeleuchtung',
   'BackgroundColor' => 'Hintergrundfarbe',
   'BackgroundColorIndicator' => 'Indikator Hintergrundfarbe',
   'BackgroundColorValue' => 'Hintergrundfarbwert',
   'BackgroundTiling' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Keines',
      },
    },
   'BaseExposureCompensation' => 'Basis-Belichtungskorrektur',
   'BaseISO' => 'Basis-ISO',
   'BaseName' => 'Basisname',
   'BaseURL' => 'Basis URL',
   'BaselineExposure' => 'Referenzbelichtung',
   'BaselineExposureOffset' => 'Referenzbelichtung Abweichung',
   'BaselineNoise' => 'Grundrauschen',
   'BaselineSharpness' => 'Referenzschärfe',
   'BatteryInfo' => 'Stromquelle',
   'BatteryLevel' => 'Batteriestatus',
   'BatteryOrder' => {
      Description => 'Akkureihenfolge',
      PrintConv => {
        'Camera Battery First' => 'Zuerst Akku in der Kamera',
        'MB-D10 First' => 'Zuerst Akkus im MB-D10',
      },
    },
   'BatteryState' => {
      Description => 'Batteriestatus',
      PrintConv => {
        'Almost full' => 'Fast Voll',
        'Empty' => 'Leer',
        'Full' => 'Voll',
        'Half full' => 'Halb Voll',
        'Low' => 'Niedrig',
      },
    },
   'BayerGreenSplit' => 'Pixelaufteilung nach Bayer',
   'BayerPattern' => 'Bayer Matrix',
   'Beep' => {
      Description => 'Tonsignal',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Tief',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'BestShotMode' => {
      Description => 'Bester Aufnahmemodus',
      PrintConv => {
        'Autumn Leaves' => 'Herbstlaub',
        'Baby CS' => 'Kleinkind',
        'Backlight' => 'Hintergrundbeleuchtung',
        'Beach' => 'Strand',
        'Candlelight Portrait' => 'Kerzenlicht Porträt',
        'Child CS' => 'Kinder',
        'Children' => 'Kinder',
        'Collection' => 'Sammlung',
        'Fireworks' => 'Feuerwerk',
        'Flower' => 'Blumen',
        'Food' => 'Lebensmittel',
        'For YouTube' => 'Für YouTube',
        'For eBay' => 'Für eBay',
        'High Sensitivity' => 'Hohe Empfindlichkeit',
        'Natural Green' => 'Naturgrün',
        'Night Scene' => 'Nachtszene',
        'Night Scene Portrait' => 'Nacht-Porträt',
        'People' => 'Menschen',
        'Pet' => 'Haustiere',
        'Pet CS' => 'Haustiere',
        'Portrait' => 'Porträt',
        'Silent' => 'Stillleben',
        'Snow' => 'Schnee',
        'Sports' => 'Sport',
        'Sports CS' => 'Sport CS',
        'Sundown' => 'Sonnenuntergang',
        'Twilight' => 'Dämmerung',
        'Underwater' => 'Unterwasser',
      },
    },
   'BigImage' => 'Big Image Vorschaubild',
   'BitDepth' => 'Bit-Tiefe',
   'BitsPerSample' => 'Anzahl der Bits pro Komponente',
   'BlackLevel' => 'Schwarzwert',
   'BlackLevel2' => 'Schwarzwert 2',
   'BlackLevelData' => 'Schwarzwert Daten',
   'BlackLevelDeltaH' => 'Schwarzwert Delta H',
   'BlackLevelDeltaV' => 'Schwarzwert Delta V',
   'BlackPoint' => 'Schwarzpunkt',
   'BlueAdjust' => 'Blau-Korrektur',
   'BlueBalance' => 'Farbabgleich Blau',
   'BlueHue' => 'Farbton Blau',
   'BlueMatrixColumn' => 'Blau-Matrixspalte',
   'BlueSaturation' => 'Sättigung Blau',
   'BlueTRC' => 'Blau-Tonwertwiedergabe-Kurve',
   'BlueX' => 'Blaupunkt X',
   'BlueY' => 'Blaupunkt Y',
   'BlurControl' => {
      Description => 'Bildstabilisierung',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Medium' => 'Mittel',
        'Off' => 'Aus',
      },
    },
   'BlurWarning' => {
      Description => 'Verwackelungswarnung',
      PrintConv => {
        'Blur Warning' => 'Verwackelungswarnung',
        'None' => 'Keine',
      },
    },
   'BodyBatteryADLoad' => 'Kamerabatterie A/D unter Last',
   'BodyBatteryADNoLoad' => 'Kamerabatterie A/D im Leerlauf',
   'BodyBatteryState' => {
      Description => 'Kamerabatterie-Status',
      PrintConv => {
        'Almost Empty' => 'Fast leer',
        'Empty or Missing' => 'Leer oder nicht vorhanden',
        'Full' => 'Voll geladen',
        'Running Low' => 'Schwach',
      },
    },
   'BodyFirmwareVersion' => 'Kamera-Firmware-Version',
   'BracketMode' => {
      Description => 'Belichtungsreihen-Modus',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'BracketSequence' => 'Belichtungsreihen-Abfolge',
   'BracketShot' => 'Belichtungsreihen-Aufnahme',
   'BracketShotNumber' => {
      Description => 'Belichtungsreihen-Bildnummer',
      PrintConv => {
        '1 of 2' => '1 von 2',
        '1 of 3' => '1 von 3',
        '1 of 5' => '1 von 5',
        '2 of 2' => '2 von 2',
        '2 of 3' => '2 von 3',
        '2 of 5' => '2 von 5',
        '3 of 3' => '3 von 3',
        '3 of 5' => '3 von 5',
        '4 of 5' => '4 von 5',
        '5 of 5' => '5 von 5',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'BracketStep' => {
      Description => 'Belichtungsreihenschritte',
      PrintConv => {
        '1 EV' => '1 LW',
        '1/3 EV' => '1/3 LW',
        '2/3 EV' => '2/3 LW',
      },
    },
   'BracketValue' => 'Belichtungsreihen-Wert',
   'Brightness' => 'Helligkeit',
   'BrightnessAdj' => 'Helligkeitskorrektur',
   'BrightnessValue' => 'Helligkeit',
   'BulbDuration' => 'Bulb-Dauer',
   'BurstMode' => {
      Description => 'Burst Modus',
      PrintConv => {
        'Auto Exposure Bracketing (AEB)' => 'Automatische Belichtungsreihe',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Unlimited' => 'Unbegrenzt',
      },
    },
   'BurstMode2' => 'Burst Modus 2',
   'BurstShot' => 'Burst Aufnahme',
   'ButtonFunctionControlOff' => {
      Description => 'Tastenfunktion wenn Schnelleinstellrad OFF',
      PrintConv => {
        'Disable main, Control, Multi-control' => 'Deaktiv Haupt-Wahlrad, Schnelleinstellrad, Multicontroller',
        'Normal (enable)' => 'Normal (eingeschaltet)',
      },
    },
   'By-line' => 'Ersteller',
   'By-lineTitle' => 'Beruf des Erstellers',
   'ByteOrder' => 'Bytereihenfolge',
   'CCDSensitivity' => 'CCD Empfindlichkeit',
   'CFALayout' => {
      Description => 'Farbfilter Anordnung',
      PrintConv => {
        'Rectangular' => 'Rechteckig',
      },
    },
   'CFAPattern' => 'Farbfiltermatrix',
   'CFAPattern2' => 'Farbfiltermatrix 2',
   'CFAPatternColumns' => 'Farbfiltermatrix Spalten',
   'CFAPatternRows' => 'Farbfiltermatrix Zeilen',
   'CFAPatternValues' => 'Farbfiltermatrix Werte',
   'CFARepeatPatternDim' => 'Farbfiltermatrix-Größe',
   'CLModeShootingSpeed' => 'Lowspeed-Bildrate',
   'CMContrast' => 'CM Kontrast',
   'CMExposureCompensation' => 'CM Belichtungskorrektur',
   'CMHue' => 'CM Farbton',
   'CMMFlags' => 'CMM-Flags',
   'CMSaturation' => 'CM Farbsättigung',
   'CMSharpness' => 'CM Schärfe',
   'CMWhiteBalance' => 'CM Weißabgleich',
   'CMWhiteBalanceComp' => 'CM Weißabgleichsausgleich',
   'CMWhiteBalanceGrayPoint' => 'CM Weißabgleich Graupunkt',
   'CPUArchitecture' => 'CPU Architektur',
   'CPUByteOrder' => {
      Description => 'CPU Bytereihenfolge',
      PrintConv => {
        'Big endian' => 'Big-endian',
        'Little endian' => 'Little-endian',
      },
    },
   'CPUCount' => 'Anzahl CPU',
   'CPUFirmwareVersion' => 'CPU-Firmware-Version',
   'CPUSubtype' => 'CPU Subtyp',
   'CPUType' => {
      Description => 'CPU Typ',
      PrintConv => {
        'Any' => 'Unbestimmt',
        'None' => 'Unbestimmt',
        'i860 big endian' => 'i860 big-endian',
        'i860 little endian' => 'i860 little-endian',
      },
    },
   'CalibrationIlluminant1' => {
      Description => 'Lichtquellenkalibrierung 1',
      PrintConv => {
        'Cloudy' => 'Bewölkt',
        'Cool White Fluorescent' => 'Neonlicht kaltweiß',
        'Day White Fluorescent' => 'Neonlicht neutralweiß',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Fine Weather' => 'Wolkenlos',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'ISO Studio Tungsten' => 'ISO Studio-Kunstlicht (Glühbirne)',
        'Other' => 'Andere Lichtquelle',
        'Shade' => 'Schatten',
        'Standard Light A' => 'Standard-Licht A',
        'Standard Light B' => 'Standard-Licht B',
        'Standard Light C' => 'Standard-Licht C',
        'Tungsten (Incandescent)' => 'Kunstlicht (Glühbirne)',
        'Unknown' => 'Unbekannt',
        'Warm White Fluorescent' => 'Neonlicht warmweiß',
        'White Fluorescent' => 'Neonlicht universalweiß',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Lichtquellenkalibrierung 2',
      PrintConv => {
        'Cloudy' => 'Bewölkt',
        'Cool White Fluorescent' => 'Neonlicht kaltweiß',
        'Day White Fluorescent' => 'Neonlicht neutralweiß',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Fine Weather' => 'Wolkenlos',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'ISO Studio Tungsten' => 'ISO Studio-Kunstlicht (Glühbirne)',
        'Other' => 'Andere Lichtquelle',
        'Shade' => 'Schatten',
        'Standard Light A' => 'Standard-Licht A',
        'Standard Light B' => 'Standard-Licht B',
        'Standard Light C' => 'Standard-Licht C',
        'Tungsten (Incandescent)' => 'Kunstlicht (Glühbirne)',
        'Unknown' => 'Unbekannt',
        'Warm White Fluorescent' => 'Neonlicht warmweiß',
        'White Fluorescent' => 'Neonlicht universalweiß',
      },
    },
   'CameraBody' => 'Kamera Gehäuse',
   'CameraByteOrder' => 'Kamera Bytereihenfolge',
   'CameraCalibration1' => 'Kamerakalibrierung 1',
   'CameraCalibration2' => 'Kamerakalibrierung 2',
   'CameraColorCalibration01' => 'Kamera Farbkalibrierung 01',
   'CameraColorCalibration02' => 'Kamera Farbkalibrierung 02',
   'CameraColorCalibration03' => 'Kamera Farbkalibrierung 03',
   'CameraColorCalibration04' => 'Kamera Farbkalibrierung 04',
   'CameraColorCalibration05' => 'Kamera Farbkalibrierung 05',
   'CameraColorCalibration06' => 'Kamera Farbkalibrierung 06',
   'CameraColorCalibration07' => 'Kamera Farbkalibrierung 07',
   'CameraColorCalibration08' => 'Kamera Farbkalibrierung 08',
   'CameraColorCalibration09' => 'Kamera Farbkalibrierung 09',
   'CameraColorCalibration10' => 'Kamera Farbkalibrierung 10',
   'CameraColorCalibration11' => 'Kamera Farbkalibrierung 11',
   'CameraColorCalibration12' => 'Kamera Farbkalibrierung 12',
   'CameraColorCalibration13' => 'Kamera Farbkalibrierung 13',
   'CameraColorCalibration14' => 'Kamera Farbkalibrierung 14',
   'CameraColorCalibration15' => 'Kamera Farbkalibrierung 15',
   'CameraID' => 'Kamera ID',
   'CameraISO' => 'Kamera-ISO',
   'CameraInfo' => 'Pentax-Modell',
   'CameraInfoByteOrder' => 'Kamerainformation Bytereihenfolge',
   'CameraModel' => 'Kamera Modell',
   'CameraOrientation' => {
      Description => 'Ausrichtung der Kamera',
      PrintConv => {
        'Downwards' => 'Abwärts',
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
        'Upwards' => 'Aufwärts',
      },
    },
   'CameraProfile' => 'Kameraprofil',
   'CameraProfileDigest' => 'Kennwert des Kameraprofils',
   'CameraSerialNumber' => 'Kamera-Seriennummer',
   'CameraSettings' => 'Kameraeinstellungen',
   'CameraSettingsVersion' => 'Kameraeinstellungen-Version',
   'CameraTemperature' => 'Kamera-Temperatur',
   'CameraType' => {
      Description => 'Kameratyp',
      PrintConv => {
        'Compact' => 'Kompakt',
        'DV Camera' => 'DV Kamera',
        'EOS High-end' => 'EOS Highend',
        'EOS Mid-range' => 'EOS Mittelklasse',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'CameraType2' => 'Kameratyp 2',
   'CanonAFInfo' => 'AF-Info',
   'CanonAFInfo2' => 'AF-Info (2)',
   'CanonExposureMode' => {
      Description => 'Belichtungsmodus',
      PrintConv => {
        'Aperture-priority AE' => 'Blendenpriorität',
        'Bulb' => 'Bulb-Modus',
        'Manual' => 'Manuell',
        'Program AE' => 'Programmautomatik',
        'Shutter speed priority AE' => 'Verschlusspriorität',
      },
    },
   'CanonFileDescription' => 'Canon Dateibeschreibung',
   'CanonFileInfo' => 'Dateiinformationen',
   'CanonFileLength' => 'Dateilänge',
   'CanonFirmwareVersion' => 'Firmware-Version',
   'CanonFlashMode' => {
      Description => 'Blitz-Modus',
      PrintConv => {
        'Auto' => 'Automatisch',
        'External flash' => 'Externer Blitz',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Red-eye reduction' => 'Rote-Augen-Reduzierung',
        'Red-eye reduction (Auto)' => 'Rote-Augen-Reduzierung (Automatisch)',
        'Red-eye reduction (On)' => 'Rote-Augen-Reduzierung (Ein)',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'CanonFocalLength' => 'Objektivart',
   'CanonImageHeight' => 'Canon-Bildhöhe',
   'CanonImageSize' => {
      Description => 'Canon-Bildgröße',
      PrintConv => {
        'Large' => 'Groß',
        'Medium' => 'Mittelgroß',
        'Medium 1' => 'Mittelgroß 1',
        'Medium 2' => 'Mittelgroß 2',
        'Medium 3' => 'Mittelgroß 3',
        'Medium Movie' => 'Mittelgroßer Film',
        'Postcard' => 'Postkarte',
        'Small' => 'Klein',
        'Small 1' => 'Klein 1',
        'Small 2' => 'Klein 2',
        'Small 3' => 'Klein 3',
        'Small Movie' => 'Kleiner Film',
        'Widescreen' => 'Breitbild',
      },
    },
   'CanonImageType' => 'Canon-Bildtyp',
   'CanonImageWidth' => 'Canon-Bildbreite',
   'CanonModelID' => 'Canon-Modell ID',
   'Caption' => 'Bildtext',
   'Caption-Abstract' => 'Beschreibung/Zusammenfassung',
   'CaptionWriter' => 'Bildtextautor',
   'CaptureXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (Mikrometer)',
      },
    },
   'CaptureYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (Mikrometer)',
      },
    },
   'CasioImageSize' => 'Casio Bildgröße',
   'CatalogSets' => 'Katalogzusammenstellungen',
   'Categories' => {
      Description => 'Kategorien',
      PrintConv => {
        'People' => 'Menschen',
        'Scenery' => 'Szene',
      },
    },
   'Category' => 'Kategorie',
   'CenterAFArea' => {
      Description => 'AF-Messfeld Mitte',
      PrintConv => {
        'Normal Zone' => 'Normal',
        'Wide Zone' => 'Groß',
      },
    },
   'CenterFocusPoint' => {
      Description => 'Fokuspunkt Mitte',
      PrintConv => {
        'Normal Zone' => 'Normaler Bereich',
        'Wide Zone' => 'Großer Bereich',
      },
    },
   'CenterWeightedAreaSize' => {
      Description => 'Messfeldgröße Mitte',
      PrintConv => {
        'Average' => 'Durchschnitt',
      },
    },
   'Certificate' => 'Zertifikat',
   'Channels' => 'Kanäle',
   'Chapter' => 'Kapitel',
   'CharacterSet' => 'Zeichensatz',
   'Children' => 'Kinder',
   'ChromaticAberration' => 'Farbabweichung',
   'ChromaticAberrationB' => 'Farbabweichung B',
   'ChromaticAberrationBlue' => 'Farbabweichung Blau',
   'ChromaticAberrationCorr' => {
      Description => 'Farbabweichung Korrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ChromaticAberrationCorrection' => {
      Description => 'Farbabweichung Korrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ChromaticAberrationOn' => 'Farbabweichung Ein',
   'ChromaticAberrationR' => 'Farbabweichung R',
   'ChromaticAberrationRed' => 'Farbabweichung Rot',
   'ChromaticAberrationSetting' => {
      Description => 'Farbabweichung Einstellung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Chromaticity' => 'Chromatizität',
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
      },
    },
   'CircleOfConfusion' => 'Unschärfekreis',
   'City' => 'Stadt/Ort',
   'ClassifyState' => 'Klassifizierungs-Status',
   'CodePage' => {
      PrintConv => {
        'Unicode UTF-16, big endian' => 'Unicode UTF-16, Big-endian',
        'Unicode UTF-16, little endian' => 'Unicode UTF-16, Little-endian',
        'Unicode UTF-32, big endian' => 'Unicode UTF-32, Big-endian',
        'Unicode UTF-32, little endian' => 'Unicode UTF-32, Little-endian',
      },
    },
   'CodedCharacterSet' => 'IPTC Characterset',
   'CodedContentScanningKind' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'ColorAberrationControl' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ColorAdjustment' => 'Farbeinstellung',
   'ColorAdjustmentMode' => {
      Description => 'Farbeinstellung Modus',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ColorBW' => 'Schwarz/Weiß',
   'ColorBalance' => 'Farbabgleich',
   'ColorBalance1' => 'Farbabgleich 1',
   'ColorBalanceA' => 'Farbabgleich A',
   'ColorBalanceAdj' => {
      Description => 'Farbabgleich Korrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ColorBalanceBlue' => 'Farbabgleich Blau',
   'ColorBalanceGreen' => 'Farbabgleich Grün',
   'ColorBalanceRed' => 'Farbangleich Rot',
   'ColorBalanceVersion' => 'Farbabgleich Version',
   'ColorBitDepth' => 'Farbtiefe',
   'ColorBoostType' => {
      PrintConv => {
        'Nature' => 'Natur',
        'People' => 'Menschen',
      },
    },
   'ColorBooster' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ColorCompensationFilter' => 'Farbkorrektur-Filter',
   'ColorComponents' => 'Anzahl der Bildkomponenten',
   'ColorCorrection' => 'Farbkorrektur',
   'ColorDataVersion' => 'Farbdaten Version',
   'ColorEffect' => {
      Description => 'Farbeffekt',
      PrintConv => {
        'Black & White' => 'Schwarz/Weiß',
        'Cool' => 'Kühl',
        'Off' => 'Aus',
      },
    },
   'ColorFilter' => {
      Description => 'Farbfilter',
      PrintConv => {
        'Black & White' => 'Schwarz/Weiß',
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Off' => 'Aus',
        'Purple' => 'Lila',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'ColorHue' => 'Farbwiedergabe',
   'ColorInfo' => 'Farb-Informationen',
   'ColorMap' => 'Farbtafel',
   'ColorMatrix' => 'Farb-Matrix',
   'ColorMatrix1' => 'Farbmatrix 1',
   'ColorMatrix2' => 'Farbmatrix 2',
   'ColorMatrixNumber' => 'Farbmatrix Nummer',
   'ColorMode' => {
      Description => 'Farbmodus',
      PrintConv => {
        'Autumn' => 'Herbst',
        'Autumn Leaves' => 'Herbstlaub',
        'B & W' => 'S/W',
        'B&W' => 'Schwarz/Weiß',
        'B&W Red Filter' => 'Schwarz-Weiß Rotfilter',
        'B&W Yellow Filter' => 'Schwarz-Weiß Gelbfilter',
        'Black & White' => 'Schwarz/Weiß',
        'Chrome' => 'Farbe',
        'Clear' => 'Klar',
        'Deep' => 'Tief',
        'Duotone' => 'Zweiton',
        'Evening' => 'Abends',
        'Grayscale' => 'Graustufen',
        'Indexed' => 'Indiziert',
        'Indexed Color' => 'Indizierte Farben',
        'Landscape' => 'Landschaft',
        'Light' => 'Hell',
        'Multichannel' => 'Mehrkanal',
        'Natural' => 'Natürlich',
        'Natural color' => 'Natürliche Farben',
        'Natural sRGB' => 'Neutral sRGB',
        'Natural+ sRGB' => 'Neutral+ sRGB',
        'Night Portrait' => 'Nachtporträt',
        'Night Scene' => 'Nachtszene',
        'Night View' => 'Abendszene',
        'Night View/Portrait' => 'Abendszene/Porträt',
        'Off' => 'Aus',
        'Portrait' => 'Porträt',
        'RGB Color' => 'RGB Farbe',
        'Solarization' => 'Solarisation',
        'Sunset' => 'Sonnenuntergang',
        'Vivid' => 'Lebhafte Farbe',
        'Vivid color' => 'Lebhafte Farbe',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorMoireReduction' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ColorMoireReductionMode' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
      },
    },
   'ColorNoiseReduction' => 'Farbrauschunterdrückung',
   'ColorNoiseReductionDetail' => 'Farbrauschunterdrückung Detail',
   'ColorPalette' => 'Farbpalette',
   'ColorPlanes' => 'Farbebenen',
   'ColorProfile' => {
      Description => 'Farbprofil',
      PrintConv => {
        'Embedded' => 'Eingebunden',
        'Not Embedded' => 'Nicht eingebunden',
      },
    },
   'ColorRepresentation' => 'Farbdarstellung',
   'ColorReproduction' => 'Farbreproduktion',
   'ColorSequence' => 'Farbreihenfolge',
   'ColorSpace' => {
      Description => 'Farbraum',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'ICC Profile' => 'ICC-Profil',
        'Monochrome' => 'Monochrom',
        'Natural sRGB' => 'Neutral sRGB',
        'Natural+ sRGB' => 'Neutral+ sRGB',
        'Uncalibrated' => 'Nicht festgelegt',
        'Undefined' => 'Nicht definiert',
      },
    },
   'ColorSpaceData' => 'Farbraum Daten',
   'ColorTempAsShot' => 'Farbtemperatur Aufnahme',
   'ColorTempAuto' => 'Farbtemperatur Auto',
   'ColorTempCloudy' => 'Farbtemperatur Bewölkt',
   'ColorTempCustom' => 'Farbtemperatur Benutzerdefiniert',
   'ColorTempCustom1' => 'Farbtemperatur Benutzerdefiniert 1',
   'ColorTempCustom2' => 'Farbtemperatur Benutzerdefiniert 2',
   'ColorTempDaylight' => 'Farbtemperatur Tageslicht',
   'ColorTempFlash' => 'Farbtemperatur Blitz',
   'ColorTempFlashData' => 'Farbtemperatur Blitzdaten',
   'ColorTempFluorescent' => 'Farbtemperatur Neonlicht',
   'ColorTempKelvin' => 'Farbtemperatur Kelvin',
   'ColorTempMeasured' => 'Farbtemperatur Messung',
   'ColorTempPC1' => 'Farbtemperatur PC1',
   'ColorTempPC2' => 'Farbtemperatur PC2',
   'ColorTempPC3' => 'Farbtemperatur PC3',
   'ColorTempShade' => 'Farbtemperatur Schatten',
   'ColorTempTungsten' => 'Farbtemperatur Glühbirne',
   'ColorTempUnknown' => 'Farbtemperatur Unbekannt',
   'ColorTempUnknown10' => 'Farbtemperatur Unbekannt 10',
   'ColorTempUnknown11' => 'Farbtemperatur Unbekannt 11',
   'ColorTempUnknown12' => 'Farbtemperatur Unbekannt 12',
   'ColorTempUnknown13' => 'Farbtemperatur Unbekannt 13',
   'ColorTempUnknown14' => 'Farbtemperatur Unbekannt 14',
   'ColorTempUnknown15' => 'Farbtemperatur Unbekannt 15',
   'ColorTempUnknown16' => 'Farbtemperatur Unbekannt 16',
   'ColorTempUnknown17' => 'Farbtemperatur Unbekannt 17',
   'ColorTempUnknown18' => 'Farbtemperatur Unbekannt 18',
   'ColorTempUnknown19' => 'Farbtemperatur Unbekannt 19',
   'ColorTempUnknown2' => 'Farbtemperatur Unbekannt 2',
   'ColorTempUnknown20' => 'Farbtemperatur Unbekannt 20',
   'ColorTempUnknown3' => 'Farbtemperatur Unbekannt 3',
   'ColorTempUnknown4' => 'Farbtemperatur Unbekannt 4',
   'ColorTempUnknown5' => 'Farbtemperatur Unbekannt 5',
   'ColorTempUnknown6' => 'Farbtemperatur Unbekannt 6',
   'ColorTempUnknown7' => 'Farbtemperatur Unbekannt 7',
   'ColorTempUnknown8' => 'Farbtemperatur Unbekannt 8',
   'ColorTempUnknown9' => 'Farbtemperatur Unbekannt 9',
   'ColorTemperature' => 'Farbtemperatur',
   'ColorTemperatureAdj' => 'Farbtemperaturkorrektur',
   'ColorTemperatureSetting' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
      },
    },
   'ColorTone' => 'Farbton',
   'ColorToneAdj' => 'Farbtonkorrektur',
   'ColorToneFaithful' => {
      Description => 'Farbton Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneLandscape' => {
      Description => 'Farbton Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneMonochrome' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneNeutral' => {
      Description => 'Farbton Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorTonePortrait' => {
      Description => 'Farbton Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneStandard' => {
      Description => 'Farbton Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneUnknown' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneUserDef1' => {
      Description => 'Farbton Benutzerdefiniert 1',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneUserDef2' => {
      Description => 'Farbton Benutzerdefiniert 2',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorToneUserDef3' => {
      Description => 'Farbton Benutzerdefiniert 3',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ColorTransform' => {
      Description => 'Farbtransformation',
      PrintConv => {
        'Unknown (RGB or CMYK)' => 'Unbekannt (RGB oder CMYK)',
      },
    },
   'ColorimetricReference' => 'Farbmetrische Referenz',
   'CommandDials' => {
      Description => 'Einstellräder',
      PrintConv => {
        'Reversed (Main Aperture, Sub Shutter)' => 'Vertauscht',
        'Standard (Main Shutter, Sub Aperture)' => 'Standard',
      },
    },
   'CommandDialsApertureSetting' => {
      Description => 'Einstellräder Blendeneinstellung',
      PrintConv => {
        'Aperture Ring' => 'Mit Blendenring',
        'Sub-command Dial' => 'Mit Einstellrad',
      },
    },
   'CommandDialsChangeMainSub' => {
      Description => 'Einstellräder Funktionsbelegung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'CommandDialsMenuAndPlayback' => {
      Description => 'Einstellräder Menüs und Wiedergabe',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'CommandDialsReverseRotation' => {
      Description => 'Einstellräder Auswahlrichtung',
      PrintConv => {
        'No' => 'Standard',
        'Yes' => 'Umgekehrt',
      },
    },
   'CommanderChannel' => 'Master-Steuerung Kanal',
   'CommanderGroupAManualOutput' => 'Master-Steuerung Gruppe A M Korr',
   'CommanderGroupAMode' => {
      Description => 'Master-Steuerung Gruppe A Modus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
      },
    },
   'CommanderGroupA_TTL-AAComp' => 'Master-Steuerung Gruppe A TTL/AA Korr',
   'CommanderGroupA_TTLComp' => 'Master-Steuerung Gruppe-A TTL-Korrektur',
   'CommanderGroupBManualOutput' => 'Master-Steuerung Gruppe B M Korr',
   'CommanderGroupBMode' => {
      Description => 'Master-Steuerung Gruppe B Modus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
      },
    },
   'CommanderGroupB_TTL-AAComp' => 'Master-Steuerung Gruppe B TTL/AA Korr',
   'CommanderGroupB_TTLComp' => 'Master-Steuerung Gruppe-B TTL-Korrektur',
   'CommanderInternalFlash' => {
      Description => 'Master-Steuerung Intgr. Blitz Modus',
      PrintConv => {
        'Manual' => 'Manuell',
        'Off' => 'Aus',
      },
    },
   'CommanderInternalManualOutput' => 'Master-Steuerung Intgr. Blitz M Korr',
   'CommanderInternalTTLComp' => 'Master-Steuerung Intgr. Blitz TTL Korr',
   'Comment' => 'Kommentar',
   'Comments' => 'Kommentare',
   'Compilation' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'ComponentBitDepth' => 'Komponente Farbtiefe',
   'ComponentsConfiguration' => 'Bedeutung jeder Komponente',
   'CompressedBitsPerPixel' => 'Bildkomprimierungsmodus',
   'CompressedImageSize' => 'Komprimierte Bildgröße',
   'Compression' => {
      Description => 'Komprimierungsschema',
      PrintConv => {
        'Bitfields' => 'Bitfelder',
        'JBIG B&W' => 'JBIG Schwarz-Weiß',
        'JBIG Color' => 'JBIG Farbe',
        'JPEG' => 'JPEG-Komprimierung',
        'JPEG (old-style)' => 'JPEG (alte Version)',
        'Kodak DCR Compressed' => 'Kodak DCR-Komprimierung',
        'Kodak KDC Compressed' => 'Kodak KDC-Komprimierung',
        'Next' => 'NeXT 2-Bit Kodierung',
        'Nikon NEF Compressed' => 'Nikon NEF-Komprimierung',
        'None' => 'Keines',
        'PNG' => 'PNG-Komprimierung',
        'Pentax PEF Compressed' => 'Pentax PEF-Komprimierung',
        'SGILog' => 'SGI 32-Bit Log Luminance Kodierung',
        'SGILog24' => 'SGI 24-Bit Log Luminance Kodierung',
        'Sony ARW Compressed' => 'Sony ARW-Komprimierung',
        'Thunderscan' => 'ThunderScan 4-Bit Kodierung',
        'Uncompressed' => 'Nicht komprimiert',
      },
    },
   'CompressionFactor' => 'Komprimierungsfaktor',
   'CompressionLevel' => 'Komprimierungsgrad',
   'CompressionRatio' => 'Komprimierungsrate',
   'CompressionType' => {
      Description => 'Komprimierungsschema',
      PrintConv => {
        'Little-endian, no compression' => 'Little-endian, keine Komprimierung',
        'None' => 'Keines',
      },
    },
   'ConditionalFEC' => 'Blitzbelichtungskorrektur',
   'ConnectionSpaceIlluminant' => 'Weißpunkt des Verbindungsfarbraums',
   'ConstrainedCropHeight' => 'Ausschnitt erzeugte Höhe',
   'ConstrainedCropWidth' => 'Ausschnitt erzeugte Breite',
   'Contact' => 'Kontakt',
   'ContentLocationCode' => 'Inhaltspositionscode',
   'ContentLocationName' => 'Inhaltspositionsname',
   'ContinuousBracketing' => {
      Description => 'Serienbild-Belichtungsreihe',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
      },
    },
   'ContinuousDrive' => {
      Description => 'Aufnahme-Modus',
      PrintConv => {
        'Continuous' => 'Serienaufnahme',
        'Movie' => 'Filmen',
        'Silent Single' => 'Stillleben',
        'Single' => 'Einzelbild',
      },
    },
   'ContinuousShootingSpeed' => {
      Description => 'Geschwindigkeit Reihenaufnahmen',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ContinuousShotLimit' => {
      Description => 'Limit Anzahl Reihenaufnahmen',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        '+1 (medium high)' => '+1 (Leicht erhöht)',
        '+2 (high)' => '+2 (Stark)',
        '+3 (very high)' => '+3 (Sehr hoch)',
        '-1 (medium low)' => '-1 (Leicht verringert)',
        '-2 (low)' => '-2 (Leicht)',
        '-3 (very low)' => '-3 (Sehr gering)',
        'Film Simulation' => 'Film-Simulation',
        'High' => 'Stark',
        'Low' => 'Leicht',
        'Med High' => 'Leicht erhöht',
        'Med Low' => 'Leicht verringert',
        'Medium High' => 'Mittel-Hoch',
        'Medium Low' => 'Mittel-Gering',
        'Very High' => 'Sehr hoch',
        'Very Low' => 'Sehr gering',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'Contrast2012' => 'Kontrast 2012',
   'ContrastAdj' => 'Kontrastkorrektur',
   'ContrastAdjustment' => 'Kontrastkorrektur',
   'ContrastCurve' => 'Kontrast-Kurve',
   'ContrastDetectAFArea' => 'AF-Bereich Kontrast gesteuert',
   'ContrastFaithful' => {
      Description => 'Kontrast Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastHighlightShadowAdj' => {
      Description => 'Kontrast helle Stellen',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ContrastLandscape' => {
      Description => 'Kontrast Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastMode' => {
      Description => 'Kontrast Modus',
      PrintConv => {
        'High' => 'Hoch',
        'High Dynamic' => 'Hoch dynamisch',
        'Low' => 'Niedrig',
        'Low Key' => 'Low-Key',
        'Medium High' => 'Mitte hoch',
        'Medium Low' => 'Mitte niedrig',
      },
    },
   'ContrastMonochrome' => {
      Description => 'Kontrast Monochrom',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastNeutral' => {
      Description => 'Kontrast Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastPortrait' => {
      Description => 'Kontrast Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastSetting' => 'Kontrasteinstellung',
   'ContrastShadow' => 'Kontrast dunkle Stellen',
   'ContrastStandard' => {
      Description => 'Kontrast Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastUnknown' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastUserDef1' => {
      Description => 'Kontrast Benutzerdefiniert 1',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastUserDef2' => {
      Description => 'Kontrast Benutzerdefiniert 2',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ContrastUserDef3' => {
      Description => 'Kontrast Benutzerdefiniert 3',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'Contributor' => 'Mitwirkender',
   'Contributors' => 'Mitwirkende',
   'ControlDialSet' => {
      PrintConv => {
        'Aperture' => 'Blende',
        'Shutter Speed' => 'Belichtungszeit',
      },
    },
   'ControlMode' => {
      Description => 'Steuerungsmethode',
      PrintConv => {
        'Camera Local Control' => 'Lokale Kamerasteuerung',
        'Computer Remote Control' => 'Kamerasteuerung per Computer',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ConversionLens' => {
      Description => 'Vorsatzlinse',
      PrintConv => {
        'Macro' => 'Makro',
        'Off' => 'Aus',
        'Telephoto' => 'Tele',
      },
    },
   'Converter' => 'Konverter',
   'Copyright' => 'Urheberrechtsvermerk',
   'CopyrightNotice' => 'Urheberrechtsvermerk',
   'CopyrightStatus' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'CoringValues' => 'Coring Werte',
   'Country' => 'Land',
   'Country-PrimaryLocationCode' => 'ISO-Ländercode',
   'Country-PrimaryLocationName' => 'Land',
   'CountryCode' => 'ISO-Ländercode',
   'Coverage' => 'Anwendungsbereich',
   'CreateDate' => 'Digitalisierungsdatum/-uhrzeit',
   'CreationDate' => 'Aufnahmedatum',
   'CreativeStyle' => {
      Description => 'Kreativmodus',
      PrintConv => {
        'Autumn' => 'Herbst',
        'Autumn Leaves' => 'Herbstlaub',
        'B&W' => 'Schwarz/Weiß',
        'Clear' => 'Klar',
        'Deep' => 'Tief',
        'Landscape' => 'Landschaft',
        'Light' => 'Hell',
        'Night View/Portrait' => 'Abendszene/Porträt',
        'Portrait' => 'Porträt',
        'Sunset' => 'Sonnenuntergang',
        'Vivid' => 'Lebhafte Farbe',
      },
    },
   'CreativeStyleSetting' => {
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Landscape' => 'Landschaft',
        'Portrait' => 'Porträt',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'Creator' => 'Ersteller',
   'CreatorAddress' => 'Ersteller - Adresse',
   'CreatorAppID' => {
      Description => 'Ersteller App ID',
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'CreatorAppVersion' => 'Ersteller App Version',
   'CreatorApplication' => 'Ersteller der Applikation',
   'CreatorCity' => 'Ersteller - Ort',
   'CreatorContactInfo' => 'Ersteller Kontaktinfo',
   'CreatorCountry' => 'Ersteller - Land',
   'CreatorPostalCode' => 'Ersteller - PLZ',
   'CreatorRegion' => 'Ersteller - Bundesland/Kanton',
   'CreatorTool' => 'Erstellertool',
   'CreatorWorkEmail' => 'Ersteller - E-Mail',
   'CreatorWorkTelephone' => 'Ersteller - Telefon',
   'CreatorWorkURL' => 'Ersteller - Webseite(n)',
   'Credit' => 'Anbieter',
   'CropActive' => {
      Description => 'Ausschnitt aktiviert',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'CropAngle' => 'Ausschnitt Winkel',
   'CropAspectRatio' => {
      Description => 'Ausschnitt Bildformat',
      PrintConv => {
        'A-size Landscape' => 'DIN A Querformat',
        'A-size Portrait' => 'DIN A Hochformat',
        'Circle' => 'Kreis',
        'Custom' => 'Benutzerdefiniert',
        'Free' => 'Frei',
        'Letter-size Landscape' => 'Letter Querformat',
        'Letter-size Portrait' => 'Letter Querformat',
      },
    },
   'CropBottom' => 'Ausschnitt Unten',
   'CropBottomMargin' => 'Ausschnitt Rand Unten',
   'CropCircleActive' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'CropHeight' => 'Ausschnitt Höhe',
   'CropHiSpeed' => 'Ausschnitt Highspeed',
   'CropLeft' => 'Ausschnitt Links',
   'CropLeftMargin' => 'Ausschnitt Rand Links',
   'CropRight' => 'Ausschnitt Rechts',
   'CropRightMargin' => 'Ausschnitt Rand Rechts',
   'CropRotation' => 'Ausschnitt Drehung',
   'CropTop' => 'Ausschnitt Oben',
   'CropTopMargin' => 'Ausschnitt Rand Oben',
   'CropUnit' => {
      Description => 'Ausschnitt Einheit',
      PrintConv => {
        'inches' => 'Zoll',
        'pixels' => 'Pixel',
      },
    },
   'CropUnits' => {
      Description => 'Ausschnitt Einheiten',
      PrintConv => {
        'inches' => 'Zoll',
        'pixels' => 'Pixel',
      },
    },
   'CropWidth' => 'Ausschnitt Breite',
   'CroppedImageHeight' => 'Ausschnitt Bildhöhe',
   'CroppedImageLeft' => 'Ausschnitt Bildanfang Links',
   'CroppedImageTop' => 'Ausschnitt Bildanfang Oben',
   'CroppedImageWidth' => 'Ausschnitt Bildbreite',
   'CurrentICCProfile' => 'Aktuelles ICC-Profile',
   'CurrentIPTCDigest' => 'Aktueller IPTC Kennwert',
   'Curves' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Custom1' => 'Benutzerdefiniert 1',
   'Custom2' => 'Benutzerdefiniert 2',
   'Custom3' => 'Benutzerdefiniert 3',
   'Custom4' => 'Benutzerdefiniert 4',
   'CustomLinear' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'CustomPictureStyleFileName' => 'Benutzer-Bildstil Dateiname',
   'CustomRendered' => {
      Description => 'Benutzerdefinierte Bildverarbeitung',
      PrintConv => {
        'Custom' => 'Benutzerdefinierter Prozess',
        'Normal' => 'Standard-Prozess',
      },
    },
   'D-LightingHQ' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'D-LightingHQSelected' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'D-LightingHS' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'D-RangeOptimizerMode' => {
      PrintConv => {
        'Manual' => 'Manuell',
        'Off' => 'Aus',
      },
    },
   'DECPosition' => {
      Description => 'DEC-Position',
      PrintConv => {
        'Contrast' => 'Kontrast',
        'Exposure' => 'Belichtung',
        'Saturation' => 'Sättigung',
      },
    },
   'DLOOn' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'DNGBackwardVersion' => 'DNG Versionskompatibilität',
   'DNGLensInfo' => 'DNG Objektiv-Informationen',
   'DNGVersion' => 'DNG-Version',
   'DOF' => 'Schärfentiefe',
   'DSPFirmwareVersion' => 'DSP-Firmware-Version',
   'DataCompressionMethod' => 'Daten Komprimierungsmethode',
   'DataDump' => 'Daten-Dump',
   'DataDump2' => 'Daten-Dump 2',
   'DataImprint' => {
      Description => 'Daten-Einblendung',
      PrintConv => {
        'MM/DD/HH:MM' => 'MM/TT/SS:MM',
        'None' => 'Keine',
        'YYYY/MM/DD' => 'JJJJ/MM/TT',
      },
    },
   'DataLength' => 'Datenlänge',
   'Date' => 'Datum',
   'DateCreated' => 'Erstellungsdatum',
   'DateDisplayFormat' => {
      Description => 'Datumsformat',
      PrintConv => {
        'D/M/Y' => 'Tag/Monat/Jahr',
        'M/D/Y' => 'Monat/Tag/Jahr',
        'Y/M/D' => 'Jahr/Monat/Tag',
      },
    },
   'DateSent' => 'Absendedatum',
   'DateStampMode' => {
      Description => 'Zeitstempel-Modus',
      PrintConv => {
        'Date' => 'Datum',
        'Off' => 'Aus',
      },
    },
   'DateTime' => 'Änderungsdatum',
   'DateTimeCreated' => 'Erstellungsdatum/-uhrzeit',
   'DateTimeDigitized' => 'Datum/Uhrzeit der Digitalisierung',
   'DateTimeOriginal' => 'Erstellungsdatum/-uhrzeit',
   'DateTimeStamp' => 'Datum Uhrzeitangabe',
   'DaylightSavings' => {
      Description => 'Sommerzeit',
      PrintConv => {
        'No' => 'Aus',
        'Yes' => 'Ein',
      },
    },
   'Declination' => 'Deklination',
   'DefaultCropOrigin' => 'System-Ausschnitt Beginn',
   'DefaultCropSize' => 'System-Ausschnitt Größe',
   'DefaultDisplayHeight' => 'Default Anzeigehöhe',
   'DefaultDisplayWidth' => 'Default Anzeigebreite',
   'DefaultEraseOption' => {
      Description => 'System-Löscheinstellung',
      PrintConv => {
        'Cancel selected' => 'Abbruch',
        'Erase selected' => 'Löschen',
      },
    },
   'DefaultImageColor' => 'System-Bildfarbe',
   'DefaultScale' => 'Systemmaß',
   'DeletedImageCount' => 'Anzahl gelöschter Bilder',
   'Description' => 'Beschreibung',
   'Destination' => 'Ziel',
   'DestinationCity' => 'Zielort',
   'DestinationCityCode' => 'Zielort-Code',
   'DestinationDST' => {
      Description => 'Zielort Sommerzeit (DST)',
      PrintConv => {
        'No' => 'Deaktiviert',
        'Yes' => 'Aktiviert',
      },
    },
   'DevelopmentDynamicRange' => 'Dynamikbereich Entwicklung',
   'DeviceAttributes' => 'Geräte-Eigenschaften',
   'DeviceManufacturer' => 'Gerätehersteller',
   'DeviceMfgDesc' => 'Gerätehersteller-Bezeichnung',
   'DeviceModel' => 'Geräte-Modell',
   'DeviceModelDesc' => 'Geräte-Modell-Bezeichnung',
   'DeviceSettingDescription' => 'Geräteeinstellung',
   'DialDirectionTvAv' => {
      Description => 'Drehung Wählrad bei Tv/Av',
      PrintConv => {
        'Reversed' => 'Umgekehrt',
      },
    },
   'DigitalCreationDate' => 'Digitalisierungsdatum',
   'DigitalCreationDateTime' => 'Digitalisierungsdatum/-uhrzeit',
   'DigitalCreationTime' => 'Digitalisierungszeit',
   'DigitalFilter01' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter02' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter03' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter04' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter05' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter06' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter07' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter08' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter09' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter10' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter11' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter12' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter13' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter14' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter15' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter16' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter17' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter18' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter19' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalFilter20' => {
      PrintConv => {
        'Color Filter' => 'Farbfilter',
        'Fisheye' => 'Fischauge',
        'High Contrast' => 'Hoher Kontrast',
      },
    },
   'DigitalGain' => 'Digitale Verstärkung',
   'DigitalZoom' => {
      Description => 'Digital-Zoom',
      PrintConv => {
        'Electronic magnification' => 'Elektronische Vergrößerung',
        'None' => 'Kein',
        'Off' => 'Aus',
        'Other' => 'Unbekannt',
      },
    },
   'DigitalZoomOn' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'DigitalZoomRatio' => 'Digitaler Zoom-Faktor',
   'Directory' => 'Verzeichnis',
   'DirectoryIndex' => 'Verzeichnis-Index',
   'DirectoryNumber' => 'Ordner-Nummer',
   'Disclaimer' => 'Haftungsbeschränkung',
   'DisplayAllAFPoints' => {
      Description => 'Anzeige aller AF-Punkte',
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'DisplayAperture' => 'Angezeigte Blende',
   'DisplayHeight' => 'Anzeigehöhe',
   'DisplayUnit' => {
      PrintConv => {
        'Pixels' => 'Pixel',
        'inches' => 'Zoll',
      },
    },
   'DisplayUnits' => {
      Description => 'Anzeigeeinheit',
      PrintConv => {
        'inches' => 'Zoll',
        'meters' => 'Meter',
      },
    },
   'DisplayXResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (Mikrometer)',
      },
    },
   'DisplayYResolutionUnit' => {
      PrintConv => {
        'um' => 'µm (Mikrometer)',
      },
    },
   'DisplayedUnitsX' => {
      Description => 'Einheit der horiz. Auflösung',
      PrintConv => {
        'inches' => 'Zoll',
      },
    },
   'DisplayedUnitsY' => {
      Description => 'Einheit der vert. Auflösung',
      PrintConv => {
        'inches' => 'Zoll',
      },
    },
   'DistortionControl' => {
      Description => 'Verzeichnungskontrolle',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'DistortionCorrection' => {
      Description => 'Verzeichnungskorrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'DistortionCorrection2' => {
      Description => 'Verzeichnungskorrektur 2',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'DistortionCorrectionOn' => 'Verzeichnungskorrektur Ein',
   'DistortionN' => 'Verzeichnung N',
   'DistortionParam02' => 'Verzeichnungsparameter 02',
   'DistortionParam04' => 'Verzeichnungsparameter 04',
   'DistortionParam08' => 'Verzeichnungsparameter 08',
   'DistortionParam09' => 'Verzeichnungsparameter 09',
   'DistortionParam11' => 'Verzeichnungsparameter 11',
   'DistortionScale' => 'Verzeichnungsausmaß',
   'DistortionVersion' => 'Verzeichnung Version',
   'DjVuVersion' => 'DjVu-Version',
   'DocSecurity' => {
      Description => 'Dokumentensicherheit',
      PrintConv => {
        'Locked for annotations' => 'Gesperrt für Anmerkungen',
        'None' => 'Keine',
        'Password protected' => 'Passwort geschützt',
        'Read-only enforced' => 'Nur Lesen - erzwungen',
        'Read-only recommended' => 'Nur Lesen - vorgeschlagen',
      },
    },
   'Document' => 'Dokument',
   'DocumentHistory' => 'Historie des Dokuments',
   'DocumentName' => 'Dokumentenname',
   'DocumentNotes' => 'Notizen zum Dokument',
   'DriveMode' => {
      Description => 'Aufnahmeart',
      PrintConv => {
        '10 s Timer' => 'Selbstauslöser 10 s',
        '2 s Timer' => 'Selbstauslöser 2 s',
        'Auto Bracket' => 'Belichtungsreihe',
        'Bracketing' => 'Belichtungsreihe',
        'Continuous' => 'Serienaufnahme',
        'Continuous (Lo)' => 'Serienaufnahme (Niedrig)',
        'Continuous Bracketing' => 'Serienbild-Belichtungsreihe',
        'Continuous Exposure Bracketing' => 'Serienaufnahme Belichtungsreihe',
        'Continuous High' => 'Serienaufnahme (Hi)',
        'Continuous Low' => 'Serienaufnahme Niedrig',
        'Continuous Shooting' => 'Serienaufnahme',
        'HS continuous' => 'High-Speed Serienbild',
        'Mirror Lock-up' => 'Spiegel hochgeklappt',
        'Multi Shot' => 'Serienaufnahme',
        'Multiple Exposure' => 'Mehrfachbelichtung',
        'No Timer' => 'Ohne Selbstauslöser',
        'Off' => 'Aus',
        'Remote Control' => 'Fernauslöser',
        'Remote Control (3 s delay)' => 'Fernauslöser (3 Sek. Verzögerung)',
        'Self-Timer 2 sec' => 'Selbstauslöser 2 s',
        'Self-timer' => 'Selbstauslöser',
        'Self-timer (12 s)' => 'Selbstauslöser (12 Sek.)',
        'Self-timer (2 s)' => 'Selbstauslöser (2 Sek.)',
        'Self-timer 10 sec' => 'Selbstauslöser 10 s',
        'Self-timer 2 sec, Mirror Lock-up' => 'Selbstauslöser 2 s, Spiegel hochgeklappt',
        'Self-timer Operation' => 'Selbstauslöser',
        'Shutter Button' => 'Kamera-Auslöser',
        'Single' => 'Einzelbild',
        'Single Exposure' => 'Einzelbelichtung',
        'Single Frame' => 'Einzelbild',
        'Single Shot' => 'Einzelbild',
        'Single-Frame Bracketing' => 'Einzelbild-Belichtungsreihe',
        'Single-frame' => 'Einzelbild',
        'Single-frame Bracketing' => 'Einzelbild-Belichtungsreihe',
        'Single-frame Exposure Bracketing' => 'Einzelbild Belichtungsreihe',
        'Single-frame Shooting' => 'Einzelbild',
        'UHS continuous' => 'Ultra High-Speed Serienbild',
        'White Balance Bracketing' => 'Weißabgleichs-Belichtungsreihe',
        'White Balance Bracketing High' => 'Weißabgleich-Belichtungsreihe Hoch',
        'White Balance Bracketing Low' => 'Weißabgleich-Belichtungsreihe Niedrig',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'DriveMode2' => {
      Description => 'Mehrfachbelichtung',
      PrintConv => {
        'Continous Bracketing Low' => 'Serienaufnahme Belichtungsreihe Niedrig',
        'Continuous' => 'Serienaufnahme',
        'Continuous (Lo)' => 'Serienaufnahme (Niedrig)',
        'Continuous Bracketing' => 'Serienaufnahme Belichtungsreihe',
        'Continuous Bracketing 0.3 EV' => 'Serienaufnahme Belichtungsreihe 0.3 EV',
        'Continuous Bracketing 0.7 EV' => 'Serienaufnahme Belichtungsreihe 0.7 EV',
        'Continuous Bracketing High' => 'Serienaufnahme Belichtungsreihe Hoch',
        'Continuous High' => 'Serienaufnahme Hoch',
        'Continuous Low' => 'Serienaufnahme Niedrig',
        'Exposure Bracket' => 'Belichtungsreihe',
        'Mirror Lock-up' => 'Spiegel hochgeklappt',
        'Multiple Exposure' => 'Mehrfachbelichtung',
        'Remote Control' => 'Fernauslöser',
        'Remote Control (3 s delay)' => 'Fernauslöser (3 s verzögert)',
        'Self-timer (12 s)' => 'Selbstauslöser (12 s)',
        'Self-timer (2 s)' => 'Selbstauslöser (2 s)',
        'Self-timer 10 sec' => 'Selbstauslöser 10 s',
        'Self-timer 2 sec' => 'Selbstauslöser 2 s',
        'Self-timer 2 sec, Mirror Lock-up' => 'Selbstauslöser 2 s, Spiegel hochgeklappt',
        'Single Frame' => 'Einzelbild',
        'Single-frame' => 'Einzelbildaufnahme',
        'Single-frame Bracketing' => 'Einzelbild Belichtungsreihe',
        'Single-frame Bracketing High' => 'Einzelbild Belichtungsreihe Hoch',
        'Single-frame Bracketing Low' => 'Einzelbild Belichtungsreihe Niedrig',
        'White Balance Bracketing High' => 'Weißabgleich Belichtungsreihe Hoch',
        'White Balance Bracketing Low' => 'Weißabgleich Belichtungsreihe Niedrig',
      },
    },
   'DriveModeSetting' => {
      Description => 'Aufnahmeart Einstellung',
      PrintConv => {
        'Continuous High' => 'Serienaufnahme Hoch',
        'Continuous Low' => 'Serienaufnahme Niedrig',
        'Self-timer 10 sec' => 'Selbstauslöser 10 s',
        'Self-timer 2 sec, Mirror Lock-up' => 'Selbstauslöser 2 s, Spiegel hochgeklappt',
        'Single Frame' => 'Einzelbild',
      },
    },
   'DriveType' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'Duration' => 'Dauer',
   'DustRemovalData' => 'Dunstentfernungsdaten',
   'DynamicAFArea' => {
      Description => 'Dynamisches AF-Messfeld',
      PrintConv => {
        '21 Points' => '21 Messfelder',
        '51 Points' => '51 Messfelder',
        '51 Points (3D-tracking)' => '51 Messfelder (3D-Tracking)',
        '9 Points' => '9 Messfelder',
      },
    },
   'DynamicRange' => {
      Description => 'Dynamikbereich',
      PrintConv => {
        'Wide' => 'Weit',
      },
    },
   'DynamicRangeExpansion' => {
      Description => 'Dynamikbereich-Erweiterung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'DynamicRangeOptimizer' => {
      Description => 'Dynamikbereich-Optimierung',
      PrintConv => {
        'Advanced Auto' => 'Erw. Automatik',
        'Advanced Lv1' => 'Erw. Stufe 1',
        'Advanced Lv2' => 'Erw. Stufe 2',
        'Advanced Lv3' => 'Erw. Stufe 3',
        'Advanced Lv4' => 'Erw. Stufe 4',
        'Advanced Lv5' => 'Erw. Stufe 5',
        'Auto' => 'Automatisch',
        'Off' => 'Aus',
      },
    },
   'DynamicRangeOptimizerBracket' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
      },
    },
   'DynamicRangeOptimizerMode' => {
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'DynamicRangeSetting' => {
      Description => 'Dynamikbereich-Einstellungen',
      PrintConv => {
        'Film Simulation' => 'Film-Simulation',
        'Wide1 (230%)' => 'Weit1 (230%)',
        'Wide2 (400%)' => 'Weit2 (400%)',
      },
    },
   'E-DialInProgram' => {
      PrintConv => {
        'Tv or Av' => 'Tv oder Av',
      },
    },
   'ETTLII' => {
      PrintConv => {
        'Average' => 'Integralmessung',
        'Evaluative' => 'Mehrfeldmessung',
      },
    },
   'EVStepInfo' => 'EV-Schritte Information',
   'EVStepSize' => {
      Description => 'Belichtungswerte',
      PrintConv => {
        '1/2 EV' => '1/2 LW',
        '1/3 EV' => '1/3 LW',
      },
    },
   'EVSteps' => {
      Description => 'LW-Schritte',
      PrintConv => {
        '1/2 EV Steps' => '1/2 LW-Schritte',
        '1/3 EV Steps' => '1/3 LW-Schritte',
      },
    },
   'EasyExposureComp' => 'Easy Belichtungskorrektur',
   'EasyExposureCompensation' => {
      Description => 'Easy Belichtungskorrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (auto reset)' => 'Einstellrad (Reset)',
      },
    },
   'EasyMode' => {
      Description => 'Easy-Modus',
      PrintConv => {
        'Beach' => 'Strand',
        'Black & White' => 'Schwarz/Weiß',
        'Blur Reduction' => 'Unschärfereduktion',
        'Color Accent' => 'Farbton',
        'Color Swap' => 'Farbwechsel',
        'Digital Macro' => 'Digitales Makro',
        'Fireworks' => 'Feuerwerk',
        'Foliage' => 'Laub',
        'Full auto' => 'Vollautomatisch',
        'Indoor' => 'Innenaufnahme',
        'Kids & Pets' => 'Kinder & Tiere',
        'Landscape' => 'Landschaft',
        'Live View Control' => 'Live View Kontrolle',
        'Low Light' => 'Wenig Licht',
        'Low Light 2' => 'Wenig Licht 2',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Night' => 'Nachtszene',
        'Night Scene' => 'Nachtszene',
        'Night Snapshot' => 'Nacht Schnappschuss',
        'Pan focus' => 'Pan Fokus',
        'Portrait' => 'Porträt',
        'Snow' => 'Schnee',
        'Sports' => 'Sport',
        'Sunset' => 'Sonnenuntergang',
        'Super Macro' => 'Super-Makro',
        'Super Macro 2' => 'Super Makro 2',
        'Underwater' => 'Unterwasser',
        'Zoom Blur' => 'Zoom Unschärfe',
      },
    },
   'EdgeNoiseReduction' => {
      Description => 'Rauschunterdrückung Ecken',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'EditStatus' => 'Bearbeitungsstatus',
   'Edition' => 'Ausgabe',
   'EditorialUpdate' => 'Redaktionelle Überarbeitung',
   'EffectiveLV' => 'Effektiver LW',
   'EffectiveMaxAperture' => 'Effektiv größte Blende',
   'Email' => 'E-Mail',
   'EmbeddedImage' => 'Eingebettetes Bild',
   'EmbeddedImageByteOrder' => 'Eingebettetes Bild Bytereihenfolge',
   'EmbeddedImageHeight' => 'Eingebettetes Bild Bildhöhe',
   'EmbeddedImageType' => 'Eingebettetes Bild Bildtyp',
   'EmbeddedImageWidth' => 'Eingebettetes Bild Bildbreite',
   'Emphasis' => {
      PrintConv => {
        'None' => 'Keine',
        'reserved' => 'reserviert',
      },
    },
   'Encoding' => {
      PrintConv => {
        'Unknown -' => 'Unbekannt -',
      },
    },
   'EncodingProcess' => 'JPEG-Kodierung Prozess',
   'Encryption' => 'Verschlüsselung',
   'EnhanceDarkTones' => {
      Description => 'Dunkle Stellen aufhellen',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Enhancement' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Off' => 'Aus',
        'Red' => 'Rot',
        'Underwater' => 'Unterwasser',
      },
    },
   'EnvelopePriority' => {
      Description => 'Priorität',
      PrintConv => {
        '0 (reserved)' => '0 (reserviert)',
        '1 (most urgent)' => '1 (sehr dringend)',
        '5 (normal urgency)' => '5 (normale Dringlichkeit)',
        '8 (least urgent)' => '8 (geringe Wichtigkeit)',
        '9 (user-defined priority)' => '9 (benutzerdefinierte Priorität)',
      },
    },
   'EnvelopeRecordVersion' => 'IPTC-Modell-1-Version',
   'EpsonImageHeight' => 'Epson-Bildhöhe',
   'EpsonImageWidth' => 'Epson-Bildbreite',
   'Equipment' => 'Equipment-IFD-Zeiger',
   'EquipmentVersion' => 'Equipment-Version',
   'Error' => 'Fehler',
   'ExifByteOrder' => 'Exif Byte-Reihenfolge',
   'ExifCameraInfo' => 'Exif Kamerainformationen',
   'ExifImageHeight' => 'Exif-Bildhöhe',
   'ExifImageWidth' => 'Exif-Bildbreite',
   'ExifOffset' => 'Exif IFD-Zeiger',
   'ExifToolVersion' => 'ExifTool-Version',
   'ExifUnicodeByteOrder' => 'Exif Unicode Byte-Reihenfolge',
   'ExifVersion' => 'Exif-Version',
   'ExitPupilPosition' => 'Austrittspupillenposition',
   'ExpandFilm' => 'Erweitert Film',
   'ExpandFilterLens' => 'Erweitert Filterlinse',
   'ExpandFlashLamp' => 'Erweitert Blitzlicht',
   'ExpandLens' => 'Erweitert Objektiv',
   'ExpandScanner' => 'Erweitert Scanner',
   'ExpandSoftware' => 'Erweitert Software',
   'ExpirationDate' => 'Ablaufdatum',
   'ExpirationTime' => 'Ablaufzeit',
   'Exposure' => 'Belichtung',
   'Exposure2012' => 'Belichtung 2012',
   'ExposureAdj' => 'Belichtungskorrektur',
   'ExposureAdj2' => 'Belichtungskorrektur 2',
   'ExposureAdjust' => 'Belichtungskorrektur',
   'ExposureBias' => 'Belichtungskorrekturwert',
   'ExposureBracketShotNumber' => 'Belichtungsreihen-Bildnummer',
   'ExposureBracketStepSize' => 'Belichtungsreihen-Stufenabstand',
   'ExposureBracketValue' => 'Belichtungsreihenwert',
   'ExposureCompStepSize' => {
      Description => 'Belichtungskorrekturstufe',
      PrintConv => {
        '1 EV' => '1 LW',
        '1/2 EV' => '1/2 LW',
        '1/3 EV' => '1/3 LW',
      },
    },
   'ExposureCompensation' => 'Belichtungskorrektur',
   'ExposureCompensation2' => 'Belichtungskorrektur 2',
   'ExposureCompensationMode' => 'Belichtungskorrekturmodus',
   'ExposureControlStepSize' => {
      Description => 'Belichtungswert',
      PrintConv => {
        '1 EV' => '1 LW',
        '1/2 EV' => '1/2 LW',
        '1/3 EV' => '1/3 LW',
      },
    },
   'ExposureDelayMode' => {
      Description => 'Spiegelvorauslösung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ExposureDifference' => 'Belichtungsabweichung',
   'ExposureIndex' => 'Belichtungsindex',
   'ExposureLevelIncrements' => {
      Description => 'Belichtungswert',
      PrintConv => {
        '1-stop set, 1/3-stop comp.' => '1-Blende, 1/3-Blendenkompensation',
        '1/2 Stop' => '1/2 LW',
        '1/2-stop set, 1/2-stop comp.' => '1/2-Blende, 1/2-Blendenkompensation',
        '1/3 Stop' => '1/3 LW',
        '1/3-stop set, 1/3-stop comp.' => '1/3-Blende, 1/3-Blendenkompensation',
      },
    },
   'ExposureMode' => {
      Description => 'Belichtungsmodus',
      PrintConv => {
        'Anti Motion Blur' => 'Verwackelungsschutz',
        'Aperture Priority' => 'Blendenpriorität',
        'Aperture-priority AE' => 'Blendenpriorität',
        'Auto' => 'Automatische Belichtung',
        'Auto bracket' => 'Belichtungsreihe',
        'Auto?' => 'Automatisch?',
        'Backlight Correction HDR' => 'Hintergrundbeleuchtung HDR Korrektur',
        'Beach' => 'Strand',
        'Bulb' => 'Bulb-Modus',
        'Fireworks' => 'Feuerwerk',
        'Food' => 'Lebensmittel',
        'High Sensitivity' => 'Hohe Empfindlichkeit',
        'Landscape' => 'Landschaft',
        'Macro' => 'Makro',
        'Manual' => 'Manuelle Belichtung',
        'Night Scene / Twilight' => 'Nachtszene / Dämmerung',
        'Night View/Portrait' => 'Abendszene/Porträt',
        'Pet' => 'Haustiere',
        'Portrait' => 'Porträt',
        'Program' => 'Programmautomatik',
        'Program AE' => 'Programmautomatik',
        'Program-shift' => 'Programm-Shift',
        'Program-shift A' => 'Programmverschiebung A',
        'Program-shift S' => 'Programmverschiebung S',
        'Shutter Priority' => 'Verschlusspriorität',
        'Shutter speed priority AE' => 'Verschlusspriorität',
        'Snow' => 'Schnee',
        'Sports' => 'Sport',
        'Sunset' => 'Sonnenuntergang',
        'Twilight Portrait' => 'Dämmerung Portät',
        'Underwater' => 'Unterwasser',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ExposureModeInManual' => {
      Description => 'Belichtungsmodus bei manueller Belichtung',
      PrintConv => {
        'Center-weighted average' => 'Mittenbetont',
        'Evaluative metering' => 'Mehrfeldmessung',
        'Partial metering' => 'Teilbild',
        'Specified metering mode' => 'Spezifizierte Messmethode',
        'Spot metering' => 'Spotmessung',
      },
    },
   'ExposureProgram' => {
      Description => 'Belichtungsprogramm',
      PrintConv => {
        'Action (High speed)' => 'Kreativ-Programm (ausgerichtet auf schnelle Verschlussgeschwindigkeit)',
        'Anti Motion Blur' => 'Verwackelungsschutz',
        'Aperture Priority' => 'Blendenpriorität',
        'Aperture-priority AE' => 'Blendenpriorität',
        'Creative (Slow speed)' => 'Kreativ-Programm (ausgerichtet auf Schärfentiefe)',
        'Landscape' => 'Landschaft',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Night Portrait' => 'Nachtporträt',
        'Night view' => 'Abendszene',
        'Night view/portrait' => 'Abendszene/Porträt',
        'Not Defined' => 'Nicht definiert',
        'Portrait' => 'Porträt',
        'Program' => 'Programmautomatik',
        'Program AE' => 'Programmautomatik',
        'Shutter Priority' => 'Verschlusspriorität',
        'Shutter speed priority AE' => 'Verschlusspriorität',
        'Sports' => 'Sport',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'ExposureTime' => 'Belichtungsdauer',
   'ExposureTime2' => 'Belichtungsdauer 2',
   'ExposureValue' => 'Belichtungsdauer',
   'ExposureWarning' => {
      Description => 'Belichtungswarnung',
      PrintConv => {
        'Bad exposure' => 'Schlechte Belichtung',
        'Good' => 'OK',
      },
    },
   'ExtendedWBDetect' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Extender' => {
      Description => 'Konverter',
      PrintConv => {
        'None' => 'Keiner',
      },
    },
   'ExtenderFirmwareVersion' => 'Konverter-Firmware-Version',
   'ExtenderMake' => 'Konverterhersteller',
   'ExtenderModel' => 'Konverter-Modell',
   'ExtenderSerialNumber' => 'Konverter-Seriennummer',
   'ExtenderStatus' => {
      Description => 'Status Telekonverter',
      PrintConv => {
        'Attached' => 'Angesetzt',
        'Not attached' => 'Nicht angesetzt',
        'Removed' => 'Entfernt',
      },
    },
   'ExternalFlash' => {
      Description => 'Externer Blitz',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ExternalFlashAE1' => 'Externer Blitz AE1',
   'ExternalFlashAE1_0' => 'Externer Blitz AE1 0',
   'ExternalFlashAE2' => 'Externer Blitz AE2',
   'ExternalFlashAE2_0' => 'Externer Blitz AE2 0',
   'ExternalFlashBounce' => {
      Description => 'Externer Blitz - Bounce',
      PrintConv => {
        'Bounce' => 'Mit Bounce',
        'Direct' => 'Direkt',
        'No' => 'Nein',
        'Yes' => 'Ja',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ExternalFlashCompensation' => 'Externe Blitzbelichtungskorrektur',
   'ExternalFlashExposureComp' => {
      Description => 'Belichtungskorrektur des externen Blitzgeräts',
      PrintConv => {
        '-0.5' => '-0.5 LW',
        '-1.0' => '-1.0 LW',
        '-1.5' => '-1.5 LW',
        '-2.0' => '-2.0 LW',
        '-2.5' => '-2.5 LW',
        '-3.0' => '-3.0 LW',
        '0.0' => '0.0 LW',
        '0.5' => '0.5 LW',
        '1.0' => '1.0 LW',
        'n/a' => 'Nicht gesetzt  (Aus oder Auto-Modi)',
        'n/a (Manual Mode)' => 'Nicht gesetzt (Manueller Modus)',
      },
    },
   'ExternalFlashFirmware' => {
      Description => 'Externer Blitz Firmware',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ExternalFlashFlags' => {
      Description => 'Externer Blitz Flags',
      PrintConv => {
        'Fired' => 'Ausgelöst',
      },
    },
   'ExternalFlashGValue' => 'Externer Blitz Leitzahl',
   'ExternalFlashGuideNumber' => 'Leitzahl des externen Blitzgeräts',
   'ExternalFlashMode' => {
      Description => 'Slave-Blitz-Messfeld 3',
      PrintConv => {
        'Off' => 'Aus',
        'On, Auto' => 'Ein, Auto',
        'On, Contrast-control Sync' => 'Ein, Kontrast-Steuerungs-Synchronisation',
        'On, Flash Problem' => 'Ein, Blitzproblem?',
        'On, High-speed Sync' => 'Ein, High-Speed-Synchronisation',
        'On, Manual' => 'Ein, Manuell',
        'On, P-TTL Auto' => 'Ein, P-TTL-Blitzautomatik',
        'On, Wireless' => 'Ein, Drahtlos',
        'On, Wireless, High-speed Sync' => 'Ein, Drahtlos, High-Speed-Synchronisation',
        'n/a - Off-Auto-Aperture' => 'K/A - Blendenring nicht auf A',
      },
    },
   'ExternalFlashZoom' => 'Externer Blitz-Zoom',
   'ExternalSensorBrightnessValue' => 'Externer Sensor Helligkeitswert',
   'ExtraSamples' => 'Zusätzliche Komponenten',
   'FEMicroadjustment' => {
      Description => 'FE Feinabstimmung',
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'FNumber' => 'F-Wert',
   'FOV' => 'Sichtfeld',
   'Face0Position' => 'Position, 0. Gesicht',
   'Face10Position' => 'Position, 10. Gesicht',
   'Face10Size' => 'Größe, 10. Gesicht',
   'Face11Position' => 'Position, 11. Gesicht',
   'Face11Size' => 'Größe, 11. Gesicht',
   'Face12Position' => 'Position, 12. Gesicht',
   'Face12Size' => 'Größe, 12. Gesicht',
   'Face13Position' => 'Position, 13. Gesicht',
   'Face13Size' => 'Größe, 13. Gesicht',
   'Face14Position' => 'Position, 14. Gesicht',
   'Face14Size' => 'Größe, 14. Gesicht',
   'Face15Position' => 'Position, 15. Gesicht',
   'Face15Size' => 'Größe, 15. Gesicht',
   'Face16Position' => 'Position, 16. Gesicht',
   'Face16Size' => 'Größe, 16. Gesicht',
   'Face17Position' => 'Position, 17. Gesicht',
   'Face17Size' => 'Größe, 17. Gesicht',
   'Face18Position' => 'Position, 18. Gesicht',
   'Face18Size' => 'Größe, 18. Gesicht',
   'Face19Position' => 'Position, 19. Gesicht',
   'Face19Size' => 'Größe, 19. Gesicht',
   'Face1Position' => 'Position, 1. Gesicht',
   'Face1Size' => 'Größe, 1. Gesicht',
   'Face20Position' => 'Position, 20. Gesicht',
   'Face20Size' => 'Größe, 20. Gesicht',
   'Face21Position' => 'Position, 21. Gesicht',
   'Face21Size' => 'Größe, 21. Gesicht',
   'Face22Position' => 'Position, 22. Gesicht',
   'Face22Size' => 'Größe, 22. Gesicht',
   'Face23Position' => 'Position, 23. Gesicht',
   'Face23Size' => 'Größe, 23. Gesicht',
   'Face24Position' => 'Position, 24. Gesicht',
   'Face24Size' => 'Größe, 24. Gesicht',
   'Face25Position' => 'Position, 25. Gesicht',
   'Face25Size' => 'Größe, 25. Gesicht',
   'Face26Position' => 'Position, 26. Gesicht',
   'Face26Size' => 'Größe, 26. Gesicht',
   'Face27Position' => 'Position, 27. Gesicht',
   'Face27Size' => 'Größe, 27. Gesicht',
   'Face28Position' => 'Position, 28. Gesicht',
   'Face28Size' => 'Größe, 28. Gesicht',
   'Face29Position' => 'Position, 29. Gesicht',
   'Face29Size' => 'Größe, 29. Gesicht',
   'Face2Position' => 'Position, 2. Gesicht',
   'Face2Size' => 'Größe, 2. Gesicht',
   'Face30Position' => 'Position, 30. Gesicht',
   'Face30Size' => 'Größe, 30. Gesicht',
   'Face31Position' => 'Position, 31. Gesicht',
   'Face31Size' => 'Größe, 31. Gesicht',
   'Face32Position' => 'Position, 32. Gesicht',
   'Face32Size' => 'Größe, 32. Gesicht',
   'Face3Position' => 'Position, 3. Gesicht',
   'Face3Size' => 'Größe, 3. Gesicht',
   'Face4Position' => 'Position, 4. Gesicht',
   'Face4Size' => 'Größe, 4. Gesicht',
   'Face5Position' => 'Position, 5. Gesicht',
   'Face5Size' => 'Größe, 5. Gesicht',
   'Face6Position' => 'Position, 6. Gesicht',
   'Face6Size' => 'Größe, 6. Gesicht',
   'Face7Position' => 'Position, 7. Gesicht',
   'Face7Size' => 'Größe, 7. Gesicht',
   'Face8Position' => 'Position, 8. Gesicht',
   'Face8Size' => 'Größe, 8. Gesicht',
   'Face9Position' => 'Position, 9. Gesicht',
   'Face9Size' => 'Größe, 9. Gesicht',
   'FaceDetect' => {
      Description => 'Gesichtserkennung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FaceDetectArea' => 'Gesichtserkennung Bereich',
   'FaceDetectFrameSize' => 'Gesichtserkennung Bereichsgröße',
   'FaceInfoUnknown' => 'Gesichterinformation Unbekannt',
   'FaceOrientation' => {
      Description => 'Ausrichtung Gesichtserkennung',
      PrintConv => {
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
      },
    },
   'FacePositions' => 'Positionen der Gesichter',
   'FacesDetected' => {
      Description => 'Gesichter erkannt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FacesRecognized' => 'Gesichter erkannt',
   'FastSeek' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'FaxProfile' => {
      Description => 'Faxprofil',
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'FaxRecvParams' => 'Fax-Empfangsparameter',
   'FaxRecvTime' => 'Fax-Empfangszeit',
   'FaxSubAddress' => 'Fax-Sub-Adresse',
   'FileAccessDate' => 'Datum/Uhrzeit des letzten Dateizugriffs',
   'FileCreateDate' => 'Datum/Uhrzeit der Dateierstellung',
   'FileDescription' => 'Dateibeschreibung',
   'FileExtension' => 'Dateiendung',
   'FileFormat' => 'Dateiformat',
   'FileIndex' => 'Datei-Index',
   'FileInfo' => 'Datei-Informationen',
   'FileInfoVersion' => 'Datei-Informationen-Version',
   'FileInodeChangeDate' => 'Datum/Uhrzeit der letzten Inode-Änderung',
   'FileModifyDate' => 'Datum/Uhrzeit der Dateiänderung',
   'FileName' => 'Dateiname',
   'FileNumber' => 'Dateinummer',
   'FileNumberMemory' => {
      Description => 'Dateinummernspeicher',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FileNumberSequence' => {
      Description => 'Nummernspeicher',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FilePermissions' => 'Dateiberechtigungen',
   'FileSequence' => 'Dateireihenfolge',
   'FileSize' => 'Dateigröße',
   'FileSource' => {
      Description => 'Dateiquelle',
      PrintConv => {
        'Digital Camera' => 'Digital-Kamera',
        'Film Scanner' => 'Film-Scanner',
        'Reflection Print Scanner' => 'Scanner',
        'Sigma Digital Camera' => 'Sigma Digital-Kamera',
      },
    },
   'FileType' => 'Dateityp',
   'FileTypeDescription' => 'Dateityp Beschreibung',
   'FileVersion' => 'Dateiformatversion',
   'Filename' => 'Dateiname',
   'FillFlashAutoReduction' => {
      Description => 'E-TTL II-Automatikblitz-System',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'FilmMode' => {
      Description => 'Film-Modus',
      PrintConv => {
        'F1/Studio Portrait' => 'F1/Studio-Porträt',
        'F1a/Studio Portrait Enhanced Saturation' => 'F1a/Studio-Porträt Erweiterte Sättigung',
        'F1b/Studio Portrait Smooth Skin Tone (ASTIA)' => 'F1b/Studio-Porträt Weiche Hauttöne',
        'F1b/Studio Portrait Smooth Skin Tone (Astia)' => 'F1b/Studio-Porträt Weiche Hauttöne',
        'F1c/Studio Portrait Increased Sharpness' => 'F1c/Studio-Porträt Erhöhte Schärfe',
        'F3/Studio Portrait Ex' => 'F3/Studio Porträt Ex',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilmType' => 'Filmtyp',
   'FilterEffect' => {
      Description => 'Filtereffekt',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Off' => 'Aus',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectFaithful' => {
      Description => 'Filtereffekt Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectLandscape' => {
      Description => 'Filtereffekt Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectMonochrome' => {
      Description => 'Filtereffekt Monochrom',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectNeutral' => {
      Description => 'Filtereffekt Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectPortrait' => {
      Description => 'Filtereffekt Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectStandard' => {
      Description => 'Filtereffekt Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectUnknown' => {
      Description => 'Filtereffekt Unbekannt',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectUserDef1' => {
      Description => 'Filtereffekt Benutzerdefiniert 1',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectUserDef2' => {
      Description => 'Filtereffekt Benutzerdefineirt 2',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FilterEffectUserDef3' => {
      Description => 'Filtereffekt Benutzerdefiniert 3',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FinderDisplayDuringExposure' => {
      Description => 'Sucheranzeige bei Belichtung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FineSharpness' => {
      Description => 'Detail-Schärfe',
      PrintConv => {
        'Extra fine' => 'Extra',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FineTuneOptCenterWeighted' => 'Feinabstimmung Mittenbetonte Messung',
   'FineTuneOptMatrixMetering' => 'Feinabstimmung Matrixmessung',
   'FineTuneOptSpotMetering' => 'Feinabstimmung Spotmessung',
   'FirmwareRevision' => 'Firmware-Revision',
   'FirmwareRevision2' => 'Firmware-Revision 2',
   'FirmwareVersion' => 'Firmware-Version',
   'FixtureIdentifier' => 'Kennzeichnung',
   'Flash' => {
      Description => 'Blitzmodus',
      PrintConv => {
        'Auto, Did not fire' => 'Blitz wurde nicht ausgelöst, Automodus',
        'Auto, Did not fire, Red-eye reduction' => 'Blitz wurde nicht ausgelöst, Rote-Augen-Reduzierung',
        'Auto, Fired' => 'Blitz wurde ausgelöst, Automodus',
        'Auto, Fired, Red-eye reduction' => 'Blitz wurde ausgelöst, Automodus, Rote-Augen-Reduzierung',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Blitz wurde ausgelöst, Automodus, Messblitz-Licht zurückgeworfen, Rote-Augen-Reduzierung',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Blitz wurde ausgelöst, Automodus, kein Messblitz-Licht zurückgeworfen, Rote-Augen-Reduzierung',
        'Auto, Fired, Return detected' => 'Blitz wurde ausgelöst, Automodus, Messblitz-Licht zurückgeworfen',
        'Auto, Fired, Return not detected' => 'Blitz wurde ausgelöst, Automodus, kein Messblitz-Licht zurückgeworfen',
        'Did not fire' => 'Blitz wurde nicht ausgelöst',
        'Fired' => 'Blitz wurde ausgelöst',
        'Fired, Red-eye reduction' => 'Blitz wurde ausgelöst, Rote-Augen-Reduzierung',
        'Fired, Red-eye reduction, Return detected' => 'Blitz wurde ausgelöst, Rote-Augen-Reduzierung, Messblitz-Licht zurückgeworfen',
        'Fired, Red-eye reduction, Return not detected' => 'Blitz wurde ausgelöst, Rote-Augen-Reduzierung, kein Messblitz-Licht zurückgeworfen',
        'Fired, Return detected' => 'Messblitz-Licht zurückgeworfen',
        'Fired, Return not detected' => 'Kein Messblitz-Licht zurückgeworfen',
        'No Flash' => 'Blitz wurde nicht ausgelöst',
        'No flash function' => 'Keine Blitzfunktion',
        'Off' => 'Aus',
        'Off, Did not fire' => 'Blitz wurde nicht ausgelöst, Blitz unterdrücken-Modus',
        'Off, Did not fire, Return not detected' => 'Deaktiviert, Blitz wurde nicht ausgelöst, kein Messblitz-Licht zurückgeworfen',
        'Off, No flash function' => 'Deaktiviert, Keine Blitzfunktion',
        'Off, Red-eye reduction' => 'Deaktiviert, Rote-Augen-Reduzierung',
        'On' => 'Ein',
        'On, Did not fire' => 'Ein, Blitz wurde nicht ausgelöst',
        'On, Fired' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus',
        'On, Red-eye reduction' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus, Rote-Augen-Reduzierung',
        'On, Red-eye reduction, Return detected' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus, Rote-Augen-Reduzierung, Messblitz-Licht zurückgeworfen',
        'On, Red-eye reduction, Return not detected' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus, Rote-Augen-Reduzierung, kein Messblitz-Licht zurückgeworfen',
        'On, Return detected' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus, Messblitz-Licht zurückgeworfen',
        'On, Return not detected' => 'Blitz wurde ausgelöst, Blitz erzwingen-Modus, kein Messblitz-Licht zurückgeworfen',
      },
    },
   'FlashAction2' => {
      PrintConv => {
        'Did not fire' => 'Blitz wurde nicht ausgelöst',
        'Fired' => 'Blitz wurde ausgelöst',
      },
    },
   'FlashActivity' => 'Blitz-Leistung',
   'FlashBatteryLevel' => 'Blitz Batteriestatus',
   'FlashBias' => 'Blitzkorrektur',
   'FlashBits' => {
      Description => 'Blitz-Details',
      PrintConv => {
        '2nd-curtain sync used' => 'Synchronisatiopn auf 2. Verschlußvorhang',
        'Built-in' => 'Integrierter Blitz',
        'External' => 'Extern',
        'FP sync enabled' => 'FP Synchronisation eingestellt',
        'FP sync used' => 'FP Synchronistaion',
        'Manual' => 'Manuell',
      },
    },
   'FlashColorFilter' => 'Blitz Farbfilter',
   'FlashCommanderMode' => {
      Description => 'Master-Steuerung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FlashCompensation' => 'Blitzbelichtungs-Korrektur',
   'FlashControlBuilt-in' => {
      PrintConv => {
        'Commander Mode' => 'Master-Steuerung',
      },
    },
   'FlashControlMode' => {
      Description => 'Blitzlichtsteuerungsmodus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
        'Repeating Flash' => 'Stroboskopblitz',
      },
    },
   'FlashCurtain' => {
      Description => 'Blitzsynchronisation auf Verschluß',
      PrintConv => {
        '1st' => '1. Vorhang',
        '2nd' => '2. Vorhang',
      },
    },
   'FlashDefault' => {
      Description => 'Systemblitz',
      PrintConv => {
        'Fill Flash' => 'Aufhellblitz',
      },
    },
   'FlashDevice' => {
      Description => 'Blitzgerät',
      PrintConv => {
        'External' => 'Extern',
        'Internal' => 'Intern',
        'Internal + External' => 'Intern + Extern',
        'None' => 'Keines',
      },
    },
   'FlashDistance' => 'Blitzabstand',
   'FlashEnergy' => 'Blitzstärke',
   'FlashExposureBracketValue' => 'Blitzbelichtungsreihenwert',
   'FlashExposureComp' => 'Blitzbelichtungskorrektur',
   'FlashExposureComp2' => 'Blitzbelichtungskorrektur 2',
   'FlashExposureComp3' => 'Blitzbelichtungskorrektur 3',
   'FlashExposureComp4' => 'Blitzbelichtungskorrektur 4',
   'FlashExposureCompSet' => 'Eingestellte Blitz-Belichtungskorrektur',
   'FlashExposureCompSet2' => 'Blitzbelichtungskorrektur 2',
   'FlashExposureIndicator' => {
      Description => 'Blitz-Belichtungsindikator',
      PrintConv => {
        'Bottom of Scale' => 'Unterer Wert',
        'Not Indicated' => 'Nicht angezeigt',
        'Over Scale' => 'Wert zu hoch',
        'Top of Scale' => 'Oberer Wert',
        'Under Scale' => 'Wert zu niedrig',
      },
    },
   'FlashExposureIndicatorLast' => {
      Description => 'Blitz-Ende-Belichtungsindikator',
      PrintConv => {
        'Bottom of Scale' => 'Unterer Wert',
        'Not Indicated' => 'Nicht angezeigt',
        'Over Scale' => 'Wert zu hoch',
        'Top of Scale' => 'Oberer Wert',
        'Under Scale' => 'Wert zu niedrig',
      },
    },
   'FlashExposureIndicatorNext' => {
      Description => 'Blitz-Folge-Belichtungsindikator',
      PrintConv => {
        'Bottom of Scale' => 'Unterer Wert',
        'Not Indicated' => 'Nicht angezeigt',
        'Over Scale' => 'Wert zu hoch',
        'Top of Scale' => 'Oberer Wert',
        'Under Scale' => 'Wert zu niedrig',
      },
    },
   'FlashExposureLock' => {
      Description => 'Blitzbelichtung-Speicherung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FlashFired' => {
      Description => 'Blitz wurde ausgelöst',
      PrintConv => {
        'False' => 'Nein',
        'No' => 'Nein',
        'True' => 'Ja',
        'Yes' => 'Ja',
      },
    },
   'FlashFiring' => {
      Description => 'Blitzzündung',
      PrintConv => {
        'Does not fire' => 'Unterdrückt',
        'Fires' => 'Aktiv',
      },
    },
   'FlashFirmwareVersion' => 'Blitz-Firmware-Version',
   'FlashFocalLength' => 'Blitz-Brennweite',
   'FlashFunction' => {
      Description => 'Blitzfunktion',
      PrintConv => {
        'Built-in flash' => 'Integrierter Blitz',
        'False' => 'Nein',
        'Manual' => 'Manuell',
        'No flash' => 'Kein Blitz',
        'Strobe' => 'Stroboskop',
        'True' => 'Ja',
      },
    },
   'FlashGNDistance' => {
      Description => 'Blitzleitzahl Entfernung',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FlashGroupACompensation' => 'Gruppe A, Blitzbelichtungs-Korrektur',
   'FlashGroupAControlMode' => {
      Description => 'Gruppe A, Blitzlichtsteuerungsmodus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
        'Repeating Flash' => 'Stroboskopblitz',
      },
    },
   'FlashGroupAOutput' => 'Gruppe A, Blitz-Leistung',
   'FlashGroupBCompensation' => 'Gruppe B, Blitzbelichtungs-Korrektur',
   'FlashGroupBControlMode' => {
      Description => 'Gruppe B, Blitzlichtsteuerungsmodus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Automatic' => 'Automatik',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
        'Repeating Flash' => 'Stroboskopblitz',
      },
    },
   'FlashGroupBOutput' => 'Gruppe B, Blitz-Leistung',
   'FlashGroupCCompensation' => 'Gruppe C, Blitzbelichtungs-Korrektur',
   'FlashGroupCControlMode' => {
      Description => 'Gruppe C, Blitzlichtsteuerungsmodus',
      PrintConv => {
        'Auto Aperture' => 'Blendenautomatik (AA)',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
        'Repeating Flash' => 'Stroboskopblitz',
      },
    },
   'FlashGroupCOutput' => 'Gruppe C, Blitz-Leistung',
   'FlashGuideNumber' => 'Blitzleitzahl',
   'FlashInfo' => 'Blitz-Informationen',
   'FlashInfoVersion' => 'Blitz-Informationen-Version',
   'FlashIntensity' => {
      Description => 'Blitz Stärke',
      PrintConv => {
        'High' => 'Hoch',
        'Strong' => 'Stark',
      },
    },
   'FlashLevel' => {
      Description => 'Blitzbelichtungskorr.',
      PrintConv => {
        'High' => 'Hoch',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FlashMake' => 'Blitzgerätehersteller',
   'FlashMetering' => {
      Description => 'Blitz-Messung',
      PrintConv => {
        'Manual flash control' => 'Manuelle Blitz-Kontrolle',
        'Pre-flash TTL' => 'Vorblitz TTL',
      },
    },
   'FlashMeteringMode' => {
      Description => 'Blitz-Belichtungsmessmethode',
      PrintConv => {
        'External Auto' => 'Extern Automatisch',
        'External Manual' => 'Extern Manuell',
        'Off' => 'Aus',
      },
    },
   'FlashMeteringSegments' => 'Blitz-Messfelder',
   'FlashMode' => {
      Description => 'Blitz-Modus',
      PrintConv => {
        '2nd Curtain' => 'Auf 2. Verschlußvorhang',
        'Auto' => 'Automatisch',
        'Auto, Did not fire' => 'Auto, nicht ausgelöst',
        'Auto, Did not fire, Red-eye reduction' => 'Auto, nicht ausgelöst, Rote-Augen-Reduzierung',
        'Auto, Fired' => 'Auto, ausgelöst',
        'Auto, Fired, Red-eye reduction' => 'Auto, ausgelöst, Rote-Augen-Reduzierung',
        'Did Not Fire' => 'Nicht ausgelöst',
        'Disabled' => 'Deaktiviert',
        'External, Auto' => 'Extern, Auto',
        'External, Contrast-control Sync' => 'Extern, Kontrast-Steuerungs-Synchronisation',
        'External, Flash Problem' => 'Extern, Blitzproblem?',
        'External, High-speed Sync' => 'Extern, High-Speed-Synchronisation',
        'External, Manual' => 'Extern, Manuell',
        'External, P-TTL Auto' => 'Extern, P-TTL-Blitzautomatik',
        'External, Wireless' => 'Extern, Drahtlos',
        'External, Wireless, High-speed Sync' => 'Extern, Drahtlos, High-Speed-Synchronisation',
        'Fill flash' => 'Aufhellblitz',
        'Fill-in' => 'Aufhellen',
        'Fired, Commander Mode' => 'Ausgelöst, Befehlsmodus',
        'Fired, External' => 'Ausgelöst, Extern',
        'Fired, Manual' => 'Ausgelöst, Manuell',
        'Fired, TTL Mode' => 'Ausgelöst, TTL-Modus',
        'Internal' => 'Intern',
        'Not Ready' => 'Nicht bereit',
        'Off' => 'Aus',
        'Off, Did not fire' => 'Aus',
        'Off?' => 'Aus?',
        'On' => 'Ein',
        'On, Did not fire' => 'Ein, nicht ausgelöst',
        'On, Did not fire, Wireless (Master)' => 'Ein, nicht ausgelöst, Drahtlos (Hauptblitz)',
        'On, Fired' => 'Ein',
        'On, Red-eye reduction' => 'Ein, Rote-Augen-Reduzierung',
        'On, Slow-sync' => 'Ein, Langzeit-Synchronisation',
        'On, Slow-sync, Red-eye reduction' => 'Ein, Langzeit-Synchronisation, Rote-Augen-Reduzierung',
        'On, Soft' => 'Ein, Softblitz',
        'On, Trailing-curtain Sync' => 'Ein, 2. Verschlussvorhang',
        'On, Wireless (Control)' => 'Ein, Drahtlos (Steuerblitz)',
        'On, Wireless (Master)' => 'Ein, Drahtlos (Hauptblitz)',
        'Rear flash sync' => 'Synchronisation auf den zweiten Verschlussvorhang',
        'Red eye' => 'Rote-Augen-Reduzierung',
        'Red-eye' => 'Rote Augen',
        'Red-eye Reduction' => 'Rote-Augen-Reduzierung',
        'Red-eye reduction' => 'Rote-Augen-Reduzierung',
        'Unknown' => 'Unbekannt',
        'Wireless' => 'Drahtlos',
        'n/a - Off-Auto-Aperture' => 'K/A - Blendenring nicht auf A',
      },
    },
   'FlashModel' => {
      Description => 'Blitz-Modell',
      PrintConv => {
        'None' => 'Keines',
      },
    },
   'FlashOptions' => {
      Description => 'Blitz-Optionen',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Auto, Red-eye reduction' => 'Auto, Rote-Augen-Reduzierung',
        'Red-eye reduction' => 'Rote-Augen-Reduzierung',
        'Slow-sync' => 'Langzeit-Synchronisation',
        'Slow-sync, Red-eye reduction' => 'Langzeit-Synchronisation, Rote-Augen-Reduzierung',
        'Trailing-curtain Sync' => '2. Verschlussvorhang',
        'Wireless (Control)' => 'Drahtlos (Steuerblitz)',
        'Wireless (Master)' => 'Drahtlos (Hauptblitz)',
      },
    },
   'FlashOptions2' => {
      Description => 'Blitz-Optionen (2)',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Auto, Red-eye reduction' => 'Auto, Rote-Augen-Reduzierung',
        'Red-eye reduction' => 'Rote-Augen-Reduzierung',
        'Slow-sync' => 'Langzeit-Synchronisation',
        'Slow-sync, Red-eye reduction' => 'Langzeit-Synchronisation, Rote-Augen-Reduzierung',
        'Trailing-curtain Sync' => '2. Verschlussvorhang',
        'Wireless (Control)' => 'Drahtlos (Steuerblitz)',
        'Wireless (Master)' => 'Drahtlos (Hauptblitz)',
      },
    },
   'FlashOutput' => 'Blitzstärke',
   'FlashRedEyeMode' => {
      Description => 'Blitz Rote-Augen-Modus',
      PrintConv => {
        'False' => 'Nein',
        'True' => 'Ja',
      },
    },
   'FlashRemoteControl' => {
      Description => 'Blitz Fernauslöser',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'FlashReturn' => {
      Description => 'Blitz Reflexion',
      PrintConv => {
        'No return detection' => 'Keine Erkennung',
        'Return detected' => 'Reflexion erkannt',
        'Return not detected' => 'Reflexion nicht erkannt',
        'Subject Inside Flash Range' => 'Objekt innerhalb der Blitzreichweite',
        'Subject Outside Flash Range' => 'Objekt ausserhalb der Blitzreichweite',
      },
    },
   'FlashSerialNumber' => 'Blitz-Seriennummer',
   'FlashSetting' => 'Blitzeinstellung',
   'FlashShutterSpeed' => 'Längste Verschlussz. (Blitz)',
   'FlashStatus' => {
      Description => 'Slave-Blitz-Messfeld 1',
      PrintConv => {
        'Built-in Flash present' => 'Integrierter Blitz vorhanden',
        'Built-in Flash present and fired' => 'Integrierter Blitz vorhanden und ausgelöst',
        'External Flash present' => 'Externer Blitz vorhanden',
        'External Flash present and fired' => 'Externer Blitz vorhanden und ausgelöst',
        'External, Did not fire' => 'Extern, nicht ausgelöst',
        'External, Fired' => 'Extern, ausgelöst',
        'Internal, Did not fire' => 'Intern, nicht ausgelöst',
        'Internal, Did not fire (0x08)' => 'Intern, nicht ausgelöst',
        'Internal, Fired' => 'Intern, ausgelöst',
        'No Flash present' => 'Kein Blitz vorhanden',
        'Off' => 'Aus',
        'Off (1)' => 'Aus (1)',
      },
    },
   'FlashSyncMode' => 'Blitz-Synchronisationsmodus',
   'FlashSyncSpeed' => 'Blitzsynchronzeit',
   'FlashSyncSpeedAv' => {
      Description => 'Blitzsynchronzeit bei Av',
      PrintConv => {
        '1/200 Fixed' => '1/200 Fest',
        '1/200-1/60 Auto' => '1/200-1/60 automatisch',
        '1/250 Fixed' => '1/250 Fest',
        '1/250-1/60 Auto' => '1/200-1/60 automatisch',
        '1/300 Fixed' => '1/300 Fest',
        'Auto' => 'Automatisch',
      },
    },
   'FlashTTLMode' => 'Blitz TTL-Modus',
   'FlashType' => {
      Description => 'Blitztyp',
      PrintConv => {
        'Built-In Flash' => 'Intern',
        'External' => 'Extern',
        'None' => 'Keiner',
      },
    },
   'FlashWarning' => {
      Description => 'Blitzsymbol',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FlashpixVersion' => 'Unterstützte Flashpix-Version',
   'FlickerReduce' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'FlipHorizontal' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'FocalLength' => 'Brennweite',
   'FocalLength35efl' => 'Brennweite',
   'FocalLengthIn35mmFormat' => 'Brennweite in 35 mm-Kleinbildformat',
   'FocalPlaneDiagonal' => 'Diagonale des Sensors',
   'FocalPlaneResolutionUnit' => {
      Description => 'Einheit der Sensorauflösung',
      PrintConv => {
        'None' => 'Keine',
        'inches' => 'Zoll',
        'um' => 'µm (Mikrometer)',
      },
    },
   'FocalPlaneXResolution' => 'Sensorauflösung horizontal',
   'FocalPlaneXSize' => 'Sensorgröße horizontal',
   'FocalPlaneXUnknown' => 'Sensorgröße horizontal unbekannt',
   'FocalPlaneYResolution' => 'Sensorauflösung vertikal',
   'FocalPlaneYSize' => 'Sensorgröße vertikal',
   'FocalPlaneYUnknown' => 'Sensorgröße vertikal unbekannt',
   'FocalType' => {
      Description => 'Objektivart',
      PrintConv => {
        'Fixed' => 'Festbrennweite',
        'Zoom' => 'Zoom-Objektiv',
      },
    },
   'FocalUnits' => 'Fokussiereinheit',
   'Focus' => {
      Description => 'Schärfepriorität',
      PrintConv => {
        'Auto-focus Didn\'t Lock' => 'Autofokus nicht gesperrt',
        'Auto-focus Locked' => 'Autofokus gesperrt',
        'Manual' => 'Manuell',
      },
    },
   'FocusArea' => {
      Description => 'Fokus-Bereich',
      PrintConv => {
        'Spot Focus' => 'Spot-AF-Messfeld',
        'Wide Focus (normal)' => 'Großes AF-Messfeld (normal)',
      },
    },
   'FocusAreaSelection' => {
      Description => 'Scrollen bei Messfeldauswahl',
      PrintConv => {
        'No Wrap' => 'Am Rand stoppen',
        'Wrap' => 'Umlaufend',
      },
    },
   'FocusContinuous' => {
      Description => 'Fortlaufende Fokussierung',
      PrintConv => {
        'Continuous' => 'Serienaufnahme',
        'Manual' => 'Manuell',
      },
    },
   'FocusDisplayAIServoAndMF' => {
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'FocusDistance' => 'Fokus-Distanz',
   'FocusDistanceLower' => 'Nahe Fokus-Distanz',
   'FocusDistanceUpper' => 'Entfernte Fokus-Distanz',
   'FocusInfoVersion' => 'FokusInfo Version',
   'FocusMode' => {
      Description => 'Fokus-Modus',
      PrintConv => {
        'AF-C' => 'AF-C (Kontinuierlicher Autofokus)',
        'AF-S' => 'AF-S (Einzelautofokus)',
        'AI Focus AF' => 'AI Fokus AF',
        'Auto' => 'Automatisch',
        'Continuous' => 'Serienaufnahme',
        'Custom' => 'Benutzerdefiniert',
        'Face Detect' => 'Gesichtserkennung AF',
        'Infinity' => 'Unendlich',
        'Macro' => 'Makro',
        'Macro (1)' => 'Makro (1)',
        'Macro (2)' => 'Makro (2)',
        'Manual' => 'Manuell',
        'Manual Focus (3)' => 'Manueller Fokus (3)',
        'Manual Focus (6)' => 'Manueller Fokus (6)',
        'Multi AF' => 'Mehrpunkt AF',
        'One-shot AF' => 'One-Shot AF',
        'Pan Focus' => 'Pan-Fokus',
        'Single' => 'Einzelbild',
        'Single AF' => 'Einpunkt AF',
        'Super Macro' => 'Super-Makro',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'FocusMode2' => {
      Description => 'Fokus-Modus 2',
      PrintConv => {
        'AF-C' => 'AF-C (Kontinuierlicher Autofokus)',
        'AF-S' => 'AF-S (Einzelautofokus)',
        'Manual' => 'Manuell',
      },
    },
   'FocusModeSetting' => {
      Description => 'Autofokus',
      PrintConv => {
        'AF-A' => 'AF-Automatik',
        'AF-C' => 'AF-C (Kontinuierlicher Autofokus)',
        'AF-S' => 'AF-S (Einzelautofokus)',
        'Manual' => 'Manuell',
      },
    },
   'FocusModeSwitch' => {
      Description => 'Fokus-Modus Schalter',
      PrintConv => {
        'Manual' => 'Manuell',
      },
    },
   'FocusPixel' => 'Fokus-Pixel',
   'FocusPointWrap' => {
      Description => 'Scrollen bei Messfeldauswahl',
      PrintConv => {
        'No Wrap' => 'Am Rand stoppen',
        'Wrap' => 'Umlaufend',
      },
    },
   'FocusPos' => 'Fokus-Position',
   'FocusPosition' => 'Fokus-Distanz',
   'FocusProcess' => {
      Description => 'Fokussierung',
      PrintConv => {
        'AF Not Used' => 'Ohne AF',
        'AF Used' => 'Mit AF',
      },
    },
   'FocusRange' => {
      Description => 'Fokus-Bereich',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Close' => 'Nah',
        'Far Range' => 'Entfernt',
        'Infinity' => 'Unendlich',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Middle Range' => 'Mittlerer Bereich',
        'Not Known' => 'Nicht bekannt',
        'Pan Focus' => 'Pan-Fokus',
        'Super Macro' => 'Super-Makro',
        'Very Close' => 'Sehr nah',
      },
    },
   'FocusStepCount' => 'Fokus-Stufenzähler',
   'FocusStepInfinity' => 'Fokus-Stufe Unendlich',
   'FocusStepNear' => 'Fokus-Stufe Nah',
   'FocusTrackingLockOn' => {
      Description => 'Schärfenarchiv. mit Lock-On',
      PrintConv => {
        '1 (Short)' => '5 (Kurz)',
        '1 Short' => '1 Kurz',
        '5 (Long)' => '5 (Lang)',
        '5 Long' => '5 Lang',
        'Long' => 'Lang',
        'Off' => 'Aus',
        'Short' => 'Kurz',
      },
    },
   'FocusWarning' => {
      Description => 'Fokus-Warnung',
      PrintConv => {
        'Good' => 'OK',
        'Out of focus' => 'Ausserhalb des Fokus',
      },
    },
   'FocusingScreen' => 'Mattscheibe',
   'FolderName' => 'Ordner-Name',
   'FrameHeight' => 'Bereichshöhe',
   'FrameNumber' => 'Bildnummer',
   'FrameRate' => 'Bildwechselfrequenz',
   'FrameSize' => 'Einzelbildgröße',
   'FrameWidth' => 'Bereichsbreite',
   'FreeByteCounts' => 'Anzahl Bytes des leeren Datenbereiches',
   'FreeMemoryCardImages' => 'Platz auf Speicherkarten für',
   'FreeOffsets' => 'Leerdatenposition',
   'FujiFlashMode' => {
      Description => 'Blitz-Modus',
      PrintConv => {
        'Auto' => 'Automatisch',
        'External' => 'Externer Blitz',
        'Off' => 'Unterdrückter Blitz',
        'On' => 'Erzwungener Blitz',
        'Red-eye reduction' => 'Rote-Augen-Reduzierung',
      },
    },
   'FullImageSize' => 'Volle Bildgröße',
   'FunctionButton' => {
      Description => 'Funktionstaste',
      PrintConv => {
        'AF-area Mode' => 'Messfeldsteuerung',
        'Auto Bracketing' => 'Belichtungsreihe',
        'Center AF Area' => 'AF-Messfeldgröße',
        'Center-weighted' => 'Mittenbetont',
        'FV Lock' => 'FV-Messwertspeicher',
        'Flash Off' => 'Blitz aus',
        'Framing Grid' => 'Gitterlinien',
        'ISO Display' => 'ISO-Anzeige',
        'Image Quality' => 'Bildqualität',
        'Matrix Metering' => 'Matrixmessung',
        'Spot Metering' => 'Spotmessung',
        'White Balance' => 'Weißabgleich',
      },
    },
   'GEImageSize' => 'GE Bildgröße',
   'GIFVersion' => 'GIF-Version',
   'GPSAltitude' => 'GPS Höhe',
   'GPSAltitudeRef' => {
      Description => 'GPS-Höhe Bezug',
      PrintConv => {
        'Above Sea Level' => 'Höhe über Normal-Null (Meeresspiegel)',
        'Below Sea Level' => 'Höhe unter Normal-Null (Meeresspiegel)',
      },
    },
   'GPSAreaInformation' => 'Name des GPS-Gebietes',
   'GPSDOP' => 'Messgenauigkeit',
   'GPSDateStamp' => 'GPS Datum',
   'GPSDateTime' => 'GPS Zeitstempel',
   'GPSDestBearing' => 'Motivrichtung',
   'GPSDestBearingRef' => {
      Description => 'Referenz für Motivrichtung',
      PrintConv => {
        'Magnetic North' => 'Magnetische Ausrichtung',
        'True North' => 'Geographische Ausrichtung',
      },
    },
   'GPSDestDistance' => 'GPS Zielentfernung',
   'GPSDestDistanceRef' => {
      Description => 'GPS-Zielentfernung Maßeinheit',
      PrintConv => {
        'Kilometers' => 'Kilometer',
        'Miles' => 'Meilen',
        'Nautical Miles' => 'Knoten',
      },
    },
   'GPSDestLatitude' => 'Breite des Zieles',
   'GPSDestLatitudeRef' => {
      Description => 'Referenz für die Breite des Zieles',
      PrintConv => {
        'North' => 'Nördliche Breite',
        'South' => 'Südliche Breite',
      },
    },
   'GPSDestLongitude' => 'Längengrad des Ziels',
   'GPSDestLongitudeRef' => {
      Description => 'Referenz für die Länge des Zieles',
      PrintConv => {
        'East' => 'Östliche Länge',
        'West' => 'Westliche Länge',
      },
    },
   'GPSDifferential' => {
      Description => 'GPS Differentialkorrektur',
      PrintConv => {
        'Differential Corrected' => 'Differentialkorrektur angewandt',
        'No Correction' => 'Messung ohne Differentialkorrektur',
      },
    },
   'GPSImgDirection' => 'Bildrichtung',
   'GPSImgDirectionRef' => {
      Description => 'Referenz für die Ausrichtung des Bildes',
      PrintConv => {
        'Magnetic North' => 'Magnetische Ausrichtung',
        'True North' => 'Geographische Ausrichtung',
      },
    },
   'GPSInfo' => 'GPS Info IFD-Zeiger',
   'GPSLatitude' => 'Geografische Breite',
   'GPSLatitudeRef' => {
      Description => 'Nördl. oder südl. Breite',
      PrintConv => {
        'North' => 'Nördliche Breite',
        'South' => 'Südliche Breite',
      },
    },
   'GPSLongitude' => 'Geografische Länge',
   'GPSLongitudeRef' => {
      Description => 'östl. oder westl. Länge',
      PrintConv => {
        'East' => 'Östliche Länge',
        'West' => 'Westliche Länge',
      },
    },
   'GPSMapDatum' => 'Geodätisches Datum',
   'GPSMeasureMode' => {
      Description => 'GPS Messverfahren',
      PrintConv => {
        '2-D' => '2-Dimensionale Messung',
        '2-Dimensional' => '2-Dimensionale Messung',
        '2-Dimensional Measurement' => '2-Dimensionale Messung',
        '3-D' => '3-Dimensionale Messung',
        '3-Dimensional' => '3-Dimensionale Messung',
        '3-Dimensional Measurement' => '3-Dimensionale Messung',
      },
    },
   'GPSProcessingMethod' => 'Name der GPS-Verarbeitungsmethode',
   'GPSSatellites' => 'Für die Messung verwendete Satelliten',
   'GPSSpeed' => 'Geschwindigkeit des GPS-Empfängers',
   'GPSSpeedRef' => {
      Description => 'Geschwindigkeitseinheit',
      PrintConv => {
        'km/h' => 'Kilometer pro Stunde',
        'knots' => 'Knoten',
        'mph' => 'Meilen pro Stunde',
      },
    },
   'GPSStatus' => {
      Description => 'GPS-Empfänger Status',
      PrintConv => {
        'Measurement Active' => 'Messung aktiv',
        'Measurement Void' => 'Messung ungültig',
      },
    },
   'GPSTimeStamp' => 'GPS-Zeit UTC',
   'GPSTrack' => 'Bewegungsrichtung',
   'GPSTrackRef' => {
      Description => 'Referenz für Bewegungsrichtung',
      PrintConv => {
        'Magnetic North' => 'Magnetische Ausrichtung',
        'True North' => 'Geographische Ausrichtung',
      },
    },
   'GPSVersionID' => 'GPS-Tag-Version',
   'GainControl' => {
      Description => 'Belichtungsverstärkung',
      PrintConv => {
        'High gain down' => 'Hohe Helligkeitsminderung',
        'High gain up' => 'Hohe Helligkeitsverstärkung',
        'Low gain down' => 'Geringe Helligkeitsminderung',
        'Low gain up' => 'Geringe Helligkeitsverstärkung',
        'None' => 'Keine',
      },
    },
   'Gapless' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'Gradation' => {
      PrintConv => {
        'Auto-Override' => 'Automatisch',
        'High Key' => 'High-Key',
        'Low Key' => 'Low-Key',
        'User-Selected' => 'Benutzerauswahl',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'GrayResponseUnit' => {
      PrintConv => {
        '0.0001' => 'Nummer stellt ein 1000tel einer Einheit dar',
        '0.001' => 'Nummer stellt ein 100tel einer Einheit dar',
        '0.1' => 'Nummer stellt ein 10tel einer Einheit dar',
        '1e-05' => 'Nummer stellt ein 10000tel einer Einheit dar',
        '1e-06' => 'Nummer stellt ein 100000tel einer Einheit dar',
      },
    },
   'GrayTRC' => 'Grau-Tonwertwiedergabe-Kurve',
   'GreenAdjust' => 'Grün-Korrektur',
   'GreenMatrixColumn' => 'Grün-Matrixspalte',
   'GreenTRC' => 'Grün-Tonwertwiedergabe-Kurve',
   'GreenX' => 'Grünpunkt X',
   'GreenY' => 'Grünpunkt Y',
   'GridDisplay' => {
      Description => 'Gitterlinien',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'GripBatteryADLoad' => 'Griffbatterie A/D unter Last',
   'GripBatteryADNoLoad' => 'Griffbatterie A/D im Leerlauf',
   'GripBatteryState' => {
      Description => 'Griffbatterie-Status',
      PrintConv => {
        'Almost Empty' => 'Fast leer',
        'Empty or Missing' => 'Leer oder nicht vorhanden',
        'Full' => 'Voll geladen',
        'Running Low' => 'Schwach',
      },
    },
   'Grouping' => 'Gruppierung',
   'HDR' => {
      Description => 'Auto HDR',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'HDRImageType' => {
      Description => 'HDR Bildtyp',
      PrintConv => {
        'HDR Image' => 'HDR Bild',
        'Original Image' => 'Originalbild',
      },
    },
   'Headline' => 'Überschrift',
   'HeightResolution' => 'Vertikale Bildauflösung',
   'HierarchicalSubject' => 'Hierarchische Schlüsselwörter',
   'HighISONoiseReduction' => {
      Description => 'Rauschunterdrückung bei hoher Empfindlichkeit',
      PrintConv => {
        '+1 (medium strong)' => '+1',
        '+2 (strong)' => '+2 (Stark)',
        '+3 (very strong)' => '+3',
        '+4 (strongest)' => '+4',
        '-1 (medium weak)' => '-1',
        '-2 (weak)' => '-2 (Gering)',
        '-3 (very weak)' => '-3',
        '-4 (weakest)' => '-4 (Sehr gering)',
        '0 (normal)' => '0 (Normal)',
        'Auto' => 'Automatisch',
        'High' => 'Stärker',
        'Low' => 'Schwächer',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Strong' => 'Stark',
        'Weak' => 'Gering',
        'Weakest' => 'Sehr gering',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'HighISONoiseReduction2' => {
      Description => 'Rauschunterdrückung bei hoher Empfindlichkeit',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'HighLowKeyAdj' => 'High-/Low-Key Abstimmung',
   'Highlight' => 'Helle Stellen',
   'HighlightTonePriority' => {
      Description => 'Tonwert Priorität',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'HometownCity' => 'Heimatort',
   'HometownCityCode' => 'Heimatort-Code',
   'HometownDST' => {
      Description => 'Heimatort Sommerzeit (DST)',
      PrintConv => {
        'No' => 'Deaktiviert',
        'Yes' => 'Aktiviert',
      },
    },
   'Hue' => 'Farbton',
   'HueAdj' => 'Farbtonkorrektur',
   'HueAdjust' => {
      Description => 'Farbtonkorrektur',
      PrintConv => {
        'Cool' => 'Kühl',
        'Off' => 'Aus',
        'Yellow' => 'Gelb',
      },
    },
   'HueAdjustment' => 'Farbtonkorrektur',
   'HueSetting' => 'Farbtoneinstellung',
   'HuffmanTable' => 'Huffman Tabelle',
   'HyperfocalDistance' => 'Hyperfokale Entfernung',
   'ICCProfile' => 'ICC-Profil',
   'ICCProfileName' => 'ICC-Profil Name',
   'ICC_Profile' => 'ICC-Profil',
   'ID3Size' => 'ID3 Datenlänge',
   'IDCCreativeStyle' => {
      PrintConv => {
        'Autumn Leaves' => 'Herbstlaub',
        'B&W' => 'Schwarz/Weiß',
        'Landscape' => 'Landschaft',
        'Night View' => 'Abendszene',
        'Portrait' => 'Porträt',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'IDCPreviewImage' => 'IDC Vorschaubild',
   'IDCPreviewLength' => 'IDC Vorschaubild-Datenlänge',
   'IDCPreviewStart' => 'IDC Vorschaubild-Datenposition',
   'IPTC-NAA' => 'IPTC-NAA Metadaten',
   'IPTCDigest' => 'IPTC Kennwert',
   'IPTCImageHeight' => 'IPTC-Bildhöhe',
   'IPTCImageRotation' => {
      Description => 'IPTC Bildausrichtung',
      PrintConv => {
        '0' => 'Normal',
        '180' => '180° gedreht',
        '270' => '90° gegen den Uhrzeigersinn',
        '90' => '90° im Uhrzeigersinn',
      },
    },
   'IPTCImageWidth' => 'IPTC-Bildbreite',
   'IPTCPictureNumber' => 'IPTC Bildnummer',
   'IPTCPixelHeight' => 'IPTC-Pixelhöhe',
   'IPTCPixelWidth' => 'IPTC-Pixelbreite',
   'ISO' => 'ISO-Empfindlichkeit',
   'ISO2' => 'ISO-Empfindlichkeit (2)',
   'ISOAuto' => 'ISO-Automatik',
   'ISODisplay' => 'ISO-Anzeige',
   'ISOExpansion' => {
      Description => 'ISO-Erweiterung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ISOExpansion2' => {
      Description => 'ISO-Erweiterung (2)',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'ISOFloor' => 'ISO-Untergrenze',
   'ISOInfo' => 'ISO-Informationen',
   'ISOSelection' => 'ISO-Auswahl',
   'ISOSetting' => {
      Description => 'ISO-Einstellung',
      PrintConv => {
        '200 (Zone Matching High)' => '200 (Zonenabgleich High)',
        '80 (Zone Matching Low)' => '80 (Zonenabgleich Low)',
        'Auto' => 'Automatisch',
        'Manual' => 'Manuell',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ISOSpeedExpansion' => {
      Description => 'ISO-Erweiterung',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'ISOSpeedIncrements' => {
      Description => 'ISO-Schrittweite',
      PrintConv => {
        '1 Stop' => '1 LW',
        '1/3 Stop' => '1/3 LW',
      },
    },
   'ISOSpeedRange' => {
      Description => 'Einstellung ISO-Bereich',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ISOStepSize' => {
      Description => 'ISO-Schrittweite',
      PrintConv => {
        '1 EV' => '1 LW',
        '1/2 EV' => '1/2 LW',
        '1/3 EV' => '1/3 LW',
      },
    },
   'ISRCNumber' => 'ISRC Nummer',
   'Illumination' => {
      Description => 'Displaybeleuchtung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Image::ExifTool::APP12::PictureInfo' => 'APP12 Bildinformation',
   'Image::ExifTool::Canon::AFMicroAdj' => 'Canon AF Feinabstimmung',
   'Image::ExifTool::Canon::CameraInfo1000D' => 'Canon KameraInfo 1000D',
   'Image::ExifTool::Canon::CameraInfo1D' => 'Canon KameraInfo 1D',
   'Image::ExifTool::Canon::CameraInfo1DX' => 'Canon KameraInfo 1DX',
   'Image::ExifTool::Canon::CameraInfo1DmkII' => 'Canon KameraInfo 1DmkII',
   'Image::ExifTool::Canon::CameraInfo1DmkIII' => 'Canon KameraInfo 1DmkIII',
   'Image::ExifTool::Canon::CameraInfo1DmkIIN' => 'Canon KameraInfo 1DmkIIN',
   'Image::ExifTool::Canon::CameraInfo1DmkIV' => 'Canon KameraInfo 1DmkIV',
   'Image::ExifTool::Canon::CameraInfo40D' => 'Canon KameraInfo 40D',
   'Image::ExifTool::Canon::CameraInfo450D' => 'Canon KameraInfo 450D',
   'Image::ExifTool::Canon::CameraInfo500D' => 'Canon KameraInfo 500D',
   'Image::ExifTool::Canon::CameraInfo50D' => 'Canon KameraInfo 50D',
   'Image::ExifTool::Canon::CameraInfo550D' => 'Canon KameraInfo 550D',
   'Image::ExifTool::Canon::CameraInfo5D' => 'Canon KameraInfo 5D',
   'Image::ExifTool::Canon::CameraInfo5DmkII' => 'Canon KameraInfo 5DmkII',
   'Image::ExifTool::Canon::CameraInfo5DmkIII' => 'Canon KameraInfo 5DmkIII',
   'Image::ExifTool::Canon::CameraInfo600D' => 'Canon KameraInfo 600D',
   'Image::ExifTool::Canon::CameraInfo60D' => 'Canon KameraInfo 60D',
   'Image::ExifTool::Canon::CameraInfo650D' => 'Canon KameraInfo 650D',
   'Image::ExifTool::Canon::CameraInfo7D' => 'Canon KameraInfo 7D',
   'Image::ExifTool::Canon::CameraInfoPowerShot' => 'Canon KameraInfo PowerShot',
   'Image::ExifTool::Canon::CameraInfoPowerShot2' => 'Canon KameraInfo PowerShot2',
   'Image::ExifTool::Canon::CameraInfoUnknown32' => 'Canon KameraInfo Unbekannt32',
   'Image::ExifTool::Canon::CropInfo' => 'Canon Ausschnitt Info',
   'Image::ExifTool::CanonRaw::ImageFormat' => 'CanonRaw Bildformat',
   'Image::ExifTool::DNG::OriginalRaw' => 'DNG Original RAW',
   'Image::ExifTool::ICC_Profile::Measurement' => 'ICC_Profil Messung',
   'Image::ExifTool::IPTC::ApplicationRecord' => 'IPTC Modell',
   'Image::ExifTool::Jpeg2000::FileType' => 'Jpeg2000 Dateityp',
   'Image::ExifTool::Jpeg2000::ImageHeader' => 'Jpeg2000 Bild-Header',
   'Image::ExifTool::Kodak::CameraInfo' => 'Kodak KameraInfo',
   'Image::ExifTool::MIE::Meta' => 'MIE Metadaten',
   'Image::ExifTool::Minolta::CameraInfoA100' => 'Minolta KameraInfo A100',
   'Image::ExifTool::Olympus::CameraSettings' => 'Olympus Kameraeinstellungen',
   'Image::ExifTool::Olympus::FocusInfo' => 'Olympus FokusInfo',
   'Image::ExifTool::Olympus::ImageProcessing' => 'Olympus Bildverarbeitung',
   'Image::ExifTool::PNG::ImageHeader' => 'PNG Bild-Header',
   'Image::ExifTool::PNG::PhysicalPixel' => 'PNG physikalische Pixel',
   'Image::ExifTool::PNG::PrimaryChromaticities' => 'PNG primäre Chromatizität',
   'Image::ExifTool::PNG::StereoImage' => 'PNG Stereobild',
   'Image::ExifTool::PNG::TextualData' => 'PNG Textdaten',
   'Image::ExifTool::PNG::VirtualPage' => 'PNG virtuelle Seite',
   'Image::ExifTool::PSP::Creator' => 'PSP Ersteller',
   'Image::ExifTool::Pentax::CameraInfo' => 'Pentax KameraInfo',
   'Image::ExifTool::Sony::CameraInfo' => 'Sony KameraInfo',
   'Image::ExifTool::Sony::CameraInfo2' => 'Sony KameraInfo2',
   'ImageAdjustment' => 'Bildanpassung',
   'ImageAreaOffset' => 'Bildbereichsoffset',
   'ImageAuthentication' => {
      Description => 'Bild-Authentifikation',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ImageBoundary' => 'Bildbegrenzung',
   'ImageByteCount' => 'Anzahl Bytes der Bilddaten',
   'ImageCount' => 'Bildzähler',
   'ImageDataDiscard' => {
      Description => 'Verworfene Bilddaten',
      PrintConv => {
        'Flexbits Discarded' => 'FlexBits verworfen',
        'Full Resolution' => 'Volle Auflösung',
        'HighPass Frequency Data Discarded' => 'Hochpass-Frequenz-Daten verworfen',
        'Highpass and LowPass Frequency Data Discarded' => 'Hochpass- und Tiefpass-Frequenz-Daten verworfen',
      },
    },
   'ImageDataSize' => 'Bilddatengröße',
   'ImageDescription' => 'Bildbeschreibung',
   'ImageDustOff' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ImageEditCount' => 'Bildverarbeitungszähler',
   'ImageEditing' => {
      Description => 'Bildverarbeitung',
      PrintConv => {
        'Cropped' => 'Beschnitten',
        'Digital Filter' => 'Digitalfilter',
        'Frame Synthesis?' => 'Rahmen?',
        'None' => 'Unbearbeitet',
      },
    },
   'ImageEffects' => {
      PrintConv => {
        'High Key' => 'High-Key',
      },
    },
   'ImageGeneration' => {
      Description => 'Bilderstellung',
      PrintConv => {
        'Original Image' => 'Originalbild',
        'Re-developed from RAW' => 'RAW generiert',
      },
    },
   'ImageHeight' => 'Bildhöhe',
   'ImageHistory' => 'Bild-Historie',
   'ImageLength' => 'Bild-Datenlänge',
   'ImageNumber' => 'Bildnummer',
   'ImageNumber2' => 'Bildnummer (2)',
   'ImageOffset' => 'Bilddatenposition',
   'ImageOptimization' => 'Bildoptimierung',
   'ImageOrientation' => {
      Description => 'Bildausrichtung',
      PrintConv => {
        'Landscape' => 'Querformat',
        'Portrait' => 'Porträt',
        'Square' => 'Quadratisch',
      },
    },
   'ImageProcessing' => 'Bildverarbeitung',
   'ImageProcessingVersion' => 'Bildverarbeitung Version',
   'ImageQuality' => {
      Description => 'Bildqualität',
      PrintConv => {
        'High' => 'Hoch',
      },
    },
   'ImageQuality2' => 'Bildqualität 2',
   'ImageReview' => {
      Description => 'Bildkontrolle',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ImageReviewTime' => 'Ausschaltzeit Bildkontrolle',
   'ImageRotated' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'ImageRotation' => {
      Description => 'Bildausrichtung',
      PrintConv => {
        'None' => 'Keine',
      },
    },
   'ImageSize' => 'Bildgröße',
   'ImageStabilization' => {
      Description => 'Bildstabilisierung',
      PrintConv => {
        'Anti-Shake' => 'Verwackeln',
        'Best Shot' => 'Beste Aufnahme',
        'CCD Shift + High Sensitivity' => 'CCD Shift + Hohe Empfindlichkeit',
        'Dynamic' => 'Dynamisch',
        'Dynamic (2)' => 'Dynamisch (2)',
        'High Sensitivity' => 'Hohe Empfindlichkeit',
        'None' => 'Keine',
        'Off' => 'Aus',
        'Off (0xbf)' => 'Aus (0xbf)',
        'Off (1)' => 'Aus (1)',
        'Off (2)' => 'Aus (2)',
        'On' => 'Ein',
        'On (0x3f)' => 'Ein (0x3f)',
        'On (2)' => 'Ein (2)',
        'On (mode 1, continuous)' => 'Ein (Modus 1, nachführend)',
        'On (mode 2, shooting only)' => 'Ein (Modus 2, Aufnahme)',
        'On, Mode 1' => 'Ein, Modus 1',
        'On, Mode 2' => 'Ein, Modus 2',
        'On, Mode 3' => 'Ein, Modus 3',
        'On, Mode 4' => 'Ein, Modus 4',
        'Optical' => 'Optisch',
        'Panning' => 'Schwenken',
        'Panning (2)' => 'Schwenken (2)',
        'Sensor-shift' => 'Sensor-Shift',
        'Shoot Only' => 'Aufnahme',
        'Shoot Only (2)' => 'Aufnahme (2)',
        'Slow Shutter' => 'Kurzer Verschluß',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ImageStabilizationSetting' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ImageStyle' => {
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Landscape' => 'Landschaft',
        'Night View/Portrait' => 'Abendszene/Porträt',
        'Portrait' => 'Porträt',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'ImageTone' => {
      Description => 'Farbdynamik',
      PrintConv => {
        'Bright' => 'Leuchtend',
        'Landscape' => 'Landschaft',
        'Monochrome' => 'Monochrom',
        'Natural' => 'Natürlich',
        'Portrait' => 'Porträt',
      },
    },
   'ImageType' => {
      Description => 'Bildtyp',
      PrintConv => {
        'Page' => 'Seite',
        'Preview' => 'Vorschau',
      },
    },
   'ImageUIDList' => 'Bilder UID Liste',
   'ImageUniqueID' => 'Eindeutige Bild-ID',
   'ImageWidth' => 'Bildbreite',
   'InfoButtonWhenShooting' => {
      Description => 'INFO-Taste bei Aufnahme',
      PrintConv => {
        'Displays camera settings' => 'Anzeige Kameradaten',
        'Displays shooting functions' => 'Anzeige Aufnahmedaten',
      },
    },
   'InitialZoomSetting' => {
      Description => 'Erste Vergrößerungsstufe',
      PrintConv => {
        'High Magnification' => 'Starke Vergrößerung',
        'Low Magnification' => 'Geringe Vergrößerung',
        'Medium Magnification' => 'Mittlere Vergrößerung',
      },
    },
   'Instructions' => 'Anweisungen',
   'IntellectualGenre' => 'Intellektuelles Genre',
   'IntelligentAuto' => {
      PrintConv => {
        'Advanced' => 'Erweitert',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'IntelligentContrast' => {
      Description => 'Intelligenter Kontrast',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'IntelligentD-Range' => {
      Description => 'Intelligenter D-Bereich',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
      },
    },
   'IntelligentExposure' => {
      Description => 'Intelligente Belichtung',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
      },
    },
   'IntelligentResolution' => {
      Description => 'Intelligente Bildauflösung',
      PrintConv => {
        'Extended' => 'Erweitert',
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
      },
    },
   'IntensityStereo' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'InternalFlash' => {
      Description => 'Integriertes Blitzgerät',
      PrintConv => {
        'Commander Mode' => 'Master-Steuerung',
        'Fired' => 'Blitz wurde ausgelöst',
        'Manual' => 'Manuell',
        'No' => 'Blitz wurde nicht ausgelöst',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Repeating Flash' => 'Stroboskopblitz',
      },
    },
   'InternalFlashAE1' => 'Integriertes Blitzgerät AE1',
   'InternalFlashAE1_0' => 'Integriertes Blitzgerät AE1 0',
   'InternalFlashAE2' => 'Integriertes Blitzgerät AE2',
   'InternalFlashAE2_0' => 'Integriertes Blitzgerät AE2 0',
   'InternalFlashMode' => {
      Description => 'Slave-Blitz-Messfeld 2',
      PrintConv => {
        'Did not fire, (Unknown 0xf4)' => 'Aus (Unbekannt 0xF4?)',
        'Did not fire, Auto' => 'Aus, Auto',
        'Did not fire, Auto, Red-eye reduction' => 'Aus, Auto, Rote-Augen-Reduzierung',
        'Did not fire, Normal' => 'Aus, Normal',
        'Did not fire, Red-eye reduction' => 'Aus, Rote-Augen-Reduzierung',
        'Did not fire, Slow-sync' => 'Aus, Langzeit-Synchronisation',
        'Did not fire, Slow-sync, Red-eye reduction' => 'Aus, Langzeit-Synchronisation, Rote-Augen-Reduzierung',
        'Did not fire, Trailing-curtain Sync' => 'Aus, 2. Verschlussvorhang',
        'Did not fire, Wireless (Control)' => 'Aus, Drahtlos (Steuerblitz)',
        'Did not fire, Wireless (Master)' => 'Aus, Drahtlos (Hauptblitz)',
        'Fired' => 'Ein',
        'Fired, Auto' => 'Ein, Auto',
        'Fired, Auto, Red-eye reduction' => 'Ein, Auto, Rote-Augen-Reduzierung',
        'Fired, Red-eye reduction' => 'Ein, Rote-Augen-Reduzierung',
        'Fired, Slow-sync' => 'Ein, Langzeit-Synchronisation',
        'Fired, Slow-sync, Red-eye reduction' => 'Ein, Langzeit-Synchronisation, Rote-Augen-Reduzierung',
        'Fired, Trailing-curtain Sync' => 'Ein, 2. Verschlussvorhang',
        'Fired, Wireless (Control)' => 'Ein, Drahtlos (Steuerblitz)',
        'Fired, Wireless (Master)' => 'Ein, Drahtlos (Hauptblitz)',
        'n/a - Off-Auto-Aperture' => 'K/A - Blendenring nicht auf A',
      },
    },
   'InternalFlashStrength' => 'Slave-Blitz-Messfeld 4',
   'InternalName' => 'Interner Name',
   'InternalSerialNumber' => 'Interne Seriennummer',
   'InteropIndex' => {
      Description => 'Interoperabilität Identifikation',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03: DCF Option-Format (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98: DCF Basic-Format (sRGB)',
        'THM - DCF thumbnail file' => 'THM: DCF Miniaturbild-Format',
      },
    },
   'InteropOffset' => 'Interoperabilitäts-Tag',
   'InteropVersion' => 'Interoperabilitäts-Version',
   'IntervalLength' => 'Intervallänge',
   'IntervalMode' => {
      Description => 'Interval-Modus',
      PrintConv => {
        'Still Image' => 'Standbild',
        'Time-lapse Movie' => 'Zeitraffer-Film',
      },
    },
   'IntervalNumber' => 'Intervalnummer',
   'IsCustomPictureStyle' => {
      Description => 'Bildstil benutzerdefinert',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'JFIFVersion' => 'JFIF-Version',
   'JPEGDigest' => 'JPEG Kennwert',
   'JPEGProc' => {
      Description => 'JPEG Verfahren',
      PrintConv => {
        'Lossless' => 'Verlustfrei',
      },
    },
   'JPEGQuality' => {
      Description => 'Bildqualität',
      PrintConv => {
        'Extra Fine' => 'Extra-Fein',
        'Fine' => 'Fein',
        'Standard' => 'Standardqualität',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'JPEGSize' => 'JPEG Größe',
   'JPEGTables' => 'JPEG Tabellen',
   'JobID' => 'Job-Kennung',
   'JpgFromRaw' => 'Jpg From Raw Bild',
   'JpgFromRawLength' => 'Jpg From Raw Datenlänge',
   'JpgFromRawStart' => 'Jpg From Raw Datenposition',
   'JpgRecordedPixels' => 'JPEG-Auflösung',
   'Key' => 'Schlüssel',
   'Keyword' => 'Schlüsselwort',
   'Keywords' => 'Schlüsselwörter',
   'KodakImageHeight' => 'Kodak-Bildhöhe',
   'KodakImageWidth' => 'Kodak-Bildbreite',
   'LC1' => 'Objektiv-Wert',
   'LC10' => 'Mv\' nv\'-Daten',
   'LC11' => 'AVC 1/EXP-Wert',
   'LC12' => 'Mv1 Avminsif-Wert',
   'LC14' => 'UNT_12 UNT_6-Wert',
   'LC15' => 'Incorporated Flash Suited END-Wert',
   'LC2' => 'Entfernungscode',
   'LC3' => 'K-Wert (LC3)',
   'LC4' => 'Wert für Aberrationskorrektur im Nahbereich',
   'LC5' => 'Wert für Aberrationskorrektur heller Farben',
   'LC6' => 'Wert für Aberrationskorrektur bei offener Blende',
   'LC7' => 'AF Minimum Actuation Condition-Wert',
   'LCDDisplayAtPowerOn' => {
      Description => 'LCD-Display bei Kamera Ein',
      PrintConv => {
        'Retain power off status' => 'Aus-Status beibehalten',
      },
    },
   'LCDDisplayReturnToShoot' => {
      Description => 'LC-Display->Zurück zur Aufn.',
      PrintConv => {
        'Also with * etc.' => 'Auch mit * etc.',
        'With Shutter Button only' => 'Nur mit Auslöser',
      },
    },
   'LCDIllumination' => {
      Description => 'Displaybeleuchtung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'LCDIlluminationDuringBulb' => {
      Description => 'LCD-Beleuchtung bei Langzeitaufnahme',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'LCDPanels' => 'LCD oben/LCD Rückwand',
   'LCHEditor' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Label' => 'Beschriftung',
   'Landmark' => 'Sehenswürdigkeit',
   'Language' => 'Sprache',
   'LanguageCode' => {
      Description => 'Sprache',
      PrintConv => {
        'Process default' => 'Systemeinstellung',
      },
    },
   'LanguageIdentifier' => 'Sprachkennung',
   'LastFileNumber' => 'Letzte Dateinummer',
   'LastKeywordIPTC' => 'Letztes IPTC Schlüsselwort',
   'LastKeywordXMP' => 'Letztes XMP Schlüsselwort',
   'LateralChromaticAberration' => {
      Description => 'Laterale Farbabweichung',
      PrintConv => {
        'Off' => 'Aus',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LeafData' => 'Leaf Daten',
   'LegacyIPTCDigest' => 'Bisheriger IPTC Kennwert',
   'Lens' => 'Objektiv',
   'Lens35efl' => 'Objektiv',
   'LensAFStopButton' => {
      Description => 'Funktion Objektiv-AF-Stopptaste',
      PrintConv => {
        'AE lock' => 'AE-Speicherung',
        'AE lock while metering' => 'AE-Sperre b. aktiv. Messung',
        'AF Stop' => 'AF-Stopp',
        'AF point: M->Auto/Auto->ctr' => 'AF-Messf: M->Aut./Aut.->Ctr',
        'AF start' => 'AF-Start',
        'AF stop' => 'AF-Stopp',
        'IS start' => 'Start Bildstabilisierung',
        'Switch to registered AF point' => 'Auf gesp. AF-Messf. schalten',
      },
    },
   'LensApertureRange' => 'Objektiv Blendenbereich',
   'LensData' => 'K-Wert',
   'LensDataVersion' => 'Objektivdaten-Version',
   'LensDistortionParams' => 'Objektiv Verzeichnungsparameter',
   'LensDriveNoAF' => {
      Description => 'Schärfensuche wenn AF unmöglich',
      PrintConv => {
        'Focus search off' => 'Schärfensuche aus',
        'Focus search on' => 'Schärfensuche ein',
      },
    },
   'LensFStops' => 'Objektiv-Blendenstufen',
   'LensFirmwareVersion' => 'Objektiv-Firmware-Version',
   'LensID' => 'Objektiv-ID',
   'LensIDNumber' => 'Objektivkennnummer',
   'LensInfo' => 'Objektiv-Informationen',
   'LensKind' => 'Objektivtyp / Version (LC0)',
   'LensMake' => 'Objektivhersteller',
   'LensManufacturer' => 'Objektivhersteller',
   'LensMaxApertureRange' => 'Objektiv Blendenbereich',
   'LensModel' => 'Objektivmodell',
   'LensProfileDigest' => 'Kennwert des Objektivprofils',
   'LensProperties' => 'Objektivfunktionen?',
   'LensSerialNumber' => 'Objektiv-Seriennummer',
   'LensSpec' => 'Objektiv',
   'LensType' => {
      Description => 'Objektivtyp',
      PrintConv => {
        'Uncoded lens' => 'Nicht kodiertes Objektiv',
      },
    },
   'LevelOrientation' => {
      Description => 'Level Ausrichtung',
      PrintConv => {
        'Downwards' => 'Abwärts',
        'Horizontal; Off Level' => 'Horizontal; ohne Level',
        'Rotate 180' => '180° gedreht',
        'Rotate 180; Off Level' => '180° gedreht; ohne Level',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 270 CW; Off Level' => '90° gegen den Uhrzeigersinn; ohne Level',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
        'Rotate 90 CW; Off Level' => '90° im Uhrzeigersinn; ohne Level',
        'Upwards' => 'Aufwärts',
      },
    },
   'License' => 'Lizenz',
   'LicenseType' => {
      Description => 'Lizenztyp',
      PrintConv => {
        'Commercial' => 'Kommerziell',
        'Unknown' => 'Unbekannt',
      },
    },
   'LightReading' => 'Helligkeitsauswertung',
   'LightSource' => {
      Description => 'Lichtquelle',
      PrintConv => {
        'Cloudy' => 'Bewölkt',
        'Cool White Fluorescent' => 'Neonlicht kaltweiß',
        'Day White Fluorescent' => 'Neonlicht neutralweiß',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Evening Sunlight' => 'Abendstimmung',
        'Fine Weather' => 'Wolkenlos',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'ISO Studio Tungsten' => 'ISO Studio-Kunstlicht (Glühbirne)',
        'One Touch White Balance' => 'Sofort-Weißabgleich',
        'Other' => 'Andere Lichtquelle',
        'Shade' => 'Schatten',
        'Standard Light A' => 'Standard-Licht A',
        'Standard Light B' => 'Standard-Licht B',
        'Standard Light C' => 'Standard-Licht C',
        'Tungsten' => 'Glühbirne',
        'Tungsten (Incandescent)' => 'Kunstlicht (Glühbirne)',
        'Unknown' => 'Unbekannt',
        'Warm White Fluorescent' => 'Neonlicht warmweiß',
        'White Fluorescent' => 'Neonlicht universalweiß',
      },
    },
   'LightSourceSpecial' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'LightValue' => 'Lichtwert(ISO)',
   'Lightness' => 'Graustufung',
   'LinearizationTable' => 'Linearisierungstabelle',
   'Lit' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'LiveViewAFAreaMode' => {
      Description => 'Live-View AF-Modus',
      PrintConv => {
        'Face-Priority' => 'Gesichtserkennung',
        'NormalArea' => 'Normaler Bereich',
        'SubjectTracking' => 'Objektnachführung',
        'WideArea' => 'Großer Bereich',
      },
    },
   'LiveViewAFMethod' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LiveViewAFMode' => 'Live-View AF-Modus',
   'LiveViewAFSetting' => {
      Description => 'Live View AF Einstellung',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LiveViewExposureSimulation' => {
      Description => 'Livebild-Belichtungssimulator',
      PrintConv => {
        'Disable (LCD auto adjust)' => 'Inaktiv (automatische LCD-Anzeige)',
        'Enable (simulates exposure)' => 'Aktiv (simuliert Belichtung)',
      },
    },
   'LiveViewFocusMode' => {
      Description => 'Live-View Fokus-Modus',
      PrintConv => {
        'Manual' => 'Manuell',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LiveViewMetering' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LiveViewShooting' => {
      Description => 'Live View Aufnahme',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'LocalizedCameraModel' => 'Lokalisiertes Kameramodell',
   'Location' => 'Aufnahmeort',
   'LockMicrophoneButton' => {
      Description => 'Mikrofone-Tastenfunktion',
      PrintConv => {
        'Protect (hold:record memo)' => 'Geschützt (drücken:Tonaufnahme)',
        'Record memo (protect:disable)' => 'Tonaufnahme (ungeschützt)',
      },
    },
   'LongExposureNoiseReduction' => {
      Description => 'Langzeit-Rauschminderung',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'LongExposureNoiseReduction2' => {
      Description => 'Langzeit Rauschunterdrückung 2',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (1D)' => 'Ein (1D)',
      },
    },
   'Luminance' => 'Luminanz',
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
      },
    },
   'MB-D10Batteries' => {
      Description => 'Akku-/Batterietyp',
      PrintConv => {
        'FR6 (AA lithium)' => 'FR6 (Mignon, Lithium)',
        'HR6 (AA Ni-MH)' => 'HR6 (Mignon, NiMH)',
        'LR6 (AA alkaline)' => 'LR6 (Mignon, Alkaline)',
        'ZR6 (AA Ni-Mn)' => 'ZR6 (Mignon, NiMn)',
      },
    },
   'MB-D10BatteryType' => 'Akku-/Batterietyp',
   'MB-D80Batteries' => {
      Description => 'Akku-/Batterietyp',
      PrintConv => {
        'FR6 (AA Lithium)' => 'FR6 (Mignon-Lithium)',
        'HR6 (AA Ni-MH)' => 'HR6 (Mignon-Ni-MH)',
        'LR6 (AA Alkaline)' => 'LR6 (Mignon-Alkaline)',
        'ZR6 (AA Ni-Mg)' => 'ZR6 (Mignon-Ni-Mg)',
      },
    },
   'MB-D80BatteryType' => 'MB-D80 Batterietyp',
   'MCUVersion' => 'MCU-Version',
   'MD5Digest' => 'MD5 Kennwert',
   'MIEVersion' => 'MIE-Version',
   'MIMEType' => 'MIME-Typ',
   'MPImage' => 'MP Vorschaubild',
   'MPImageFlags' => 'MP Vorschaubild Flags',
   'MPImageFormat' => 'MP Vorschaubild Format',
   'MPImageLength' => 'MP Vorschaubild-Datenlänge',
   'MPImageStart' => 'MP Vorschaubild Datenposition',
   'MPImageType' => {
      Description => 'MP Vorschaubild Typ',
      PrintConv => {
        'Large Thumbnail (VGA equivalent)' => 'Vorschaubild groß (VGA)',
        'Large Thumbnail (full HD equivalent)' => 'Vorschaubild groß (HD)',
        'Multi-frame Panorama' => 'Mehrbild Panorama',
        'Undefined' => 'Nicht definiert',
      },
    },
   'MSStereo' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Macro' => {
      Description => 'Makro',
      PrintConv => {
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Super Macro' => 'Super-Makro',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'MacroLED' => {
      Description => 'Makro LED',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'MacroMagnification' => 'Makro Vergrößerung',
   'MacroMode' => {
      Description => 'Makro-Modus',
      PrintConv => {
        'Macro' => 'Makro',
        'Macro Zoom' => 'Makro-Zoom',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Super Macro' => 'Super-Makro',
        'Tele-Macro' => 'Tele-Makro',
      },
    },
   'MagicFilter' => {
      PrintConv => {
        'Drawing' => 'Zeichnung',
        'Fish Eye' => 'Fischauge',
        'Off' => 'Aus',
        'Reflection' => 'Reflektierung',
        'Soft Focus' => 'Weichzeichner',
        'Soft Focus 2' => 'Weichzeichner 2',
        'Sparkle' => 'Perleffekt',
        'Watercolor' => 'Wasserfarben',
      },
    },
   'MagnifiedView' => {
      Description => 'Lupenfunktion',
      PrintConv => {
        'Image playback only' => 'Nur bei Bildwiedergabe',
        'Image review and playback' => 'Sofortbild u. Wiedergabe',
      },
    },
   'MainDialExposureComp' => {
      Description => 'Main Dial Belichtungskorrektur',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Make' => 'Gerätehersteller',
   'MakeAndModel' => 'Hersteller und Modell',
   'MakerNote' => 'Hersteller-eigene Daten',
   'MakerNoteApple' => 'Herstellerdaten Apple',
   'MakerNoteCanon' => 'Herstellerdaten Canon',
   'MakerNoteCasio' => 'Herstellerdaten Casio',
   'MakerNoteCasio2' => 'Herstellerdaten Casio 2',
   'MakerNoteFLIR' => 'Herstellerdaten FLIR',
   'MakerNoteFujiFilm' => 'Herstellerdaten Fuji Film',
   'MakerNoteGE' => 'Herstellerdaten GE',
   'MakerNoteGE2' => 'Herstellerdaten GE2',
   'MakerNoteHP' => 'Herstellerdaten HP',
   'MakerNoteHP2' => 'Herstellerdaten HP2',
   'MakerNoteHP4' => 'Herstellerdaten HP4',
   'MakerNoteHP6' => 'Herstellerdaten HP6',
   'MakerNoteHasselblad' => 'Herstellerdaten Hasselblad',
   'MakerNoteISL' => 'Herstellerdaten ISL',
   'MakerNoteJVC' => 'Herstellerdaten JVC',
   'MakerNoteJVCText' => 'Herstellerdaten JVC Text',
   'MakerNoteKodak10' => 'Herstellerdaten Kodak 10',
   'MakerNoteKodak1a' => 'Herstellerdaten Kodak 1a',
   'MakerNoteKodak1b' => 'Herstellerdaten Kodak 1b',
   'MakerNoteKodak2' => 'Herstellerdaten Kodak 2',
   'MakerNoteKodak3' => 'Herstellerdaten Kodak 3',
   'MakerNoteKodak4' => 'Herstellerdaten Kodak 4',
   'MakerNoteKodak5' => 'Herstellerdaten Kodak 5',
   'MakerNoteKodak6a' => 'Herstellerdaten Kodak 6a',
   'MakerNoteKodak6b' => 'Herstellerdaten Kodak 6b',
   'MakerNoteKodak7' => 'Herstellerdaten Kodak 7',
   'MakerNoteKodak8a' => 'Herstellerdaten Kodak 8a',
   'MakerNoteKodak8b' => 'Herstellerdaten Kodak 8b',
   'MakerNoteKodak9' => 'Herstellerdaten Kodak 9',
   'MakerNoteKodakUnknown' => 'Herstellerdaten Kodak Unbekannt',
   'MakerNoteKyocera' => 'Herstellerdaten Kyocera',
   'MakerNoteLeica' => 'Herstellerdaten Leica',
   'MakerNoteLeica2' => 'Herstellerdaten Leica 2',
   'MakerNoteLeica3' => 'Herstellerdaten Leica 3',
   'MakerNoteLeica4' => 'Herstellerdaten Leica 4',
   'MakerNoteLeica5' => 'Herstellerdaten Leica 5',
   'MakerNoteLeica6' => 'Herstellerdaten Leica 6',
   'MakerNoteMinolta' => 'Herstellerdaten Minolta',
   'MakerNoteMinolta2' => 'Herstellerdaten Minolta 2',
   'MakerNoteMinolta3' => 'Herstellerdaten Minolta 3',
   'MakerNoteNikon' => 'Herstellerdaten Nikon',
   'MakerNoteNikon2' => 'Herstellerdaten Nikon 2',
   'MakerNoteNikon3' => 'Herstellerdaten Nikon 3',
   'MakerNoteOlympus' => 'Herstellerdaten Olympus',
   'MakerNoteOlympus2' => 'Herstellerdaten Olympus 2',
   'MakerNotePanasonic' => 'Herstellerdaten Panasonic',
   'MakerNotePanasonic2' => 'Herstellerdaten Panasonic 2',
   'MakerNotePentax' => 'Herstellerdaten Pentax',
   'MakerNotePentax2' => 'Herstellerdaten Pentax 2',
   'MakerNotePentax3' => 'Herstellerdaten Pentax 3',
   'MakerNotePentax4' => 'Herstellerdaten Pentax 4',
   'MakerNotePentax5' => 'Herstellerdaten Pentax 5',
   'MakerNotePentax6' => 'Herstellerdaten Pentax 6',
   'MakerNotePhaseOne' => 'Herstellerdaten Phase One',
   'MakerNoteReconyx' => 'Herstellerdaten Reconyx',
   'MakerNoteRicoh' => 'Herstellerdaten Ricoh',
   'MakerNoteRicohText' => 'Herstellerdaten Ricoh Text',
   'MakerNoteSafety' => {
      Description => 'Sicherheit der Hersteller-Informationsdaten',
      PrintConv => {
        'Safe' => 'Sicher',
        'Unsafe' => 'Unsicher',
      },
    },
   'MakerNoteSamsung1a' => 'Herstellerdaten Samsung 1a',
   'MakerNoteSamsung1b' => 'Herstellerdaten Samsung 1b',
   'MakerNoteSamsung2' => 'Herstellerdaten Samsung 2',
   'MakerNoteSanyo' => 'Herstellerdaten Sanyo',
   'MakerNoteSanyoC4' => 'Herstellerdaten Sanyo C4',
   'MakerNoteSanyoPatch' => 'Herstellerdaten Sanyo Patch',
   'MakerNoteSigma' => 'Herstellerdaten Sigma',
   'MakerNoteSony' => 'Herstellerdaten Sony',
   'MakerNoteSony2' => 'Herstellerdaten Sony 2',
   'MakerNoteSony3' => 'Herstellerdaten Sony 3',
   'MakerNoteSony4' => 'Herstellerdaten Sony 4',
   'MakerNoteSony5' => 'Herstellerdaten Sony 5',
   'MakerNoteSonyEricsson' => 'Herstellerdaten Sony Ericsson',
   'MakerNoteSonySRF' => 'Herstellerdaten Sony SRF',
   'MakerNoteType' => 'Benutzerdaten Typ',
   'MakerNoteUnknown' => 'Herstellerdaten Unbekannt',
   'MakerNoteUnknownBinary' => 'Herstellerdaten Unbekannt-Binär',
   'MakerNoteUnknownText' => 'Herstellerdaten Unbekannt-Text',
   'MakerNoteVersion' => 'MakerNote-Version',
   'MakerNotes' => 'Hinweise des Herstellers',
   'ManometerPressure' => 'Gemessener Luft- bzw. Wasserdruck',
   'ManometerReading' => 'Berechnete Höhe oder Tauchtiefe',
   'ManualAFPointSelectPattern' => 'Manuelle Wahl der AF-Punkte',
   'ManualFlash' => 'Manueller Blitz',
   'ManualFlashOutput' => {
      Description => 'Manuelle Blitzstärke',
      PrintConv => {
        'Full' => 'Voll',
        'Low' => 'Gering',
        'Medium' => 'Mittel',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ManualFlashStrength' => 'Manuelle Blitzstärke',
   'ManualFocusDistance' => 'Manuelle Fokusdistanz',
   'ManualTv' => {
      Description => 'Manuelle Tv/Av-Einstellung für Manuelle Belichtung',
      PrintConv => {
        'Tv=Control/Av=Main' => 'Tv=Schnelleinstellrad/Av=Haupt-Wahlrad',
        'Tv=Main/Av=Control' => 'Tv=Haupt-Wahlrad/Av=Schnelleinstellrad',
      },
    },
   'ManufactureDate' => 'Herstellungsdatum',
   'Marked' => 'Markiert',
   'MasterDocumentID' => 'ID des Originaldokuments',
   'MatrixMetering' => 'Mehrfeldmessung',
   'MaxAperture' => 'Größte Blende',
   'MaxApertureAtCurrentFocal' => 'Größte Blende bei aktueller Brennweite',
   'MaxApertureAtMaxFocal' => 'Größte Blende bei größter Brennweite',
   'MaxApertureAtMinFocal' => 'Größte Blende bei geringster Brennweite',
   'MaxApertureValue' => 'Größtmögliche Blende',
   'MaxContinuousRelease' => 'Max. Bildanzahl pro Serie',
   'MaxFocalLength' => 'Größte Brennweite',
   'MaxJPEGTableIndex' => 'Größter Index JPEG Tabellen',
   'MaxSampleValue' => 'Größter Sample Wert',
   'MeasuredEV' => 'Gemessener LW',
   'MeasuredLV' => 'Lichtwert gemessen',
   'MeasuredRGGB' => 'Messung RGGB',
   'MeasuredRGGBData' => 'Messung RGGB',
   'MeasurementBacking' => 'Basis der Messung',
   'MeasurementFlare' => 'Messung Lichtschein',
   'MeasurementGeometry' => {
      Description => 'Geometrie der Messung',
      PrintConv => {
        '0/45 or 45/0' => '0/45 oder 45/0',
        '0/d or d/0' => '0/d oder d/0',
      },
    },
   'MeasurementIlluminant' => 'Messung Beleuchtung',
   'MeasurementObserver' => 'Messung nach',
   'MediaBlackPoint' => 'Medium-Schwarzpunkt',
   'MediaWhitePoint' => 'Medium-Weißpunkt',
   'Medium' => 'Mittelgroß',
   'MenuButtonDisplayPosition' => {
      Description => 'Positionsanzeige Menuetaste',
      PrintConv => {
        'Previous' => 'Vorherige Anzeige',
        'Previous (top if power off)' => 'Vorherige (Anfang nach AUS)',
        'Top' => 'Oben',
      },
    },
   'MenuButtonReturn' => {
      PrintConv => {
        'Previous' => 'Vorherige Anzeige',
        'Top' => 'Oben',
      },
    },
   'MetadataCreator' => 'Metadaten Ersteller',
   'MetadataDate' => 'Datum der Metadaten',
   'MetadataID' => 'Metadaten ID',
   'Metering' => {
      Description => 'Belichtungsmessung',
      PrintConv => {
        'Center-weighted' => 'Mittenbetont',
        'Matrix' => 'Mehrfeldmessung',
        'Spot' => 'Spotmessung',
      },
    },
   'MeteringMode' => {
      Description => 'Belichtungsmessmethode',
      PrintConv => {
        'Average' => 'Integralmessung',
        'Center-weighted Average' => 'Mittenbetont',
        'Center-weighted average' => 'Mittenbetont',
        'Default' => 'System',
        'Evaluative' => 'Mehrfeldmessung',
        'Multi-segment' => 'Multi-Segment',
        'Multi-spot' => 'MultiSpot',
        'Other' => 'Andere',
        'Partial' => 'Teilbild',
        'Spot' => 'Spotmessung',
        'Spot+Highlight control' => 'Spot+Helligkeitsbetont',
        'Spot+Shadow control' => 'Spot+Schattenbetont',
        'Unknown' => 'Unbekannt',
      },
    },
   'MeteringMode2' => {
      Description => 'Belichtungs-Messmethode 2',
      PrintConv => {
        'Center-weighted average' => 'Mittenbetont',
        'Multi-segment' => 'Multi-Segment',
      },
    },
   'MeteringMode3' => {
      Description => 'Belichtungs-Messmethode (3)',
      PrintConv => {
        'Multi-segment' => 'Multi-Segment',
      },
    },
   'MeteringTime' => {
      Description => 'Ausschaltzeit Belichtungsmesser',
      PrintConv => {
        'No Limit' => 'Unbegrenzt',
      },
    },
   'MinAperture' => 'Kleinste Blende',
   'MinApertureValue' => 'Kleinste Blende',
   'MinFocalLength' => 'Kleinste Brennweite',
   'MinSampleValue' => 'Kleinster Sample Wert',
   'MiniatureFilterOrientation' => {
      PrintConv => {
        'Vertical' => 'Vertikal',
      },
    },
   'MinoltaCameraSettings2' => 'Kameraeinstellungen 2',
   'MinoltaCameraSettings5D' => 'Kameraeinstellungen (5D)',
   'MinoltaCameraSettings7D' => 'Kameraeinstellungen (7D)',
   'MinoltaDate' => 'Minolta-Datum',
   'MinoltaImageSize' => {
      Description => 'Minolta-Bildgröße',
      PrintConv => {
        'Full' => 'Volle Größe',
        'Large' => 'Groß',
        'Medium' => 'Mittelgroß',
        'Small' => 'Klein',
      },
    },
   'MinoltaMakerNote' => 'Minolta-Herstellerinformationen',
   'MinoltaModelID' => 'Minolta-Modell ID',
   'MinoltaQuality' => {
      Description => 'Minolta-Bildqualität',
      PrintConv => {
        'Extra Fine' => 'Extra-Fein',
        'Fine' => 'Fein',
        'RAW+JPEG' => 'RAW + JPEG',
        'Super Fine' => 'Super-Fein',
      },
    },
   'MinoltaTime' => 'Minolta-Zeit',
   'MirrorLockup' => {
      Description => 'Spiegelverriegelung',
      PrintConv => {
        'Disable' => 'Ausgeschaltet',
        'Enable' => 'Eingeschaltet',
        'Enable: Down with Set' => 'Eingeschaltet: Abwärts mit SET (Taste)',
      },
    },
   'ModeDialPosition' => {
      PrintConv => {
        'Aperture-priority AE' => 'Blendenpriorität',
        'Manual' => 'Manuell',
        'No Flash' => 'Kein Blitz',
        'Program AE' => 'Programmautomatik',
        'Shutter speed priority AE' => 'Verschlußpriorität',
      },
    },
   'Model' => 'Kameramodell',
   'Model2' => 'Kameramodell (2)',
   'ModelID' => 'Modell ID',
   'ModelingFlash' => {
      Description => 'Einstelllicht',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ModifiedColorTemp' => 'Geänderte Farbtemperatur',
   'ModifiedDigitalGain' => 'Digitale Verstärkung geändert',
   'ModifiedPictureStyle' => {
      Description => 'Geänderter Bildstil',
      PrintConv => {
        'Faithful' => 'Natürlich',
        'Landscape' => 'Landschaft',
        'Monochrome' => 'Monochrom',
        'None' => 'Keiner',
        'Portrait' => 'Porträt',
        'User Def. 1' => 'Benutzerdefiniert 1',
        'User Def. 2' => 'Benutzerdefiniert 2',
        'User Def. 3' => 'Benutzerdefiniert 3',
      },
    },
   'ModifiedSaturation' => {
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'ModifiedSharpness' => 'Schärfe verändert',
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'High' => 'Hoch',
        'Highest' => 'Höchste',
        'Low' => 'Leicht',
        'Lowest' => 'Niedrigste',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ModifiedToneCurve' => {
      Description => 'Tonwertkurve verändert',
      PrintConv => {
        'Custom' => 'Benutzerdefiniert',
        'Manual' => 'Manuell',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Black & White' => 'Schwarz/Weiß',
        'Cloudy' => 'Bewölkt',
        'Custom' => 'Benutzerdefiniert',
        'Custom 1' => 'Benutzerdefiniert 1',
        'Custom 2' => 'Benutzerdefiniert 2',
        'Custom 3' => 'Benutzerdefiniert 3',
        'Custom 4' => 'Benutzerdefiniert 4',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'Manual Temperature (Kelvin)' => 'Manuelle Temperatur (Kelvin)',
        'Shade' => 'Schatten',
        'Tungsten' => 'Glühbirne',
        'Underwater' => 'Unterwasser',
      },
    },
   'ModifyDate' => 'Änderungsdatum',
   'MoireFilter' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'MonitorOffTime' => 'Ausschaltzeit des Monitors',
   'MonochromeFilterEffect' => {
      Description => 'Filtereffekt Monochrom',
      PrintConv => {
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'MonochromeLinear' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'MonochromeToningEffect' => {
      Description => 'Tönungseffekt Monochrom',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
      },
    },
   'Month' => 'Monat',
   'MultiBurstImageHeight' => 'Multi-Burst Bildhöhe',
   'MultiBurstImageWidth' => 'Multi-Burst Bildbreite',
   'MultiBurstMode' => {
      Description => 'Mehrfach-Burst Modus',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'MultiControllerWhileMetering' => {
      Description => 'Multicontroller bei Messung',
      PrintConv => {
        'AF point selection' => 'AF-Punkt-Auswahl',
        'Off' => 'Aus',
      },
    },
   'MultiExposure' => 'Mehrfachbelichtungsdaten',
   'MultiExposureAutoGain' => {
      Description => 'Mehrfachbelichtung Automatik',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'MultiExposureMode' => {
      Description => 'Mehrfachbelichtungsmodus',
      PrintConv => {
        'Image Overlay' => 'Bildüberlagerung',
        'Multiple Exposure' => 'Mehrfachbelichtung',
        'Off' => 'Aus',
      },
    },
   'MultiExposureShots' => 'Mehrfachbelichtung Anzahl Aufnahmen',
   'MultiExposureVersion' => 'Mehrfachbelichtungsdaten-Version',
   'MultiFrameNoiseReduction' => {
      Description => 'Ruisond. Multi Frame',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'MultiFunctionLock' => {
      PrintConv => {
        'Main dial' => 'Haupt-Wahlrad',
        'Multi-controller' => 'Multicontroller',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Quick control dial' => 'Schnelleinstellrad',
      },
    },
   'MultiSelector' => {
      Description => 'Multifunktionswähler',
      PrintConv => {
        'Do Nothing' => 'Ohne Funktion',
        'Reset Meter-off Delay' => 'Ruhezustand verzögern',
      },
    },
   'MultiSelectorPlaybackMode' => {
      Description => 'Mitteltaste Bei Wiedergabe',
      PrintConv => {
        'Choose Folder' => 'Ordner auswählen',
        'Thumbnail On/Off' => 'Bildindex ein/aus',
        'View Histograms' => 'Histogramme anzeigen',
        'Zoom On/Off' => 'Ausschnitt ein/aus',
      },
    },
   'MultiSelectorShootMode' => {
      Description => 'Mitteltaste Bei Aufnahme',
      PrintConv => {
        'Highlight Active Focus Point' => 'AF-Messfeld hervorheben',
        'Not Used' => 'Ohne Funktion',
        'Select Center Focus Point' => 'Mittleres AF-Messfeld',
      },
    },
   'MultipleExposureMode' => {
      PrintConv => {
        'Off' => 'Aus',
        'On (2 frames)' => 'Ein (2 Bilder',
        'On (3 frames)' => 'Ein (3 Bilder',
      },
    },
   'MultipleExposureSet' => {
      Description => 'Mehrfachbelichtung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Mute' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'MyColorMode' => {
      Description => 'My Color-Modus',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Custom' => 'Benutzerdefiniert',
        'Off' => 'Aus',
      },
    },
   'MyColors' => 'My Color-Modus',
   'NDFilter' => {
      Description => 'ND-Filter',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'NEFBitDepth' => {
      PrintConv => {
        'n/a (JPEG)' => 'Nicht gesetzt  (JPEG)',
      },
    },
   'NEFCompression' => {
      Description => 'RAW-Komprimierung',
      PrintConv => {
        'Lossless' => 'Verlustfrei',
        'Lossy (type 1)' => 'Verlustbehaftet (Type 1)',
        'Lossy (type 2)' => 'Verlustbehaftet (Type 2)',
        'Uncompressed' => 'Nicht komprimiert',
      },
    },
   'NEFLinearizationTable' => 'Linearisierungstabelle',
   'NativeDigest' => 'EXIF Kennwert',
   'NetExposureCompensation' => 'Net Belichtungskorrektur',
   'NewMieTag1' => 'Weiterer MIE Tag 1',
   'NewPngTag1' => 'Weiterer PNG Tag 1',
   'NewPngTag2' => 'Weiterer PNG Tag 2',
   'NewPngTag3' => 'Weiterer PNG Tag 3',
   'NewRawImageDigest' => 'Neuer RAW Image Kennwert',
   'NewsPhotoVersion' => 'IPTC-Modell-3-Version',
   'NikonCaptureData' => 'Nikon Capture-Daten',
   'NikonCaptureOutput' => 'Nikon Capture-Ausgabe',
   'NikonCaptureVersion' => 'Nikon Capture-Version',
   'NikonICCProfile' => 'Nikon ICC-Profil',
   'NikonImageSize' => {
      Description => 'Nikon Bildgröße',
      PrintConv => {
        'Large' => 'Groß',
        'Large (10.0 M)' => 'Groß (10.0M)',
        'Medium' => 'Mittel',
        'Medium (5.6 M)' => 'Mittel (5.6M)',
        'Small' => 'Klein',
        'Small (2.5 M)' => 'Klein (2.5M)',
      },
    },
   'NoMemoryCard' => {
      Description => 'Auslösesperre',
      PrintConv => {
        'Enable Release' => 'Aus',
        'Release Locked' => 'Ein',
      },
    },
   'Noise' => 'Bildrauschen',
   'NoiseFilter' => {
      Description => 'Rauschfilter',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Off' => 'Aus',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'NoiseReduction' => {
      Description => 'Rauschreduktion',
      PrintConv => {
        'Auto' => 'Automatisch',
        'High (+1)' => 'Hoch (+1)',
        'Highest (+2)' => 'Am höchsten (+2)',
        'Low' => 'Gering',
        'Low (-1)' => 'Niedrig (-1)',
        'Lowest (-2)' => 'Am niedrigsten (+2)',
        'Max' => 'Maximal',
        'Noise Filter' => 'Rauschfilter',
        'Noise Filter (ISO Boost)' => 'Rauschfilter (ISO Boost)',
        'Noise Reduction' => 'Rauschreduktion',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Strong' => 'Stark',
        'Weak' => 'Schwach',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'NoiseReduction2' => {
      Description => 'Rauschunterdrückung 2',
      PrintConv => {
        'Noise Filter' => 'Rauschfilter',
        'Noise Filter (ISO Boost)' => 'Rauschfilter (ISO Boost)',
        'Noise Reduction' => 'Rauschreduktion',
      },
    },
   'NoiseReductionIntensity' => 'Stärke Rauschunterdrückung',
   'NoiseReductionMethod' => {
      Description => 'Rauschunterdrückung Methode',
      PrintConv => {
        'Better Quality' => 'Bessere Qualität"',
        'Faster' => 'Schneller',
      },
    },
   'NoiseReductionMode' => {
      Description => 'Rauschunterdrückungsmodus',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'NoiseReductionSharpness' => 'Rauschunterdrückung Schärfe',
   'NoiseReductionValue' => 'Rauschunterdrückungswert',
   'NominalMaxAperture' => 'Nominaler AVmin',
   'NominalMinAperture' => 'Nominaler AVmax',
   'Notes' => 'Hinweise',
   'NumAFPoints' => 'Anzahl der AF-Punkte',
   'NumChannels' => 'Anzahl Kanäle',
   'NumColors' => 'Anzahl Farben',
   'NumFacePositions' => 'Gesichtspositionen',
   'NumImportantColors' => 'Anzahl Hauptfarben',
   'NumberOfFocusPoints' => {
      Description => 'Anzahl AF-Punkte',
      PrintConv => {
        '11 Points' => '11 Punkte',
        '39 Points' => '39 Punkte',
      },
    },
   'NumberOfImages' => 'Anzahl Bilder',
   'NumberOfPlanes' => 'Anzahl Ebenen',
   'OPIProxy' => {
      PrintConv => {
        'Higher resolution image does not exist' => 'Höher aufgelöstes Bild nicht vorhanden',
        'Higher resolution image exists' => 'Höher aufgelöstes Bild vorhanden',
      },
    },
   'ObjectAttributeReference' => 'Gattung',
   'ObjectCycle' => {
      Description => 'Objektzyklus',
      PrintConv => {
        'Both Morning and Evening' => 'Beides',
        'Evening' => 'Abends',
        'Morning' => 'Morgens',
      },
    },
   'ObjectDistance' => 'Objektabstand',
   'ObjectFileType' => {
      Description => 'Objekt Dateityp',
      PrintConv => {
        'Executable file' => 'Ausführbare Datei',
        'None' => 'Keiner',
        'Unknown' => 'Unbekannt',
      },
    },
   'ObjectName' => 'Titel',
   'ObjectPreviewData' => 'Objektdatenvorschau',
   'ObjectPreviewFileFormat' => 'Dateiformat der Objektdatenvorschau',
   'ObjectPreviewFileVersion' => 'Dateiformatversion der Objektdatenvorschau',
   'ObjectTypeReference' => 'Objekttypreferenz',
   'OffsetSchema' => 'Offset-Schema',
   'OldSubfileType' => {
      Description => 'Unterdatei-Typ',
      PrintConv => {
        'Full-resolution image' => 'Bild in voller Auflösung',
        'Reduced-resolution image' => 'Bild in reduzierter Auflösung',
        'Single page of multi-page image' => 'Einzelbild eines mehrseitigen Bildes',
      },
    },
   'OlympusImageHeight' => 'Olympus-Bildhöhe',
   'OlympusImageWidth' => 'Olympus-Bildbreite',
   'OneTouchWB' => {
      Description => 'Sofort-Weißabgleich',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (Preset)' => 'Ein (Preset)',
      },
    },
   'OperatingSystem' => {
      PrintConv => {
        'unknown' => 'Unbekannt',
      },
    },
   'OpticalZoom' => 'Optischer Zoom',
   'OpticalZoomCode' => 'Optischer Zoom-Code',
   'OpticalZoomMode' => {
      Description => 'Optischer Zoom-Modus',
      PrintConv => {
        'Extended' => 'Erweitert',
      },
    },
   'OpticalZoomOn' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Opto-ElectricConvFactor' => 'Optoelektronischer Umrechnungsfaktor',
   'OrderNumber' => 'Auftragsnummer',
   'Organization' => 'Organisation',
   'Orientation' => {
      Description => 'Ausrichtung',
      PrintConv => {
        'Mirror horizontal' => 'Horizontal gespiegelt',
        'Mirror horizontal and rotate 270 CW' => 'Horizontal gespiegelt und 90° gegen den Uhrzeigersinn',
        'Mirror horizontal and rotate 90 CW' => 'Horizontal gespiegelt und 90° im Uhrzeigersinn',
        'Mirror vertical' => 'Vertikal gespiegelt',
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
      },
    },
   'Orientation2' => {
      Description => 'Ausrichtung 2',
      PrintConv => {
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
      },
    },
   'OriginalFileName' => 'Original Dateiname',
   'OriginalFileType' => 'Original Dateityp',
   'OriginalImageHeight' => 'Bildbreite Original',
   'OriginalImageWidth' => 'Bildbreite Original',
   'OriginalRawCreator' => 'Original RAW Ersteller',
   'OriginalRawFileData' => 'Original Raw Daten',
   'OriginalRawFileDigest' => 'Original RAW Image Kennwert',
   'OriginalRawFileName' => 'Original Raw Dateiname',
   'OriginalRawFileType' => 'Original RAW Dateityp',
   'OriginalRawImage' => 'Original RAW Bild',
   'OriginalRawResource' => 'Original RAW Basisdaten',
   'OriginalTHMCreator' => 'Original THM Ersteller',
   'OriginalTHMFileType' => 'Original THM Dateityp',
   'OriginalTHMImage' => 'Original THM Bild',
   'OriginalTHMResource' => 'Original THM Basisdaten',
   'OriginalTransmissionReference' => 'Anbietervermerk Verweis',
   'OriginatingProgram' => 'Erstellungsprogramm',
   'OtherImage' => 'Other Image Vorschaubild',
   'OtherImageLength' => 'OtherImage Datenlänge',
   'OtherImageStart' => 'Other Image Datenposition',
   'OutputImageHeight' => 'Ausgabe-Bildhöhe',
   'OutputImageWidth' => 'Ausgabe-Bildbreite',
   'OutputResolution' => 'Ausgabe Auflösung',
   'OverlayPlanes' => 'Überlagerungsebenen',
   'Owner' => 'Besitzer',
   'OwnerID' => 'Besitzer-ID',
   'OwnerName' => 'Name des Besitzers',
   'PEFVersion' => 'PEF-Version',
   'PNGWarning' => 'PNG Warnung',
   'Padding' => 'Platzhalter',
   'PageName' => 'Seitenname',
   'PageNumber' => 'Seitenummer',
   'Pages' => 'Seiten',
   'PanOrientation' => {
      Description => 'Pan Ausrichtung',
      PrintConv => {
        'Bottom to top' => 'Von Unten nach Oben',
        'Clockwise' => 'Im Uhrzeigersinn',
        'Counter clockwise' => 'Gegen den Uhrzeigersinn',
        'Left to right' => 'Von Links nach Rechts',
        'Right to left' => 'Von Rechts nach Links',
        'Start at bottom left' => 'Links unten beginnend',
        'Start at bottom right' => 'Rechts unten beginnend',
        'Start at top left' => 'Links oben beginnend',
        'Start at top right' => 'Rechts oben beginnend',
        'Top to bottom' => 'Von Oben nach Unten',
        '[unused]' => '[nicht verwendet]',
      },
    },
   'PanasonicImageHeight' => 'Panasonic Bildhöhe',
   'PanasonicImageWidth' => 'Panasonic Bildbreite',
   'PanasonicTitle' => 'Titel',
   'PanoramaCropBottom' => 'Panorama Ausschnitt Unten',
   'PanoramaCropLeft' => 'Panorama Ausschnitt Links',
   'PanoramaCropRight' => 'Panorame Ausschnitt Rechts',
   'PanoramaCropTop' => 'Panorama Ausschnitt Oben',
   'PanoramaDirection' => {
      Description => 'Panorama-Richtung',
      PrintConv => {
        '2x2 Matrix (Clockwise)' => '2x2 Matrix (im Uhrzeigersinn)',
        'Bottom to Top' => 'Unten nach Oben',
        'Left to Right' => 'Links nach Rechts',
        'Right to Left' => 'Rechts nach Links',
        'Top to Bottom' => 'Oben nach Unten',
      },
    },
   'PanoramaFrameNumber' => 'Panorama-Bild',
   'PanoramaMode' => 'Panorama Modus',
   'PanoramaSize3D' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PentaxImageSize' => {
      Description => 'Pentax-Bildgröße',
      PrintConv => {
        '2304x1728 or 2592x1944' => '2304 x 1728 oder 2592 x 1944',
        '2560x1920 or 2304x1728' => '2560 x 1920 oder 2304 x 1728',
        '2816x2212 or 2816x2112' => '2816 x 2212 oder 2816 x 2112',
        '3008x2008 or 3040x2024' => '3008 x 2008 oder 3040 x 2024',
        'Full' => 'Voll',
      },
    },
   'PentaxModelID' => 'Pentax-Modell ID',
   'PentaxVersion' => 'Pentax-Version',
   'People' => 'Menschen',
   'Permits' => {
      PrintConv => {
        'Distribution' => 'Verteilung',
        'Reproduction' => 'Reproduktion',
      },
    },
   'PhaseDetectAF' => {
      Description => 'Auto-Fokus',
      PrintConv => {
        'Off' => 'Aus',
        'On (11-point)' => 'Ein (11-Punkt)',
        'On (39-point)' => 'Ein (39 Punkte)',
        'On (51-point)' => 'Ein (51-Punkt)',
        'On (hybrid)' => 'Ein (Hybrid)',
      },
    },
   'PhoneNumber' => 'Telefonnummer',
   'PhotoEffect' => {
      Description => 'Foto-Effekt',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Custom' => 'Benutzerdefiniert',
        'Off' => 'Aus',
      },
    },
   'PhotoEffects' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'PhotoEffectsData' => 'Bildeffekt-Daten',
   'PhotoEffectsType' => {
      Description => 'Bildeffekt-Methode',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'None' => 'Keine',
        'Tinted' => 'Getont',
      },
    },
   'PhotoInfoPlayback' => {
      Description => 'Bildinfos & Wiedergabe',
      PrintConv => {
        'Info Left-right, Playback Up-down' => 'Info <> / Wiedergabe',
        'Info Up-down, Playback Left-right' => 'Info / Wiedergabe <>',
      },
    },
   'PhotoStyle' => {
      Description => 'Fotostil',
      PrintConv => {
        'Monochrome' => 'Schwarz/Weiß',
        'Natural' => 'Natürlich',
        'Portrait' => 'Porträt',
        'Scenery' => 'Szene',
        'Standard or Custom' => 'Standard oder benutzerdefiniert',
        'Vivid' => 'Lebendig',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Pixel-Schema',
      PrintConv => {
        'BlackIsZero' => 'Schwarz ist Null',
        'Color Filter Array' => 'CFA (Farbfiltermatrix)',
        'Pixar LogL' => 'CIE Log2(L) (Log Luminanz)',
        'Pixar LogLuv' => 'CIE Log2(L)(u\',v\') (Log Luminanz und Chrominanz)',
        'Transparency Mask' => 'Transparenzmaske',
        'WhiteIsZero' => 'Weiß ist Null',
      },
    },
   'PhotoshopBGRThumbnail' => 'Photoshop BGR-Vorschaubild',
   'PhotoshopFormat' => 'Photoshop-Format',
   'PhotoshopQuality' => 'Photoshop-Qualität',
   'PhysicalImageSize' => 'Physikalische Bildgröße',
   'PictureControl' => {
      Description => 'Bildoptimierung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'PictureControlActive' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'PictureControlAdjust' => {
      Description => 'Bildoptimierung-Anpassung',
      PrintConv => {
        'Default Settings' => 'Standardeinstellungen',
        'Full Control' => 'Manuelle Einstellung',
        'Quick Adjust' => 'Schnelleinstellung',
      },
    },
   'PictureControlBase' => 'Bildoptimierung-Basis',
   'PictureControlName' => 'Bildoptimierung-Name',
   'PictureControlQuickAdjust' => 'Bildoptimierung-Schnelleinstellung',
   'PictureControlVersion' => 'Bildoptimierung-Version',
   'PictureFinish' => {
      PrintConv => {
        'Evening Scene' => 'Abendszene',
        'Monochrome' => 'Monochrom',
        'Natural' => 'Natürlich',
        'Natural+' => 'Natürlich+',
        'Night Portrait' => 'Nachtporträt',
        'Night Scene' => 'Nachtszene',
        'Portrait' => 'Porträt',
        'Wind Scene' => 'Windszene',
      },
    },
   'PictureMode' => {
      Description => 'Motivprogramm',
      PrintConv => {
        '1/2 EV steps' => '1/2 LW Schritte',
        '1/3 EV steps' => '1/3 LW Schitte',
        'Anti-blur' => 'Motivschärfe-Modus',
        'Aperture Priority' => 'Zeitautomatik',
        'Aperture Priority, Off-Auto-Aperture' => 'Zeitautomatik (Blendenring nicht auf A)',
        'Aperture-priority AE' => 'Blendenpriorität',
        'Auto' => 'Automatisch',
        'Auto PICT (Landscape)' => 'Auto PICT (Landschaft)',
        'Auto PICT (Macro)' => 'Auto PICT (Makro)',
        'Auto PICT (Portrait)' => 'Auto PICT (Porträt)',
        'Auto PICT (Sport)' => 'Auto PICT (Motiv in Bewegung)',
        'Auto PICT (Standard)' => 'Auto PICT (Normal)',
        'Autumn' => 'Herbst',
        'Backlight Silhouette' => 'Hintergrundbeleuchtung Silhouette',
        'Beach' => 'Strand',
        'Beach & Snow' => 'Strand & Schnee',
        'Black & White' => 'Schwarz/Weiß',
        'Blue' => 'Blau',
        'Blur Control' => 'Bildstabilisierung',
        'Blur Reduction' => 'Unschärfereduktion',
        'Bulb' => 'Bulb-Modus',
        'Bulb, Off-Auto-Aperture' => 'Bulb (Blendenring nicht auf A)',
        'Candlelight' => 'Kerzenlicht',
        'DOF Program' => 'Schärfentiefe-Priorität',
        'DOF Program (HyP)' => 'Schärfentiefe-Priorität (Hyper-Programm)',
        'Dark Pet' => 'Haustier (Dunkel)',
        'Digital Filter' => 'Digitalfilter',
        'Fireworks' => 'Feuerwerk',
        'Fisheye' => 'Fischauge',
        'Flash X-Sync Speed AE' => 'Blitz X-synch. Zeit',
        'Flower' => 'Blumen',
        'Food' => 'Lebensmittel',
        'Forest' => 'Wald',
        'Frame Composite' => 'Rahmen',
        'Green' => 'Grün',
        'Green Mode' => 'Grüner Modus',
        'Half-length Portrait' => 'Brustbild',
        'Hi-speed Program' => 'HS-Priorität',
        'Hi-speed Program (HyP)' => 'HS-Priorität (Hyper-Programm)',
        'Illustrations' => 'Dokument',
        'Kids' => 'Kinder',
        'Landscape' => 'Landschaft',
        'Light Pet' => 'Haustier (Hell)',
        'MTF Program' => 'MTF-Priorität',
        'MTF Program (HyP)' => 'MTF-Priorität (Hyper-Programm)',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Manual, Off-Auto-Aperture' => 'Manuell (Blendenring nicht auf A)',
        'Medium Pet' => 'Haustier (Neutrale Helligkeit)',
        'Natural' => 'Natur',
        'Natural Light' => 'Umgebungslicht',
        'Natural Light & Flash' => 'Umgebungslicht & Blitz',
        'Natural Skin Tone' => 'Nat. Hautton',
        'Night Scene' => 'Nachtszene',
        'Night Scene HDR' => 'Nachtszene',
        'Night Scene Portrait' => 'Nacht-Porträt',
        'No Flash' => 'Kein Blitz',
        'Pet' => 'Haustiere',
        'Pink' => 'Rosa',
        'Portrait' => 'Porträt',
        'Portrait 2' => 'Porträt 2',
        'Program' => 'Programmautomatik',
        'Program (HyP)' => 'Programmautomatik (Hyper-Programm)',
        'Program AE' => 'Programmautomatik',
        'Program Av Shift' => 'Av Shift-Belichtungsprogramm',
        'Program Tv Shift' => 'Tv Shift-Belichtungsprogramm',
        'Purple' => 'Violett',
        'Red' => 'Rot',
        'Self Portrait' => 'Selbstporträt',
        'Sensitivity Priority AE' => 'Blenden- & Zeitautomatik (Sv, ISO-Vorgabe)',
        'Shutter & Aperture Priority AE' => 'Empfindlichkeitsautomatik (TAv, Zeit-/Blendenvorgabe)',
        'Shutter Speed Priority' => 'Verschlusspriorität',
        'Shutter speed priority AE' => 'Verschlusspriorität',
        'Snow' => 'Schnee',
        'Soft' => 'Soft (Weichzeichnung)',
        'Sports' => 'Sport',
        'Sunset' => 'Sonnenuntergang',
        'Surf & Snow' => 'Surf & Schnee',
        'Synchro Sound Record' => 'Synchr. Sprachnotiz',
        'Underwater' => 'Unterwasser',
        'Yellow' => 'Gelb',
      },
    },
   'PictureMode2' => {
      Description => 'Motivprogramm 2',
      PrintConv => {
        'Aperture Priority' => 'Blendenpriorität',
        'Aperture Priority, Off-Auto-Aperture' => 'Zeitautomatik (Blendenring nicht auf A)',
        'Bulb' => 'Bulb-Modus',
        'Bulb, Off-Auto-Aperture' => 'Bulb (Blendenring nicht auf A)',
        'Flash X-Sync Speed AE' => 'Blitz X-synch. Zeit',
        'Green Mode' => '"Grünes" AE-Programm',
        'Manual' => 'Manuell',
        'Manual, Off-Auto-Aperture' => 'Manuell (Blendenring nicht auf A)',
        'Program AE' => 'Programmautomatik',
        'Program Av Shift' => 'Av Shift-Belichtungsprogramm',
        'Program Tv Shift' => 'Tv Shift-Belichtungsprogramm',
        'Scene Mode' => 'Motivprogramm',
        'Sensitivity Priority AE' => 'Blenden- & Zeitautomatik (Sv, ISO-Vorgabe)',
        'Shutter & Aperture Priority AE' => 'Empfindlichkeitsautomatik (TAv, Zeit-/Blendenvorgabe)',
        'Shutter Speed Priority' => 'Verschlusspriorität',
      },
    },
   'PictureModeBWFilter' => {
      Description => 'Motivprogramm S/W Filter',
      PrintConv => {
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PictureModeContrast' => 'Motivprogramm Kontrast',
   'PictureModeEffect' => {
      Description => 'Motivprogramm Effekt',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PictureModeHue' => 'Motivprogramm Farbton',
   'PictureModeSaturation' => 'Motivprogramm Farbsättigung',
   'PictureModeSharpness' => 'Motivprogramm Schärfe',
   'PictureModeTone' => {
      Description => 'Motivprogramm Tonwert',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PictureStyle' => {
      Description => 'Bildstil',
      PrintConv => {
        'Custom' => 'Benutzerdefiniert',
        'Faithful' => 'Natürlich',
        'High Saturation' => 'Hohe Farbsättigung',
        'Landscape' => 'Landschaft',
        'Low Saturation' => 'Geringe Farbsättigung',
        'Monochrome' => 'Monochrom',
        'None' => 'Keiner',
        'Portrait' => 'Porträt',
        'Unknown?' => 'Unbekannt?',
        'User Def. 1' => 'Benutzerdefiniert 1',
        'User Def. 2' => 'Benutzerdefiniert 2',
        'User Def. 3' => 'Benutzerdefiniert 3',
      },
    },
   'PictureWizardMode' => {
      PrintConv => {
        'Forest' => 'Wald',
        'Landscape' => 'Landschaft',
        'Portrait' => 'Porträt',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PixelFormat' => {
      Description => 'Pixel-Format',
      PrintConv => {
        'Black & White' => 'Schwarz/Weiß',
      },
    },
   'PixelUnits' => {
      Description => 'Pixel Einheit',
      PrintConv => {
        'Unknown' => 'Unbekannt',
        'meters' => 'Meter',
      },
    },
   'PixelsPerMeterX' => 'Pixel per Meter X',
   'PixelsPerMeterY' => 'Pixel per Meter Y',
   'PixelsPerUnitX' => 'Pixel pro X-Einheit',
   'PixelsPerUnitY' => 'Pixel pro Y-Einheit',
   'PlanarConfiguration' => {
      Description => 'Bilddatenausrichtung',
      PrintConv => {
        'Chunky' => 'Kompaktformat',
        'Planar' => 'Ebenes Format',
      },
    },
   'Planes' => 'Ebenen',
   'PostalCode' => 'Postleitzahl',
   'PowerSource' => {
      Description => 'Stromquelle',
      PrintConv => {
        'Body Battery' => 'Batterie im Gehäuse',
        'External Power Supply' => 'Externe Stromversorgung',
        'Grip Battery' => 'Batterie im Griff',
      },
    },
   'Predictor' => {
      Description => 'Prädiktor',
      PrintConv => {
        'Horizontal differencing' => 'Horizontale Differenzierung',
        'None' => 'Kein Prädiktor-Schema in Benutzung',
      },
    },
   'PresetWhiteBalance' => {
      Description => 'Weißabgleich Voreinstellung',
      PrintConv => {
        'Cloudy' => 'Bewölkt',
        'Daylight' => 'Tageslicht',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'Shade' => 'Schatten',
        'Tungsten' => 'Glühbirne',
      },
    },
   'PresetWhiteBalanceAdj' => 'Weißabgleichkorrektur-Einstellung',
   'Preview' => 'Preview-IFD-Zeiger',
   'Preview0' => 'Vorschau 0',
   'Preview1' => 'Vorschau 1',
   'Preview2' => 'Vorschau 2',
   'PreviewColorSpace' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'PreviewCropBottom' => 'Vorschau Ausschnitt Unten',
   'PreviewCropLeft' => 'Vorschau Ausschnitt Links',
   'PreviewCropRight' => 'Vorschau Ausschnitt Rechts',
   'PreviewCropTop' => 'Vorschau Ausschnitt Oben',
   'PreviewIFD' => 'Preview-IFD-Zeiger',
   'PreviewImage' => 'Vorschaubild',
   'PreviewImageBorders' => 'Vorschaubild-Ränder',
   'PreviewImageData' => 'Vorschaubild-Daten',
   'PreviewImageHeight' => 'Vorschaubild-Höhe',
   'PreviewImageLength' => 'Vorschaubild-Datenlänge',
   'PreviewImageName' => 'Vorschaubild-Name',
   'PreviewImageSize' => 'Vorschaubild-Größe',
   'PreviewImageStart' => 'Vorschaubild-Datenposition',
   'PreviewImageType' => 'Vorschaubild-Typ',
   'PreviewImageValid' => {
      Description => 'Vorschaubild gültig',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'PreviewImageWidth' => 'Vorschaubild-Breite',
   'PreviewPNG' => 'PNG Vorschaubild',
   'PreviewQuality' => {
      Description => 'Vorschaubild-Qualität',
      PrintConv => {
        'Fine' => 'Fein',
        'Superfine' => 'Superfein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'PreviewSettingsDigest' => 'Kennwert der Voreinstellungen',
   'PreviewWMF' => 'WMF Vorschaubild',
   'PrimaryAFPoint' => {
      Description => 'Primärer AF-Punkt',
      PrintConv => {
        '(none)' => '(keiner)',
        'Bottom' => 'Unten',
        'C6 (Center)' => 'C6 (Mitte)',
        'Center' => 'Mitte',
        'Far Left' => 'Weit links',
        'Far Right' => 'Weit rechts',
        'Lower-left' => 'Links unten',
        'Lower-right' => 'Rechts unten',
        'Mid-left' => 'Links mitte',
        'Mid-right' => 'Rechts mitte',
        'Top' => 'Oben',
        'Upper-left' => 'Links oben',
        'Upper-right' => 'Rechts oben',
      },
    },
   'PrimaryChromaticities' => 'Chromatizität der Primärfarben',
   'PrimaryPlatform' => 'Hauptplattform',
   'ProcessingInfo' => 'Verarbeitungsinformationen',
   'ProcessingSoftware' => 'Verarbeitungssoftware',
   'Producer' => 'Produzent',
   'ProducerKeywords' => 'Hersteller Schlüsselwörter',
   'Producers' => 'Produzent',
   'ProductID' => 'Produkt-ID',
   'ProductionCode' => 'Herstellungskennzeichen',
   'ProfileCMMType' => 'Profil CMM-Typ',
   'ProfileClass' => {
      Description => 'Profil-Klasse',
      PrintConv => {
        'Abstract Profile' => 'Abstract-Profil',
        'ColorSpace Conversion Profile' => 'Farbraum-Konvertierungsprofile',
        'DeviceLink Profile' => 'DeviceLink-Profil',
        'Display Device Profile' => 'Bildschirm-Geräteprofil',
        'Input Device Profile' => 'Eingabe-Geräteprofil',
        'NamedColor Profile' => 'Named Color-Profil',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Nikon-Profil ("nkpf")',
        'Output Device Profile' => 'Ausgabe-Geräteprofil',
      },
    },
   'ProfileConnectionSpace' => 'Profil-Verbindungsfarbraum',
   'ProfileCopyright' => 'Urheberrechtsvermerk',
   'ProfileCreator' => 'Profilersteller',
   'ProfileDateTime' => 'Profil-Erstellungszeit',
   'ProfileDescription' => 'Farbprofil Name',
   'ProfileDescriptionML' => 'Farbprofil Name mehrsprachig',
   'ProfileFileSignature' => 'Profil-Datei-Signatur',
   'ProfileID' => 'Profile-ID',
   'ProfileSequenceDesc' => 'Profilsequenz-Beschreibung',
   'ProfileToneCurve' => 'Tonwertkurve Profil',
   'ProfileType' => {
      Description => 'Profiltyp',
      PrintConv => {
        'Group 3 FAX' => 'Gruppe 3 Fax',
        'Unspecified' => 'Nicht bekannt',
      },
    },
   'ProfileVersion' => 'Profil-Version',
   'ProgramISO' => {
      Description => 'ISO Programm',
      PrintConv => {
        'Intelligent ISO' => 'ISO intelligent',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ProgramLine' => {
      Description => 'Belichtungsprogrammtyp',
      PrintConv => {
        'Depth' => 'Schärfentiefe-Priorität',
        'Hi Speed' => 'HS-Priorität',
        'MTF' => 'MTF-Priorität',
      },
    },
   'ProgramMode' => {
      Description => 'Programmmodus',
      PrintConv => {
        'Night Portrait' => 'Nachtporträt',
        'None' => 'Keiner',
        'Portrait' => 'Porträt',
        'Sports' => 'Sport',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'ProgramShift' => 'Programmverschiebung',
   'ProgramVersion' => 'Programmversion',
   'Prohibits' => {
      Description => 'Verbote',
      PrintConv => {
        'Commercial Use' => 'Kommerzielle Verwendung',
      },
    },
   'Protect' => 'Schutz',
   'Province-State' => 'Bundesland/Kanton',
   'Publisher' => 'Herausgeber',
   'Quality' => {
      Description => 'Qualität',
      PrintConv => {
        'Best' => 'Optimal',
        'Better' => 'Besser',
        'Compressed RAW' => 'Komprimiertes RAW',
        'Compressed RAW + JPEG' => 'Komprimiertes RAW + JPEG',
        'Extra Fine' => 'Extra-Fein',
        'Fine' => 'Fein',
        'Good' => 'Gut',
        'High' => 'Hoch',
        'Low' => 'Leicht',
        'Normal' => 'Standardqualität',
        'RAW + JPEG' => 'RAW+JPEG',
        'Super Fine' => 'Super-Fein',
        'Superfine' => 'Superfein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'Quality2' => 'Qualität 2',
   'QualityMode' => {
      PrintConv => {
        'Fine' => 'Fein',
      },
    },
   'QuickAdjust' => 'Schnelleinstellung',
   'QuickControlDialInMeter' => {
      Description => 'Schnelleinstellrad bei Messung',
      PrintConv => {
        'AF point selection' => 'Auswahl des AF-Messfelds',
        'Exposure comp/Aperture' => 'Belichtungskorrektur/Blende',
        'ISO speed' => 'ISO-Empfindlichkeit',
      },
    },
   'QuickShot' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'RAFVersion' => 'RAF-Version',
   'RasterizedCaption' => 'Rasterbeschreibung',
   'Rating' => 'Bewertung',
   'RatingPercent' => 'Bewertung in Prozent',
   'RawAndJpgRecording' => {
      Description => 'Dateiformat und JPEG-Qualität',
      PrintConv => {
        'JPEG (Best)' => 'JPEG (Optimal)',
        'JPEG (Better)' => 'JPEG (Besser)',
        'JPEG (Good)' => 'JPEG (Gut)',
        'RAW (DNG, Best)' => 'RAW (DNG, Optimal)',
        'RAW (DNG, Better)' => 'RAW (DNG, Besser)',
        'RAW (DNG, Good)' => 'RAW (DNG, Gut)',
        'RAW (PEF, Best)' => 'RAW (PEF, Optimal)',
        'RAW (PEF, Better)' => 'RAW (PEF, Besser)',
        'RAW (PEF, Good)' => 'RAW (PEF, Gut)',
        'RAW+JPEG (DNG, Best)' => 'RAW+JPEG (DNG, Optimal)',
        'RAW+JPEG (DNG, Better)' => 'RAW+JPEG (DNG, Besser)',
        'RAW+JPEG (DNG, Good)' => 'RAW+JPEG (DNG, Gut)',
        'RAW+JPEG (PEF, Best)' => 'RAW+JPEG (PEF, Optimal)',
        'RAW+JPEG (PEF, Better)' => 'RAW+JPEG (PEF, Besser)',
        'RAW+JPEG (PEF, Good)' => 'RAW+JPEG (PEF, Gut)',
        'RAW+Large/Fine' => 'RAW+Groß/Fein',
        'RAW+Large/Normal' => 'RAW+Groß/Normal',
        'RAW+Medium/Fine' => 'RAW+Mittel/Fein',
        'RAW+Medium/Normal' => 'RAW+Mittel/Normal',
        'RAW+Small/Fine' => 'RAW+Klein/Fein',
        'RAW+Small/Normal' => 'RAW+Klein/Normal',
      },
    },
   'RawBrightnessAdj' => 'Raw Helligkeitskorrektur',
   'RawColorAdj' => {
      Description => 'Raw Farbkorrektur',
      PrintConv => {
        'Custom' => 'Benutzerdefiniert',
        'Faithful' => 'Natürlich',
        'Shot Settings' => 'Aufnahmeeinstellung',
      },
    },
   'RawCropBottom' => 'Raw Ausschnitt Unten',
   'RawCropLeft' => 'Raw Ausschnitt Links',
   'RawCropRight' => 'Raw Ausschnitt Rechts',
   'RawCropTop' => 'Raw Ausschnitt Oben',
   'RawData' => 'Raw-Daten',
   'RawDataByteOrder' => 'RAW Daten Bytereihenfolge',
   'RawDataLength' => 'RAW-Daten Länge',
   'RawDataOffset' => 'RAW-Daten Offset',
   'RawDataUniqueID' => 'Raw-Daten eindeutige ID',
   'RawDevArtFilter' => {
      PrintConv => {
        'Drawing' => 'Zeichnung',
        'Fish Eye' => 'Fischauge',
        'Off' => 'Aus',
        'Reflection' => 'Reflektierung',
        'Soft Focus' => 'Weichzeichner',
        'Soft Focus 2' => 'Weichzeichner 2',
        'Sparkle' => 'Perleffekt',
        'Watercolor' => 'Wasserfarbe',
        'Watercolor II' => 'Wasserfarbe II',
      },
    },
   'RawDevAutoGradation' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'RawDevNoiseReduction' => {
      Description => 'RAW Rauschunterdrückung',
      PrintConv => {
        'Noise Filter' => 'Rauschfilter',
        'Noise Filter (ISO Boost)' => 'Rauschfilter (ISO Boost)',
        'Noise Reduction' => 'Rauschreduktion',
      },
    },
   'RawDevPMPictureTone' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Purple' => 'Lila',
      },
    },
   'RawDevPM_BWFilter' => {
      PrintConv => {
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'RawDevPictureMode' => {
      PrintConv => {
        'Natural' => 'Natürlich',
      },
    },
   'RawDevWhiteBalance' => {
      PrintConv => {
        'Color Temperature' => 'Farbtemperatur',
      },
    },
   'RawFile' => 'RAW Datei',
   'RawFileName' => 'RAW Dateiname',
   'RawImageCenter' => 'RAW-Bildmitte',
   'RawImageDigest' => 'RAW Image Kennwert',
   'RawImageHeight' => 'Raw Bildhöhe',
   'RawImageSegmentation' => 'Raw Bild Aufteilung',
   'RawImageSize' => 'RAW-Bildgröße',
   'RawImageWidth' => 'Raw Bildbreite',
   'RawInfoVersion' => 'RawInfo Version',
   'RawJpgQuality' => {
      Description => 'RAW JPEG-Qualität',
      PrintConv => {
        'Fine' => 'Fein',
        'Superfine' => 'Superfein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'RawJpgSize' => {
      Description => 'RAW JPEG-Größe',
      PrintConv => {
        'Large' => 'Groß',
        'Medium' => 'Mittelgroß',
        'Medium 1' => 'Mittelgroß 1',
        'Medium 2' => 'Mittelgroß 2',
        'Medium 3' => 'Mittelgroß 3',
        'Medium Movie' => 'Mittelgroßer Film',
        'Postcard' => 'Postkarte',
        'Small' => 'Klein',
        'Small Movie' => 'Kleiner Film',
        'Widescreen' => 'Breitbild',
      },
    },
   'RawMeasuredRGGB' => 'Raw Messung RGGB',
   'RecognizedFace1Age' => 'Alter erkanntes Gesicht 1',
   'RecognizedFace1Name' => 'Name erkanntes Gesicht 1',
   'RecognizedFace1Position' => 'Position erkanntes Gesicht 1',
   'RecognizedFace2Age' => 'Alter erkanntes Gesicht 2',
   'RecognizedFace2Name' => 'Name erkanntes Gesicht 2',
   'RecognizedFace2Position' => 'Position erkanntes Gesicht 2',
   'RecognizedFace3Age' => 'Alter erkanntes Gesicht 3',
   'RecognizedFace3Name' => 'Name erkanntes Gesicht 3',
   'RecognizedFace3Position' => 'Position erkanntes Gesicht 3',
   'RecordMode' => {
      Description => 'Aufzeichnungsmodus',
      PrintConv => {
        'Aperture Priority' => 'Blendenpriorität',
        'Manual' => 'Manuell',
        'Shutter Priority' => 'Verschlusspriorität',
      },
    },
   'RecordingMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
        'Landscape' => 'Landschaft',
        'Manual' => 'Manuell',
        'Night Scene' => 'Nachtszene',
        'Portrait' => 'Porträt',
      },
    },
   'RedAdjust' => 'Rot-Korrektur',
   'RedBalance' => 'Farbabgleich Rot',
   'RedEyeCorrection' => {
      Description => 'Rote-Augen-Reduzierung',
      PrintConv => {
        'Automatic' => 'Automatisch',
        'Click on Eyes' => 'Klick auf Augen',
        'Off' => 'Aus',
      },
    },
   'RedEyeReduction' => {
      Description => 'Rote Augen Reduzierung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'RedMatrixColumn' => 'Rot-Matrixspalte',
   'RedSaturation' => 'Farbsättingung Rot',
   'RedTRC' => 'Rot-Tonwertwiedergabe-Kurve',
   'RedX' => 'Rotpunkt X',
   'RedY' => 'Rotpunkt Y',
   'ReductionMatrix1' => 'Reduktionsmatrix 1',
   'ReductionMatrix2' => 'Reduktionsmatrix 2',
   'ReferenceBlackWhite' => 'Schwarz-Weiß-Referenzpunkte',
   'ReferenceDate' => 'Referenzdatum',
   'ReferenceNumber' => 'Referenznummer',
   'ReferenceService' => 'Referenzdienst',
   'References' => 'Verweise',
   'RelatedImageFileFormat' => 'Dateiformat der Bilddaten',
   'RelatedImageHeight' => 'Bildhöhe',
   'RelatedImageWidth' => 'Bildbreite',
   'RelatedSoundFile' => 'Zugehörige Audio-Datei',
   'ReleaseButtonToUseDial' => {
      Description => 'Tastenverhalten',
      PrintConv => {
        'No' => 'Gedrückt halten',
        'Yes' => 'Ein & aus',
      },
    },
   'ReleaseDate' => 'Veröffentlichungsdatum',
   'ReleaseMode' => {
      Description => 'Auslösemodus',
      PrintConv => {
        'AE Bracketing' => 'Belichtungsreihe',
        'Contrast Bracketing' => 'Kontrast Belichtungsreihe',
        'High Speed Burst' => 'Aufnahme Burst',
        'WB Bracketing' => 'Weißabgleich-Belichtungsreihe',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ReleaseTime' => 'Veröffentlichungszeit',
   'RemoteOnDuration' => 'Fernauslöser',
   'RenderingIntent' => {
      Description => 'Umrechnungsmethode',
      PrintConv => {
        'ICC-Absolute Colorimetric' => 'Absolut farbmetrisch',
        'Media-Relative Colorimetric' => 'Relativ farbmetrisch',
        'Perceptual' => 'Wahrnehmungsorientiert (perzeptiv, fotografisch)',
        'Saturation' => 'Sättigungserhaltend',
      },
    },
   'RepeatingFlashCount' => 'Stroboskopblitz Anzahl',
   'RepeatingFlashOutput' => 'Stroboskopblitz Leistung',
   'RepeatingFlashRate' => 'Stroboskopblitz Freq.',
   'ResampleParamsQuality' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
      },
    },
   'Resaved' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'Resolution' => 'Bildauflösung',
   'ResolutionUnit' => {
      Description => 'Einheit der X- und Y-Auflösung',
      PrintConv => {
        'None' => 'Keine',
        'inches' => 'Zoll',
      },
    },
   'RetouchHistory' => {
      Description => 'Bildbearbeitungsschritte',
      PrintConv => {
        'B & W' => 'Schwarz/Weiß',
        'Color Custom' => 'Farbabgleich',
        'Cyanotype' => 'Blauton',
        'Distortion Control' => 'Verzeichnungskontrolle',
        'Fisheye' => 'Fischauge',
        'Image Overlay' => 'Bildmontage',
        'None' => 'Keine',
        'Perspective Control' => 'Perspektivenkontrolle',
        'Red Eye' => 'Rote-Augen-Korrektur',
        'Sky Light' => 'Skylight',
        'Small Picture' => 'Kompaktbild',
        'Soft Filter' => 'Weichzeichner',
        'Trim' => 'Beschneiden',
        'Warm Tone' => 'Warmer Farbton',
      },
    },
   'ReverseIndicators' => 'Skalen spiegeln',
   'ReverseShutterSpeedAperture' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'RevisionDate' => 'Revisionsdatum',
   'RevisionNumber' => 'Revisionsnummer',
   'RicohDate' => 'Ricoh Datum',
   'RicohImageHeight' => 'Ricoh-Bildhöhe',
   'RicohImageWidth' => 'Ricoh-Bildbreite',
   'Rights' => 'Rechte',
   'Rotation' => {
      Description => 'Ausrichtung',
      PrintConv => {
        'Horizontal (Normal)' => 'Horizontal (normal)',
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
        'Rotated 180' => '180° gedreht',
        'Rotated 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotated 90 CW' => '90° im Uhrzeigersinn',
      },
    },
   'RowsPerStrip' => 'Anzahl der Bild-Zeilen',
   'SPIFFVersion' => 'SPIFF-Version',
   'SRAWQuality' => {
      Description => 'SRAW Qualität',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SRActive' => {
      Description => 'Bildstabilisator',
      PrintConv => {
        'No' => 'Deaktiviert',
        'Yes' => 'Aktiviert',
      },
    },
   'SRFocalLength' => 'SR Brennweite',
   'SRHalfPressTime' => 'Auslöseverzögerung',
   'SRResult' => {
      Description => 'Bildstabilisator',
      PrintConv => {
        'Not stabilized' => 'Nicht stabilisiert',
      },
    },
   'SVGVersion' => 'SVG-Version',
   'SafetyShift' => {
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable (ISO speed)' => 'Möglich (ISO Empfindlichkeit)',
        'Enable (Tv/Av)' => 'Möglich (Tv/Av)',
      },
    },
   'SafetyShiftInAvOrTv' => {
      Description => 'Safety Shift in AV oder TV',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'SampleFormat' => {
      PrintConv => {
        'Complex int' => 'Komplexer Integer',
        'Float' => 'Fließkommawert',
        'Signed' => 'Vorzeichenbehafteter Integer',
        'Undefined' => 'Nicht definiert',
        'Unsigned' => 'Vorzeichenloser Integer',
      },
    },
   'SamplesPerPixel' => 'Anzahl der Komponenten',
   'Saturation' => {
      Description => 'Farbsättigung',
      PrintConv => {
        '+1 (medium high)' => '+1 (Leicht erhöht)',
        '+2 (high)' => '+2 (Hohe Farbsättigung)',
        '+3 (very high)' => '+3 (Sehr hoch)',
        '+4 (highest)' => '+4',
        '+4 (maximum)' => '+4',
        '-1 (medium low)' => '-1 (Leicht verringert)',
        '-2 (low)' => '-2 (Geringe Farbsättigung)',
        '-3 (very low)' => '-3 (Sehr gering)',
        '-4 (lowest)' => '-4',
        '-4 (minimum)' => '-4',
        '0 (normal)' => '0 (Normal)',
        'B&W' => 'Schwarz/Weiß',
        'B&W Green Filter' => 'Schwarz-Weiß Grünfilter',
        'B&W Red Filter' => 'Schwarz-Weiß Rotfilter',
        'B&W Sepia' => 'Schwarz-Weiß Sepia',
        'B&W Yellow Filter' => 'Schwarz-Weiß Gelbfilter',
        'Black & White' => 'Schwarz/Weiß',
        'Film Simulation' => 'Film-Simulation',
        'High' => 'Hohe Farbsättigung',
        'Low' => 'Geringe Farbsättigung',
        'Medium High' => 'Mittel-Hoch',
        'Medium Low' => 'Mittel-Gering',
        'Natural' => 'Natürlich',
        'None' => 'Nicht gesetzt',
        'None (B&W)' => 'Keine (S&W)',
        'Toning Effect' => 'Tönungseffekt',
        'Vintage B&W' => 'Vintage Schwarz-Weiß',
        'Vivid' => 'Lebhaft',
      },
    },
   'SaturationAdj' => 'Sättigungskorrektur',
   'SaturationAdjustmentAqua' => 'Farbsättigung Korrektur Cyan',
   'SaturationAdjustmentBlue' => 'Farbsättigung Korrektur Blau',
   'SaturationAdjustmentGreen' => 'Farbsättigung Korrektur Grün',
   'SaturationAdjustmentMagenta' => 'Farbsättigung Korrektur Magenta',
   'SaturationAdjustmentOrange' => 'Farbsättigung Korrektur Orange',
   'SaturationAdjustmentPurple' => 'Farbsättigung Korrektur Lila',
   'SaturationAdjustmentRed' => 'Farbsättigung Korrektur Rot',
   'SaturationAdjustmentYellow' => 'Farbsättigung Korrektur Gelb',
   'SaturationFaithful' => {
      Description => 'Farbsättigung Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationInfo' => 'Farbsättigung',
   'SaturationLandscape' => {
      Description => 'Farbsättigung Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationMonochrome' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationNeutral' => {
      Description => 'Farbsättigung Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationPlanes' => 'Sättigungsebenen',
   'SaturationPortrait' => {
      Description => 'Farbsättigung Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationSetting' => 'Sättigungseinstellung',
   'SaturationStandard' => {
      Description => 'Farbsättigung Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationUnknown' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationUserDef1' => {
      Description => 'Farbsättigung Benutzerdefiniert 1',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationUserDef2' => {
      Description => 'Farbsättigung Benutzerdefiniert 2',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SaturationUserDef3' => {
      Description => 'Farbsättigung Benutzerdefiniert 3',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ScaleFactor35efl' => 'Formatfaktor zu 35 mm',
   'ScanImageEnhancer' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Scene' => 'Szene',
   'SceneAssist' => 'Szenen-Assistent',
   'SceneCaptureType' => {
      Description => 'Szenenaufnahmetyp',
      PrintConv => {
        'Landscape' => 'Landschaft',
        'Night' => 'Nachtszene',
        'Portrait' => 'Porträt',
      },
    },
   'SceneMode' => {
      Description => 'Szenen-Modus',
      PrintConv => {
        '3D Sweep Panorama' => '3D Sweep-Panorama',
        'Anti Motion Blur' => 'Verwackelungsschutz',
        'Aperture Priority' => 'Blendenpriorität',
        'Auto' => 'Automatisch',
        'Available Light' => 'Verfügbares Licht',
        'Baby' => 'Kleinkind',
        'Beach' => 'Strand',
        'Beach & Snow' => 'Strand & Schnee',
        'Behind Glass' => 'Hinterglas',
        'Candle' => 'Kerzenlicht',
        'Candlelight' => 'Kerzenlicht',
        'Children' => 'Kinder',
        'Color Effects' => 'Farbeffekte',
        'Cont. Priority AE' => 'Andauernde AE Priorität',
        'Creative Control' => 'Kreativprogramm',
        'Digital Filter' => 'Digitaler Filter',
        'Documents' => 'Dokumente',
        'Fireworks' => 'Feuerwerk',
        'Food' => 'Lebensmittel',
        'Handheld Night Shot' => 'Nachtaufnahme händisch',
        'High Key' => 'High-Key',
        'High Sensitivity' => 'Hohe Empfindlichkeit',
        'Indoor' => 'Innenaufnahme',
        'Intelligent ISO' => 'Iso Intelligent',
        'Landscape' => 'Landschaft',
        'Landscape+Portrait' => 'Landschaft+Porträt',
        'Low Key' => 'Low-Key',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'My Mode' => 'Benutzerdefiniert',
        'Night Landscape' => 'Landschaft bei Nacht',
        'Night Portrait' => 'Nachtporträt',
        'Night Scene' => 'Nachtszene',
        'Night Scenery' => 'Nachtszene',
        'Night View/Portrait' => 'Abendszene/Porträt',
        'Night+Portrait' => 'Nacht+Porträt',
        'Off' => 'Aus',
        'Pet' => 'Haustiere',
        'Portrait' => 'Porträt',
        'Program' => 'Programmautomatik',
        'Self Portrait' => 'Selbstporträt',
        'Self Portrait+Self Timer' => 'Selbstporträt+Selbstauslöser',
        'Shutter Priority' => 'Verschlusspriorität',
        'Snow' => 'Schnee',
        'Sports' => 'Sport',
        'Spot' => 'Spotmessung',
        'Starry Night' => 'Sternennacht',
        'Sunset' => 'Sonnenuntergang',
        'Super Macro' => 'Super-Makro',
        'Sweep Panorama' => 'Sweep-Panorama',
        'Underwater' => 'Unterwasser',
        'Underwater Macro' => 'Unterwasser Makro',
        'Underwater Wide1' => 'Unterwasserlandschaft',
        'Underwater Wide2' => 'Unterwasserlandschaft 2',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SceneModeUsed' => {
      Description => 'Szenen-Modus',
      PrintConv => {
        'Aperture Priority' => 'Blendenpriorität',
        'Beach' => 'Strand',
        'Candlelight' => 'Kerzenlicht',
        'Children' => 'Kinder',
        'Fireworks' => 'Feuerwerk',
        'Landscape' => 'Landschaft',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Night Landscape' => 'Landschaft bei Nacht',
        'Night Portrait' => 'Nachtporträt',
        'Portrait' => 'Porträt',
        'Program' => 'Programmautomatik',
        'Shutter Priority' => 'Verschlusspriorität',
        'Snow' => 'Schnee',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'SceneSelect' => {
      PrintConv => {
        'Night' => 'Nachtszene',
        'Off' => 'Aus',
      },
    },
   'SceneType' => {
      Description => 'Szenentyp',
      PrintConv => {
        'Directly photographed' => 'Direkt aufgenommenes Bild',
      },
    },
   'Security' => {
      Description => 'Sicherheit',
      PrintConv => {
        'Locked for annotations' => 'Gesperrt für Anmerkungen',
        'None' => 'Keine',
        'Password protected' => 'Passwort geschützt',
        'Read-only enforced' => 'Nur Lesen - erzwungen',
        'Read-only recommended' => 'Nur Lesen - vorgeschlagen',
      },
    },
   'SecurityClassification' => {
      Description => 'Sicherheitsklassifizierung',
      PrintConv => {
        'Confidential' => 'Vertraulich',
        'Restricted' => 'Eingeschränkt',
        'Secret' => 'Geheim',
        'Top Secret' => 'Streng geheim',
        'Unclassified' => 'Nicht klassifiziert',
      },
    },
   'SelectAFAreaSelectMode' => {
      Description => 'AF-Bereich Auswahlmodus',
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
        'Select AF-modes' => 'Wahl AF-Modus',
      },
    },
   'SelectableAFPoint' => {
      Description => 'Wählbares AF-Feld',
      PrintConv => {
        '11 points' => '11 Felder',
        '19 Points, Multi-controller selectable' => '19 Punkte, wählbar mit Multicontroller',
        '19 points' => '19 Felder',
        '45 points' => '45 Felder',
        'Inner 9 Points, Multi-controller selectable' => 'Innere 9 Punkte, wählbar mit Multicontroller',
        'Inner 9 points' => 'Innere 9 Felder',
        'Outer 9 Points, Multi-controller selectable' => 'Äußere 9 Punkte, wählbar mit Multicontroller',
        'Outer 9 points' => 'Äußere 9 Felder',
      },
    },
   'SelfTimer' => {
      Description => 'Selbstauslöser',
      PrintConv => {
        '10 s / 3 pictures' => '10 s / 3 Bilder',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'SelfTimer2' => 'Selbstauslöser (2)',
   'SelfTimerMode' => 'Selbstauslösermodus',
   'SelfTimerTime' => 'Selbstauslöser-Vorlaufzeit',
   'SensingMethod' => {
      Description => 'Messmethode',
      PrintConv => {
        'Color sequential area' => 'Color-Sequential-Area-Sensor',
        'Color sequential linear' => 'Color-Sequential-Linear-Sensor',
        'Monochrome area' => 'Monochrom-Sensor',
        'Monochrome linear' => 'Monochrom-linearer Sensor',
        'Not defined' => 'Nicht definiert',
        'One-chip color area' => 'Ein-Chip-Farbsensor',
        'Three-chip color area' => 'Drei-Chip-Farbsensor',
        'Trilinear' => 'Trilinearer Sensor',
        'Two-chip color area' => 'Zwei-Chip-Farbsensor',
      },
    },
   'SensitivityAdjust' => 'ISO-Empfindlichkeitsanpassung',
   'SensitivitySteps' => {
      Description => 'Empfindlichkeits-Schritte',
      PrintConv => {
        '1 EV Steps' => '1 LW-Schritte',
        'As EV Steps' => 'Wie LW-Schritte',
      },
    },
   'SensitivityType' => {
      Description => 'Art der Empfindlichkeit',
      PrintConv => {
        'ISO Speed' => 'ISO Empfindlichkeit',
        'Recommended Exposure Index' => 'Empfohlener Belichtungsindex',
        'Recommended Exposure Index and ISO Speed' => 'Empfohlener Belichtungsindex und ISO Empfindlichkeit',
        'Standard Output Sensitivity' => 'Standard Ausgabeempfindlichkeit',
        'Standard Output Sensitivity and ISO Speed' => 'Standard Ausgabeempfindlichkeit und ISO Empfindlichkeit',
        'Standard Output Sensitivity and Recommended Exposure Index' => 'Standard Ausgabeempfindlichkeit und empfohlener Belichtungsindex',
        'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed' => 'Standard Ausgabeempfindlichkeit,empfohlener Belichtungsindex und ISO Empfindlichkeit',
        'Unknown' => 'Unbekannt',
      },
    },
   'SensorBlueLevel' => 'Sensor Blau-Level',
   'SensorBottomBorder' => 'Sensor unterer Rand',
   'SensorCleaning' => {
      Description => 'Sensorreinigung',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'SensorHeight' => 'Sensor Höhe',
   'SensorImageHeight' => 'Sensor-Bildhöhe',
   'SensorImageWidth' => 'Sensor-Bildbreite',
   'SensorLeftBorder' => 'Sensor linker Rand',
   'SensorPixelSize' => 'Sensor-Pixelgröße',
   'SensorRedLevel' => 'Sensor Rot-Level',
   'SensorRightBorder' => 'Sensor rechter Rand',
   'SensorSize' => 'Sensorgröße',
   'SensorTemperature' => 'Sensor Temperatur',
   'SensorTopBorder' => 'Sensor oberer Rand',
   'SensorWidth' => 'Sensor Breite',
   'SequenceNumber' => {
      Description => 'Bildsequenznummer',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SequentialShot' => {
      Description => 'Reihenaufnahme',
      PrintConv => {
        'Adjust Exposure' => 'Belichtungskorrektur',
        'Best' => 'Beste',
        'None' => 'Keine',
      },
    },
   'SerialNumber' => 'Seriennummer',
   'SerialNumberFormat' => 'Seriennummer-Format',
   'ServiceIdentifier' => 'Service-ID',
   'SetButtonCrossKeysFunc' => {
      Description => 'SET Taste/Kreuztaste Funkt.',
      PrintConv => {
        'Cross keys: AF point select' => 'Kreuztaste:AF Feld Auswahl',
        'Set: Flash Exposure Comp' => 'SET:Blitzbelichtungskorrektur',
        'Set: Parameter' => 'SET:Parameter ändern',
        'Set: Picture Style' => 'SET:Bildstil',
        'Set: Playback' => 'SET:Wiedergabe',
        'Set: Quality' => 'SET:Qualität',
      },
    },
   'SetButtonFunction' => 'Funktion SET-Taste b. Aufnahme',
   'SetButtonWhenShooting' => {
      Description => 'SET-Taste bei Aufnahme',
      PrintConv => {
        'Change ISO speed' => 'ISO-Wert ändern',
        'Change parameters' => 'Parameter ändern',
        'Default (no function)' => 'Normal (gesperrt)',
        'Disabled' => 'Gesperrt',
        'Flash exposure compensation' => 'Blitzbelichtungskorrektur',
        'ISO speed' => 'ISO-Empfindlichkeit',
        'Image playback' => 'Bildwiedergabe',
        'Image quality' => 'Qualität ändern',
        'Image size' => 'Bildgröße',
        'LCD monitor On/Off' => 'LCD-Monitor Ein/Aus',
        'Menu display' => 'Menüanzeige',
        'Normal (disabled)' => 'Normal (gesperrt)',
        'Picture style' => 'Bildstil',
        'Quick control screen' => 'Schnelleinstellung Bildschirm',
        'Record func. + media/folder' => 'Aufnahme-Funktion + Medium/Ordner',
        'Record movie (Live View)' => 'Movie-Aufnahme (Livebild)',
        'White balance' => 'Weißabgleich',
      },
    },
   'SetFunctionWhenShooting' => {
      Description => 'SET-Taste bei Aufnahme',
      PrintConv => {
        'Change Parameters' => 'Parameter ändern',
        'Change Picture Style' => 'Bildstil',
        'Change quality' => 'Qualität ändern',
        'Default (no function)' => 'Normal (gesperrt)',
        'Image replay' => 'Bildwiedergabe',
        'Menu display' => 'Menüanzeige',
      },
    },
   'ShadingCompensation' => {
      Description => 'Schattenaufhellung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ShadingCompensation2' => {
      Description => 'Schattenaufhellung 2',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Shadow' => 'Schatten',
   'Shadows' => 'Schatten',
   'ShakeReduction' => {
      Description => 'Bildstabilisator (Einstellung)',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ShakeReductionInfo' => 'Bildstabilisator',
   'SharpenDetail' => 'Schärfungsdetail',
   'SharpenRadius' => 'Schärfungsradius',
   'Sharpening' => {
      Description => 'Schärfung',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Medium High' => 'Mittel',
        'Off' => 'Aus',
      },
    },
   'SharpeningAdj' => 'Schärfekorrektur',
   'Sharpness' => {
      Description => 'Schärfe',
      PrintConv => {
        '+1 (medium hard)' => '+1 (Leicht erhöht)',
        '+2 (hard)' => '+2 (Stark)',
        '+3 (very hard)' => '+3 (Sehr hoch)',
        '+4 (hardest)' => '+4',
        '+4 (maximum)' => '+4',
        '-1 (medium soft)' => '-1 (Leicht verringert)',
        '-2 (soft)' => '-2 (Leicht)',
        '-3 (very soft)' => '-3 (Sehr weich)',
        '-4 (minimum)' => '-4',
        '-4 (softest)' => '-4',
        '0 (normal)' => '0 (Normal)',
        'Film Simulation' => 'Film-Simulation',
        'Hard' => 'Stark',
        'Sharp' => 'Hart',
        'Soft' => 'Leicht',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessAdj' => 'Schärfekorrektur',
   'SharpnessFactor' => 'Schärfungsfaktor',
   'SharpnessFaithful' => {
      Description => 'Schärfe Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'High' => 'Hoch',
        'Highest' => 'Höchste',
        'Low' => 'Leicht',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessLandscape' => {
      Description => 'Schärfe Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessMonochrome' => {
      Description => 'Schärfe Monochrom',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessNeutral' => {
      Description => 'Schärfe Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessOvershoot' => 'Schärfe Grenzwertüberschreitung',
   'SharpnessPortrait' => {
      Description => 'Schärfe Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessSetting' => 'Schärfeeinstellung',
   'SharpnessStandard' => {
      Description => 'Schärfe Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessTable' => 'Schärfe Tabelle',
   'SharpnessThreshold' => 'Schärfe Grenzwert',
   'SharpnessUndershoot' => 'Schärfe Grenzwertunterschreitung',
   'SharpnessUnknown' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessUserDef1' => {
      Description => 'Schärfe Benutzerdefiniert 1',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessUserDef2' => {
      Description => 'Schärfe Benutzerdefiniert 2',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SharpnessUserDef3' => {
      Description => 'Schärfe Benutzerdefiniert 3',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ShootingInfoDisplay' => {
      Description => 'Aufnahmeinfo-Ansicht',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Manual (dark on light)' => 'Manuell - Dunkel auf Hell',
        'Manual (light on dark)' => 'Manuell - Hell auf dunkel',
      },
    },
   'ShootingMode' => {
      Description => 'Aufnahmemodus',
      PrintConv => {
        'Aperture Priority' => 'Blendenpriorität',
        'Baby' => 'Kleinkind',
        'Beach' => 'Strand',
        'Candlelight' => 'Kerzenlicht',
        'Color Effects' => 'Farbeffekte',
        'Fireworks' => 'Feuerwerk',
        'Food' => 'Lebensmittel',
        'High Sensitivity' => 'Hohe Empfindlichkeit',
        'Macro' => 'Makro',
        'Manual' => 'Manuell',
        'Night Portrait' => 'Nachtporträt',
        'Night Scenery' => 'Nachtszene',
        'Pet' => 'Haustiere',
        'Portrait' => 'Porträt',
        'Program' => 'Programmautomatik',
        'Self Portrait' => 'Selbstportait',
        'Shutter Priority' => 'Verschlusspriorität',
        'Snow' => 'Schnee',
        'Sports' => 'Sport',
        'Spot' => 'Spotmessung',
        'Starry Night' => 'Sternennacht',
        'Sunset' => 'Sonnenuntergang',
        'Underwater' => 'Unterwasser',
      },
    },
   'ShootingModeSetting' => {
      Description => 'Messfeldsteuerung',
      PrintConv => {
        'Continuous' => 'Serienaufnahme',
        'Delayed Remote' => 'Fernauslöser m. Vorlauf',
        'Quick-response Remote' => 'Fernauslöser',
        'Self-timer' => 'Selbstauslöser',
        'Single Frame' => 'Einzelbild',
      },
    },
   'ShortDescription' => 'Kurzbeschreibung',
   'ShortDocumentID' => 'Kurze Bild-ID',
   'ShortReleaseTimeLag' => {
      Description => 'Verkürzte Auslöseverzögerung',
      PrintConv => {
        'Disable' => 'Ausgeschaltet',
        'Enable' => 'Eingeschaltet',
      },
    },
   'ShotInfoVersion' => 'Aufnahmeinfo-Version',
   'Shutter-AELock' => {
      Description => 'Auslöser/AE-Speicherung',
      PrintConv => {
        'AE lock/AF' => 'AE-Speicherung/AF',
        'AE/AF, No AE lock' => 'AE/AF, keine AE-Speicherung',
        'AF/AE lock' => 'AF/AE-Speicherung',
        'AF/AF lock' => 'AF/AF-Speicherung',
        'AF/AF lock, No AE lock' => 'AF/AF-Speicherung, keine AE-Speicherung',
      },
    },
   'ShutterAELButton' => 'Auslöser/AE-Speichertaste',
   'ShutterButtonAFOnButton' => {
      Description => 'Auslöser/AF-Starttaste',
      PrintConv => {
        'AE lock/Metering + AF start' => 'AESpeicherung/Messung+AFStart',
        'Metering + AF start' => 'Messung+AFStart',
        'Metering + AF start/AF stop' => 'Messung+AFStart / AFStopp',
        'Metering + AF start/disable' => 'Messung+AFStart/Deaktiviert',
        'Metering start/Meter + AF start' => 'Messung Start/Mess.+AFStart',
      },
    },
   'ShutterCount' => 'Anzahl der Auslösungen',
   'ShutterCurtainSync' => {
      Description => 'Verschluss-Synchronisation',
      PrintConv => {
        '1st-curtain sync' => '1. Verschlussvorhang',
        '2nd-curtain sync' => '2. Verschlussvorhang',
      },
    },
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => 'Blendenpriorität',
        'Auto' => 'Automatisch',
      },
    },
   'ShutterReleaseButtonAE-L' => {
      Description => 'Belichtungsspeicher',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ShutterReleaseMethod' => {
      PrintConv => {
        'Continuous Shooting' => 'Serienaufnahme',
        'Single Shot' => 'Einzelbild',
      },
    },
   'ShutterReleaseNoCFCard' => {
      Description => 'Verschlussausl. ohne Karte',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'ShutterReleaseTiming' => {
      PrintConv => {
        'Priority on focus' => 'Schärfepriorität',
        'Priority on shutter' => 'Verschlußpriorität',
      },
    },
   'ShutterSpeed' => 'Belichtungsdauer',
   'ShutterSpeedDisplayed' => 'Angezeigte Belichtungszeit',
   'ShutterSpeedRange' => {
      Description => 'Einstellung Blendenbereich',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ShutterSpeedSetting' => 'Belichtungszeit Einstellung',
   'ShutterSpeedValue' => 'Belichtungszeit',
   'SimilarityIndex' => 'Bildgleichheits-Index',
   'SingleFrameBracketing' => {
      Description => 'Einzelbild-Belichtungsreihe',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
      },
    },
   'SlaveFlashMeteringSegments' => 'Slave-Blitz-Messfeld',
   'SlideShow' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'SlowShutter' => {
      Description => 'Langzeitbelichtungseinstellung',
      PrintConv => {
        'Night Scene' => 'Nachtszene',
        'None' => 'Keine',
        'Off' => 'Aus',
        'On' => 'Ein',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SlowSync' => {
      Description => 'Slow-Synchro',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'SoftSkinEffect' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'SonyImageSize' => {
      Description => 'Sony Bildgröße',
      PrintConv => {
        'Large' => 'Groß',
        'Large (16:9)' => 'Groß (16:9)',
        'Large (3:2)' => 'Groß (3:2)',
        'Medium' => 'Mittel',
        'Medium (16:9)' => 'Mittel (16:9)',
        'Medium (3:2)' => 'Mittel (3:2)',
        'Small' => 'Klein',
        'Small (16:9)' => 'Klein (16:9)',
        'Small (3:2)' => 'Klein (3:2)',
      },
    },
   'Source' => 'Quelle',
   'SpatialFrequencyResponse' => 'Raumfrequenz-Antwort',
   'SpecialEffectMode' => {
      Description => 'Spezialeffekte Modus',
      PrintConv => {
        'Mist Removal' => 'Dunstentfernung',
        'Off' => 'Aus',
        'Vivid Landscape' => 'Lebende Landschaft',
      },
    },
   'SpecialEffectsOpticalFilter' => {
      Description => 'Spezialeffekt Filter',
      PrintConv => {
        'Colored' => 'Farbfilter',
        'Diffusion' => 'Diffusionsfilter',
        'Multi-image' => 'Mehrfachbildfilter',
        'None' => 'Keiner',
        'Polarizing' => 'Polarisationsfilter',
        'Split-field' => 'Splitfilter',
        'Star' => 'Sternenfilter',
      },
    },
   'SpecialInstructions' => 'Anweisungen',
   'SpecialMode' => 'Spezialmodus',
   'SpectralSensitivity' => 'Spektralempfindlichkeit',
   'SpotFocusPointX' => 'Spot-Fokuspunkt X',
   'SpotFocusPointY' => 'Spot-Fokuspunkt Y',
   'SpotMeterLinkToAFPoint' => {
      Description => 'Spotmessung AF-Feld verknüpft',
      PrintConv => {
        'Disable (use center AF point)' => 'Deaktiviert (zentrales AF-Feld)',
        'Enable (use active AF point)' => 'Aktiviert (aktives AF-Feld)',
      },
    },
   'SpotMeteringMode' => {
      Description => 'Spot-Messmethode',
      PrintConv => {
        'AF Point' => 'AF-Punkt',
        'Center' => 'Mitte',
      },
    },
   'State' => 'Bundesland/Kanton',
   'StereoMode' => 'Stereomodus',
   'StripByteCounts' => 'Anzahl Bytes pro komprimiertem Bildabschnitt',
   'StripOffsets' => 'Bilddatenposition',
   'Sub-location' => 'Ort des Motivs',
   'SubSecCreateDate' => 'Digitalisierungsdatum/-uhrzeit',
   'SubSecDateTimeOriginal' => 'Erstellungsdatum/-uhrzeit',
   'SubSecModifyDate' => 'Änderungsdatum',
   'SubSecTime' => 'Datum/Uhrzeit 1/100 Sekunden',
   'SubSecTimeDigitized' => 'Digitalisierungsdatum/-uhrzeit 1/100 Sekunden',
   'SubSecTimeOriginal' => 'Erstellungsdatum/-uhrzeit 1/100 Sekunden',
   'SubfileType' => {
      Description => 'Unterdatei-Typ',
      PrintConv => {
        'Alternate reduced-resolution image' => 'Alternatives Bild in reduzierter Auflösung',
        'Full-resolution image' => 'Bild in voller Auflösung',
        'Reduced-resolution image' => 'Bild in reduzierter Auflösung',
        'Single page of multi-page image' => 'Einzelbild eines mehrseitigen Bildes',
        'Single page of multi-page reduced-resolution image' => 'Einzelbild eines mehrseitigen Bildes in reduzierter Auflösung',
        'TIFF-FX mixed raster content' => 'TIFF-FX gersteter Inhalt',
        'TIFF/IT final page' => 'TIFF/IT endgültige Seite',
        'Thumbnail image' => 'Miniaturbild',
        'Transparency mask' => 'Transparenzmaske',
        'Transparency mask of multi-page image' => 'Transparenzmaske eines mehrseitigen Bildes',
        'Transparency mask of reduced-resolution image' => 'Transparenzmaske eines Bildes in reduzierter Auflösung',
        'Transparency mask of reduced-resolution multi-page image' => 'Transparenzmaske eines mehrseitigen Bildes in reduzierter Auflösung',
      },
    },
   'SubimageColor' => {
      PrintConv => {
        'Monochrome' => 'Monochrom',
      },
    },
   'Subject' => 'Themen/Schlüsselwörter',
   'SubjectArea' => 'Hauptobjektposition',
   'SubjectCode' => 'IPTC Themencode',
   'SubjectDistance' => 'Objektentfernung',
   'SubjectDistanceRange' => {
      Description => 'Objektdistanzbereich',
      PrintConv => {
        'Close' => 'Nahaufnahme',
        'Distant' => 'Fernaufnahme',
        'Macro' => 'Makro',
        'Unknown' => 'Unbekannt',
      },
    },
   'SubjectLocation' => 'Hauptobjektposition',
   'SubjectProgram' => {
      Description => 'Szenenauswahl',
      PrintConv => {
        'Night portrait' => 'Nachtporträt',
        'None' => 'Keine',
        'Portrait' => 'Porträt',
        'Sports action' => 'Sportereignis',
        'Sunset' => 'Sonnenuntergang',
      },
    },
   'SubjectReference' => 'Themencode',
   'SubjectUnits' => {
      PrintConv => {
        'meters' => 'Meter',
        'radians' => 'Winkelgrade',
      },
    },
   'Subsystem' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'SuperMacro' => {
      Description => 'Super Makro',
      PrintConv => {
        'Off' => 'Aus',
      },
    },
   'SuperimposedDisplay' => {
      Description => 'Eingeblendete Anzeige',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'SupplementalCategories' => 'Zusätzliche Kategorien',
   'SvISOSetting' => 'Sv ISO-Einstellung',
   'SwitchToRegisteredAFPoint' => {
      Description => 'Auf gesp. AF-Messf. schalten',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
        'Only while AEL is pressed' => 'Nur während AEL gedrückt',
        'Switch with multi-controller' => 'Wechseln mit Multicontroller',
      },
    },
   'T4Options' => 'Füllbits hinzugefügt',
   'T6Options' => 'T6 Optionen',
   'T82Options' => 'T82 Option',
   'TIFFPreview' => 'TIFF Vorschaubild',
   'TIFF_FXExtensions' => {
      PrintConv => {
        'B&W JBIG2' => 'Schwarz-Weiß JBIG2',
      },
    },
   'TTL_DA_ADown' => 'Slave-Blitz-Messfeld 6',
   'TTL_DA_AUp' => 'Slave-Blitz-Messfeld 5',
   'TTL_DA_BDown' => 'Slave-Blitz-Messfeld 8',
   'TTL_DA_BUp' => 'Slave-Blitz-Messfeld 7',
   'Tagged' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'TargetAperture' => 'Zielblendenwert',
   'TargetCompressionRatio' => 'Ziel-Komprimierungsrate',
   'TargetExposureTime' => 'Zielbelichtungszeit',
   'Technology' => {
      Description => 'Technologie',
      PrintConv => {
        'Active Matrix Display' => 'Aktives Matrix-Display',
        'Cathode Ray Tube Display' => 'Kathodenstrahlröhrenbildschirm',
        'Digital Camera' => 'Digitalkamera',
        'Dye Sublimation Printer' => 'Thermosublimationsdrucker',
        'Electrophotographic Printer' => 'Laserdrucker',
        'Electrostatic Printer' => 'Elektrostatischer Drucker',
        'Film Scanner' => 'Film-Scanner',
        'Film Writer' => 'Film-Writer',
        'Flexography' => 'Flexographie',
        'Gravure' => 'Gravur',
        'Ink Jet Printer' => 'Tintenstrahldrucker',
        'Offset Lithography' => 'Offset Lithographie',
        'Passive Matrix Display' => 'Passives Matrix-Display',
        'Photo CD' => 'Photo-CD',
        'Photo Image Setter' => 'Foto-Filmbelichter',
        'Photographic Paper Printer' => 'Fotopapierdrucker',
        'Projection Television' => 'Projektionsfernsehgerät',
        'Reflective Scanner' => 'Reflexionsscanner',
        'Thermal Wax Printer' => 'Thermowachsdrucker',
        'Video Camera' => 'Videokamera',
        'Video Monitor' => 'Video-Monitor',
      },
    },
   'Teleconverter' => {
      Description => 'Telekonverter',
      PrintConv => {
        'None' => 'Keiner',
      },
    },
   'TextEncoding' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'TextStamp' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ThumbnailFileName' => 'Miniaturbild-Dateiname',
   'ThumbnailFormat' => 'Miniaturbild-Format',
   'ThumbnailHeight' => 'Miniaturbild-Höhe',
   'ThumbnailImage' => 'Miniaturbild',
   'ThumbnailImageName' => 'Miniaturbild-Name',
   'ThumbnailImageSize' => 'Miniaturbild-Größe',
   'ThumbnailImageType' => 'Miniaturbild-Typ',
   'ThumbnailImageValidArea' => 'Gültiger Bereich des Miniaturbildes',
   'ThumbnailLength' => 'Miniaturbild-Datenlänge',
   'ThumbnailOffset' => 'Miniaturbild-Datenposition',
   'ThumbnailWidth' => 'Miniaturbild-Breite',
   'Time' => 'Zeit',
   'TimeCreated' => 'Erstellungszeit',
   'TimeScaleParamsQuality' => {
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Leicht',
      },
    },
   'TimeSent' => 'Absendezeit',
   'TimeSincePowerOn' => 'Einschaltdauer',
   'TimeStamp' => 'Zeitstempel',
   'TimeStamp1' => 'Zeitstempel (1)',
   'TimeZone' => 'Zeitzone',
   'TimeZoneCity' => {
      Description => 'Zeitzone Stadt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'TimeZoneCode' => 'Zeitzonen-Code',
   'TimeZoneInfo' => 'Zeitzonen-Info',
   'TimeZoneOffset' => 'Zeitzonen-Offset',
   'TimerFunctionButton' => {
      Description => 'Funktionstaste',
      PrintConv => {
        'Auto Bracketing' => 'Belichtungsreihe',
        'ISO' => 'ISO-Empfindlichkeit',
        'Image Quality/Size' => 'Bildqualität/-größe',
        'Self-timer' => 'Selbstauslöser',
        'Shooting Mode' => 'Aufnahmebetriebsart',
        'White Balance' => 'Weißabgleich',
      },
    },
   'TimerLength' => {
      Description => 'Intervalldauer für Timer',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'Title' => 'Titel',
   'ToneComp' => 'Tonwertkorrektur',
   'ToneCurve' => {
      Description => 'Ton-Kurve',
      PrintConv => {
        'Custom' => 'Benutzerdefiniert',
        'Manual' => 'Manuell',
      },
    },
   'ToneCurve1' => 'Tonwertkurve 1',
   'ToneCurve2' => 'Tonwertkurve 2',
   'ToneCurve3' => 'Tonwertkurve 3',
   'ToneCurve4' => 'Tonwertkurve 4',
   'ToneCurveActive' => {
      Description => 'Tonwertkurve aktiv',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'ToneCurveBlue' => 'Tonwertkurve Blau',
   'ToneCurveBlueX' => 'Tonwertkurve Blau X',
   'ToneCurveBlueY' => 'Tonwertkurve Blau Y',
   'ToneCurveBrightnessX' => 'Tonwertkurve Helligkeit X',
   'ToneCurveBrightnessY' => 'Tonwertkurve Helligkeit Y',
   'ToneCurveFileName' => 'Tonwertkurve Dateiname',
   'ToneCurveGreen' => 'Tonwertkurve Grün',
   'ToneCurveGreenX' => 'Tonwertkurve Grün X',
   'ToneCurveGreenY' => 'Tonwertkurve Grün Y',
   'ToneCurveInterpolation' => {
      Description => 'Tonwertkurve Interpolation',
      PrintConv => {
        'Curve' => 'Kurve',
        'Straight' => 'Gerade',
      },
    },
   'ToneCurveMatching' => 'Tonwertkurve Übereinstimmung',
   'ToneCurveMode' => {
      Description => 'Tonwertkurve Modus',
      PrintConv => {
        'Luminance' => 'Luminanz',
      },
    },
   'ToneCurveName' => {
      Description => 'Tonwertkurve Name',
      PrintConv => {
        'Custom' => 'Benutzerdefiniert',
        'Medium Contrast' => 'Kontrast mittel',
        'Strong Contrast' => 'Kontrast stark',
      },
    },
   'ToneCurveName2012' => 'Tonwertkurve Name 2012',
   'ToneCurvePV2012' => 'Tonwertkurve PV2012',
   'ToneCurvePV2012Blue' => 'Tonwertkurve PV2012 Blau',
   'ToneCurvePV2012Green' => 'Tonwertkurve PV2012 Grün',
   'ToneCurvePV2012Red' => 'Tonwertkurve PV2012 Rot',
   'ToneCurveProperty' => {
      Description => 'Tonwertkurve Eigenschaft',
      PrintConv => {
        'Custom 1' => 'Benutzerdefiniert 1',
        'Custom 2' => 'Benutzerdefiniert 2',
        'Custom 3' => 'Benutzerdefiniert 3',
        'Custom 4' => 'Benutzerdefiniert 4',
        'Custom 5' => 'Benutzerdefiniert 5',
        'Shot Settings' => 'Aufnahmeeinstellung',
      },
    },
   'ToneCurveRed' => 'Tonwertkurve Rot',
   'ToneCurveRedX' => 'Tonwertkurve Rot X',
   'ToneCurveRedY' => 'Tonwertkurve Rot Y',
   'ToneCurveTable' => 'Tonwertkurve Tabelle',
   'ToneCurves' => 'Ton-Kurven',
   'ToningEffect' => {
      Description => 'Tönungseffekt',
      PrintConv => {
        'B&W' => 'Schwarz/Weiß',
        'Blue' => 'Blau',
        'Blue-green' => 'Blau-Grün',
        'Color' => 'Farbe',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'Purple-blue' => 'Violett-Blau',
        'Red' => 'Rot',
        'Red-purple' => 'Rot-Violett',
        'Yellow' => 'Gelb',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectFaithful' => {
      Description => 'Tönungseffekt Natürlich',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectLandscape' => {
      Description => 'Tönungseffekt Landschaft',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectMonochrome' => {
      Description => 'Tönungseffekt Monochrom',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectNeutral' => {
      Description => 'Tönungseffekt Neutral',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectPortrait' => {
      Description => 'Tönungseffekt Porträt',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectStandard' => {
      Description => 'Tönungseffekt Standard',
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectUnknown' => {
      Description => 'Tönungseffekt Unbekannt',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectUserDef1' => {
      Description => 'Tönungseffekt Benutzerdefiniert 1',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectUserDef2' => {
      Description => 'Tönungseffekt Benutzerdefiniert 2',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningEffectUserDef3' => {
      Description => 'Tönungseffekt Benutzerdefiniert 3',
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'None' => 'Keiner',
        'Purple' => 'Lila',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ToningSaturation' => 'Tönungssättigung',
   'TotalZoom' => 'Gesamtzoom',
   'TrailerSignature' => 'Signatur des Nachspanns',
   'TransferFunction' => 'Transformationsfunktion',
   'Transform' => {
      Description => 'Transformation',
      PrintConv => {
        'Off' => 'Aus',
        'Slim High' => 'Stark abnehmend',
        'Slim Low' => 'Wenig abnehmend',
        'Stretch High' => 'Stark streckend',
        'Stretch Low' => 'Wenig streckend',
      },
    },
   'Transformation' => {
      PrintConv => {
        'Mirror horizontal' => 'Horizontal gespiegelt',
        'Mirror horizontal and rotate 270 CW' => 'Horizontal gespiegelt und 90° gegen den Uhrzeigersinn',
        'Mirror horizontal and rotate 90 CW' => 'Horizontal gespiegelt und 90° im Uhrzeigersinn',
        'Mirror vertical' => 'Vertikal gespiegelt',
        'Rotate 180' => '180° gedreht',
        'Rotate 270 CW' => '90° gegen den Uhrzeigersinn',
        'Rotate 90 CW' => '90° im Uhrzeigersinn',
      },
    },
   'TransmissionReference' => 'Anbietervermerk',
   'Trapped' => {
      PrintConv => {
        'Unknown' => 'Unbekannt',
      },
    },
   'TravelDay' => 'Reisetag',
   'TvExposureTimeSetting' => 'Tv Belichtungszeit-Einstellung',
   'Type' => 'Typ',
   'TypeOfOriginal' => {
      PrintConv => {
        'B&W Document' => 'Schwarz-Weiß Dokument',
        'B&W Print' => 'Schwarz-Weiß Druck',
        'Color Document' => 'Farb Dokument',
        'Color Print' => 'Farbdruck',
      },
    },
   'USMLensElectronicMF' => {
      Description => 'USM-Objektiv, elektr. MF',
      PrintConv => {
        'Disable after one-shot AF' => 'Nicht mögl. nach One-Shot AF',
        'Disable in AF mode' => 'Nicht möglich im AF-Modus',
        'Enable after one-shot AF' => 'Möglich nach One-Shot AF',
      },
    },
   'Uncompressed' => {
      Description => 'Unkomprimiert',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'UniqueCameraModel' => 'Eindeutige Kamerabezeichnung',
   'UniqueDocumentID' => 'Eindeutige Bild-ID',
   'Unknown' => 'Unbekannt',
   'UnknownInfoVersion' => 'Unbekannte Info Version',
   'UnknownLinear' => {
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'Unknown_CNDB' => 'CNDB unbekannt',
   'Unsharp1Color' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'Unsharp2Color' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'Unsharp3Color' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'Unsharp4Color' => {
      PrintConv => {
        'Blue' => 'Blau',
        'Green' => 'Grün',
        'Red' => 'Rot',
        'Yellow' => 'Gelb',
      },
    },
   'UnsharpMask' => {
      Description => 'Unschärfemaske',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Urgency' => {
      Description => 'Dringlichkeit',
      PrintConv => {
        '0 (reserved)' => '0 (reserviert)',
        '1 (most urgent)' => '1 (sehr dringend)',
        '5 (normal urgency)' => '5 (normale Dringlichkeit)',
        '8 (least urgent)' => '8 (geringe Dringlichkeit)',
        '9 (user-defined priority)' => '9 (benutzerdefinierte Priorität)',
      },
    },
   'UsableMeteringModes' => {
      Description => 'Wahl nutzbarer Messmethoden',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'UsableShootingModes' => {
      Description => 'Wahl nutzbarer Aufnahmemodi',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'UsageTerms' => 'Nutzungsbedingungen',
   'UserComment' => 'Benutzerkommentar',
   'UserDef1PictureStyle' => {
      Description => 'Bildstil Benutzerdefiniert 1',
      PrintConv => {
        'Faithful' => 'Natürlich',
        'Landscape' => 'Landschaft',
        'Monochrome' => 'Monochrom',
        'Portrait' => 'Porträt',
      },
    },
   'UserDef2PictureStyle' => {
      Description => 'Bildstil Benutzerdefiniert 2',
      PrintConv => {
        'Faithful' => 'Natürlich',
        'Landscape' => 'Landschaft',
        'Monochrome' => 'Monochrom',
        'Portrait' => 'Porträt',
      },
    },
   'UserDef3PictureStyle' => {
      Description => 'Bildstil Benutzerdefiniert 3',
      PrintConv => {
        'Faithful' => 'Natürlich',
        'Landscape' => 'Landschaft',
        'Monochrome' => 'Monochrom',
        'Portrait' => 'Porträt',
      },
    },
   'UserFields' => 'Benutzerfelder',
   'UserProfile' => {
      Description => 'Benutzerprofil',
      PrintConv => {
        'User Profile 0 (Dynamic)' => 'Benutzerprofil 0 (dynamisch)',
        'User Profile 1' => 'Benutzerprofil 1',
        'User Profile 2' => 'Benutzerprofil 2',
        'User Profile 3' => 'Benutzerprofil 3',
      },
    },
   'VFDisplayIllumination' => {
      PrintConv => {
        'Disable' => 'Deaktiviert',
        'Enable' => 'Aktiviert',
      },
    },
   'VRDVersion' => 'VRD-Version',
   'VRInfo' => 'Bildstabilisator-Informationen',
   'VRInfoVersion' => 'VR-Info-Version',
   'VR_0x66' => {
      PrintConv => {
        'Off' => 'Aus',
        'On (active)' => 'Ein (Aktiv)',
        'On (normal)' => 'Ein (Normal)',
      },
    },
   'ValidAFPoints' => 'Gültige AF-Punkte',
   'ValidBits' => 'Verwendete Bits',
   'ValidPixelDepth' => 'Farbtiefe',
   'VariProgram' => 'Aufnahmeprogramm',
   'VibrationReduction' => {
      Description => 'Bildstabilisation',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
        'On (1)' => 'Ein (1)',
        'On (2)' => 'Ein (2)',
        'On (3)' => 'Ein (3)',
        'n/a' => '(nicht gesetzt)',
      },
    },
   'ViewInfoDuringExposure' => {
      Description => 'Sucherinfo bei Belichtung',
      PrintConv => {
        'Disable' => 'Nicht möglich',
        'Enable' => 'Möglich',
      },
    },
   'ViewfinderWarning' => {
      Description => 'Warnsymbol im Sucher',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'ViewfinderWarnings' => {
      PrintConv => {
        'ISO expansion' => 'ISO-Erweiterung',
      },
    },
   'ViewingMode2' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'VignetteControl' => {
      Description => 'Vignettierungskorrektur',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Schwach',
        'Normal' => 'Mittel',
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'VignetteControlIntensity' => 'Vignettierungskorrektur Stärke',
   'VignettingCorrection' => {
      PrintConv => {
        'n/a' => '(nicht gesetzt)',
      },
    },
   'VirtualImageHeight' => 'Virtuelle Bildhöhe',
   'VirtualImageWidth' => 'Virtuelle Bildbreite',
   'VirtualPageUnits' => 'Virtuelle Seitenzahl',
   'VoiceMemo' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Volume' => 'Band',
   'WBAdjColorTemp' => 'Weißabgleich Farbtemperaturkorrektur',
   'WBAdjLighting' => {
      PrintConv => {
        'Daylight (cloudy)' => 'Tageslicht (2)',
        'Daylight (direct sunlight)' => 'Tageslicht (0)',
        'Daylight (shade)' => 'Tageslicht (1)',
        'Flash' => 'Blitz',
        'Incandescent' => 'Glühbirne',
        'None' => 'Keines',
      },
    },
   'WBBlueLevel' => 'Farbabgleich Blau',
   'WBBracketMode' => {
      Description => 'Weißabgleich Belichtungsreihen-Modus',
      PrintConv => {
        'Off' => 'Aus',
        'On (shift AB)' => 'Ein (AB-Verschiebung)',
        'On (shift GM)' => 'Ein (GM-Verschiebung)',
      },
    },
   'WBBracketShotNumber' => 'Weißabgleich-Belichtungsreihen-Bildnummer',
   'WBBracketValueAB' => 'Weißabgleich AB-Belichtungsreihen-Wert',
   'WBBracketValueGM' => 'Weißabgleich GM-Belichtungsreihen-Wert',
   'WBFineTuneActive' => {
      Description => 'Weißabgleich Feinabstimmung aktiv',
      PrintConv => {
        'No' => 'Nein',
        'Yes' => 'Ja',
      },
    },
   'WBFineTuneSaturation' => 'Weißabgleich Sättigung Feinabstimmung',
   'WBFineTuneTone' => 'Weißabgleich Farbton Feinabstimmung',
   'WBGreenLevel' => 'Farbabgleich Grün',
   'WBMediaImageSizeSetting' => {
      Description => 'WB+Media/Bildgrößeneinstellung',
      PrintConv => {
        'LCD monitor' => 'LCD-Monitor',
        'Rear LCD panel' => 'Hinteres LCD-Panel',
      },
    },
   'WBMode' => {
      PrintConv => {
        'Auto' => 'Automatisch',
      },
    },
   'WBRedLevel' => 'Farbabgleich Rot',
   'WBShiftAB' => 'Weißabgleich AB-Korrektur',
   'WBShiftGM' => 'Weißabgleich GM-Korrektur',
   'WB_GBRGLevels' => 'Weißabgleich GBRG-Farbverteilung',
   'WB_GLevel' => 'Weißabgleich G-Farbverteilung',
   'WB_GLevel3000K' => 'Weißabgleich G-Farbverteilung 3000K',
   'WB_GLevel3300K' => 'Weißabgleich G-Farbverteilung 3300K',
   'WB_GLevel3600K' => 'Weißabgleich G-Farbverteilung 3600K',
   'WB_GLevel3900K' => 'Weißabgleich G-Farbverteilung 3900K',
   'WB_GLevel4000K' => 'Weißabgleich G-Farbverteilung 4000K',
   'WB_GLevel4300K' => 'Weißabgleich G-Farbverteilung 4300K',
   'WB_GLevel4500K' => 'Weißabgleich G-Farbverteilung 4500K',
   'WB_GLevel4800K' => 'Weißabgleich G-Farbverteilung 4800K',
   'WB_GLevel5300K' => 'Weißabgleich G-Farbverteilung 5300K',
   'WB_GLevel6000K' => 'Weißabgleich G-Farbverteilung 6000K',
   'WB_GLevel6600K' => 'Weißabgleich G-Farbverteilung 6600K',
   'WB_GLevel7500K' => 'Weißabgleich G-Farbverteilung 7500K',
   'WB_GRBGLevels' => 'Weißabgleich GRBG-Farbverteilung',
   'WB_GRGBLevels' => 'Weißabgleich GRGB-Farbverteilung',
   'WB_RBGGLevels' => 'Weißabgleich RBGG-Farbverteilung',
   'WB_RBLevels' => 'Weißabgleich RB-Farbverteilung',
   'WB_RBLevels3000K' => 'Weißabgleich RB-Farbverteilung 3000K',
   'WB_RBLevels3300K' => 'Weißabgleich RB-Farbverteilung 3300K',
   'WB_RBLevels3600K' => 'Weißabgleich RB-Farbverteilung 3600K',
   'WB_RBLevels3900K' => 'Weißabgleich RB-Farbverteilung 3800K',
   'WB_RBLevels4000K' => 'Weißabgleich RB-Farbverteilung 4000K',
   'WB_RBLevels4300K' => 'Weißabgleich RB-Farbverteilung 4300K',
   'WB_RBLevels4500K' => 'Weißabgleich RB-Farbverteilung 4500K',
   'WB_RBLevels4800K' => 'Weißabgleich RB-Farbverteilung 4800K',
   'WB_RBLevels5300K' => 'Weißabgleich RB-Farbverteilung 5300K',
   'WB_RBLevels6000K' => 'Weißabgleich RB-Farbverteilung 6000K',
   'WB_RBLevels6600K' => 'Weißabgleich RB-Farbverteilung 6600K',
   'WB_RBLevels7500K' => 'Weißabgleich RB-Farbverteilung 7500K',
   'WB_RBLevelsAuto' => 'Weißabgleich RB-Farbverteilung Automatik',
   'WB_RBLevelsCWB1' => 'Weißabgleich RB-Farbverteilung CWB1',
   'WB_RBLevelsCWB2' => 'Weißabgleich RB-Farbverteilung CWB2',
   'WB_RBLevelsCWB3' => 'Weißabgleich RB-Farbverteilung CWB3',
   'WB_RBLevelsCWB4' => 'Weißabgleich RB-Farbverteilung CWB4',
   'WB_RBLevelsCloudy' => 'Weißabgleich RB-Farbverteilung Bewölkt',
   'WB_RBLevelsCoolWhiteFluor' => 'Weißabgleich RB-Farbverteilung Neonlicht kaltweiß',
   'WB_RBLevelsDayWhiteFluor' => 'Weißabgleich RB-Farbverteilung Neonlicht neutralweiß',
   'WB_RBLevelsDaylightFluor' => 'Weißabgleich RB-Farbverteilung Neonlicht tageslichtweiß',
   'WB_RBLevelsEveningSunlight' => 'Weißabgleich RB-Farbverteilung Sonnenuntergang',
   'WB_RBLevelsFineWeather' => 'Weißabgleich RB-Farbverteilung Wolkenlos',
   'WB_RBLevelsShade' => 'Weißabgleich RB-Farbverteilung Schatten',
   'WB_RBLevelsTungsten' => 'Weißabgleich RB-Farbverteilung Glühbirne',
   'WB_RBLevelsUsed' => 'Weißabgleich RB-Farbverteilung verwendet',
   'WB_RBLevelsWhiteFluorescent' => 'Weißabgleich RB-Farbverteilung Neonlicht universalweiß',
   'WB_RGBGLevels' => 'Weißabgleich RGBG-Farbverteilung',
   'WB_RGBLevels' => 'Weißabgleich RGB-Farbverteilung',
   'WB_RGBLevelsCloudy' => 'Weißabgleich RGB-Farbverteilung Bewölkt',
   'WB_RGBLevelsDaylight' => 'Weißabgleich RGB-Farbverteilung Tageslicht',
   'WB_RGBLevelsFlash' => 'Weißabgleich RGB-Farbverteilung Blitz',
   'WB_RGBLevelsFluorescent' => 'Weißabgleich RGB-Farbverteilung Neonlicht',
   'WB_RGBLevelsShade' => 'Weißabgleich RGB-Farbverteilung Schatten',
   'WB_RGBLevelsTungsten' => 'Weißabgleich RGB-Farbverteilung Glühbirne',
   'WB_RGGBLevels' => 'Weißabgleich RGGB-Farbverteilung',
   'WB_RGGBLevelsAsShot' => 'Weißabgleich RGGB-Farbverteilung Aufnahme',
   'WB_RGGBLevelsAuto' => 'Weißabgleich RGGB-Farbverteilung Auto',
   'WB_RGGBLevelsCloudy' => 'Weißabgleich RGGB-Farbverteilung Bewölkt',
   'WB_RGGBLevelsCustom' => 'Weißabgleich RGGB-Farbverteilung Benutzerdefiniert',
   'WB_RGGBLevelsCustom1' => 'Weißabgleich RGGB-Farbverteilung Benutzerdefiniert 1',
   'WB_RGGBLevelsCustom2' => 'Weißabgleich RGGB-Farbverteilung Benutzerdefiniert 2',
   'WB_RGGBLevelsDaylight' => 'Weißabgleich RGGB-Farbverteilung Tageslicht',
   'WB_RGGBLevelsFlash' => 'Weißabgleich RGGB-Farbverteilung Blitz',
   'WB_RGGBLevelsFluorescent' => 'Weißabgleich RGGB-Farbverteilung Neonlicht',
   'WB_RGGBLevelsFluorescentD' => 'Weißabgleich RGGB-Farbverteilung Neonlicht D',
   'WB_RGGBLevelsFluorescentN' => 'Weißabgleich RGGB-Farbverteilung Neonlicht N',
   'WB_RGGBLevelsFluorescentW' => 'Weißabgleich RGGB-Farbverteilung Neonlicht W',
   'WB_RGGBLevelsKelvin' => 'Weißabgleich RGGB-Farbverteilung Kelvin',
   'WB_RGGBLevelsMeasured' => 'Weißabgleich RGGB-Farbverteilung Messung',
   'WB_RGGBLevelsPC1' => 'Weißabgleich RGGB-Farbverteilung PC1',
   'WB_RGGBLevelsPC2' => 'Weißabgleich RGGB-Farbverteilung PC2',
   'WB_RGGBLevelsPC3' => 'Weißabgleich RGGB-Farbverteilung PC3',
   'WB_RGGBLevelsShade' => 'Weißabgleich RGGB-Farbverteilung Schatten',
   'WB_RGGBLevelsTungsten' => 'Weißabgleich RGGB-Farbverteilung Glühbirne',
   'WB_RGGBLevelsUnknown' => 'Weißabgleich RGGB-Farbverteilung Unbekannt',
   'WB_RGGBLevelsUnknown10' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 10',
   'WB_RGGBLevelsUnknown11' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 11',
   'WB_RGGBLevelsUnknown12' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 12',
   'WB_RGGBLevelsUnknown13' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 13',
   'WB_RGGBLevelsUnknown14' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 14',
   'WB_RGGBLevelsUnknown15' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 15',
   'WB_RGGBLevelsUnknown16' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 16',
   'WB_RGGBLevelsUnknown17' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 17',
   'WB_RGGBLevelsUnknown18' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 18',
   'WB_RGGBLevelsUnknown19' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 19',
   'WB_RGGBLevelsUnknown2' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 2',
   'WB_RGGBLevelsUnknown20' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 20',
   'WB_RGGBLevelsUnknown3' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 3',
   'WB_RGGBLevelsUnknown4' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 4',
   'WB_RGGBLevelsUnknown5' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 5',
   'WB_RGGBLevelsUnknown6' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 6',
   'WB_RGGBLevelsUnknown7' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 7',
   'WB_RGGBLevelsUnknown8' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 8',
   'WB_RGGBLevelsUnknown9' => 'Weißabgleich RGGB-Farbverteilung Unbekannt 9',
   'WCSProfiles' => 'Windows Color System-Profil',
   'WangAnnotation' => 'Wang Anmerkung',
   'Warning' => 'Warnung',
   'Watermark' => 'Wasserzeichen',
   'WhiteBalance' => {
      Description => 'Weißabgleich',
      PrintConv => {
        'As Shot' => 'Aufnahme',
        'Auto' => 'Automatisch',
        'Black & White' => 'Schwarz/Weiß',
        'Cloudy' => 'Bewölkt',
        'Color Temperature/Color Filter' => 'Farbtemperatur/Farbfilter',
        'Cool White Fluorescent' => 'Neonlicht kaltweiß',
        'Custom' => 'Benutzerdefiniert',
        'Custom 1' => 'Benutzerdefiniert 1',
        'Custom 2' => 'Benutzerdefiniert 2',
        'Custom 3' => 'Benutzerdefiniert 3',
        'Custom 4' => 'Benutzerdefiniert 4',
        'Custom2' => 'Benutzerdefiniert 2',
        'Custom3' => 'Benutzerdefiniert 3',
        'Custom4' => 'Benutzerdefiniert 4',
        'Custom5' => 'Benutzerdefiniert 5',
        'Day White Fluorescent' => 'Neonlicht neutralweiß',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Flash' => 'Blitz',
        'Flash?' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'Incandescent' => 'Glühbirne',
        'Living Room Warm White Fluorescent' => 'Neonlicht Wohnzimmer-warmweiß)',
        'Manual' => 'Manuell',
        'Manual Temperature (Kelvin)' => 'Manuelle Temperatur (Kelvin)',
        'Shade' => 'Schatten',
        'Tungsten' => 'Glühbirne',
        'Underwater' => 'Unterwasser',
        'Underwater 1 (Blue Water)' => 'Unterwasser 1 (blaues Wasser)',
        'Underwater 2 (Green Water)' => 'Unterwasser 2 (grünes Wasser)',
        'Unknown' => 'Unbekannt',
        'User-Selected' => 'Benutzerdefiniert',
        'Warm White Fluorescent' => 'Neonlicht warmweiß',
        'White Fluorescent' => 'Neonlicht universalweiß',
      },
    },
   'WhiteBalance2' => {
      Description => 'Weißabgleich 2',
      PrintConv => {
        '3000K (Tungsten light)' => '3000K (Glühbirne)',
        '3600K (Tungsten light-like)' => '3600K (ähnlich Glühbirne)',
        '4000K (Cool white fluorescent)' => '4000K (Neonlicht kaltweiß)',
        '4500K (Neutral white fluorescent)' => '4500K (Neonlicht neutralweiß)',
        '5300K (Fine Weather)' => '5300K (Wolkenlos)',
        '6000K (Cloudy)' => '6000K (Bewölkt)',
        '6600K (Daylight fluorescent)' => '6600K (Neonlicht tageslichtweiß)',
        '7500K (Fine Weather with Shade)' => '7500K (Sonne und Schatten)',
        'Auto' => 'Automatisch',
      },
    },
   'WhiteBalanceAdj' => {
      Description => 'Weißabgleich Korrektur',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Cloudy' => 'Bewölkt',
        'Daylight' => 'Tageslicht',
        'Flash' => 'Blitz',
        'Fluorescent' => 'Neonlicht',
        'Off' => 'Aus',
        'On' => 'Ein',
        'Shade' => 'Schatten',
        'Tungsten' => 'Glühbirne',
      },
    },
   'WhiteBalanceAutoAdjustment' => {
      Description => 'Weißabgleich automatische Abstimmung',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'WhiteBalanceBias' => 'Weißabgleich Bias',
   'WhiteBalanceBlue' => 'Farbabgleich Blau',
   'WhiteBalanceBracket' => 'Weißabgleichs-Belichtungsreihe',
   'WhiteBalanceBracketing' => {
      Description => 'Weißabgleichs-Belichtungsreihe',
      PrintConv => {
        'High' => 'Hoch',
        'Low' => 'Niedrig',
        'Off' => 'Aus',
      },
    },
   'WhiteBalanceComp' => 'Weißabgleichsausgleich',
   'WhiteBalanceFineTune' => 'Weißabgleichsfeineinstellung',
   'WhiteBalanceMatching' => 'Weißabgleich Übereinstimmung',
   'WhiteBalanceMode' => {
      Description => 'Weißabgleich-Modus',
      PrintConv => {
        'Auto (Cloudy)' => 'Automatisch (Bewölkt)',
        'Auto (Day White Fluorescent)' => 'Automatisch (Neonlicht neutralweiß)',
        'Auto (Daylight Fluorescent)' => 'Automatisch (Neonlicht tageslichtweiß)',
        'Auto (Daylight)' => 'Automatisch (Tageslicht)',
        'Auto (Flash)' => 'Automatisch (Blitz)',
        'Auto (Shade)' => 'Automatisch (Schatten)',
        'Auto (Tungsten)' => 'Automatisch (Glühbirne)',
        'Auto (White Fluorescent)' => 'Automatisch (Neonlicht universalweiß)',
        'Unknown' => 'Unbekannt',
        'User-Selected' => 'Benutzerdefiniert',
      },
    },
   'WhiteBalanceRed' => 'Farbabgleich Rot',
   'WhiteBalanceSet' => {
      Description => 'Eingestellter Weißabgleich',
      PrintConv => {
        'Auto' => 'Automatisch',
        'Cloudy' => 'Bewölkt',
        'Day White Fluorescent' => 'Neonlicht neutralweiß',
        'Daylight' => 'Tageslicht',
        'Daylight Fluorescent' => 'Neonlicht tageslichtweiß',
        'Flash' => 'Blitz',
        'Manual' => 'Manuell',
        'Set Color Temperature 1' => 'Farbtemperatur-Einstellung 1',
        'Set Color Temperature 2' => 'Farbtemperatur-Einstellung 2',
        'Set Color Temperature 3' => 'Farbtemperatur-Einstellung 3',
        'Shade' => 'Schatten',
        'Tungsten' => 'Glühbirne',
        'White Fluorescent' => 'Neonlicht universalweiß',
      },
    },
   'WhiteBalanceSetting' => {
      Description => 'Weißabgleichs-Einstellung',
      PrintConv => {
        'Color Temperature/Color Filter' => 'Farbtemperatur/Farbfilter',
        'Custom' => 'Benutzerdefiniert',
        'Preset' => 'Voreinstellung',
      },
    },
   'WhiteBalanceTable' => 'Weißabgleich Tabelle',
   'WhiteBalanceTemperature' => 'Weißabgleich Farbtemperatur',
   'WhiteBoard' => 'Whiteboard Funktion',
   'WhiteLevel' => 'Weißwert',
   'WhitePoint' => 'Weißpunkt-Chromatizität',
   'WhitePointX' => 'Weißpunkt X',
   'WhitePointY' => 'Weißpunkt Y',
   'Wide' => 'Breit',
   'WideFocusZone' => {
      Description => 'Zone des großen AF-Messfeldes',
      PrintConv => {
        'Center zone (horizontal orientation)' => 'Mittlere Zone (horizontale Ausrichtung)',
        'Center zone (vertical orientation)' => 'Mittlere Zone (vertikale Ausrichtung)',
        'Left zone' => 'Linke Zone',
        'No zone' => 'Keine Zone',
        'Right zone' => 'Rechte Zone',
      },
    },
   'WideRange' => {
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'WidthResolution' => 'Horizontale Bildauflösung',
   'WorldTime' => 'Zeitzone',
   'WorldTimeLocation' => {
      Description => 'Weltzeit-Position',
      PrintConv => {
        'Destination' => 'Zielort',
        'Home' => 'Heimatort',
        'Hometown' => 'Heimatort',
      },
    },
   'Writer-Editor' => 'Verfasser der Beschreibung',
   'Writers' => 'Schreiber',
   'XMP' => 'XMP Metadaten',
   'XPAuthor' => 'XP Autor',
   'XPComment' => 'XP Kommentar',
   'XPKeywords' => 'XP Schlüsselwörter',
   'XPSubject' => 'XP Thema',
   'XPTitle' => 'XP Titel',
   'XResolution' => 'Horizontale Bildauflösung',
   'XYResolution' => 'XY Auflösung',
   'YCbCrCoefficients' => 'YCbCr-Koeffizienten',
   'YCbCrPositioning' => {
      Description => 'Y und C Ausrichtung',
      PrintConv => {
        'Centered' => 'Zentriert',
        'Co-sited' => 'Benachbart',
      },
    },
   'YCbCrSubSampling' => 'Subsampling Rate von Y bis C',
   'YResolution' => 'Vertikale Bildauflösung',
   'Year' => 'Jahr',
   'ZipCompression' => 'Zip Komprimierung',
   'ZoneMatching' => {
      Description => 'Zonenabgleich',
      PrintConv => {
        'High Key' => 'Hi',
        'ISO Setting Used' => 'Aus (ISO-Einstellung verwendet)',
        'Low Key' => 'Lo',
      },
    },
   'ZoneMatchingOn' => {
      Description => 'Zonenabgleich',
      PrintConv => {
        'Off' => 'Aus',
        'On' => 'Ein',
      },
    },
   'Zoom' => 'Zoom-Objektiv',
   'ZoomPos' => 'Zoom Position',
   'ZoomSourceWidth' => 'Vergrößerungs-Ursprungsgröße',
   'ZoomStepCount' => 'Zoom-Stufenzähler',
   'ZoomTargetWidth' => 'Vergrößerungs-Endgröße',
   'ZoomedPreviewImage' => 'Vergrößertes Vorschaubild',
   'ZoomedPreviewLength' => 'Vergößertes Vorschaubild-Datenlänge',
   'ZoomedPreviewSize' => 'Vergößertes Vorschaubild-Größe',
   'ZoomedPreviewStart' => 'Vergößertes Vorschaubild-Datenposition',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::de.pm - ExifTool German language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Herbert Kauer and Jobi for providing this
translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
