#------------------------------------------------------------------------------
# File:         it.pm
#
# Description:  ExifTool Italian language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::it;

use strict;
use vars qw($VERSION);

$VERSION = '1.14';

%Image::ExifTool::Lang::it::Translate = (
   'A100DataOffset' => 'Offset dati A100',
   'AAFManufacturerID' => 'ID AAF produttore',
   'ACoordOfBottomRightCorner' => 'Una coord in basso a destra',
   'ACoordOfTopRightCorner' => 'Una coord in alto a destra',
   'AEAperture' => 'Apertura esposizione automatica',
   'AEBAutoCancel' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AEBSequenceAutoCancel' => {
      PrintConv => {
        '-,0,+/Disabled' => '-,0,+/Disabilitato',
        '-,0,+/Enabled' => '-,0,+/Abilitato',
        '0,-,+/Disabled' => '0,-,+/Disabilitato',
        '0,-,+/Enabled' => '0,-,+/Abilitato',
      },
    },
   'AEExposureTime' => 'Durata esposizione automatica',
   'AEFlags' => {
      Description => 'Flag esposizione automatica',
      PrintConv => {
        'AE lock' => 'Blocco esposizione automatica',
        'Aperture wide open' => 'Diaframma molto aperto',
        'Flash recommended?' => 'Flash consigliato?',
      },
    },
   'AELExposureIndicator' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'AELock' => {
      Description => 'Blocco esposizione automatica',
      PrintConv => {
        'Off' => 'Spento',
        'On' => 'Acceso',
      },
    },
   'AELockButton' => {
      Description => 'Pulsante blocco esposizione automatica',
      PrintConv => {
        'Flash Off' => 'Flash spento',
        'None' => 'Nessuno',
        'Preview' => 'Anteprima',
        'Virtual Horizon' => 'Orizzonte virtuale',
      },
    },
   'AELockButtonPlusDials' => {
      PrintConv => {
        'Choose Image Area' => 'Seleziona area immagine',
        'None' => 'Nessuno',
      },
    },
   'AEMeteringMode' => {
      PrintConv => {
        'Center-weighted average' => 'Media centrale ponderata',
        'Multi-segment' => 'Multi zona',
      },
    },
   'AEMicroadjustment' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'AEProgramMode' => {
      PrintConv => {
        'Kids' => 'Bambini',
        'Landscape' => 'Orizzontale',
        'No Flash' => 'No flash',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Sunset' => 'Tramonto',
        'Text' => 'Testo',
      },
    },
   'AESetting' => {
      PrintConv => {
        'Exposure Compensation' => 'Compensazione esposizione',
      },
    },
   'AE_ISO' => 'ISO esposizione automatica',
   'AFAndMeteringButtons' => {
      PrintConv => {
        'No function' => 'Nessuna funzione',
      },
    },
   'AFAperture' => 'Diaframma AF',
   'AFAreaIllumination' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AFAreaMode' => {
      Description => 'Modo AF',
      PrintConv => {
        'Dynamic Area' => 'Area Dinamica',
        'Dynamic Area (closest subject)' => 'Area Dinamica più Vicina al Soggetto',
        'Group Dynamic' => 'Gruppo Dinamico',
        'Local' => 'Locale',
        'Off (Manual Focus)' => 'Spento (focus manuale)',
        'Single Area' => 'Area Singola',
      },
    },
   'AFAssist' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AFDuringLiveView' => {
      PrintConv => {
        'Enable' => 'Abilita',
        'Quick mode' => 'Modo veloce',
      },
    },
   'AFFineTune' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AFIlluminator' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'AFInfo' => 'Modo AF',
   'AFMode' => {
      Description => 'Modo AF',
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'AFOnAELockButtonSwitch' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'AFPoint' => {
      Description => 'Punto AF',
      PrintConv => {
        '(none)' => '(nessuno)',
        'Bottom' => 'Basso',
        'Center' => 'Centro',
        'Far Left' => 'Tutto a sinistra',
        'Far Right' => 'Tutto a destra',
        'Left' => 'Sinistra',
        'Lower-left' => 'Inferiore sinistro',
        'Lower-right' => 'Inferiore destro',
        'Mid-left' => 'Centro/Sinistra',
        'Mid-right' => 'Centro/Destra',
        'None' => 'Nessuno',
        'Right' => 'Destra',
        'Right (horizontal)' => 'Destra (orizzontale)',
        'Right (vertical)' => 'Destra (verticale)',
        'Top' => 'Alto',
        'Upper-left' => 'Superiore sinistro',
        'Upper-right' => 'Superiore destro',
      },
    },
   'AFPointActivationArea' => {
      PrintConv => {
        'Expanded' => 'Espanso',
      },
    },
   'AFPointAreaExpansion' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'AFPointBrightness' => {
      PrintConv => {
        'Low' => 'Basso',
        'Normal' => 'Normale',
      },
    },
   'AFPointDisplayDuringFocus' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AFPointIllumination' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AFPointMode' => {
      PrintConv => {
        'Fixed Center' => 'Centro fisso',
        'Select' => 'Seleziona',
      },
    },
   'AFPointRegistration' => {
      PrintConv => {
        'Bottom' => 'Basso',
        'Center' => 'Centro',
        'Extreme Left' => 'Tutto a Sinistra',
        'Extreme Right' => 'Tutto a Destra',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
        'Top' => 'Alto',
      },
    },
   'AFPointSelected' => {
      PrintConv => {
        'Bottom' => 'Basso',
        'Center' => 'Centro',
        'Far Left' => 'Tutto a sinistra',
        'Far Right' => 'Tutto a destra',
        'Fixed Center' => 'Centro fisso',
        'Left' => 'Sinistra',
        'None' => 'Nessuno',
        'Right' => 'Destra',
        'Top' => 'Alto',
      },
    },
   'AFPointSelected2' => {
      PrintConv => {
        'Center' => 'Centro',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'AFPointSelection' => {
      PrintConv => {
        '11 Points' => '11 punti',
      },
    },
   'AFPointSelectionMethod' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'AFPoints' => {
      PrintConv => {
        'Center' => 'Centro',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'AFPointsInFocus' => {
      Description => 'Punti a fuoco',
      PrintConv => {
        'Bottom' => 'Basso',
        'Center' => 'Centro',
        'Far Left' => 'Tutto a sinistra',
        'Far Right' => 'Tutto a destra',
        'Left' => 'Sinistra',
        'Lower-left' => 'Inferiore sinistro',
        'Lower-right' => 'Inferiore destro',
        'None' => 'Nessuno',
        'Right' => 'Destra',
        'Top' => 'Alto',
        'Upper-left' => 'Superiore sinistro',
        'Upper-right' => 'Superiore destro',
      },
    },
   'AFPointsInFocus1D' => 'Punti a fuoco 1D',
   'AFPointsInFocus5D' => {
      Description => 'Punti a fuoco 5D',
      PrintConv => {
        'Center' => 'Centro',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'AFPointsUnknown1' => {
      PrintConv => {
        'Center' => 'Centro',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'AFPointsUnknown2' => {
      PrintConv => {
        'Center' => 'Centro',
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'AFPointsUsed' => {
      PrintConv => {
        'Bottom' => 'Basso',
        'Center' => 'Centro',
        'Far Left' => 'Tutto a sinistra',
        'Far Right' => 'Tutto a destra',
        'Lower-left' => 'Inferiore sinistro',
        'Lower-right' => 'Inferiore destro',
        'Top' => 'Alto',
        'Upper-left' => 'Superiore sinistro',
        'Upper-right' => 'Superiore destro',
      },
    },
   'AFSearch' => {
      PrintConv => {
        'Not Ready' => 'Non pronto',
        'Ready' => 'Pronto',
      },
    },
   'AFWithShutter' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AIServoImagePriority' => {
      PrintConv => {
        '1: AF, 2: Drive speed' => '1: AF, 2: drive speed',
        '1: AF, 2: Tracking' => '1: AF, 2: puntamento',
        '1: Release, 2: Drive speed' => '1: rilascio, 2: drive speed',
        '1: Release, 2: Tracking' => '1: rilascio, 2: puntamento',
      },
    },
   'AIServoTrackingSensitivity' => {
      PrintConv => {
        'Fast' => 'Veloce',
      },
    },
   'APEVersion' => 'Versione APE',
   'ARMIdentifier' => 'ID ARM',
   'ARMVersion' => 'Versione ARM',
   'AToB0' => 'Da A a B0',
   'AToB1' => 'Da A a B1',
   'AToB2' => 'Da A a B2',
   'AberrationCorrectionDistance' => 'Distanza della correzione di aberrazione',
   'About' => 'Informazioni su',
   'AbsPeakAudioFilePath' => 'Percorso file audio del picco assoluto',
   'AbsoluteChannelDisplayScale' => 'Scala assoluta canale',
   'Abstract' => 'Sommario',
   'AbstractPriorCodeSequence' => 'Sequenza codice precedente astratto',
   'AbstractPriorValue' => 'Valore codice precedente astratto',
   'AccessDate' => 'Data di accesso',
   'AccessionNumber' => 'Numero di adesione',
   'AccessoryCode' => 'Codice accessorio',
   'AccessoryType' => 'Tipo accessorio',
   'AccountName' => 'Nome account',
   'AccountingReferenceNumber' => 'Riferimento contabile',
   'AcqreconRecordChecksum' => 'Checksum del record AcqRecon',
   'AcquiredImageAreaDoseProduct' => 'Dose prodotto dell\'area acquisita',
   'AcquisitionComments' => 'Commenti acquisizione',
   'AcquisitionContextDescription' => 'Descrizione contesto acquisizione',
   'AcquisitionContextSequence' => 'Sequenza contesto acquisizione',
   'AcquisitionContrast' => 'Contrasto acquisizione',
   'AcquisitionDate' => 'Data acquisizione',
   'AcquisitionDateTime' => 'Data/ora acquisizione',
   'AcquisitionDeviceProcessingCode' => 'Codice processo di acquisizione del dispositivo',
   'AcquisitionDeviceProcessingDescr' => 'Descrizione processo di acquisizione del dispositivo',
   'AcquisitionDeviceTypeCodeSequence' => 'Sequenza codici tipo di acquisizione del dispositivo',
   'AcquisitionDuration' => 'Durata acquisizione',
   'AcquisitionEndConditionData' => 'Dati condizione fine acquisizione',
   'AcquisitionGroupLength' => 'Lunghezza gruppo di acquisizione',
   'AcquisitionIndex' => 'Indice acquisizione',
   'AcquisitionMatrix' => 'Matrice acquisizione',
   'AcquisitionNumber' => 'Numero acquisizione',
   'AcquisitionProtocolDescription' => 'Descrizione protocollo di acquisizione',
   'AcquisitionProtocolName' => 'Nome protocollo di acquisizione',
   'AcquisitionStartCondition' => 'Condizione iniziale acquisizione',
   'AcquisitionStartConditionData' => 'Dati condizione iniziale acquisizione',
   'AcquisitionTerminationCondition' => 'Condizione finale acquisizione',
   'AcquisitionTime' => 'Ora acquisizione',
   'AcquisitionTimeDay' => 'Ora acquisizione - Giorno',
   'AcquisitionTimeMonth' => 'Ora acquisizione - Mese',
   'AcquisitionTimeSynchronized' => 'Ora acquisizione sincronizzata',
   'AcquisitionTimeYear' => 'Ora acquisizione - Anno',
   'AcquisitionTimeYearMonth' => 'Ora acquisizione - Anno mese',
   'AcquisitionTimeYearMonthDay' => 'Ora acquisizione - Anno mese giorno',
   'AcquisitionType' => 'Tipo acquisizione',
   'AcquisitionsInSeries' => 'Acquisizioni in serie',
   'AcquisitionsInStudy' => 'Acquisizioni in esame',
   'AcrossScanSpatialResolution' => 'Attraverso risoluzione di scansione spaziale',
   'ActionAdvised' => {
      Description => 'Azione consigliata',
      PrintConv => {
        'Object Append' => 'Allega oggetto',
        'Object Kill' => 'Distruzione oggetto',
        'Object Reference' => 'Riferimento oggetto',
        'Object Replace' => 'Sostituzione oggetto',
      },
    },
   'ActiveArea' => 'Area attiva',
   'ActiveD-Lighting' => {
      Description => 'D-Lighting attivo',
      PrintConv => {
        'Extra High' => 'Molto alto',
        'High' => 'Alto',
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'On' => 'Acceso',
      },
    },
   'ActiveD-LightingMode' => {
      Description => 'Modalità D-Lighting attiva',
      PrintConv => {
        'Extra High' => 'Molto alto',
        'High' => 'Alto',
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Unchanged' => 'Immutato',
      },
    },
   'ActiveFormatDescriptor' => 'Descrittore formato attivo',
   'ActiveLinesperFrame' => 'Linee per quadro attuali',
   'ActiveSamplesperLine' => 'Linee per campione attuali',
   'ActiveSourceDiameter' => 'Diametro sorgente attuale',
   'ActiveSourceLength' => 'Lunghezza sorgente attuale',
   'ActiveState' => 'Stato attivo',
   'Actor' => 'Attore',
   'ActualCardiacTriggerDelayTime' => 'Tempo di ritardo corrente trigger cardiaco',
   'ActualCompensation' => 'Compensazione corrente',
   'ActualFrameDuration' => 'Durata frame attuale',
   'ActualHumanPerformersSequence' => 'Sequenza attuale esecutori umani',
   'ActualReceiveGainAnalog' => 'Guadagno analogico in ricezione attuale',
   'ActualReceiveGainDigital' => 'Guadagno digitale in ricezione attuale',
   'ActualRespiratoryTriggerDelayTime' => 'Tempo di ritardo corrente trigger respiratorio',
   'ActualSeriesDataTimeStamp' => 'Marca temporale serie di dati attuali',
   'Ad-ID' => 'ID Ad',
   'AdaptiveMapFormat' => 'Formato mappa adattativa',
   'AddAspectRatioInfo' => {
      Description => 'Aggiunta info rapporto di aspetto',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AddIntermediateSequence' => 'Aggiunta sequenza intermedia',
   'AddNearSequence' => 'Aggiunta sequenza vicina',
   'AddOriginalDecisionData' => {
      Description => 'Aggiunta dati decisione originale',
      PrintConv => {
        'Off' => 'Spento',
        'On' => 'Acceso',
      },
    },
   'AddOtherSequence' => 'Aggiunta altra sequenza',
   'AddPower' => 'Aggiunta potenza',
   'AdditionalDrugSequence' => 'Sequenza farmaci addizionali',
   'AdditionalModelInformation' => 'Ulteriori informazioni modello',
   'AdditionalPatientHistory' => 'Ulteriore storia del paziente',
   'Address' => 'Indirizzo',
   'AddressLine' => 'Linea indirizzo',
   'AddressNameValueSets' => 'Gruppi di valori nome indirizzo',
   'AddressSets' => 'Gruppi di indirizzi',
   'AddressTrial' => 'Studio indirizzo',
   'AdjustmentMode' => 'Modo adattamento',
   'AdministrationRouteCodeSequence' => 'Sequenza codici percorso di amministrazione',
   'AdmissionID' => 'ID ammissione',
   'AdmittingDate' => 'Data ammissione',
   'AdmittingDiagnosesCodeSequence' => 'Sequenza codici diagnosi ammissione',
   'AdmittingDiagnosesDescription' => 'Descrizioni diagnosi ammissione',
   'AdmittingTime' => 'Ora ammissione',
   'AdobeCMType' => 'Tipo Adobe CM',
   'AdoptedNeutral' => 'Adottato neutro',
   'AdultContentWarning' => {
      Description => 'Avviso contenuto per adulti',
      PrintConv => {
        'Adult Content Warning Required' => 'Avviso contenuto per adulti richiesto',
        'Not Required' => 'Non richiesto',
        'Unknown' => 'Sconosciuto',
      },
    },
   'AdvancedContentEncryption' => 'Crittografia avanzata del contenuto',
   'AdvancedMutualExcl' => 'Mutua esclusione avanzata',
   'AdvancedRaw' => {
      Description => 'Raw avanzato',
      PrintConv => {
        'Off' => 'Spento',
        'On' => 'Acceso',
      },
    },
   'AdvancedSceneMode' => {
      Description => 'Modo scena avanzato',
      PrintConv => {
        'Architecture' => 'Architettura',
        'Color Select' => 'Selezione colore',
        'Creative Macro' => 'Macro creativo',
        'Creative Night Scenery' => 'Scenario notturno creativo',
        'Creative Portrait' => 'Ritratto creativo',
        'Creative Scenery' => 'Scenario creativo',
        'Creative Sports' => 'Sport creativi',
        'Cross Process' => 'Cross process',
        'Dynamic Art' => 'Arte dinamica',
        'Dynamic Monochrome' => 'Monocromatico dinamico',
        'Elegant' => 'Elegante',
        'Expressive' => 'Espressivo',
        'Flower' => 'Fiore',
        'HDR Art' => 'HDR artistico',
        'HDR B&W' => 'HDR in bianco e nero',
        'High Dynamic' => 'High dynamic',
        'High Key' => 'High key',
        'Illuminations' => 'Illuminazioni',
        'Indoor Portrait' => 'Ritratto al chiuso',
        'Indoor Sports' => 'Sport al chiuso',
        'Low Key' => 'Low key',
        'Minature' => 'Minuatura',
        'Monochrome' => 'Monocromatico',
        'Nature' => 'Natura',
        'Objects' => 'Oggetti',
        'Off' => 'Spento',
        'Outdoor Portrait' => 'Ritratto all\'aperto',
        'Outdoor Sports' => 'Sport all\'aperto',
        'Pure' => 'Puro',
        'Retro' => 'Retrò',
        'Sepia' => 'Seppia',
        'Soft' => 'Morbido',
        'Star' => 'Stella',
        'Toy Effect' => 'Effetto giocattolo',
      },
    },
   'AdvancedSceneType' => 'Tipo scena avanzato',
   'AdvantageCompOverflow' => 'Overflow componente avanzato',
   'AdvantageCompUnderflow' => 'Underflow componente avanzato',
   'AdventRevision' => 'Revisione arrivo',
   'AdventScale' => 'Scala arrivo',
   'AdvertisingMaterialReference' => 'Riferimento materiale pubblicitario',
   'Advisory' => 'Consultivo',
   'AlbumArtistSortOrder' => 'Ordinamento album-artista',
   'AlbumSortOrder' => 'Ordinamento album',
   'AliasLayerMetadata' => 'Livello metadati alias',
   'AlphaByteCount' => 'Numero byte trasparenza',
   'AlphaDataDiscard' => {
      Description => 'Scarto dati trasparenza',
      PrintConv => {
        'Flexbits Discarded' => 'Flexbit scartati',
        'Full Resolution' => 'Risoluzione piena',
        'HighPass Frequency Data Discarded' => 'Dati in frequenza passa-alto scartati',
        'Highpass and LowPass Frequency Data Discarded' => 'Dati in frequenza passa-alto e passa-basso scartati',
      },
    },
   'AlphaInterlace' => {
      PrintConv => {
        'Noninterlaced' => 'Non interlacciato',
      },
    },
   'AlphaOffset' => 'Scostamento trasparenza',
   'AlphaTransparency' => {
      PrintConv => {
        'Not Inverted' => 'Non invertito',
      },
    },
   'AmbienceSelection' => {
      PrintConv => {
        'Vivid' => 'Vivace',
      },
    },
   'AnalogBalance' => 'Bilanciamento analogico',
   'Annotation' => 'Annotazione',
   'Annotations' => 'Annotazioni',
   'Anti-Blur' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'AntiAliasStrength' => 'Forza antialiasing',
   'Aperture' => 'Diaframma',
   'ApertureDisplayed' => 'Diaframma mostrato',
   'ApertureRange' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ApertureRingUse' => {
      PrintConv => {
        'Prohibited' => 'Proibito',
      },
    },
   'ApertureValue' => 'Apertura diaframma',
   'AppleStoreCountry' => {
      PrintConv => {
        'Italy' => 'Italia',
        'Japan' => 'Giappone',
        'Norway' => 'Norvegia',
        'Portugal' => 'Portogallo',
        'Sweden' => 'Svezia',
        'United Kingdom' => 'Regno Unito',
        'United States' => 'Stati Uniti',
      },
    },
   'ApplicationNotes' => 'Note applicazione',
   'ApplicationRecordVersion' => 'Versione Registrazione Applicazione',
   'ApplyShootingMeteringMode' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ArtFilter' => {
      PrintConv => {
        'Fish Eye' => 'Fish-eye',
        'Fragmented' => 'Frammentato',
        'Gentle Sepia' => 'Seppia leggero',
        'Off' => 'Spento',
        'Reflection' => 'Riflessione',
      },
    },
   'ArtFilterEffect' => {
      PrintConv => {
        'Fish Eye' => 'Fish-eye',
        'Fragmented' => 'Frammentato',
        'Gentle Sepia' => 'Seppia leggero',
        'Off' => 'Spento',
        'Reflection' => 'Riflessione',
        'Star Light' => 'Luce stelle',
      },
    },
   'ArtMode' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'Artist' => 'Persona che ha creato l\'immagine',
   'Artist2' => 'Artista 2',
   'ArtistURL' => 'URL artista',
   'AsShotICCProfile' => 'Profilo ICC allo scatto',
   'AsShotNeutral' => 'Neutrale allo scatto',
   'AsShotPreProfileMatrix' => 'Matrice pre-profilo allo scatto',
   'AsShotProfileName' => 'Nome profilo allo scatto',
   'AsShotWhiteXY' => 'Bilanciamento del bianco X-T allo scatto',
   'AspectRatio' => 'Rapporto immagine',
   'AspectRatioType' => {
      Description => 'Tipo rapporto immagine',
      PrintConv => {
        'Fixed' => 'Fisso',
      },
    },
   'AspectRatioX' => 'Rapporto immagine X',
   'AspectRatioY' => 'Rapporto immagine Y',
   'AssistButtonFunction' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'Attachments' => 'Allegati',
   'Audio' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'AudioAttributes' => {
      Description => 'Attributi audio',
      PrintConv => {
        'Encrypted' => 'Crittografato',
      },
    },
   'AudioBytes' => 'Byte audio',
   'AudioChannelType' => 'Tipo canale audio',
   'AudioChannels' => 'Canali audio',
   'AudioCodec' => 'Codec audio',
   'AudioCodecDescription' => 'Descrizione codec audio',
   'AudioCodecID' => {
      Description => 'ID codec audio',
      PrintConv => {
        'QDesign Music' => 'Musica QDesign',
        'Unknown -' => 'Sconosciuto -',
      },
    },
   'AudioCodecInfo' => 'Info codec audio',
   'AudioCodecName' => 'Nome codec audio',
   'AudioCompression' => 'Compressione audio',
   'AudioCompressionAlgorithm' => 'Algoritmo di compressione audio',
   'AudioFormat' => 'Formato audio',
   'AudioLayer' => 'Livello audio',
   'AudioStreamType' => {
      PrintConv => {
        'Reserved' => 'Riservato',
      },
    },
   'AudioType' => {
      Description => 'Tipo Audio',
      PrintConv => {
        'Text Only' => 'Solo testo',
      },
    },
   'Author' => 'Autore',
   'AuthorsPosition' => 'Posizione dell\'autore',
   'AutoAperture' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoBracketModeM' => {
      PrintConv => {
        'Flash Only' => 'Solo flash',
      },
    },
   'AutoBracketSet' => {
      PrintConv => {
        'Exposure' => 'Esposizione',
        'Flash Only' => 'Solo flash',
      },
    },
   'AutoBracketing' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoDistortionControl' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoExposureBracketing' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoFP' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoFocus' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoISO' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoLightingOptimizer' => {
      PrintConv => {
        'Enable' => 'Abilita',
        'Low' => 'Basso',
        'Off' => 'Spento',
        'Strong' => 'Forte',
        'n/a' => 'n/d',
      },
    },
   'AutoLightingOptimizerOn' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'AutoRedEye' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'AutoRotate' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
        'n/a' => 'n/d',
      },
    },
   'AuxiliaryLens' => 'Obiettivo Ausiliario',
   'AvSettingWithoutLens' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'AverageLevel' => 'Livello medio',
   'Azimuth' => {
      PrintConv => {
        'NNW' => 'NNO',
      },
    },
   'BWMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'BackgroundColorIndicator' => {
      Description => 'Indicatore colore di sfondo',
      PrintConv => {
        'Specified Background Color' => 'Colore di sfondo specificato',
        'Unspecified Background Color' => 'Colore di sfondo non specificato',
      },
    },
   'BackgroundColorValue' => 'Valore colore di sfondo',
   'BackgroundTiling' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'BadFaxLines' => 'Linee fax non valide',
   'BannerImageType' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'BaselineExposure' => 'Esposizione di riferimento',
   'BaselineNoise' => 'Rumore di riferimento',
   'BaselineSharpness' => 'Nitidezza di riferimento',
   'BatteryLevel' => 'Livello batteria',
   'BatteryState' => {
      PrintConv => {
        'Low' => 'Basso',
      },
    },
   'BeatsPerMinute' => 'Battiti al minuto',
   'Beep' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'BeepPitch' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'BeepVolume' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'BestQualityScale' => 'Scala qualità migliore',
   'BestShotMode' => {
      PrintConv => {
        'Children' => 'Bambini',
        'Fireworks' => 'Fuochi artificiali',
        'Flower' => 'Fiore',
        'For YouTube' => 'Per YouTube',
        'For eBay' => 'Per eBay',
        'Off' => 'Spento',
        'Retro' => 'Retrò',
        'Scenery' => 'Paesaggio',
        'Short Movie' => 'Filmato breve',
        'Text' => 'Testo',
      },
    },
   'BitsPerComponent' => 'Bits per componente',
   'BitsPerExtendedRunLength' => 'Bit per rrun-length esteso',
   'BitsPerRunLength' => 'Bit per rrun-length',
   'BitsPerSample' => 'Numero di bit per componente',
   'BlackLevel' => 'Livello del nero',
   'BlackLevelDeltaH' => 'Livello del nero - Delta H',
   'BlackLevelDeltaV' => 'Livello del nero - Delta V',
   'BlackLevelRepeatDim' => 'Dim ripeti livello del nero',
   'BleachBypassToning' => {
      PrintConv => {
        'Green' => 'Verde',
        'Off' => 'Spento',
        'Purple' => 'Porpora',
        'Red' => 'Rosso',
      },
    },
   'BlocksPerFrame' => 'Blocchi per frame',
   'BlueBalance' => 'Bilanciamento del blu',
   'BlueMatrixColumn' => 'Colonna della Matrice Blu',
   'BlueTRC' => 'Curva riproduzione tono blu',
   'BlurControl' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'BlurWarning' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'BracketMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'BracketShotNumber' => {
      PrintConv => {
        '1 of 2' => '1 di 5',
        '1 of 3' => '1 di 3',
        '1 of 5' => '1 di 2',
        'n/a' => 'n/d',
      },
    },
   'Brightness' => 'Luminosità',
   'BrightnessValue' => 'Valore di luminosità',
   'BuildDate' => 'Data compilazione',
   'BuildVersion' => 'Versione compilazione',
   'BurstMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ButtonFunctionControlOff' => {
      PrintConv => {
        'Normal (enable)' => 'Normale (abilitato)',
      },
    },
   'By-line' => 'Creatore',
   'By-lineTitle' => 'Titolo Creatore',
   'CCDScanMode' => {
      PrintConv => {
        'Progressive' => 'Progressivo',
      },
    },
   'CFALayout' => {
      Description => 'Layout CFA',
      PrintConv => {
        'Even columns offset down 1/2 row' => 'Colonne pari giù di 1/2 riga',
        'Even columns offset up 1/2 row' => 'Colonne pari su di 1/2 riga',
        'Even rows offset down by 1/2 row, even columns offset left by 1/2 column' => 'Righe pari giù di 1/2 riga, colonne pari a sinistra di 1/2 colonna',
        'Even rows offset down by 1/2 row, even columns offset right by 1/2 column' => 'Righe pari giù di 1/2 riga, colonne pari a destra di 1/2 colonna',
        'Even rows offset left 1/2 column' => 'Righe pari a sinistra di 1/2 colonna',
        'Even rows offset right 1/2 column' => 'Righe pari a destra di 1/2 colonna',
        'Even rows offset up by 1/2 row, even columns offset left by 1/2 column' => 'Righe pari su 1/2 riga, colonne pari a sinistra di 1/2 colonna',
        'Even rows offset up by 1/2 row, even columns offset right by 1/2 column' => 'Righe pari su 1/2 riga, colonne pari a destra di 1/2 colonna',
        'Rectangular' => 'Rettangolare',
      },
    },
   'CFAPattern' => 'Pattern CFA',
   'CFAPattern2' => 'Pattern CFA 2',
   'CFAPlaneColor' => 'Piano colori CFA',
   'CFARepeatPatternDim' => 'Dim pattern ripetuto CFA',
   'CHMVersion' => 'Versione CHM',
   'CIP3DataFile' => 'File di dati CIP3',
   'CIP3Sheet' => 'Foglio CIP3',
   'CIP3Side' => 'Lato CIP3',
   'CMYKEquivalent' => 'CMYK equivalente',
   'CPUArchitecture' => 'Architettura CPU',
   'CPUByteOrder' => {
      Description => 'Ordine byte CPU',
      PrintConv => {
        'Big endian' => 'Big-endian',
        'Little endian' => 'Little-endian',
      },
    },
   'CPUCount' => 'Numero processori',
   'CPUSubtype' => {
      Description => 'Sottotipo CPU',
      PrintConv => {
        'ARM (all)' => 'ARM (tutti)',
        'HPPA (all)' => 'HPPA (tutti)',
        'MC680x0 (all)' => 'MC680x0 (tutti)',
        'MC88000 (all)' => 'MC88000 (tutti)',
        'MC98000 (all)' => 'MC98000 (tutti)',
        'MIPS (all)' => 'MIPS (tutti)',
        'NS32032 (all)' => 'NS32032 (tutti)',
        'NS32032 DPC (032 CPU)' => 'NS32032 DPC (CPU 032)',
        'NS32332 (all)' => 'NS32332 (tutti)',
        'NS32332 DPC (032 CPU)' => 'NS32332 DPC (CPU 032)',
        'PowerPC (all)' => 'PowerPC (tutti)',
        'RS6000 (all)' => 'RS6000 (tutti)',
        'RT (all)' => 'RT (tutti)',
        'SPARC (all)' => 'SPARC (tutti)',
        'VAX (all)' => 'VAX (tutti)',
        'i386 (all)' => 'i386 (tutti)',
        'i860 (all)' => 'i860 (tutti)',
        'i860 little (all)' => 'i860 little (tutti)',
      },
    },
   'CPUType' => {
      Description => 'Tipo CPU',
      PrintConv => {
        'Any' => 'Qualsiasi',
        'Axis Communications 32-bit embedded processor' => 'Processore integrato a 32 bit Axis Communications',
        'None' => 'Nessuno',
        'S/390 (old)' => 'S/390 (precedente)',
        'i860 big endian' => 'i860 big-endian',
        'i860 little endian' => 'i860 little-endian',
        'm32r (old)' => 'm32r (precedente)',
        'v850 (old)' => 'v850 (precedente)',
      },
    },
   'CalibrationDateTime' => 'Data/ora di calibrazione',
   'CalibrationIlluminant1' => {
      Description => 'Calibration illuminazione 1',
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Cool White Fluorescent' => 'Fluorescente a luce calda',
        'Day White Fluorescent' => 'Fluorescente a luce del giorno bianca',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Fine Weather' => 'Bel tempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno studio ISO',
        'Other' => 'Altra Sorgente di Luce',
        'Shade' => 'Ombrato',
        'Standard Light A' => 'Luce standard A',
        'Standard Light B' => 'Luce standard B',
        'Standard Light C' => 'Luce standard C',
        'Tungsten (Incandescent)' => 'Tungsteno (luce incandescente)',
        'Unknown' => 'Sconosciuto',
        'Warm White Fluorescent' => 'Luce fluorescente bianca calda',
        'White Fluorescent' => 'Fluorescente bianca',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Calibration illuminazione 2',
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Cool White Fluorescent' => 'Fluorescente a luce calda',
        'Day White Fluorescent' => 'Fluorescente a luce del giorno bianca',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Fine Weather' => 'Bel tempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno studio ISO',
        'Other' => 'Altra Sorgente di Luce',
        'Shade' => 'Ombrato',
        'Standard Light A' => 'Luce standard A',
        'Standard Light B' => 'Luce standard B',
        'Standard Light C' => 'Luce standard C',
        'Tungsten (Incandescent)' => 'Tungsteno (luce incandescente)',
        'Unknown' => 'Sconosciuto',
        'Warm White Fluorescent' => 'Luce fluorescente bianca calda',
        'White Fluorescent' => 'Fluorescente bianca',
      },
    },
   'CameraAngle' => 'Angolo fotocamera',
   'CameraBody' => 'Corpo fotocamera',
   'CameraByteOrder' => 'Ordine byte fotocamera',
   'CameraCalibration1' => 'Calibrazione fotocamera 1',
   'CameraCalibration2' => 'Calibrazione fotocamera 2',
   'CameraCalibrationSig' => 'Segnale calibrazione fotocamera',
   'CameraColorCalibration01' => 'Calibrazione colore fotocamera 01',
   'CameraColorCalibration02' => 'Calibrazione colore fotocamera 02',
   'CameraColorCalibration03' => 'Calibrazione colore fotocamera 03',
   'CameraColorCalibration04' => 'Calibrazione colore fotocamera 04',
   'CameraColorCalibration05' => 'Calibrazione colore fotocamera 05',
   'CameraColorCalibration06' => 'Calibrazione colore fotocamera 06',
   'CameraColorCalibration07' => 'Calibrazione colore fotocamera 07',
   'CameraColorCalibration08' => 'Calibrazione colore fotocamera 08',
   'CameraColorCalibration09' => 'Calibrazione colore fotocamera 09',
   'CameraColorCalibration10' => 'Calibrazione colore fotocamera 10',
   'CameraColorCalibration11' => 'Calibrazione colore fotocamera 11',
   'CameraColorCalibration12' => 'Calibrazione colore fotocamera 12',
   'CameraColorCalibration13' => 'Calibrazione colore fotocamera 13',
   'CameraColorCalibration14' => 'Calibrazione colore fotocamera 14',
   'CameraColorCalibration15' => 'Calibrazione colore fotocamera 15',
   'CameraDateTime' => 'Data/ora fotocamera',
   'CameraDirection' => 'Direzione fotocamera',
   'CameraID' => 'ID fotocamera',
   'CameraISO' => 'ISO fotocamera',
   'CameraIdentifier' => 'Identificativo fotocamera',
   'CameraLabel' => 'Etichetta fotocamera',
   'CameraMaker' => 'Marca fotocamera',
   'CameraManufacturer' => 'Produttore fotocamera',
   'CameraModel' => 'Modello fotocamera',
   'CameraMotion' => 'Camera motion',
   'CameraMove' => 'Sposta fotocamera',
   'CameraName' => 'Nome fotocamera',
   'CameraObjBackType' => 'Nome oggetto nero fotocamera',
   'CameraObjName' => 'Nome oggetto fotocamera',
   'CameraObjType' => 'Tipo oggetto fotocamera',
   'CameraObjVersion' => 'Versione oggetto fotocamera',
   'CameraOrientation' => {
      Description => 'Orientazione fotocamera',
      PrintConv => {
        'Horizontal (normal)' => 'Orizzontale (normale)',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
      },
    },
   'CameraOwner' => 'Proprietario fotocamera',
   'CameraParameters' => 'Parametri fotocamera',
   'CameraProfile' => 'Profilo fotocamera',
   'CameraProfileDigest' => 'Sommario profilo fotocamera',
   'CameraProfileVersion' => 'Versione profilo fotocamera',
   'CameraSerialNumber' => 'Numero di serie fotocamera',
   'CameraSettingsVersion' => 'Versione impostazioni fotocamera',
   'CameraTemperature' => 'Temperatura fotocamera',
   'CameraTemperature2' => 'Temperatura fotocamera 2',
   'CameraTemperature3' => 'Temperatura fotocamera 3',
   'CameraTemperature4' => 'Temperatura fotocamera 4',
   'CameraTemperature5' => 'Temperatura fotocamera 5',
   'CameraType' => {
      Description => 'Tipo fotocamera',
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'CameraType2' => 'Tipo fotocamera 2',
   'CanonExposureMode' => {
      PrintConv => {
        'Aperture-priority AE' => 'Priorità diaframma',
        'Manual' => 'Manuale',
        'Program AE' => 'Programma AE',
        'Shutter speed priority AE' => 'Priorità otturatore AE',
      },
    },
   'CanonFlashMode' => {
      PrintConv => {
        'External flash' => 'Flash esterno',
        'Off' => 'Spento',
        'Red-eye reduction' => 'Riduzione occhi rossi',
        'Red-eye reduction (Auto)' => 'Riduzione occhi rossi (auto)',
        'Red-eye reduction (On)' => 'Riduzione occhi rossi (attivo)',
      },
    },
   'CanonImageSize' => {
      PrintConv => {
        'Postcard' => 'Cartolina',
      },
    },
   'Caption-Abstract' => 'Didascalia/descrizione',
   'CaptionWriter' => 'Autore della didascalia',
   'CardShutterLock' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'CasioQuality' => {
      Description => 'Qualità Casio',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'Categories' => {
      Description => 'Categorie',
      PrintConv => {
        'Events' => 'Eventi',
        'Scenery' => 'Paesaggio',
        'To Do' => 'Da fare',
      },
    },
   'Category' => 'Categoria',
   'CellLength' => 'Lunghezza cella',
   'CellWidth' => 'Larghezza cella',
   'CenterWeightedAreaSize' => {
      PrintConv => {
        'Average' => 'Media',
      },
    },
   'ChExtra' => 'Larghezza aggiunta per caratteri non spaziatori',
   'Channel' => 'Canale',
   'ChannelID' => 'ID canale',
   'ChannelIdentificationCode' => 'Codice identificativo canale',
   'ChannelLabel' => 'Etichetta canale',
   'ChannelLength' => 'Lunghezza canale',
   'ChannelMode' => 'Modo canale',
   'ChannelNumber' => 'Numero canale',
   'ChannelPositions' => 'Posizioni canale',
   'ChannelStatus' => 'Stato canale',
   'ChannelStatusMode' => 'Modo stato canale',
   'ChannelTotalTime' => 'Tempo totale canale',
   'ChannelWidth' => 'Larghezza canale',
   'Channels' => 'Canali',
   'Chapter' => 'Capitolo',
   'ChapterPhysicalEquivalent' => {
      PrintConv => {
        'Session' => 'Sessione',
        'Side' => 'Lato',
      },
    },
   'ChapterProcessTime' => {
      PrintConv => {
        'For Duration of Chapter' => 'Per la durata del capitolo',
      },
    },
   'CharacterSet' => {
      Description => 'Set di caratteri',
      PrintConv => {
        'Windows, Arabic' => 'Windows, Arabo',
        'Windows, Chinese (Simplified)' => 'Windows, Cinese semplificato',
        'Windows, Cyrillic' => 'Windows, Cirillico',
        'Windows, Greek' => 'Windows, Greco',
        'Windows, Hebrew' => 'Windows, Ebraico',
        'Windows, Japan (Shift - JIS X-0208)' => 'Windows, Giappone (Shift - JIS X-0208)',
        'Windows, Korea (Shift - KSC 5601)' => 'Windows, Corea (Shift - KSC 5601)',
        'Windows, Latin2 (Eastern European)' => 'Windows, Latin2 (Europa orientale)',
        'Windows, Turkish' => 'Windows, Turco',
      },
    },
   'Characters' => 'Caratteri',
   'CharactersWithSpaces' => 'Caratteri con spazi',
   'Children' => 'Bambini',
   'ChromaBlurRadius' => 'Raggio sfocatura cromatica',
   'ChromaticAberrationCorrection' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ChromaticAdaptation' => 'Adattamento cromatico',
   'Chromaticity' => 'Cromatismo',
   'ChrominanceNR_TIFF_JPEG' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'ChrominanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'City' => 'Città',
   'ClassifyState' => 'Stato di Classificazione',
   'CleanFaxData' => {
      Description => 'Dati fax precisi',
      PrintConv => {
        'Clean' => 'Pulito',
        'Regenerated' => 'Rigenerato',
        'Unclean' => 'Sporco',
      },
    },
   'ClipPath' => 'Percoso clip',
   'CodePage' => {
      PrintConv => {
        'Japanese (JIS 0208-1990 and 0121-1990)' => 'Giapponese (JIS 0208-1990 e 0121-1990)',
        'Russian/Cyrillic (KOI8-R)' => 'Russo/Cirillico (KOI8-R)',
      },
    },
   'CodeSize' => 'Dimensione codice',
   'CodedCharacterSet' => 'Impostazione Caratteri Codificati',
   'CodedContentScanningKind' => {
      PrintConv => {
        'Mixed' => 'Misto',
        'Progressive' => 'Progressivo',
        'Unknown' => 'Sconosciuto',
      },
    },
   'CodingMethods' => {
      Description => 'Metodi di codifica',
      PrintConv => {
        'Baseline JPEG' => 'Linea di base JPEG',
        'JBIG color' => 'JBIG a colori',
        'Modified Huffman' => 'Huffman modificato',
        'Unspecified compression' => 'Compressione non specificata',
      },
    },
   'ColorAberrationControl' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ColorAdjustmentMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ColorBalance' => 'Bilanciamento Colore',
   'ColorBalanceAdj' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ColorBalanceVersion' => 'Versione Bilanciamento Colore',
   'ColorBooster' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ColorCharacterization' => 'Caratterizzazione colore',
   'ColorClass' => {
      PrintConv => {
        '0 (None)' => '0 (Nessuno)',
        '1 (Winner)' => '1 (Vincitore)',
      },
    },
   'ColorEffect' => {
      PrintConv => {
        'Off' => 'Spento',
        'Sepia' => 'Seppia',
      },
    },
   'ColorFilter' => {
      Description => 'Filtro colori',
      PrintConv => {
        'Green' => 'Verde',
        'Off' => 'Spento',
        'Purple' => 'Porpora',
        'Red' => 'Rosso',
        'Sepia' => 'Seppia',
        'Yellow' => 'Giallo',
      },
    },
   'ColorHue' => 'Colore Hue',
   'ColorMap' => 'Mappa colore',
   'ColorMatrix1' => 'Matrice colore 1',
   'ColorMatrix2' => 'Matrice colore 2',
   'ColorMode' => {
      Description => 'Modo colore',
      PrintConv => {
        'Autumn Leaves' => 'Foglie d\'autunno',
        'B&W' => 'B/N',
        'Clear' => 'Trasparente',
        'Deep' => 'Cupa',
        'Evening' => 'Sera',
        'Grayscale' => 'Scala di grigi',
        'Landscape' => 'Orizzontale',
        'Light' => 'Chiara',
        'Neutral' => 'Neutra',
        'Night View' => 'Visione notturna',
        'Night View/Portrait' => 'Rit. notturno',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Portrait' => 'Verticale',
        'RGB Color' => 'Colore RGB',
        'Saturated Color' => 'Colore saturato',
        'Sepia' => 'Seppia',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
        'Vivid color' => 'Colore vivace',
        'n/a' => 'n/d',
      },
    },
   'ColorMoireReduction' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ColorMoireReductionMode' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'ColorPalette' => 'Palette di Colore',
   'ColorProfile' => {
      PrintConv => {
        'Not Embedded' => 'Non incorporato',
      },
    },
   'ColorRepresentation' => {
      Description => 'Rappresentazione del Colore',
      PrintConv => {
        'No Image, Single Frame' => 'Nessuna immagine, quadro singolo',
      },
    },
   'ColorResponseUnit' => 'Unità risposta colore',
   'ColorSequence' => 'Sequenza colori',
   'ColorSpace' => {
      Description => 'Spazio colore',
      PrintConv => {
        'Gray-scale' => 'Scala di grigi',
        'Grayscale' => 'Scala di grigi',
        'ICC Profile' => 'Profilo ICC',
        'Uncalibrated' => 'Non calibrato',
      },
    },
   'ColorSpaceData' => 'Dati Spazio Colore',
   'ColorSpecApproximation' => {
      PrintConv => {
        'Not Specified' => 'Non specificato',
      },
    },
   'ColorSpecMethod' => {
      PrintConv => {
        'Enumerated' => 'Enumerato',
      },
    },
   'ColorTable' => 'Tabella colore',
   'ColorTemperature' => 'Temperatura colore',
   'ColorTemperatureSetting' => {
      PrintConv => {
        'Temperature' => 'Temperatura',
      },
    },
   'ColorTone' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'ColorToneFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneMonochrome' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorTonePortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneUnknown' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneUserDef1' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneUserDef2' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorToneUserDef3' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ColorType' => {
      PrintConv => {
        'Grayscale' => 'Scala di grigi',
        'RGB with Alpha' => 'RGB con trasparenza',
      },
    },
   'ColorimetricReference' => 'Riferimento colorimetrico',
   'CommandDialsChangeMainSub' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'CommandDialsMenuAndPlayback' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'CommandDialsReverseRotation' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'CommanderGroupAMode' => {
      PrintConv => {
        'Auto Aperture' => 'Diaframma automatico',
        'Manual' => 'Manuale',
        'Off' => 'Spento',
      },
    },
   'CommanderGroupBMode' => {
      PrintConv => {
        'Auto Aperture' => 'Diaframma automatico',
        'Manual' => 'Manuale',
        'Off' => 'Spento',
      },
    },
   'CommanderInternalFlash' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'Off' => 'Spento',
      },
    },
   'Comment' => 'Commento',
   'CommentTime' => 'Ora commento',
   'Comments' => 'Commenti',
   'CommercialURL' => 'URL pubblicitario',
   'CompanyName' => 'Nome azienda',
   'Compilation' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ComponentsConfiguration' => 'Configurazione componenti',
   'Composer' => 'Compositore',
   'ComposerSortOrder' => 'Ordinamento compositore',
   'CompositionAdjust' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'CompositionMode' => {
      PrintConv => {
        'Replace' => 'Sostituisci',
      },
    },
   'Compressed' => {
      PrintConv => {
        'False' => 'Falso',
      },
    },
   'CompressedBitsPerPixel' => 'Bit per pixel compressi',
   'Compression' => {
      Description => 'Compressione',
      PrintConv => {
        'Adobe Deflate' => 'Deflate Adobe',
        'JBIG B&W' => 'JBIG B&N',
        'JBIG Color' => 'JBIG a colori',
        'JPEG' => 'Compressione JPEG',
        'JPEG (old-style)' => 'JPEG (vecchio stile)',
        'Kodak DCR Compressed' => 'Kodak DCR compresso',
        'Kodak KDC Compressed' => 'Kodak KDC compresso',
        'Microsoft Document Imaging (MDI) Binary Level Codec' => 'Microsoft Document Imaging (MDI) - Codec a livello binario',
        'Microsoft Document Imaging (MDI) Progressive Transform Codec' => 'Microsoft Document Imaging (MDI) - Codec a trasformazione progressiva',
        'Microsoft Document Imaging (MDI) Vector' => 'Microsoft Document Imaging (MDI) - Vettore',
        'Next' => 'Codifica NeXT',
        'Nikon NEF Compressed' => 'Nikon NEF compresso',
        'None' => 'Nessuno',
        'Packed RAW' => 'RAW pacchettizzato',
        'Pentax PEF Compressed' => 'Pentax PEF compresso',
        'RLE Encoding' => 'Codifica RLE',
        'Samsung SRW Compressed' => 'Samsung SRW compresso',
        'Sony ARW Compressed' => 'Sony ARW compresso',
        'T4/Group 3 Fax' => 'Fax T4/Group 3',
        'T6/Group 4 Fax' => 'Fax T6/Group 4',
        'Uncompressed' => 'Non compresso',
      },
    },
   'CompressionLevel' => 'Livello di compressione',
   'CompressionType' => {
      Description => 'Tipo compressione',
      PrintConv => {
        'Little-endian, no compression' => 'Little-endian, senza compressione',
        'None' => 'Nessuno',
      },
    },
   'CompressorName' => 'Nome compressione',
   'ConditionalFEC' => 'Compensazione Esposizione Flash',
   'Conductor' => 'Direttore d\'orchestra',
   'ConsecutiveBadFaxLines' => 'Linee fax non valide consecutive',
   'Contact' => 'Contatto',
   'ContentEncodingType' => {
      PrintConv => {
        'Encryption' => 'Crittografia',
      },
    },
   'ContentEncryptionAlgorithm' => {
      PrintConv => {
        'Not Encrypted' => 'Non crittografato',
      },
    },
   'ContentLocationCode' => 'Codice ubicazione contenuto',
   'ContentLocationName' => 'Nome Ubicazione Contenuto',
   'ContentSignatureAlgorithm' => {
      PrintConv => {
        'Not Signed' => 'Senza segno',
      },
    },
   'ContentSignatureHashAlgorithm' => {
      PrintConv => {
        'Not Signed' => 'Senza segno',
      },
    },
   'ContinuousBracketing' => {
      PrintConv => {
        'Low' => 'Basso',
      },
    },
   'ContinuousShootingSpeed' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ContinuousShotLimit' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'Contrast' => {
      Description => 'Contrasto',
      PrintConv => {
        'High' => 'Alto',
        'Low' => 'Basso',
        'Normal' => 'Normale',
      },
    },
   'ContrastCurve' => 'Curva di contrasto',
   'ContrastDetectAF' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ContrastFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastHighlightShadowAdj' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ContrastLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastMode' => {
      PrintConv => {
        'Elegant (My Color)' => 'Elegante (mio colore)',
        'Expressive' => 'Espressivo',
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Nostalgic (Color Film)' => 'Nostalgico (pellicola a colori)',
        'Retro' => 'Retrò',
        'Retro (My Color)' => 'Retro (colore personalizzato)',
      },
    },
   'ContrastMonochrome' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastPortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastUnknown' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastUserDef1' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastUserDef2' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ContrastUserDef3' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ControlDialSet' => {
      PrintConv => {
        'Shutter Speed' => 'Tempo esposizione',
      },
    },
   'ControlMode' => {
      PrintConv => {
        'Camera Local Control' => 'Controllo locale fotocamera',
        'n/a' => 'n/d',
      },
    },
   'ConversionLens' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Converter' => 'Convertitore',
   'Copyright' => 'Titolare del copyright',
   'CopyrightFlag' => {
      PrintConv => {
        'False' => 'Falso',
      },
    },
   'CopyrightNotice' => 'Info Copyright',
   'CopyrightStatus' => {
      PrintConv => {
        'Not specified' => 'Non specificato',
        'Protected' => 'Protetto',
        'Public Domain' => 'Pubblico dominio',
        'Unknown' => 'Sconosciuto',
      },
    },
   'CopyrightURL' => 'URL copyright',
   'Country' => 'Paese',
   'Country-PrimaryLocationCode' => 'Codice ISO Nazione',
   'Country-PrimaryLocationName' => 'Nazione',
   'CreateDate' => 'Data di creazione',
   'CreationDate' => 'Data di creazione',
   'CreativeStyle' => {
      PrintConv => {
        'Autumn Leaves' => 'Foglie d\'autunno',
        'B&W' => 'B/N',
        'Clear' => 'Trasparente',
        'Deep' => 'Cupa',
        'Landscape' => 'Orizzontale',
        'Light' => 'Chiara',
        'Neutral' => 'Neutra',
        'Night View/Portrait' => 'Rit. notturno',
        'Portrait' => 'Verticale',
        'Sepia' => 'Seppia',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
      },
    },
   'CreativeStyleSetting' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
      },
    },
   'Creator' => 'Creatore',
   'CreatorAddress' => 'Autore - Indirizzo',
   'CreatorAppID' => {
      PrintConv => {
        'Unknown' => 'Sconosciuto',
      },
    },
   'CreatorCity' => 'Autore - Città',
   'CreatorCountry' => 'Autore - Nazione',
   'CreatorPostalCode' => 'Autore - Codice Postale',
   'CreatorRegion' => 'Autore - Stato/Provincia',
   'CreatorWorkEmail' => 'Autore - E.Mail',
   'CreatorWorkTelephone' => 'Autore - Numero di Telefono',
   'CreatorWorkURL' => 'Autore - Sito web',
   'Credit' => 'Fornitore',
   'CreditLineRequired' => {
      PrintConv => {
        'Not Required' => 'Non richiesto',
      },
    },
   'CropActive' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'CropUnit' => {
      PrintConv => {
        'inches' => 'Pollici',
      },
    },
   'CropUnits' => {
      PrintConv => {
        'inches' => 'Pollici',
      },
    },
   'CrossProcess' => {
      PrintConv => {
        'Favorite 1' => 'Preferito 1',
        'Favorite 2' => 'Preferito 2',
        'Favorite 3' => 'Preferito 3',
        'Off' => 'Spento',
        'Random' => 'Casuale',
      },
    },
   'CurrentICCProfile' => 'Profilo ICC attuale',
   'CurrentPreProfileMatrix' => 'Matrice di pre-profilo attuale',
   'Curves' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'CustomRendered' => {
      Description => 'Resa personalizzata',
      PrintConv => {
        'Custom' => 'Personalizzata',
        'Normal' => 'Normale',
      },
    },
   'CustomWBError' => {
      PrintConv => {
        'Error' => 'Errore',
      },
    },
   'D-LightingHQ' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'D-LightingHQSelected' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'D-LightingHS' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'D-RangeOptimizerMode' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'Off' => 'Spento',
      },
    },
   'DECPosition' => {
      PrintConv => {
        'Exposure' => 'Esposizione',
        'Saturation' => 'Saturazione',
      },
    },
   'DNGBackwardVersion' => 'Versione precedente DNG',
   'DNGLensInfo' => 'Informazioni lenti DNG',
   'DNGPrivateData' => 'Dati privati DNG',
   'DNGVersion' => 'Versione DNG',
   'DataImprint' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Text' => 'Testo',
      },
    },
   'DataType' => 'Tipo di dati',
   'Date' => 'Data',
   'DateCreated' => 'Data di creazione',
   'DateDisplayFormat' => {
      Description => 'Formato Data',
      PrintConv => {
        'D/M/Y' => 'Giorno/mese/anno',
        'M/D/Y' => 'Mese/giorno/anno',
        'Y/M/D' => 'Anno/mese/giorno',
      },
    },
   'DateImprint' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DateSent' => 'Data d\'invio',
   'DateStampMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DateTime' => 'Data/ora',
   'DateTimeOriginal' => 'Data/ora originale di creazione',
   'DaylightSavings' => {
      PrintConv => {
        'Off' => 'Spento',
        'Yes' => 'Sì',
      },
    },
   'Decode' => 'Decodifica',
   'DefaultCropOrigin' => 'Ritaglio origine predefinito',
   'DefaultCropSize' => 'Ritaglio dimensione predefinito',
   'DefaultImageColor' => 'Colore immagine predefinito',
   'DefaultScale' => 'Scala predefinita',
   'DeletedImageCount' => 'Conteggio Immagini Cancellate',
   'DeltaType' => {
      PrintConv => {
        'Absolute' => 'Assoluto',
      },
    },
   'Description' => 'Descrizione',
   'Destination' => 'Destinazione',
   'DestinationCity' => {
      PrintConv => {
        'Jerusalem' => 'Gerusalemme',
        'Lisbon' => 'Lisbona',
        'London' => 'Londra',
        'Milan' => 'Milano',
        'Prague' => 'Praga',
        'Rome' => 'Roma',
        'Stockholm' => 'Stoccolma',
        'Tehran' => 'Teheran',
      },
    },
   'DestinationDST' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'DeviceAttributes' => 'Attributi Dispositivo',
   'DeviceManufacturer' => 'Costruttore dispositivo',
   'DeviceMfgDesc' => 'Descrizione del Costruttore Dispositivo',
   'DeviceModel' => 'Modello Dispositivo',
   'DeviceModelDesc' => 'Descrizione Modello Dispositivo',
   'DeviceSettingDescription' => 'Descrizione impostazioni dispositivo',
   'DialDirectionTvAv' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Reversed' => 'Invertito',
      },
    },
   'DigitalCreationDate' => 'Data di creazione digitale',
   'DigitalCreationTime' => 'Ora di Creazione Digitale',
   'DigitalFilter01' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter02' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter03' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter04' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter05' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter06' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter07' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter08' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter09' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter10' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter11' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter12' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter13' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter14' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter15' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter16' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter17' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter18' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter19' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalFilter20' => {
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Retro' => 'Retrò',
        'Shading' => 'Ombreggiatura',
        'Starburst' => 'Esplosione stellare',
      },
    },
   'DigitalZoom' => {
      Description => 'Zoom Digitale',
      PrintConv => {
        'None' => 'Nessuno',
        'Off' => 'Spento',
      },
    },
   'DigitalZoomOn' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DigitalZoomRatio' => 'Rapporto zoom digitale',
   'Directory' => 'Posizione file',
   'DisplayAllAFPoints' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'DisplaySize' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'DisplayUnits' => {
      PrintConv => {
        'inches' => 'Pollici',
      },
    },
   'DisplayedUnitsX' => {
      PrintConv => {
        'inches' => 'Pollici',
      },
    },
   'DisplayedUnitsY' => {
      PrintConv => {
        'inches' => 'Pollici',
      },
    },
   'DistortionControl' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DistortionCorrection' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'DistortionCorrection2' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DjVuVersion' => 'Versione DjVu',
   'DocSecurity' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'DocumentHistory' => 'Cronologia documento',
   'DocumentName' => 'Nome documento',
   'DocumentNotes' => 'Note del Documento',
   'DotRange' => 'Intervallo puntuale',
   'DriveMode' => {
      Description => 'Modalità esecuzione',
      PrintConv => {
        '10 s Timer' => 'Timer 10 s',
        'Off' => 'Spento',
        'Self-Timer 2 sec' => 'Autoscatto 2 sec',
        'Self-timer' => 'Autoscatto',
        'Self-timer (12 s)' => 'Autoscatto (12 s)',
        'Self-timer (2 s)' => 'Autoscatto (2 s)',
        'Self-timer 10 sec' => 'Autoscatto 10 sec',
        'Self-timer Operation' => 'Operazione autoscatto',
        'n/a' => 'n/d',
      },
    },
   'DriveMode2' => {
      PrintConv => {
        'Self-timer (12 s)' => 'Autoscatto (12 s)',
        'Self-timer (2 s)' => 'Autoscatto (2 s)',
        'Self-timer 10 sec' => 'Autoscatto 10 sec',
        'Self-timer 2 sec' => 'Autoscatto 2 sec',
      },
    },
   'DriveModeSetting' => {
      PrintConv => {
        'Self-timer 10 sec' => 'Autoscatto 10 sec',
      },
    },
   'DriveType' => {
      PrintConv => {
        'Fixed Disk' => 'Disco fisso',
        'Unknown' => 'Sconosciuto',
      },
    },
   'DynamicRangeExpansion' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DynamicRangeOptimizer' => {
      Description => 'Ott.gamma din.',
      PrintConv => {
        'Advanced Auto' => 'Avanz.autom.',
        'Advanced Lv1' => 'Liv.Avanzato 1',
        'Advanced Lv2' => 'Liv.Avanzato 2',
        'Advanced Lv3' => 'Liv.Avanzato 3',
        'Advanced Lv4' => 'Liv.Avanzato 4',
        'Advanced Lv5' => 'Liv.Avanzato 5',
        'Auto' => 'Automatico',
        'Off' => 'Spento',
      },
    },
   'DynamicRangeOptimizerBracket' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'DynamicRangeOptimizerMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DynamicRangeOptimizerSetting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'DynamicRangeSetting' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'ETTLII' => {
      PrintConv => {
        'Average' => 'Media',
      },
    },
   'EVSteps' => {
      PrintConv => {
        '1/2 EV Steps' => 'Step 1/2 EV',
        '1/3 EV Steps' => 'Step 1/3 EV',
      },
    },
   'EXIFVersion' => 'Versione EXIF',
   'EXRAuto' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'EasyExposureComp' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'EasyExposureCompensation' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'EasyMode' => {
      PrintConv => {
        'Fireworks' => 'Fuochi artificiali',
        'Fisheye Effect' => 'Effetto fish-eye',
        'Flash Off' => 'Flash spento',
        'Kids & Pets' => 'Bambini e animali dimestici',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Night' => 'Scena notturna',
        'Nostalgic' => 'Nostalgico',
        'Portrait' => 'Verticale',
        'Quick Shot' => 'Scatto veloce',
        'Sepia' => 'Seppia',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
        'Zoom Blur' => 'Sfocatura zoom',
      },
    },
   'EdgeNoiseReduction' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'EditStatus' => 'Modifica Stato',
   'EditorialUpdate' => {
      Description => 'Aggiornamento Editoriale',
      PrintConv => {
        'Additional language' => 'Linguaggio addizionale',
      },
    },
   'EffectiveMaxAperture' => 'Diaframma massimo effettivo',
   'Elevation' => 'Elevazione',
   'Emphasis' => {
      PrintConv => {
        'None' => 'Nessuno',
        'reserved' => 'riservato',
      },
    },
   'EncodedBy' => 'Codificato da',
   'EncodedPixelsDimensions' => 'Dimensioni pixel codificati',
   'EncodedUsing' => 'Codificato usando',
   'EncodedWith' => 'Codificato con',
   'Encoder' => 'Codificatore',
   'EncoderSettings' => 'Impostazioni codificatore',
   'EncoderVersion' => 'Versione codificatore',
   'Encoding' => {
      Description => 'Codifica',
      PrintConv => {
        'QDesign Music' => 'Musica QDesign',
        'Unknown -' => 'Sconosciuto -',
      },
    },
   'EncodingProcess' => 'Processo di codifica',
   'EncodingScheme' => 'Schema di codifica',
   'EncodingSettings' => 'Impostazioni di codifica',
   'EncodingTime' => 'Durata codifica',
   'Encryption' => 'Crittografia',
   'EndOfItems' => 'Fine elementi',
   'EndPoints' => 'Punti finali',
   'EndTime' => 'Ora fine',
   'EndUser' => 'Utente finale',
   'EndUserID' => 'ID utente finale',
   'EndUserName' => 'Nome utente finale',
   'EndingPage' => 'Pagina finale',
   'EnhanceDarkTones' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Enhancement' => {
      Description => 'Miglioramento',
      PrintConv => {
        'Green' => 'Verde',
        'Off' => 'Spento',
        'Red' => 'Rosso',
        'Scenery' => 'Paesaggio',
      },
    },
   'EnhancementOrModificationDescription' => 'Descrizione miglioramento/modifica',
   'EntryPoint' => 'Punto d\'ingresso',
   'EnvelopePriority' => {
      PrintConv => {
        '0 (reserved)' => '0 (riservato)',
        '1 (most urgent)' => '1 (molto urgente)',
        '5 (normal urgency)' => '5 (urgenza normale)',
        '8 (least urgent)' => '8 (meno urgente)',
        '9 (user-defined priority)' => '9 (priorità definita dall\'utente)',
      },
    },
   'Error' => 'Errore',
   'ErrorCorrection' => 'Correzione errore',
   'ErrorCorrectionType' => 'Tipo correzione errore',
   'EthnicGroup' => 'Gruppo etnico',
   'Event' => 'Evento',
   'EventAbsoluteDuration' => 'Durata assoluta evento',
   'Events' => 'Eventi',
   'ExcursionTolerance' => {
      PrintConv => {
        'Allowed' => 'Possibile',
        'Not Allowed' => 'Non permesso',
      },
    },
   'ExecutionStatus' => 'Stato esecuzione',
   'ExecutionStatusInfo' => 'Info stato esecuzione',
   'ExifByteOrder' => 'Ordine dei byte Exif',
   'ExifCameraInfo' => 'Info Exif fotocamera',
   'ExifImageHeight' => 'Altezza immagine Exif',
   'ExifImageWidth' => 'Larghezza immagine Exif',
   'ExifInfo2' => 'Info 2 Exif',
   'ExifOffset' => 'Puntatore Exif IFD',
   'ExifToolVersion' => 'Numero versione ExifTool',
   'ExifUnicodeByteOrder' => 'Ordine unicode dei byte Exif',
   'ExifVersion' => 'Versione Exif',
   'ExitPupilPosition' => 'Posizione pupilla d\'uscita',
   'ExpirationDate' => 'Data scadenza',
   'ExpirationTime' => 'Ora scadenza',
   'Expires' => 'Scade',
   'ExposedArea' => 'Area esposta',
   'Exposure' => 'Esposizione',
   'Exposure2012' => 'Esposizione 2012',
   'ExposureBracketValue' => 'Valore Esposizione a Forcella',
   'ExposureBracketingIndicatorLast' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'ExposureCompensation' => 'Compensazione esposizione',
   'ExposureCompensationMode' => 'Modo compensazione esposizione',
   'ExposureControlMode' => 'Modo controllo esposizione',
   'ExposureControlModeDescription' => 'Descrizione modo controllo esposizione',
   'ExposureDelayMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ExposureDifference' => 'Differenza esposizione',
   'ExposureIndex' => 'Indice di esposizione',
   'ExposureIndicator' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'ExposureLevelIncrements' => {
      PrintConv => {
        '1/2 Stop' => '1/2 stop',
        '1/3 Stop' => '1/3 stop',
      },
    },
   'ExposureMode' => {
      Description => 'Modo esposizione',
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
        'Aperture-priority AE' => 'Priorità diaframma',
        'Auto' => 'Esposizione automatica',
        'Auto bracket' => 'A forcella automatica',
        'Fireworks' => 'Fuochi artificiali',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Program AE' => 'Programma AE',
        'Shutter Priority' => 'Priorità otturatore',
        'Shutter speed priority AE' => 'Priorità otturatore AE',
        'Sunset' => 'Tramonto',
        'n/a' => 'n/d',
      },
    },
   'ExposureModeInManual' => {
      Description => 'Modo esposizione in manuale',
      PrintConv => {
        'Center-weighted average' => 'Media centrale ponderata',
        'Partial metering' => 'Parziale',
      },
    },
   'ExposureModulationType' => 'Tipo modulazione esposizione',
   'ExposureProgram' => {
      Description => 'Programma esposizione',
      PrintConv => {
        'Action (High speed)' => 'Azione (alta velocità)',
        'Aperture Priority' => 'Priorità diaframma',
        'Aperture-priority AE' => 'Priorità diaframma',
        'Bulb' => 'Bulbo',
        'Creative (Slow speed)' => 'Creativo (bassa velocità)',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Not Defined' => 'Non definito',
        'Portrait' => 'Verticale',
        'Posterization' => 'Posterizzazione',
        'Posterization B/W' => 'Posterizzazione B&N',
        'Program' => 'Programma',
        'Program AE' => 'Programma AE',
        'Retro Photo' => 'Foto retrò',
        'Shutter Priority' => 'Priorità otturatore',
        'Shutter speed priority AE' => 'Priorità otturatore AE',
        'Sunset' => 'Tramonto',
        'Sweep Panorama' => 'Panoramica ad arco',
      },
    },
   'ExposureSequence' => 'Sequenza esposizione',
   'ExposureStatus' => 'Stato esposizione',
   'ExposureTime' => 'Tempo esposizione',
   'ExposureTime2' => 'Tempo esposizione 2',
   'ExposureTimeInMicroSec' => 'Tempo esposizione in microsecondi',
   'ExposureTimeInMilliSec' => 'Tempo esposizione in millisecondi',
   'ExposureTuning' => 'Sintonizzazione esposizione',
   'ExposureUnknown' => 'Esposizione sconosciuta',
   'ExposureValue' => 'Valore esposizione',
   'ExtendedWBDetect' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Extender' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'ExtenderStatus' => {
      PrintConv => {
        'Not attached' => 'Non applicabile',
        'Removed' => 'Rimosso',
      },
    },
   'ExtensionPersistence' => {
      PrintConv => {
        'Potentially Invalidated By Modification' => 'Potenzialmente invalidato per modifica',
      },
    },
   'ExternalFlash' => {
      Description => 'Flash esterno',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ExternalFlashBounce' => {
      PrintConv => {
        'Yes' => 'Sì',
        'n/a' => 'n/d',
      },
    },
   'ExternalFlashExposureComp' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ExternalFlashFirmware' => {
      PrintConv => {
        '1.01 (SB-800 or Metz 58 AF-1)' => '1.01 (SB-800 o Metz 58 AF-1)',
        'n/a' => 'n/d',
      },
    },
   'ExternalFlashFlags' => 'Flags Flash Esterno',
   'ExternalFlashMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ExtraFlags' => {
      PrintConv => {
        '(none)' => '(nessuno)',
        'Fastest Algorithm' => 'Algoritmo più veloce',
      },
    },
   'ExtraSamples' => {
      Description => 'Campioni extra',
      PrintConv => {
        'Associated Alpha' => 'Associati alla trasparenza',
        'Unassociated Alpha' => 'Non associati alla trasparenza',
        'Unspecified' => 'Non specificati',
      },
    },
   'EyeStartAF' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FEMicroadjustment' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'FNumber' => 'Numero F',
   'Face1Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face2Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face3Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face4Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face5Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face6Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face7Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'Face8Category' => {
      PrintConv => {
        'Family' => 'Famiglia',
      },
    },
   'FaceDetection' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Orizzontale (normale)',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
      },
    },
   'FacesDetected' => {
      Description => 'Facce rimosse',
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FacesRecognized' => 'Facce riconosciute',
   'FamilyName' => 'Nome famiglia',
   'FastSeek' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'FaxNumber' => 'Numero di fax',
   'FaxProfile' => {
      Description => 'Profilo fax',
      PrintConv => {
        'Extended B&W lossless, F' => 'Bianco e nero esteso senza perdita, F',
        'Lossless JBIG B&W, J' => 'Senza perdita JBIG bianco e nero, J',
        'Lossless color and grayscale, L' => 'Senza perdita a colori e scala di grigi, L',
        'Lossy color and grayscale, C' => 'Con perdita a colori e scala di grigi, C',
        'Minimal B&W lossless, S' => 'Bianco e nero minimale con perdita, S',
        'Mixed raster content, M' => 'Contenuto raster misto, M',
        'Multi Profiles' => 'Profili multipli',
        'Profile T' => 'Profilo T',
        'Unknown' => 'Sconosciuto',
      },
    },
   'FaxRecvParams' => 'Parametri ricezione fax',
   'FaxRecvTime' => 'Ora ricezione fax',
   'FaxSubAddress' => 'Sotto-indirizzo fax',
   'FileAttributes' => {
      PrintConv => {
        'Encrypted' => 'Crittografato',
        'Encrypted?' => 'Crittografato?',
        'Normal' => 'Normale',
        'Not indexed' => 'Non indicizzato',
        'Read-only' => 'Sola lettura',
        'System' => 'Sistema',
      },
    },
   'FileDescription' => 'Descrizione file',
   'FileDescriptors' => 'Descrittori file',
   'FileFlags' => {
      Description => 'Attributi file',
      PrintConv => {
        'Info inferred' => 'Info desunte',
        'Private build' => 'Compilazione privata',
        'Special build' => 'Compilazione speciale',
      },
    },
   'FileFlagsMask' => 'Maschera attributi file',
   'FileFormat' => {
      Description => 'Formato file',
      PrintConv => {
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Versione Ritzaus Bureau NITF (RBNITF DTD)',
      },
    },
   'FileFunctionFlags' => {
      PrintConv => {
        'Fragmented' => 'Frammentato',
      },
    },
   'FileID' => 'ID file',
   'FileIndex' => 'Indice file',
   'FileIndex2' => 'Indice file 2',
   'FileLength' => 'Dimensioni file',
   'FileModifyDate' => 'Data aggiornamento',
   'FileName' => 'Nome file',
   'FileNameLength' => 'Lunghezza nome file',
   'FileNumber' => 'Numero file',
   'FileNumberMemory' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FileNumberSequence' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FileOS' => 'OS del file',
   'FileOwner' => 'Proprietario del file',
   'FilePermissions' => 'Permessi file',
   'FileProfileVersion' => 'Versione profilo file',
   'FileSecurityReport' => 'Rapporto sicurezza file',
   'FileSequence' => 'Sequenza file',
   'FileSetID' => 'ID gruppo file',
   'FileSize' => 'Dimensione file',
   'FileSizeBytes' => 'Dimensione file in byte',
   'FileSource' => {
      Description => 'Origine file',
      PrintConv => {
        'Digital Camera' => 'Fotocamera digitale',
        'Film Scanner' => 'Scanner per pellicola',
        'Reflection Print Scanner' => 'Scanner a riflessione',
        'Sigma Digital Camera' => 'Fotocamera digitale Sigma',
      },
    },
   'FileSubtype' => 'Sottotipo file',
   'FileType' => 'Tipo di file',
   'FileURL' => 'URL file',
   'FileVersion' => 'Versione file',
   'FileVersionNumber' => 'Numero versione file',
   'Filename' => 'Nome file',
   'Files' => 'File',
   'FillFlashAutoReduction' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'FillOrder' => {
      Description => 'Ordine riempimento',
      PrintConv => {
        'Normal' => 'Normale',
        'Reversed' => 'Invertito',
      },
    },
   'FilmMode' => {
      PrintConv => {
        'Nostalgic' => 'Nostalgico',
        'Standard (B&W)' => 'Standard (B&N)',
        'Standard (color)' => 'Standard (colori)',
        'n/a' => 'n/d',
      },
    },
   'FilterEffect' => {
      Description => 'Effetto Filtro',
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Off' => 'Spento',
        'Orange' => 'Arancio',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
        'n/a' => 'n/d',
      },
    },
   'FilterEffectFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FilterEffectLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FilterEffectMonochrome' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Orange' => 'Arancio',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
        'n/a' => 'n/d',
      },
    },
   'FilterEffectNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FilterEffectPortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FilterEffectStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FilterEffectUnknown' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Red' => 'Rosso',
        'n/a' => 'n/d',
      },
    },
   'FilterEffectUserDef1' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Red' => 'Rosso',
        'n/a' => 'n/d',
      },
    },
   'FilterEffectUserDef2' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Red' => 'Rosso',
        'n/a' => 'n/d',
      },
    },
   'FilterEffectUserDef3' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Red' => 'Rosso',
        'n/a' => 'n/d',
      },
    },
   'FinalFrameBlocks' => 'Blocchi frame finali',
   'FinderDisplayDuringExposure' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FineSharpness' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Off' => 'Spento',
      },
    },
   'FirmwareDate' => 'Data firmware',
   'FirmwareID' => 'ID firmware',
   'FirmwareName' => 'Nome firmware',
   'FirmwareRevision' => 'Revisione firmware',
   'FirmwareRevision2' => 'Revisione 2 firmware',
   'FirmwareVersion' => 'Versione firmware',
   'FirstChar' => 'Primo carattere',
   'FirstObject' => 'Primo oggetto',
   'FirstObjectID' => 'ID primo oggetto',
   'FixtureIdentifier' => 'Identificatore d\'installazione',
   'Flags' => {
      PrintConv => {
        'FileName' => 'Nome file',
        'Text' => 'Testo',
      },
    },
   'Flash' => {
      PrintConv => {
        'Auto, Did not fire' => 'Auto, flash non emesso',
        'Auto, Did not fire, Red-eye reduction' => 'Auto, flash non emesso, riduzione occhi rossi',
        'Auto, Fired' => 'Auto, flash emesso',
        'Auto, Fired, Red-eye reduction' => 'Auto, flash emesso, riduzione occhi rossi',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Auto, flash emesso, riduzione occhi rossi, luce di ritorno rilevata',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Auto, flash emesso, riduzione occhi rossi, luce di ritorno non rilevata',
        'Auto, Fired, Return detected' => 'Auto, flash emesso, luce di ritorno rilevata',
        'Auto, Fired, Return not detected' => 'Auto, flash emesso, luce di ritorno non rilevata',
        'Did not fire' => 'Flash non emesso',
        'Fired' => 'Emesso',
        'Fired, Red-eye reduction' => 'Emesso, riduzione occhi rossi',
        'Fired, Red-eye reduction, Return detected' => 'Emesso, riduzione occhi rossi, ritorno rilevato',
        'Fired, Red-eye reduction, Return not detected' => 'Emesso, riduzione occhi rossi, ritorno non rilevato',
        'Fired, Return detected' => 'Emesso, ritorno rilevato',
        'Fired, Return not detected' => 'Emesso, ritorno non rilevato',
        'Flash Fired' => 'Flash emesso',
        'No Flash' => 'Flash non emesso',
        'No flash function' => 'Nessuna funzione flash',
        'Off' => 'Flash non emesso, modalità flash forzata',
        'Off, Did not fire' => 'Flash non emesso, modalità flash forzata',
        'Off, Did not fire, Return not detected' => 'Off, Non emesso. Luce di ritorno non rilevata',
        'Off, No flash function' => 'Off, Nessuna funzione flash',
        'Off, Red-eye reduction' => 'Disattivato, riduzione occhi rossi',
        'On' => 'Flash emesso, modalità flash forzata',
        'On, Did not fire' => 'Attivato, non emesso',
        'On, Fired' => 'Attivato, emesso',
        'On, Red-eye reduction' => 'Attivato, riiduzione occhi rossi',
        'On, Red-eye reduction, Return detected' => 'Attivato, riiduzione occhi rossi, ritorno rilevato',
        'On, Red-eye reduction, Return not detected' => 'Attivato, riiduzione occhi rossi, ritorno non rilevato',
        'On, Return detected' => 'Attivato, ritorno rilevato',
        'On, Return not detected' => 'Attivato, ritorno non rilevato',
      },
    },
   'FlashBits' => {
      PrintConv => {
        'External' => 'Esterno',
        'Manual' => 'Manuale',
      },
    },
   'FlashButtonFunction' => {
      PrintConv => {
        'ISO speed' => 'Velocità ISO',
      },
    },
   'FlashColorFilter' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Red' => 'Rosso',
      },
    },
   'FlashCommanderMode' => {
      Description => 'Modo Commander',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlashCompensation' => 'Compensazione flash',
   'FlashControl' => 'Controllo flash',
   'FlashControlBuilt-in' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'FlashControlMode' => {
      Description => 'Modo Controllo Flash',
      PrintConv => {
        'Auto Aperture' => 'Diaframma automatico',
        'Manual' => 'Manuale',
        'Off' => 'Spento',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'FlashDevice' => {
      PrintConv => {
        'External' => 'Esterno',
        'None' => 'Nessuno',
      },
    },
   'FlashEnergy' => 'Energia del flash',
   'FlashExposureBracketValue' => 'Valore Esposizione a Forcella Flash',
   'FlashExposureComp' => 'Compensazione Esposizione Flash',
   'FlashExposureIndicator' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'FlashExposureIndicatorLast' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'FlashExposureIndicatorNext' => {
      PrintConv => {
        'Not Indicated' => 'Non indicato',
      },
    },
   'FlashExposureLock' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlashFired' => {
      Description => 'Flash emesso',
      PrintConv => {
        'External' => 'Esterno',
        'Yes' => 'Sì',
      },
    },
   'FlashFiring' => 'Emissione flash',
   'FlashFirmwareVersion' => 'Versione firmware flash',
   'FlashFocalLength' => 'Lunghezza focale flash',
   'FlashFunction' => {
      Description => 'Funzione flash',
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FlashGNDistance' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'FlashGroupAControlMode' => {
      Description => 'Gruppo A Modo Controllo Flash',
      PrintConv => {
        'Auto Aperture' => 'Diaframma automatico',
        'Manual' => 'Manuale',
        'Off' => 'Spento',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'FlashGroupBControlMode' => {
      Description => 'Gruppo B Modo Controllo Flash',
      PrintConv => {
        'Auto Aperture' => 'Diaframma automatico',
        'Manual' => 'Manuale',
        'Off' => 'Spento',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'FlashGroupCControlMode' => {
      Description => 'Gruppo C Modo Controllo Flash',
      PrintConv => {
        'Manual' => 'Manuale',
        'Off' => 'Spento',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'FlashInfoVersion' => 'Info Versione Flash',
   'FlashIntensity' => {
      PrintConv => {
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Strong' => 'Forte',
      },
    },
   'FlashLevel' => {
      Description => 'Livello flash',
      PrintConv => {
        'Low' => 'Basso',
        'Normal' => 'Normale',
      },
    },
   'FlashMeteringMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlashMode' => {
      Description => 'Modo flash',
      PrintConv => {
        'Did Not Fire' => 'Non emesso',
        'Fired, Commander Mode' => 'Emesso, Modalità Commander',
        'Fired, External' => 'Emesso Esterno',
        'Fired, Manual' => 'Emesso, Manuale',
        'Fired, TTL Mode' => 'Emesso, Modalità TTL',
        'Flash Off' => 'Flash spento',
        'Normal' => 'Normale',
        'Not Ready' => 'Non pronto',
        'Off' => 'Spento',
        'Off, Did not fire' => 'Spendo, non emesso',
        'Off?' => 'Spento?',
        'Red eye' => 'Occhi rossi',
        'Red-Eye' => 'Occhi rossi',
        'Red-Eye?' => 'Occhi rossi?',
        'Red-eye' => 'Occhi rossi',
        'Red-eye Reduction' => 'Riduzione occhi rossi',
        'Red-eye reduction' => 'Riduzione occhi rossi',
        'Unknown' => 'Sconosciuta',
      },
    },
   'FlashModel' => {
      Description => 'Modello flash',
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'FlashOptions' => {
      Description => 'Opzioni flash',
      PrintConv => {
        'Normal' => 'Normale',
        'Red-eye reduction' => 'Riduzione occhi rossi',
      },
    },
   'FlashOptions2' => {
      Description => 'Opzioni 2 flash',
      PrintConv => {
        'Normal' => 'Normale',
        'Red-eye reduction' => 'Riduzione occhi rossi',
      },
    },
   'FlashRemoteControl' => {
      Description => 'Telecomando flash',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlashReturn' => 'Ritorno flash',
   'FlashSerialNumber' => 'Numero di serie del flash',
   'FlashSetting' => 'Impostazione flash',
   'FlashSource' => {
      Description => 'Fonte flash',
      PrintConv => {
        'External' => 'Esterno',
        'None' => 'Nessuno',
      },
    },
   'FlashStatus' => {
      Description => 'Stato flash',
      PrintConv => {
        'Off' => 'Spento',
        'Off (1)' => 'Spento (1)',
      },
    },
   'FlashSyncSpeed' => {
      PrintConv => {
        '1/250 s (auto FP)' => '1/250 s (piano focale auto)',
        '1/320 s (auto FP)' => '1/320 s (piano focale auto)',
      },
    },
   'FlashSyncSpeedAv' => {
      PrintConv => {
        '1/200 Fixed' => '1/200 fisso',
        '1/200-1/60 Auto' => '1/200-1/60 auto',
        '1/250 Fixed' => '1/250 fisso',
        '1/250-1/60 Auto' => '1/250-1/60 auto',
        '1/300 Fixed' => '1/300 fisso',
        '1/300-1/60 Auto' => '1/300-1/60 auto',
      },
    },
   'FlashType' => {
      Description => 'Tipo di flash',
      PrintConv => {
        'External' => 'Esterno',
        'None' => 'Nessuno',
        'Off' => 'Spento',
      },
    },
   'FlashVersion' => 'Versione del flash',
   'FlashWarning' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlashpixVersion' => 'Versione Flashpix',
   'FlickerReduce' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'FlipHorizontal' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'FocalLength' => 'Lunghezza focale',
   'FocalLength35efl' => 'Lunghezza focale',
   'FocalLength35mm' => 'Lunghezza focale 35mm',
   'FocalLengthIn35mmFormat' => 'Lunghezza focale in formato 35mm',
   'FocalPlaneDiagonal' => 'Diagonale piano focale',
   'FocalPlaneResolutionUnit' => {
      Description => 'Unità risoluzione piano focale',
      PrintConv => {
        'None' => 'Nessuno',
        'inches' => 'pollici',
      },
    },
   'FocalPlaneXResolution' => 'Risoluzione X del piano focale',
   'FocalPlaneXSize' => 'Dimensione X del piano focale',
   'FocalPlaneXUnknown' => 'X del piano focale sconisciuta',
   'FocalPlaneYResolution' => 'Risoluzione Y del piano focale',
   'FocalPlaneYSize' => 'Dimensione Y del piano focale',
   'FocalPlaneYUnknown' => 'Y del piano focale sconisciuta',
   'FocalType' => {
      PrintConv => {
        'Fixed' => 'Fisso',
      },
    },
   'Focus' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FocusContinuous' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FocusDisplayAIServoAndMF' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'FocusDistance' => 'Distanza fuoco',
   'FocusMode' => {
      Description => 'Modalità messa a fuoco',
      PrintConv => {
        'Manual' => 'Manuale',
        'Normal' => 'Normale',
        'n/a' => 'n/d',
      },
    },
   'FocusMode2' => {
      Description => 'Modalità messa a fuoco 2',
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FocusModeSetting' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FocusModeSwitch' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'FocusPos' => 'Posizione fuoco',
   'FocusPosition' => 'Posizione fuoco',
   'FocusRange' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'Normal' => 'Normale',
        'Not Known' => 'Sconosciuto',
        'Very Close' => 'Molto vicino',
      },
    },
   'FocusStatus' => {
      PrintConv => {
        'Failed' => 'Fallito',
        'Not confirmed' => 'Non confermato',
      },
    },
   'FocusTrackingLockOn' => {
      PrintConv => {
        '1 (Short)' => '1 (Breve)',
        '1 Short' => '1 breve',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Short' => 'Breve',
      },
    },
   'FontFamily' => {
      Description => 'Tipo di carattere',
      PrintConv => {
        'Swiss' => 'Svizzera',
      },
    },
   'FontFileName' => 'Nome file carattere',
   'FontName' => 'Nome carattere',
   'FontSize' => 'Dimensioni carattere',
   'FontSubfamily' => 'Sottotipo di carattere',
   'FontSubfamilyID' => 'ID sottotipo di carattere',
   'FontType' => 'Tipo di carattere',
   'FontVersion' => 'Versione carattere',
   'FontWeight' => 'Peso carattere',
   'Fonts' => 'Caratteri',
   'For' => 'Per',
   'Format' => 'Formato',
   'ForwardMatrix1' => 'Matrice predittiva 1',
   'ForwardMatrix2' => 'Matrice predittiva 2',
   'FrameRate' => 'Frequenza fotogramma',
   'FrameSize' => 'Dimensioni fotogrammi',
   'FreeByteCounts' => 'Byte disponibili',
   'FreeOffsets' => 'Offset disponibili',
   'FujiFlashMode' => {
      PrintConv => {
        'External' => 'Esterno',
        'Off' => 'Spento',
        'Red-eye reduction' => 'Riduzione occhi rossi',
      },
    },
   'FuncButton' => {
      PrintConv => {
        'Flash Off' => 'Flash spento',
        'None' => 'Nessuno',
        'Preview' => 'Anteprima',
        'Virtual Horizon' => 'Orizzonte virtuale',
      },
    },
   'FuncButtonPlusDials' => {
      PrintConv => {
        'Choose Image Area' => 'Seleziona area immagine',
        'None' => 'Nessuno',
      },
    },
   'FunctionButton' => {
      PrintConv => {
        'Flash Off' => 'Flash spento',
      },
    },
   'GDALMetadata' => 'Metadati GDAL',
   'GDALNoData' => 'Nessun dato GDAL',
   'GIFVersion' => 'Versione GIF',
   'GPSAltitude' => 'Altitudine GPS',
   'GPSAltitudeRef' => {
      Description => 'Riferimento altitudine GPS',
      PrintConv => {
        'Above Sea Level' => 'Sul livello del mare',
        'Below Sea Level' => 'Sotto il livello del mare',
      },
    },
   'GPSAreaInformation' => 'Informazioni area GPS',
   'GPSDOP' => 'Precisione misurazione GPS',
   'GPSDateStamp' => 'Data GPS',
   'GPSDateTime' => 'Ora GPS (orologio atomico)',
   'GPSDestBearing' => 'Direzione destinazione GPS',
   'GPSDestBearingRef' => {
      Description => 'Riferimento direzione destinazione GPS',
      PrintConv => {
        'Magnetic North' => 'Nord magnetico',
        'True North' => 'Nord geografico',
      },
    },
   'GPSDestDistance' => 'Distanza destinazione GPS',
   'GPSDestDistanceRef' => {
      Description => 'Riferimento distanza destinazione GPS',
      PrintConv => {
        'Kilometers' => 'Chilometri',
        'Miles' => 'Miglia',
        'Nautical Miles' => 'Miglia nautiche',
      },
    },
   'GPSDestLatitude' => 'Latitudine destinazione GPS',
   'GPSDestLatitudeRef' => {
      Description => 'Riferimento latitudine destinazione GPS',
      PrintConv => {
        'North' => 'Nord',
        'South' => 'Sud',
      },
    },
   'GPSDestLongitude' => 'Longitudine destinazione GPS',
   'GPSDestLongitudeRef' => {
      Description => 'Riferimento per longitudine di destinazione',
      PrintConv => {
        'East' => 'Est',
        'West' => 'Ovest',
      },
    },
   'GPSDifferential' => {
      Description => 'Correzione differenziale GPS',
      PrintConv => {
        'Differential Corrected' => 'Correzione differenziale applicata',
        'No Correction' => 'Misurazione senza correzione differenziale',
      },
    },
   'GPSHPositioningError' => 'Errore posizionamento orizzontale GPS',
   'GPSImgDirection' => 'Direzione di immagine',
   'GPSImgDirectionRef' => {
      Description => 'Riferimento per direzione di immagine',
      PrintConv => {
        'Magnetic North' => 'Nord magnetico',
        'True North' => 'Nord geografico',
      },
    },
   'GPSInfo' => 'Puntatore GPS Info IFD',
   'GPSLatitude' => 'Latitudine',
   'GPSLatitudeRef' => {
      Description => 'Latitudine Nord o Sud',
      PrintConv => {
        'North' => 'Latitudine Nord',
        'South' => 'Latitudine Sud',
      },
    },
   'GPSLongitude' => 'Longitudine',
   'GPSLongitudeRef' => {
      Description => 'Longitudine Est o Ovest',
      PrintConv => {
        'East' => 'Longitudine Est',
        'West' => 'Longitudine Ovest',
      },
    },
   'GPSMapDatum' => 'Dati di rilevamento geodetico utilizzati',
   'GPSMeasureMode' => {
      Description => 'Modalità misurazione GPS',
      PrintConv => {
        '2-Dimensional Measurement' => 'Misurazione 2D',
        '3-Dimensional Measurement' => 'Misurazione tridimensionale',
      },
    },
   'GPSProcessingMethod' => 'Nome metodo di elaborazione GPS',
   'GPSSatellites' => 'Satelliti GPS usati per la misurazione',
   'GPSSpeed' => 'Velocità ricevitore GPS',
   'GPSSpeedRef' => {
      Description => 'Unità velocità',
      PrintConv => {
        'km/h' => 'Chilometri all\'ora',
        'knots' => 'Nodi',
        'mph' => 'Miglie all\'ora',
      },
    },
   'GPSStatus' => {
      Description => 'Stato ricevitore GPS',
      PrintConv => {
        'Measurement Active' => 'Misurazione in corso',
        'Measurement Void' => 'Interoperatività misurazione',
      },
    },
   'GPSTimeStamp' => 'Ora GPS (orologio atomico)',
   'GPSTrack' => 'Direzione di movimento',
   'GPSTrackRef' => {
      Description => 'Riferimento per direzione di movimento',
      PrintConv => {
        'Magnetic North' => 'Direzione magnetica',
        'True North' => 'Direzione effettiva',
      },
    },
   'GPSVersionID' => 'Versione tag GPS',
   'GTModelType' => {
      PrintConv => {
        'Projected' => 'Proiettato',
      },
    },
   'GainControl' => {
      Description => 'Controllo di Guadagno',
      PrintConv => {
        'High gain down' => 'Diminuire il guadagno alto',
        'High gain up' => 'Aumentare il guadagno alto',
        'Low gain down' => 'Diminuire il guadagno basso',
        'Low gain up' => 'Aumentare il guadagno basso',
        'None' => 'Nessuno',
      },
    },
   'GammaCompensatedValue' => 'Valore di Compensazione Gamma',
   'Gapless' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'Genre' => {
      Description => 'Genere',
      PrintConv => {
        'Acapella' => 'A cappella',
        'Acid Jazz' => 'Acid jazz',
        'Acid Punk' => 'Acid punk',
        'Acoustic' => 'Acustica',
        'Ambient' => 'Ambientale',
        'Avantgarde' => 'Avanguardia',
        'Ballad' => 'Ballata',
        'Bass' => 'Basso',
        'Big Band' => 'Big band',
        'Black Metal' => 'Black metal',
        'Booty Bass' => 'Basso booty',
        'Celtic' => 'Celtica',
        'Chamber Music' => 'Musica da camera',
        'Chorus' => 'Coro',
        'Christian Gangsta' => 'Gangsta cristiano',
        'Christian Rap' => 'Rap cristiano',
        'Christian Rock' => 'Rock cristiano',
        'Classic Rock' => 'Rock classico',
        'Classical' => 'Classica',
        'Club-House' => 'Club-house',
        'Comedy' => 'Commedia',
        'Contemporary C' => 'C contemporaneo',
        'Country' => 'Nazione',
        'Cover' => 'Copertina',
        'Dance Hall' => 'Dance hall',
        'Death Metal' => 'Death metal',
        'Drum & Bass' => 'Batteria e basso',
        'Drum Solo' => 'Batteria solo',
        'Duet' => 'Duetto',
        'Easy Listening' => 'Easy listening',
        'Electronic' => 'Elettronica',
        'Ethnic' => 'Etnica',
        'Euro-House' => 'Euro-house',
        'Euro-Techno' => 'Euro-techno',
        'Fast Fusion' => 'Fast fusion',
        'Folk-Rock' => 'Folk-rock',
        'Game' => 'Gioco',
        'Gothic' => 'Gotica',
        'Gothic Rock' => 'Rock gotico',
        'Hard Rock' => 'Hard rock',
        'Heavy Metal' => 'Heavy metal',
        'Hip-Hop' => 'Hip-hop',
        'Humour' => 'Umoristica',
        'Instrumental' => 'Strumentale',
        'Instrumental Pop' => 'Pop strumentale',
        'Instrumental Rock' => 'Rock strumentale',
        'Jazz+Funk' => 'Jazz+funk',
        'Latin' => 'Latina',
        'Lo-Fi' => 'Lo-fi',
        'Meditative' => 'Medidativa',
        'National Folk' => 'Folklore nazionale',
        'Native American' => 'Nativi americani',
        'New Age' => 'New age',
        'New Wave' => 'New wave',
        'Noise' => 'Rumore',
        'None' => 'Nessuno',
        'Oldies' => 'Vecchi successi',
        'Other' => 'Altro',
        'Polsk Punk' => 'Punk polacco',
        'Pop-Folk' => 'Pop-folk',
        'Pop/Funk' => 'Pop/funk',
        'Porn Groove' => 'Porn groove',
        'Power Ballad' => 'Power ballad',
        'Progressive Rock' => 'Progressive rock',
        'Psychadelic' => 'Psichedelica',
        'Psychedelic Rock' => 'Rock psichedelica',
        'Punk Rock' => 'Punk rock',
        'Retro' => 'Retrò',
        'Rhythmic Soul' => 'Rhythmic soul',
        'Rock & Roll' => 'Rock & roll',
        'Satire' => 'Satira',
        'Slow Jam' => 'Slow jam',
        'Slow Rock' => 'Slow rock',
        'Sound Clip' => 'Clip sonora',
        'Soundtrack' => 'Colonna sonora',
        'Southern Rock' => 'Southern rock',
        'Speech' => 'Parlato',
        'Symphonic Rock' => 'Rock sinfonico',
        'Symphony' => 'Sinfonia',
        'Techno-Industrial' => 'Techno-industrial',
        'Terror' => 'Terrore',
        'Thrash Metal' => 'Thrash metal',
        'Tribal' => 'Tribale',
        'Trip-Hop' => 'Trip-hop',
      },
    },
   'GenreID' => 'ID genere',
   'GeoTiffAsciiParams' => 'Parametri Geo Tiff Ascii',
   'GeoTiffDirectory' => 'Cartella Geo Tiff',
   'GeoTiffDoubleParams' => 'Parametri Geo Tiff Double',
   'GeogGeodeticDatum' => {
      PrintConv => {
        'Lisbon' => 'Lisbona',
        'Stockholm 1938' => 'Stoccolma 1938',
      },
    },
   'GeogPrimeMeridian' => {
      PrintConv => {
        'Lisbon' => 'Lisbona',
        'Rome' => 'Roma',
        'Stockholm' => 'Stoccolma',
      },
    },
   'GeographicType' => {
      PrintConv => {
        'Greek' => 'Greco',
        'Lisbon' => 'Lisbona',
        'Stockholm 1938' => 'Stoccolma 1938',
      },
    },
   'Gradation' => {
      Description => 'Ottimizzaz.',
      PrintConv => {
        'Normal' => 'Normale',
        'n/a' => 'n/d',
      },
    },
   'GrayResponseCurve' => 'Curva di risposta Grigio',
   'GrayResponseUnit' => {
      Description => 'Unità di risposta Grigio',
      PrintConv => {
        '1e-05' => '1e-005',
        '1e-06' => '1e-006',
      },
    },
   'GrayTRC' => 'Curva Riproduzione Tono Grigio',
   'GreenMatrixColumn' => 'Colonna della Matrice Verde',
   'GreenTRC' => 'Curva Riproduziozne Tono Verde',
   'GridDisplay' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Grouping' => 'Raggruppamento',
   'HCUsage' => {
      Description => 'Uso HC',
      PrintConv => {
        'Line Art' => 'Line art',
      },
    },
   'HDR' => {
      Description => 'HDR auto',
      PrintConv => {
        'Off' => 'Disattivata',
      },
    },
   'HDRSetting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'HalftoneHints' => 'Suggerimenti mezzi toni',
   'HandlerType' => {
      PrintConv => {
        'Text' => 'Testo',
      },
    },
   'Headline' => 'Intestazione',
   'HeightResolution' => 'Risoluzione altezza',
   'HighISONoiseReduction' => {
      Description => 'Riduzione Rumore High ISO',
      PrintConv => {
        'Auto' => 'Automatico',
        'High' => 'Hi',
        'Low' => 'Leggero',
        'Normal' => 'Normale',
        'Off' => 'Disattivata',
        'Strong' => 'Forte',
        'n/a' => 'n/d',
      },
    },
   'HighSpeedSync' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'HighlightTonePriority' => {
      PrintConv => {
        'Enable' => 'Abilita',
        'Off' => 'Spento',
      },
    },
   'HometownCity' => {
      PrintConv => {
        'Jerusalem' => 'Gerusalemme',
        'Lisbon' => 'Lisbona',
        'London' => 'Londra',
        'Milan' => 'Milano',
        'Prague' => 'Praga',
        'Rome' => 'Roma',
        'Stockholm' => 'Stoccolma',
        'Tehran' => 'Teheran',
      },
    },
   'HometownDST' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'HostComputer' => 'Computer ospite',
   'HotKey' => {
      PrintConv => {
        '(none)' => '(nessuno)',
      },
    },
   'Hue' => {
      Description => 'Tonalità',
      PrintConv => {
        'None' => 'Nessuno',
        'Normal' => 'Normale',
      },
    },
   'HueAdjustment' => 'Regolazione Hue',
   'ICCProfile' => 'Profili ICC',
   'IDCCreativeStyle' => {
      PrintConv => {
        'Camera Setting' => 'Impostazioni fotocamera',
        'Landscape' => 'Orizzontale',
        'Light' => 'Chiara',
        'Real' => 'Reale',
        'Sepia' => 'Seppia',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
      },
    },
   'INGRReserved' => 'Riseervato a INGR',
   'IPTC-NAA' => 'Metadati IPTC-NAA',
   'IPTCBitsPerSample' => 'Numero di Bits per Campione',
   'IPTCImageHeight' => 'Numero di linee',
   'IPTCImageRotation' => {
      Description => 'Rotazione Immagine',
      PrintConv => {
        '0' => 'Nessuna rotazione',
        '180' => 'Rotazione di 180 gradi',
        '270' => 'Rotazione di 270 gradi',
        '90' => 'Rotazione di 90 gradi',
      },
    },
   'IPTCImageWidth' => 'Pixels per linea',
   'IPTCPictureNumber' => 'Numero Immagine',
   'ISO' => 'Sensibilità ISO',
   'ISOAuto' => {
      Description => 'ISO auto',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ISOAutoMinSpeed' => {
      Description => 'Parametri ISO auto',
      PrintConv => {
        'Auto Fast' => 'Auto veloce',
      },
    },
   'ISODisplay' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ISOExpansion' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ISOExpansion2' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ISOInfo' => 'Info ISO',
   'ISOSelection' => 'Selezione ISO',
   'ISOSensitivityStep' => 'Passo sensibilità ISO',
   'ISOSetting' => {
      Description => 'Impostazione ISO',
      PrintConv => {
        '100 (Low)' => '100 (basso)',
        'Manual' => 'Manuale',
        'n/a' => 'n/d',
      },
    },
   'ISOSpeed' => 'Velocità ISO',
   'ISOSpeedExpansion' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ISOSpeedIncrements' => {
      PrintConv => {
        '1 Stop' => '1 stop',
        '1/3 Stop' => '1/3 stop',
      },
    },
   'ISOSpeedLatitudeyyy' => 'Velocità ISO - Latitudine yyy',
   'ISOSpeedLatitudezzz' => 'Velocità ISO - Latitudine zzz',
   'ISOSpeedRange' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ISOValue' => 'Valore ISO',
   'IT8Header' => 'Intestazione IT8',
   'Illumination' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Image::ExifTool::AIFF::Comment' => 'Commento AIFF',
   'Image::ExifTool::EXE::CHM' => 'EXE CHM',
   'Image::ExifTool::EXE::ELF' => 'EXE ELF',
   'Image::ExifTool::EXE::MachO' => 'EXE MachO',
   'Image::ExifTool::EXE::Main' => 'EXE',
   'Image::ExifTool::EXE::PEF' => 'EXE PEF',
   'Image::ExifTool::EXE::PEString' => 'EXE PEString',
   'Image::ExifTool::EXE::PEVersion' => 'EXE PEVersion',
   'Image::ExifTool::Exif::Main' => 'Exif',
   'Image::ExifTool::FLAC::Main' => 'FLAC',
   'Image::ExifTool::Flash::Main' => 'Flash',
   'Image::ExifTool::Flash::Video' => 'Video flash',
   'Image::ExifTool::FlashPix::Main' => 'FlashPix',
   'Image::ExifTool::Font::Main' => 'Carattere',
   'Image::ExifTool::Font::Name' => 'Nome carattere',
   'Image::ExifTool::Font::PFM' => 'Carattere PFM',
   'Image::ExifTool::GPS::Main' => 'GPS',
   'Image::ExifTool::ID3::Private' => 'ID3 privato',
   'Image::ExifTool::ID3::v1' => 'ID3 v1',
   'Image::ExifTool::ID3::v1_Enh' => 'ID3 v1_Enh',
   'Image::ExifTool::ID3::v2_2' => 'ID3 v2_2',
   'Image::ExifTool::ID3::v2_3' => 'ID3 v2_3',
   'Image::ExifTool::ID3::v2_4' => 'ID3 v2_4',
   'Image::ExifTool::ITC::Main' => 'ITC',
   'Image::ExifTool::JFIF::Main' => 'JFIF',
   'Image::ExifTool::Kodak::DcMD' => 'Kodak DcMD',
   'Image::ExifTool::Kodak::Free' => 'Kodak Free',
   'Image::ExifTool::Kodak::KDC_IFD' => 'KDC_IFD Kodak',
   'Image::ExifTool::Kodak::Main' => 'Kodak',
   'Image::ExifTool::Microsoft::Stitch' => 'Microsoft Stitch',
   'Image::ExifTool::Olympus::AVI' => 'AVI Olympus',
   'Image::ExifTool::Olympus::Main' => 'Olympus',
   'Image::ExifTool::PSP::Creator' => 'Creatore PSP',
   'Image::ExifTool::PSP::Image' => 'Immagine PSP',
   'Image::ExifTool::PSP::Main' => 'PSP',
   'Image::ExifTool::PanasonicRaw::Main' => 'PanasonicRaw',
   'Image::ExifTool::PostScript::Main' => 'PostScript',
   'Image::ExifTool::PrintIM::Main' => 'PrintIM',
   'Image::ExifTool::Qualcomm::Main' => 'Qualcomm',
   'Image::ExifTool::QuickTime::Main' => 'QuickTime',
   'Image::ExifTool::RIFF::Main' => 'RIFF',
   'Image::ExifTool::Radiance::Main' => 'Radianza',
   'Image::ExifTool::Rawzor::Main' => 'Rawzor',
   'Image::ExifTool::Real::AudioV3' => 'Real AudioV3',
   'Image::ExifTool::Real::AudioV4' => 'Real AudioV4',
   'Image::ExifTool::Real::AudioV5' => 'Real AudioV5',
   'Image::ExifTool::Ricoh::AVI' => 'AVI Ricoh',
   'Image::ExifTool::Ricoh::FaceInfo' => 'FaceInfo Ricoh',
   'Image::ExifTool::Ricoh::FirmwareInfo' => 'Info firmware Ricoh',
   'Image::ExifTool::Ricoh::ImageInfo' => 'Info immagine Ricoh',
   'Image::ExifTool::Ricoh::Main' => 'Ricoh',
   'Image::ExifTool::Ricoh::RMETA' => 'RMETA Ricoh',
   'Image::ExifTool::Ricoh::SerialInfo' => 'Info seriale Ricoh',
   'Image::ExifTool::Ricoh::Subdir' => 'Sotto-cartella Ricoh',
   'Image::ExifTool::Ricoh::Text' => 'Testo Ricoh',
   'Image::ExifTool::Samsung::INFO' => 'Samsung INFO',
   'Image::ExifTool::Samsung::MP4' => 'Samsung MP4',
   'Image::ExifTool::Samsung::PictureWizard' => 'Samsung PictureWizard',
   'Image::ExifTool::Sanyo::Main' => 'Sanyo',
   'Image::ExifTool::Sigma::Main' => 'Sigma',
   'ImageAdjustment' => 'Regolazione Immagine',
   'ImageAlterationConstraints' => {
      PrintConv => {
        'No Merging' => 'Nessuna fusione',
      },
    },
   'ImageAuthentication' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ImageByteCount' => 'Numero di byte dell\'immagine',
   'ImageColorIndicator' => {
      Description => 'Indicazione colore dell\'immagine',
      PrintConv => {
        'Specified Image Color' => 'Colore immagine specificato',
        'Unspecified Image Color' => 'Colore immagine non specificato',
      },
    },
   'ImageColorValue' => 'Valore colore dell\'immagine',
   'ImageCount' => 'Conteggio Immagini',
   'ImageDataDiscard' => {
      Description => 'Dati scartati dell\'immagine',
      PrintConv => {
        'Flexbits Discarded' => 'Flexbit scartati',
        'Full Resolution' => 'Risoluzione completa',
        'HighPass Frequency Data Discarded' => 'Dati in frequenza passa-alto scartati',
        'Highpass and LowPass Frequency Data Discarded' => 'Dati in frequenza passa-alto e passa-basso scartati',
      },
    },
   'ImageDataSize' => 'Dimensione Dati Immagine',
   'ImageDepth' => 'Profondità Immagine',
   'ImageDescription' => 'Descrizione Immagine',
   'ImageDustOff' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ImageEditing' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Resized' => 'Ridimensionato',
      },
    },
   'ImageHeight' => 'Altezza immagine',
   'ImageHistory' => 'Cronologia immagine',
   'ImageID' => 'ID Immagine',
   'ImageLayer' => 'Livello immagine',
   'ImageNumber' => 'Numero immagine',
   'ImageOffset' => 'Offset immagine',
   'ImageOptimization' => 'Ottimizzazione Immagine',
   'ImageOrientation' => {
      Description => 'Orientamento Immagine',
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Portrait' => 'Verticale',
        'Square' => 'Quadrata',
      },
    },
   'ImageQuality' => {
      Description => 'Qualità Immagine',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'ImageReview' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ImageRotated' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ImageRotation' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'ImageSize' => 'Dimensioni immagini',
   'ImageSourceData' => 'Dati origine immagine',
   'ImageStabilization' => {
      Description => 'Stabilizzazione Immagine',
      PrintConv => {
        'Off' => 'Spento',
        'Off (1)' => 'Spento (1)',
        'Off (2)' => 'Spento (2)',
        'n/a' => 'n/d',
      },
    },
   'ImageStabilizationSetting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ImageStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Sunset' => 'Tramonto',
        'Vivid' => 'Vivace',
      },
    },
   'ImageTone' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Portrait' => 'Verticale',
        'Radiant' => 'Radiante',
      },
    },
   'ImageType' => {
      Description => 'Tipo immagine',
      PrintConv => {
        'Page' => 'Pagina',
        'Preview' => 'Anteprima',
      },
    },
   'ImageUniqueID' => 'ID unico immagine',
   'ImageVersion' => 'Versione immagine',
   'ImageWidth' => 'Larghezza immagine',
   'Index' => 'Indice',
   'Indexable' => {
      PrintConv => {
        'False' => 'Falso',
      },
    },
   'Indexed' => {
      Description => 'Indicizzato',
      PrintConv => {
        'Indexed' => 'Indicizzato',
        'Not indexed' => 'Non indicizzato',
      },
    },
   'InfraredIlluminator' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'InitialDisplayEffect' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'InitialKey' => 'Chiave iniziale',
   'InitializedDataSize' => 'Dimensione dati inizializzati',
   'InkNames' => 'Nomi inchiostri',
   'InkSet' => {
      Description => 'Gruppo inchiostri',
      PrintConv => {
        'Not CMYK' => 'Non CMYK',
      },
    },
   'Instructions' => 'Istruzioni',
   'IntellectualGenre' => 'Genere Intellettuale',
   'IntelligentAuto' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'IntelligentContrast' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'IntelligentD-Range' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'IntelligentExposure' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'IntelligentResolution' => {
      PrintConv => {
        'Extended' => 'Esteso',
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'IntensityStereo' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Interlace' => {
      Description => 'Interlacciamento',
      PrintConv => {
        'Noninterlaced' => 'Non interlacciato',
        'Progressive' => 'Progressivo',
      },
    },
   'InterleavedField' => {
      PrintConv => {
        'Even' => 'Pari',
      },
    },
   'InternalFlash' => {
      PrintConv => {
        'Fired' => 'Flash emesso',
        'Manual' => 'Manuale',
        'No' => 'Flash non emesso',
        'Off' => 'Spento',
        'Repeating Flash' => 'Ripetizione flash',
      },
    },
   'InternalName' => 'Nome interno',
   'InternetRadioStationName' => 'Nome stazione radio internet',
   'InternetRadioStationOwner' => 'Proprietario stazione radio internet',
   'InternetRadioStationURL' => 'URL stazione radio internet',
   'InteropIndex' => {
      Description => 'Identificazione interoperatività',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03 - file opzioni DCF (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98 - file base DCF (sRGB)',
        'THM - DCF thumbnail file' => 'File miniatura THM - DCF',
      },
    },
   'InteropOffset' => 'Etichetta interoperatività',
   'InteropVersion' => 'Versione interoperatività',
   'InterpretedBy' => 'Interpetrato da',
   'InvolvedPeople' => 'Persone coinvolte',
   'Italic' => 'Corsivo',
   'JFIFVersion' => 'Versione JFIF',
   'JPEGACTables' => 'Tabelle JPEGAC',
   'JPEGDCTables' => 'Tabelle JPEGDC',
   'JPEGLosslessPredictors' => 'Predittori JPEG senza perdita',
   'JPEGPointTransforms' => 'Trasformazioni puntiali JPEG',
   'JPEGProc' => {
      Description => 'Proc JPEG',
      PrintConv => {
        'Baseline' => 'Linea di base',
        'Lossless' => 'Senza perdita',
      },
    },
   'JPEGQTables' => 'Tabelle JPEGQ',
   'JPEGQuality' => {
      Description => 'Qualità',
      PrintConv => {
        'Extra Fine' => 'Extra fine',
        'Standard' => 'Normale',
        'n/a' => 'n/d',
      },
    },
   'JPEGRestartInterval' => 'Intervallo riavvio JPEG',
   'JPEGTables' => 'Tabelle JPEG',
   'JobID' => 'ID processo',
   'JobName' => 'Nome processo',
   'JobRef' => 'Rif processo',
   'JobStatus' => 'Stato processo',
   'JpgFromRaw' => 'JPG da raw',
   'JpgFromRawLength' => 'Lunghezza JPG da raw',
   'JpgFromRawStart' => 'Inizio JPG da raw',
   'Keyword' => 'Parola chiave',
   'KeywordInfo' => 'Info parola chiave',
   'Keywords' => 'Parole chiave',
   'Kinds' => 'Tipi',
   'LCDIllumination' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LCDIlluminationDuringBulb' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LCHEditor' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Language' => 'Lingua',
   'LanguageCode' => {
      Description => 'Codice lingua',
      PrintConv => {
        'Albanian' => 'Albanese',
        'Arabic' => 'Arabo',
        'Armenian' => 'Armeno',
        'Azeri' => 'Azero',
        'Basque' => 'Basco',
        'Belarusian' => 'Bielorusso',
        'Breton' => 'Bretone',
        'Bulgarian' => 'Bulgaro',
        'Catalan' => 'Catalano',
        'Chinese (Simplified)' => 'Cinese (semplificato)',
        'Chinese (Traditional)' => 'Cinese (tradizionale)',
        'Croato-Serbian (Latin)' => 'Serbo-croato (alfabeto latino)',
        'Czech' => 'Ceco',
        'Danish' => 'Danese',
        'Dutch' => 'Olandese',
        'Dutch (Belgian)' => 'Olandese (Belgio)',
        'English (Australian)' => 'Inglese (Australia)',
        'English (British)' => 'Inglese (Gran Bretagna)',
        'English (Canadian)' => 'Inglese (Canada)',
        'English (U.S.)' => 'Inglese (U.S.A.)',
        'Estonian' => 'Estone',
        'Farsi' => 'Persiano',
        'Finnish' => 'Finlandese',
        'French' => 'Francese',
        'French (Belgian)' => 'Francese (Belgio)',
        'French (Canadian)' => 'Francese (Canada)',
        'French (Swiss)' => 'Francese (Svizzera)',
        'Gaelic' => 'Gaelico',
        'Galician' => 'Galiziano',
        'Georgian' => 'Georgiano',
        'German' => 'Tedesco',
        'German (Austrian)' => 'Tedesco (Austria)',
        'German (Swiss)' => 'Tedesco (Svizzera)',
        'Greek' => 'Greco',
        'Hebrew' => 'Ebraico',
        'Hungarian' => 'Ungherese',
        'Icelandic' => 'Islandese',
        'Indonesian' => 'Indonesiano',
        'Invariant' => 'Invariante',
        'Italian' => 'Italiano',
        'Italian (Swiss)' => 'Italiano (Svizzera)',
        'Japanese' => 'Giapponese',
        'Kazak' => 'Kazako',
        'Korean' => 'Coreano',
        'Kyrgyz' => 'Kirghizo',
        'Latvian' => 'Lettone',
        'Lithuanian' => 'Lituano',
        'Macedonian' => 'Macedone',
        'Mongolian' => 'Mongolo',
        'Nepali' => 'Nepalese',
        'Neutral' => 'Neutrale',
        'Neutral 2' => 'Neutrale 2',
        'Norwegian (Bokml)' => 'Norvegese (Bokmål)',
        'Norwegian (Nynorsk)' => 'Norvegese (Nynorsk)',
        'Polish' => 'Polacco',
        'Portuguese' => 'Portogese',
        'Portuguese (Brazilian)' => 'Portoghese (Brasile)',
        'Process default' => 'Predefinita del processo',
        'Rhaeto-Romanic' => 'Reto-romanico',
        'Romanian' => 'Rumeno',
        'Russian' => 'Russo',
        'Sanskrit' => 'Sanscrito',
        'Serbo-Croatian (Cyrillic)' => 'Serbo-croato (cirillico)',
        'Slovak' => 'Slovacco',
        'Slovenian' => 'Sloveno',
        'Spanish (Castilian)' => 'Spagnolo (Castiglia)',
        'Spanish (Mexican)' => 'Spagnolo (Messico)',
        'Spanish (Modern)' => 'Spagnolo moderno',
        'Swedish' => 'Svedese',
        'Syriac' => 'Siriaco',
        'Turkish' => 'Turco',
        'Ukrainian' => 'Ucraino',
        'Uzbek' => 'Usbeco',
        'Vietnamese' => 'Vietnamita',
        'Welsh' => 'Gallese',
      },
    },
   'LanguageIdentifier' => 'Identificativo lingua',
   'LanguageList' => 'Lista lingue',
   'LanguageName' => 'Nome lingua',
   'LateralChromaticAberration' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'Layout' => {
      PrintConv => {
        'Tiles' => 'Tasselli',
      },
    },
   'LegalCopyright' => 'Copyright legali',
   'LegalTrademarks' => 'Marchi legali',
   'Length' => 'Durata',
   'Lens' => 'Obiettivo',
   'Lens35efl' => 'Obiettivo',
   'LensDataVersion' => 'Versione Dati Obiettivo',
   'LensFStops' => 'F-Stops Obiettivo',
   'LensID' => {
      Description => 'Obiettivo Utilizzato',
      PrintConv => {
        'Ricoh Lens A16 24-85mm F3.5-5.5' => 'Obiettivo Ricoh A16 24-85mm F3.5-5.5',
        'Ricoh Lens P10 28-300mm F3.5-5.6 VC' => 'Obiettivo Ricoh P10 28-300mm F3.5-5.6 VC',
        'Ricoh Lens S10 24-70mm F2.5-4.4 VC' => 'Obiettivo Ricoh S10 24-70mm F2.5-4.4 VC',
      },
    },
   'LensIDNumber' => 'Numero ID Obiettivo',
   'LensInfo' => 'Informazioni obiettivo',
   'LensMake' => 'Marca obiettivo',
   'LensModel' => 'Modello obiettivo',
   'LensSerialNumber' => 'Numero di serie obiettivo',
   'LensShutterLock' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LensSpec' => 'Obiettivo',
   'LensType' => {
      Description => 'Tipo obiettivo',
      PrintConv => {
        '02 Standard Zoom 5-15mm F2.8-4.5' => '02 Zoom standard 5-15mm F2.8-4.5',
        '04 Toy Lens Wide 6.3mm F7.1' => '04 Grandangolare toy 6.3mm F7.1',
        '05 Toy Lens Telephoto 18mm F8' => '05 Teleobiettivo toy 18mm F8',
        '1.4x Teleconverter' => 'Moltiplicatore di focale 1.4x',
        'None' => 'Nessuno',
        'Sigma 100-300mm F4 EX (APO (D) or D IF)' => 'Sigma 100-300mm F4 EX (APO (D) o D IF)',
      },
    },
   'LevelGaugePitch' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LevelGaugeRoll' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LevelOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Orizzontale',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
      },
    },
   'License' => 'Licenza',
   'LicenseID' => 'ID licenza',
   'LicenseType' => {
      PrintConv => {
        'Public Domain' => 'Pubblico dominio',
        'Unknown' => 'Sconosciuto',
      },
    },
   'LightCondition' => 'Condizione luce',
   'LightSource' => {
      Description => 'Sorgente di luce',
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Cool White Fluorescent' => 'Fluorescente a luce calda',
        'Day White Fluorescent' => 'Fluorescente a luce del giorno bianca',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Evening Sunlight' => 'Luce solare serale',
        'Fine Weather' => 'Bel tempo',
        'Fluorescent' => 'Fluorescente',
        'ISO Studio Tungsten' => 'Tungsteno studio ISO',
        'Other' => 'Altra Sorgente di Luce',
        'Shade' => 'Ombrato',
        'Standard Light A' => 'Luce standard A',
        'Standard Light B' => 'Luce standard B',
        'Standard Light C' => 'Luce standard C',
        'Tungsten (Incandescent)' => 'Tungsteno (luce incandescente)',
        'Unknown' => 'Sconosciuto',
        'Warm White Fluorescent' => 'Luce fluorescente bianca calda',
        'White Fluorescent' => 'Fluorescente bianca',
      },
    },
   'LightSourceSpecial' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LightingMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Lightness' => 'Luce',
   'Line' => 'Linea',
   'LineOrder' => {
      PrintConv => {
        'Random Y' => 'Casuale Y',
      },
    },
   'LinearResponseLimit' => 'Limite risposta lineare',
   'LinearizationTable' => 'Tabella di linearizzazione',
   'Lines' => 'Linee',
   'LinkAEToAFPoint' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LinkerVersion' => 'Versione linker',
   'Lit' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'LiveViewAFMethod' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'LiveViewAFSetting' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'LiveViewFocusMode' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'n/a' => 'n/d',
      },
    },
   'LiveViewMetering' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'LiveViewShooting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LocalDeltaType' => {
      PrintConv => {
        'Absolute' => 'Assoluto',
      },
    },
   'LocalizedCameraModel' => 'Modello fotocamera localizzato',
   'Location' => 'Località',
   'LocationKind' => 'Tipo località',
   'LocationName' => 'Nome località',
   'LocationNote' => 'Note località',
   'LongDescription' => 'Descrizione estesa',
   'LongExposureNoiseReduction' => {
      Description => 'Espososizione lunga riduzione rumore',
      PrintConv => {
        'Off' => 'Spento',
        'Off (65535)' => 'Spento (65535)',
        'On' => 'Attivata',
        'n/a' => 'n/d',
      },
    },
   'LongExposureNoiseReduction2' => {
      Description => 'Espososizione lunga riduzione rumore 2',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'LongText' => 'Testo lungo',
   'LoopStyle' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'LuminanceNoiseReduction' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'Lyricist' => 'Paroliere',
   'Lyrics' => 'Testo',
   'MCUVersion' => 'Versione MCU',
   'MDColorTable' => 'Tabella colori MD',
   'MDFileTag' => 'Tag file MD',
   'MDFileUnits' => 'Unità file MD',
   'MDLabName' => 'Nome lab MD',
   'MDPrepDate' => 'Data prep MD',
   'MDPrepTime' => 'Ora prep MD',
   'MDSampleInfo' => 'Info campione MD',
   'MDScalePixel' => 'Scala pixel MD',
   'MIEVersion' => 'Versione MIE',
   'MIMEType' => 'Tipo mime',
   'MIMETypeOfEncapsulatedDocument' => 'Tipo mime del documento incapsulato',
   'MSDocumentText' => 'Testo documento MS',
   'MSDocumentTextPosition' => 'Posizione testo documento MS',
   'MSPropertySetStorage' => 'Gruppo di memoria proprietà MS',
   'MSStereo' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MachineType' => {
      Description => 'Tipo macchina',
      PrintConv => {
        'ARM little endian' => 'ARM little-endian',
        'Alpha AXP (old)' => 'Alpha AXP (precedente)',
        'EFI Byte Code' => 'Byte code EFI',
        'Intel 386 or later, and compatibles' => 'Intel 386 o successivo e compatibili',
        'MIPS little endian (R4000)' => 'MIPS little-endian (R4000)',
        'MIPS little endian WCI v2' => 'MIPS little-endian WCI v2',
        'MIPS with FPU' => 'MIPS con FPU',
        'MIPS16 with FPU' => 'MIPS16 con FPU',
        'Mitsubishi M32R little endian' => 'Mitsubishi M32R little-endian',
        'Motorola 68000 series' => 'Serie Motorola 68000',
        'PowerPC little endian' => 'PowerPC little-endian',
        'PowerPC with floating point support' => 'PowerPC con supporto numeri in in virgola mobile',
        'clr pure MSIL' => 'clr con MSIL puro',
      },
    },
   'Macro' => {
      PrintConv => {
        'Manual' => 'Manuale',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'MacroLED' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MacroMode' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Off' => 'Spento',
      },
    },
   'MagicFilter' => {
      PrintConv => {
        'Fish Eye' => 'Fish-eye',
        'Fragmented' => 'Frammentato',
        'Gentle Sepia' => 'Seppia leggero',
        'Off' => 'Spento',
        'Reflection' => 'Riflessione',
      },
    },
   'MainDialExposureComp' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Make' => 'Costruttore',
   'MakeAndModel' => 'Marca e Modello',
   'MakerNote' => 'Dati DNG Privati',
   'MakerNoteCanon' => 'Note marca Canon',
   'MakerNoteCasio' => 'Note marca Casio',
   'MakerNoteCasio2' => 'Note marca Casio 2',
   'MakerNoteFujiFilm' => 'Note marca Fuji Film',
   'MakerNoteGE' => 'Note marca GE',
   'MakerNoteGE2' => 'Note marca GE2',
   'MakerNoteHP' => 'Note marca HP',
   'MakerNoteHP2' => 'Note marca HP2',
   'MakerNoteHP4' => 'Note marca HP4',
   'MakerNoteHP6' => 'Note marca HP6',
   'MakerNoteHasselblad' => 'Note marca Hasselblad',
   'MakerNoteISL' => 'Note marca ISL',
   'MakerNoteJVC' => 'Note marca JVC',
   'MakerNoteJVCText' => 'Note marca JVC - Testo',
   'MakerNoteKodak10' => 'Note marca Kodak 10',
   'MakerNoteKodak1a' => 'Note marca Kodak 1a',
   'MakerNoteKodak1b' => 'Note marca Kodak 1b',
   'MakerNoteKodak2' => 'Note marca Kodak 2',
   'MakerNoteKodak3' => 'Note marca Kodak 3',
   'MakerNoteKodak4' => 'Note marca Kodak 4',
   'MakerNoteKodak5' => 'Note marca Kodak 5',
   'MakerNoteKodak6a' => 'Note marca Kodak 6a',
   'MakerNoteKodak6b' => 'Note marca Kodak 6b',
   'MakerNoteKodak7' => 'Note marca Kodak 7',
   'MakerNoteKodak8a' => 'Note marca Kodak 8a',
   'MakerNoteKodak8b' => 'Note marca Kodak 8b',
   'MakerNoteKodak9' => 'Note marca Kodak 9',
   'MakerNoteKodakUnknown' => 'Note marca Kodak sconosciuta',
   'MakerNoteKyocera' => 'Note marca Kyocera',
   'MakerNoteLeica' => 'Note marca Leica',
   'MakerNoteLeica2' => 'Note marca Leica 2',
   'MakerNoteLeica3' => 'Note marca Leica 3',
   'MakerNoteLeica4' => 'Note marca Leica 4',
   'MakerNoteLeica5' => 'Note marca Leica 5',
   'MakerNoteLeica6' => 'Note marca Leica 6',
   'MakerNoteMinolta' => 'Note marca Minolta',
   'MakerNoteMinolta2' => 'Note marca Minolta 2',
   'MakerNoteMinolta3' => 'Note marca Minolta 3',
   'MakerNoteNikon' => 'Note marca Nikon',
   'MakerNoteNikon2' => 'Note marca Nikon 2',
   'MakerNoteNikon3' => 'Note marca Nikon 3',
   'MakerNoteOlympus' => 'Note marca Olympus',
   'MakerNoteOlympus2' => 'Note marca Olympus 2',
   'MakerNotePanasonic' => 'Note marca Panasonic',
   'MakerNotePanasonic2' => 'Note marca Panasonic 2',
   'MakerNotePentax' => 'Note marca Pentax',
   'MakerNotePentax2' => 'Note marca Pentax 2',
   'MakerNotePentax3' => 'Note marca Pentax 3',
   'MakerNotePentax4' => 'Note marca Pentax 4',
   'MakerNotePentax5' => 'Note marca Pentax 5',
   'MakerNotePentax6' => 'Note marca Pentax 6',
   'MakerNotePhaseOne' => 'Note marca Phase One',
   'MakerNoteReconyx' => 'Note marca Reconyx',
   'MakerNoteRicoh' => 'Note marca Ricoh',
   'MakerNoteRicohText' => 'Note marca Ricoh - Testo',
   'MakerNoteSafety' => {
      Description => 'Note marca Safety',
      PrintConv => {
        'Safe' => 'Sicuro',
        'Unsafe' => 'Non sicuro',
      },
    },
   'MakerNoteSamsung1a' => 'Note marca Samsung 1a',
   'MakerNoteSamsung1b' => 'Note marca Samsung 1b',
   'MakerNoteSamsung2' => 'Note marca Samsung 2',
   'MakerNoteSanyo' => 'Note marca Sanyo',
   'MakerNoteSanyoC4' => 'Note marca Sanyo C4',
   'MakerNoteSanyoPatch' => 'Note marca Sanyo Patch',
   'MakerNoteSigma' => 'Note marca Sigma',
   'MakerNoteSony' => 'Note marca Sony',
   'MakerNoteSony2' => 'Note marca Sony 2',
   'MakerNoteSony3' => 'Note marca Sony 3',
   'MakerNoteSony4' => 'Note marca Sony 4',
   'MakerNoteSonyEricsson' => 'Note marca Sony Ericsson',
   'MakerNoteSonySRF' => 'Note marca Sony SRF',
   'MakerNoteUnknown' => 'Note marca sconosciuta',
   'MakerNoteUnknownText' => 'Note marca sconosciuta - Testo',
   'MakerNoteVersion' => 'Note Versione Costruttore',
   'MakerNotes' => 'Note produttore',
   'ManualFlashOutput' => {
      PrintConv => {
        'Low' => 'Basso',
        'n/a' => 'n/d',
      },
    },
   'ManualFocusDistance' => 'Messa a Fuoco Manuale',
   'MarkerID' => 'ID marker',
   'MaskedAreas' => 'Aree mascherate',
   'MasterDocumentID' => 'ID Documento Principale',
   'Matteing' => 'Opacizzazione',
   'MaxAperture' => 'Massima apertura delle lenti',
   'MaxApertureAtMaxFocal' => 'Diaframma massimo alla focale massima',
   'MaxApertureAtMinFocal' => 'Diaframma massimo alla focale minima',
   'MaxApertureValue' => 'Diaframma massimo obiettivo',
   'MaxFocalLength' => 'Lunghezza focale massima',
   'MaxSampleValue' => 'Massimo valore campioni',
   'MeasurementGeometry' => {
      PrintConv => {
        '0/45 or 45/0' => '0/45 o 45/0',
        '0/d or d/0' => '0/d o d/0',
      },
    },
   'MediaBlackPoint' => 'Media Punto Nero',
   'MediaType' => {
      PrintConv => {
        'Normal (Music)' => 'Normale (musica)',
      },
    },
   'MediaWhitePoint' => 'Media Punto Bianco',
   'MenuButtonDisplayPosition' => {
      PrintConv => {
        'Top' => 'Alto',
      },
    },
   'MenuButtonReturn' => {
      PrintConv => {
        'Top' => 'Alto',
      },
    },
   'MeteringMode' => {
      Description => 'Modalità misurazione',
      PrintConv => {
        'Average' => 'Media',
        'Center-weighted Average' => 'Media centrale ponderata',
        'Center-weighted average' => 'Media centrale ponderata',
        'Multi-segment' => 'Multi-zona',
        'Multi-spot' => 'Multi-punto',
        'Other' => 'Altro',
        'Partial' => 'Parziale',
        'Spot' => 'Punto',
        'Unknown' => 'Sconosciuto',
      },
    },
   'MeteringMode2' => {
      PrintConv => {
        'Center-weighted average' => 'Media centrale ponderata',
        'Multi-segment' => 'Multi zona',
      },
    },
   'MeteringMode3' => {
      PrintConv => {
        'Center-weighted average' => 'Media centrale ponderata',
        'Multi-segment' => 'Multi zona',
      },
    },
   'MeteringTime' => {
      PrintConv => {
        'No Limit' => 'Nessun limite',
      },
    },
   'MinFocalLength' => 'Lunghezza focale minima',
   'MinSampleValue' => 'Minimo valore campioni',
   'MiniatureFilterOrientation' => {
      PrintConv => {
        'Horizontal' => 'Orizzontale',
        'Vertical' => 'Verticale',
      },
    },
   'MinoltaQuality' => {
      Description => 'Qualità',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'MirrorLockup' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ModeNumber' => 'Numero modo',
   'Model' => 'Nome modello fotocamera',
   'Model2' => 'Modello 2',
   'ModelReleaseStatus' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Not Applicable' => 'Non applicabile',
      },
    },
   'ModelingFlash' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ModifiedPictureStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'None' => 'Nessuno',
        'Portrait' => 'Verticale',
      },
    },
   'ModifiedSaturation' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ModifiedSharpnessFreq' => {
      PrintConv => {
        'Low' => 'Basso',
        'n/a' => 'n/d',
      },
    },
   'ModifiedToneCurve' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'ModifiedWhiteBalance' => {
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Fluorescent' => 'Fluorescente',
        'Shade' => 'Ombrato',
        'Tungsten' => 'Tungsteno (luce incandescente)',
      },
    },
   'ModifyDate' => 'Data modifica',
   'MoireFilter' => {
      Description => 'Filtro moire',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MonitorDisplayOff' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'MonochromeFilterEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Orange' => 'Arancio',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'MonochromeLinear' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'MonochromeToning' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'MonochromeToningEffect' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
      },
    },
   'MultiBurstMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultiControllerWhileMetering' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultiExposure' => 'Dati Esposizione Multipla',
   'MultiExposureAutoGain' => {
      Description => 'Guadagno Automatico Esposizione Multipla',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultiExposureMode' => {
      Description => 'Modo Esposizione Multipla',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultiExposureShots' => 'Scatti Esposizione Multipla',
   'MultiExposureVersion' => 'Versione Dati Esposizione Multipla',
   'MultiFrameNoiseReduction' => {
      Description => 'Riduz. distur. su più fotogr.',
      PrintConv => {
        'None' => 'Nessuno',
        'Off' => 'Spento',
        'On' => 'Attivata',
        'n/a' => 'n/d',
      },
    },
   'MultiFunctionLock' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultiProfiles' => {
      Description => 'Profili multipli',
      PrintConv => {
        'JBIG2 Profile M' => 'JBIG2 TIFF FX',
        'N Layer Profile M' => 'Livello N profilo M',
        'Profile C' => 'Profilo C',
        'Profile F' => 'Profilo F',
        'Profile J' => 'Profilo J',
        'Profile L' => 'Profilo L',
        'Profile M' => 'Profilo M',
        'Profile S' => 'Profilo S',
        'Profile T' => 'Profilo T',
        'Resolution/Image Width' => 'Risoluzione/larghezza immagine',
        'Shared Data' => 'Dati condivisi',
      },
    },
   'MultiSelectorLiveView' => {
      PrintConv => {
        'Not Used' => 'Non usato',
        'Reset' => 'Reimposta',
        'Zoom On/Off' => 'Zoon sì/no',
      },
    },
   'MultiSelectorPlaybackMode' => {
      PrintConv => {
        'Choose Folder' => 'Seleziona cartella',
        'Thumbnail On/Off' => 'Thumbnail sì/no',
        'Zoom On/Off' => 'Zoon sì/no',
      },
    },
   'MultiSelectorShootMode' => {
      PrintConv => {
        'Not Used' => 'Non usato',
      },
    },
   'MultipleExposureMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MultipleExposureSet' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MusicCDIdentifier' => 'Identificativo CD musicale',
   'MusicianCredits' => 'Info sul musicista',
   'Mute' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'MyColorMode' => {
      PrintConv => {
        'Off' => 'Spento',
        'Sepia' => 'Seppia',
        'Vivid' => 'Vivace',
        'Vivid Blue' => 'Blu vivace',
        'Vivid Green' => 'Verde vivace',
      },
    },
   'NDFilter' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'NEFCompression' => {
      Description => 'Compressione RAW',
      PrintConv => {
        'Lossless' => 'Senza perdita',
        'Uncompressed' => 'Non compresso',
      },
    },
   'Name' => 'Nome',
   'NamedColor2' => 'Colore Chiamato 2',
   'NativeDisplayInfo' => 'Info Display Nativo',
   'NeutralDensityFilter' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'NikonCaptureData' => 'Dati Nikon Capture',
   'NikonCaptureVersion' => 'Versione Nikon Capture',
   'NoMemoryCard' => 'Scheda di memoria assente',
   'Noise' => 'Rumore',
   'NoiseFilter' => {
      Description => 'Filtro rumore',
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'NoiseProfile' => 'Profilo rumore',
   'NoiseReduction' => {
      Description => 'Riduzione rumore',
      PrintConv => {
        'Low' => 'Basso',
        'Low (-1)' => 'Basso (-1)',
        'Noise Filter' => 'Filtro rumore',
        'Noise Reduction' => 'Riduzione rumore',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Strong' => 'Forte',
        'n/a' => 'n/d',
      },
    },
   'NoiseReduction2' => {
      Description => 'Riduzione rumore 2',
      PrintConv => {
        'Noise Filter' => 'Filtro rumore',
        'Noise Reduction' => 'Riduzione rumore',
      },
    },
   'NoiseReductionApplied' => 'Riduzione rumore applicata',
   'NoiseReductionIntensity' => 'Intensità riduzione rumore',
   'NoiseReductionMethod' => {
      Description => 'Metodo riduzione rumore',
      PrintConv => {
        'Faster' => 'Più veloce',
      },
    },
   'NoiseReductionMode' => {
      Description => 'Modo riduzione rumore',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'NoiseReductionValue' => 'Valore riduzione rumore',
   'Notes' => 'Note',
   'Now' => 'Adesso',
   'NumChannels' => 'Numero canali',
   'NumSampleFrames' => 'Numero fotogrammi campionamento',
   'NumberOfFocusPoints' => {
      PrintConv => {
        '11 Points' => '11 punti',
      },
    },
   'NumberofInks' => 'Numero di inchiostri',
   'OKButton' => {
      PrintConv => {
        'Not Used' => 'Non usato',
        'Off' => 'Spento',
      },
    },
   'OPIProxy' => {
      Description => 'Proxy OPI',
      PrintConv => {
        'Higher resolution image does not exist' => 'Immagine a risoluzione maggiore non esistente',
        'Higher resolution image exists' => 'Immagine a risoluzione maggiore esistente',
      },
    },
   'OSVersion' => 'Versione OS',
   'Object' => 'Oggetto',
   'ObjectAttributeReference' => 'Genere intellettuale',
   'ObjectCycle' => {
      Description => 'Ciclo oggetto',
      PrintConv => {
        'Both Morning and Evening' => 'Entrambi',
        'Evening' => 'Sera',
        'Morning' => 'Mattino',
      },
    },
   'ObjectFileType' => {
      Description => 'Tipo file oggetto',
      PrintConv => {
        'Core file' => 'File core',
        'Demand paged executable' => 'Eseguibile paginato a richiesta',
        'Dynamic link editor' => 'Editor con collegamenti dinamici',
        'Dynamic link library' => 'Libreria a collegamento dinamico',
        'Dynamically bound bundle' => 'Pacchetto incorporato dinamicamente',
        'Dynamically bound shared library' => 'Libreria condivisa collegata dinamicamente',
        'Executable application' => 'Applicazione eseguibile',
        'Executable file' => 'File eseguibile',
        'Fixed VM shared library' => 'Libreria fissa VM condivisa',
        'Font' => 'Carattere',
        'None' => 'Nessuno',
        'Preloaded executable' => 'Eseguibile precaricato',
        'Relocatable file' => 'File rilocabile',
        'Relocatable object' => 'Oggetto rilocabile',
        'Shared library stub for static linking' => 'Stub libreria condivisa per il collegamenti statici',
        'Shared object file' => 'File con oggetti condivisi',
        'Static library' => 'Libreria statica',
        'Unknown' => 'Sconosciuto',
      },
    },
   'ObjectName' => 'Titolo',
   'ObjectPreviewFileFormat' => {
      PrintConv => {
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Versione Ritzaus Bureau NITF (RBNITF DTD)',
      },
    },
   'ObjectTypeReference' => 'Riferimento Tipo Oggetto',
   'OffsetSchema' => 'Schema Offset',
   'OldSubfileType' => {
      Description => 'Vecchio tipo sotto-file',
      PrintConv => {
        'Full-resolution image' => 'Immagine con risoluzione originale',
        'Reduced-resolution image' => 'Immagine a risoluzione ridotta',
        'Single page of multi-page image' => 'Singola pagina di un\'immagine multi-pagina',
      },
    },
   'OneTouchWB' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'OpEndPic' => 'Fine immagine',
   'OpcodeList1' => 'Lista opcode 1',
   'OpcodeList2' => 'Lista opcode 2',
   'OpcodeList3' => 'Lista opcode 3',
   'OpticalZoomMode' => {
      PrintConv => {
        'Extended' => 'Esteso',
      },
    },
   'OpticalZoomOn' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Opto-ElectricConvFactor' => 'Fattore di conversione optoelettrica',
   'Orientation' => {
      Description => 'Orientamento',
      PrintConv => {
        'Horizontal (normal)' => 'Orizzontale (normale)',
        'Mirror horizontal' => 'Rifletti orizzontalmente',
        'Mirror horizontal and rotate 270 CW' => 'Rifletti orizzontalmente e ruota di 270° in senso orario',
        'Mirror horizontal and rotate 90 CW' => 'Rifletti orizzontalmente e ruota di 90° in senso orario',
        'Mirror vertical' => 'Rifletti verticalmente',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
        'Tiled' => 'Tassellato',
      },
    },
   'Origin' => 'Origine',
   'OriginCode' => 'Codice origine',
   'OriginPlatform' => {
      Description => 'Piattaforma origine',
      PrintConv => {
        'Print' => 'Stampa',
      },
    },
   'OriginalAlbum' => 'Album originale',
   'OriginalAlbumTitle' => 'Titolo album originale',
   'OriginalArtist' => 'Artista originale',
   'OriginalFileName' => 'Nome file originale',
   'OriginalLyricist' => 'Paroliere originale',
   'OriginalMedia' => {
      PrintConv => {
        'False' => 'Falso',
      },
    },
   'OriginalRawFileData' => 'Dati file raw originale',
   'OriginalRawFileDigest' => 'Sommario file raw originale',
   'OriginalRawFileName' => 'Nome file raw originale',
   'OriginalReleaseTime' => 'Ora di rilascio originale',
   'OriginalReleaseYear' => 'Anno di rilascio originale',
   'OriginalTransmissionReference' => 'ID Lavoro',
   'OriginatingProgram' => 'Programma d\'origine',
   'OtherCodecDescription' => 'Descrizione altro codec',
   'OtherCodecName' => 'Nome altro codec',
   'OtherImageLength' => 'Altra lunghezza immagine',
   'OtherImageStart' => 'Altro inizio immagine',
   'OwnerID' => 'ID proprietario',
   'OwnerName' => 'Nome proprietario',
   'PEFVersion' => 'Versione PEF',
   'PEType' => 'Tipo PE',
   'PackingMethod' => {
      PrintConv => {
        'Fast' => 'Veloce',
        'Fastest' => 'Massimamente veloce',
        'Normal' => 'Normale',
      },
    },
   'PageName' => 'Nome pagina',
   'PageNumber' => 'Numero pagina',
   'PanOrientation' => {
      PrintConv => {
        '[unused]' => '[non usato]',
      },
    },
   'PanasonicRawVersion' => 'Versione raw Panasonic',
   'PanasonicTitle' => 'Titolo Panasonic',
   'PanasonicTitle2' => 'Titolo Panasonic 2',
   'PanoramaSize3D' => {
      Description => 'Dimensione panomara 3D',
      PrintConv => {
        'Wide' => 'Ampio',
        'n/a' => 'n/d',
      },
    },
   'PartOfSet' => 'Parte del gruppo',
   'PaymentURL' => 'URL pagamento',
   'PeakValue' => 'Valore di picco',
   'PerformerSortOrder' => 'Ordinamento interprete',
   'PeripheralLighting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PeripheralLightingSetting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PhaseDetectAF' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PhotoEffect' => {
      PrintConv => {
        'Off' => 'Spento',
        'Sepia' => 'Seppia',
        'Vivid' => 'Vivace',
      },
    },
   'PhotoEffects' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PhotoEffectsType' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Sepia' => 'Seppia',
        'Tinted' => 'Tinteggiato',
      },
    },
   'PhotoInfoPlayback' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PhotometricInterpretation' => {
      Description => 'Interpretazione fotometrica',
      PrintConv => {
        'BlackIsZero' => 'Nero è zero',
        'Color Filter Array' => 'Array filtro colore',
        'Linear Raw' => 'Raw lineare',
        'Pixar LogL' => 'Pixar LogLuv',
        'Pixar LogLuv' => 'Pixar LogL',
        'RGB Palette' => 'Tavolozza RGB',
        'Transparency Mask' => 'Maschera trasparenza',
        'WhiteIsZero' => 'Bianco è zero',
      },
    },
   'PhotoshopFormat' => {
      PrintConv => {
        'Progressive' => 'Progressivo',
      },
    },
   'Picture' => 'Immagine',
   'PictureControl' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PictureControlActive' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'PictureControlAdjust' => {
      PrintConv => {
        'Default Settings' => 'Impostazioni Predefinite',
        'Full Control' => 'Controllo completo',
        'Quick Adjust' => 'Regolazione rapida',
      },
    },
   'PictureDescription' => 'Descrizione immagine',
   'PictureEffect' => {
      PrintConv => {
        'Off' => 'Spento',
        'Posterization' => 'Posterizzazione',
        'Posterization B/W' => 'Posterizzazione B&N',
        'Retro Photo' => 'Foto retrò',
      },
    },
   'PictureFinish' => {
      PrintConv => {
        'Evening Scene' => 'Scena serale',
        'Portrait' => 'Verticale',
      },
    },
   'PictureFormat' => 'Formato immagine',
   'PictureMIMEType' => 'MIME type immagine',
   'PictureMode' => {
      PrintConv => {
        '1/2 EV steps' => 'Step 1/2 EV',
        '1/3 EV steps' => 'Step 1/3 EV',
        'Aperture-priority AE' => 'Priorità diaframma',
        'Fireworks' => 'Fuochi artificiali',
        'Fisheye' => 'Fish-eye',
        'Flower' => 'Fiore',
        'Forest' => 'Foresta',
        'Green' => 'Verde',
        'Kids' => 'Bambini',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'No Flash' => 'No flash',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Program (HyP)' => 'Programma (HyP)',
        'Program AE' => 'Programma AE',
        'Purple' => 'Porpora',
        'Quick Macro' => 'Macro veloce',
        'Red' => 'Rosso',
        'Sepia' => 'Seppia',
        'Shutter speed priority AE' => 'Priorità otturatore AE',
        'Sunset' => 'Tramonto',
        'Text' => 'Testo',
        'Vivid' => 'Vivace',
      },
    },
   'PictureMode2' => {
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
        'Manual' => 'Manuale',
        'Program AE' => 'Programma AE',
        'Scene Mode' => 'Modo scena',
        'Shutter Speed Priority' => 'Priorità otturatore',
      },
    },
   'PictureModeBWFilter' => {
      PrintConv => {
        'Green' => 'Verde',
        'Orange' => 'Arancio',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
        'n/a' => 'n/d',
      },
    },
   'PictureModeEffect' => {
      PrintConv => {
        'Low' => 'Basso',
        'n/a' => 'n/d',
      },
    },
   'PictureModeTone' => {
      PrintConv => {
        'Green' => 'Verde',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'None' => 'Nessuno',
        'Portrait' => 'Verticale',
      },
    },
   'PictureType' => {
      Description => 'Tipo immagine',
      PrintConv => {
        '32x32 PNG Icon' => 'Icona PNG 32x32',
        'Artist' => 'Artista',
        'Back Cover' => 'Retrocopertina',
        'Band Logo' => 'Logo band',
        'Bright(ly) Colored Fish' => 'Pesci dai vivaci colori',
        'Capture from Movie or Video' => 'Acquisizione da film o video',
        'Composer' => 'Compositore',
        'Conductor' => 'Direttore d\'orchestra',
        'Front Cover' => 'Copertina',
        'Illustration' => 'Illustrazione',
        'Lead Artist' => 'Artista principale',
        'Leaflet' => 'Volantino',
        'Lyricist' => 'Paroliere',
        'Other' => 'Altro',
        'Other Icon' => 'Altra icona',
        'Performance' => 'Interpretazione',
        'Publisher Logo' => 'Logo editore',
        'Recording Session' => 'Sessione registrazione',
        'Recording Studio or Location' => 'Studio/luogo registrazione',
      },
    },
   'PictureWizardMode' => {
      PrintConv => {
        'Forest' => 'Foresta',
        'Landscape' => 'Orizzontale',
        'Retro' => 'Retrò',
        'Vivid' => 'Vivace',
        'n/a' => 'n/d',
      },
    },
   'PixelFormat' => {
      Description => 'Formato pixel',
      PrintConv => {
        '112-bit 6 Channels Alpha' => '112-bit 6 canali trasparenza',
        '112-bit 7 Channels' => '112-bit 7 canali',
        '128-bit 7 Channels Alpha' => '128-bit 7 canali trasparenza',
        '128-bit 8 Channels' => '128-bit 8 canali',
        '128-bit PRGBA Float' => '128-bit PRGBA virgola mobile',
        '128-bit RGB Float' => '128-bit RGB virgola mobile',
        '128-bit RGBA Fixed Point' => '128-bit RGBA virgola fissa',
        '128-bit RGBA Float' => '128-bit RGBA virgola mobile',
        '144-bit 8 Channels Alpha' => '144-bit 8 canali trasparenza',
        '16-bit Gray' => '16-bit grigio',
        '16-bit Gray Half' => '16-bit grigio metà',
        '24-bit 3 Channels' => '24-bit 3 canali',
        '32-bit 3 Channels Alpha' => '32-bit 3 canali trasparenza',
        '32-bit 4 Channels' => '32-bit 4 canali',
        '32-bit Gray Fixed Point' => '32-bit punto grigio virgola fissa',
        '32-bit Gray Float' => '32-bit punto grigio virgola mobile',
        '40-bit 4 Channels Alpha' => '40-bit 4 canali trasparenza',
        '40-bit 5 Channels' => '40-bit 5 canali',
        '40-bit CMYK Alpha' => '40-bit CMYK trasparenza',
        '48-bit 3 Channels' => '48-bit 3 canali',
        '48-bit 5 Channels Alpha' => '48-bit 5 canali trasparenza',
        '48-bit 6 Channels' => '48-bit 6 canali',
        '48-bit RGB Fixed Point' => '48-bit RGB virgola fissa',
        '48-bit RGB Half' => '48-bit RGB metà',
        '56-bit 6 Channels Alpha' => '56-bit 6 canali trasparenza',
        '56-bit 7 Channels' => '56-bit 7 canali',
        '64-bit 3 Channels Alpha' => '64-bit 3 canali trasparenza',
        '64-bit 4 Channels' => '64-bit 4 canali',
        '64-bit 7 Channels Alpha' => '64-bit 7 canali trasparenza',
        '64-bit 8 Channels' => '64-bit 8 canali',
        '64-bit RGBA Fixed Point' => '64-bit RGBA virgola fissa',
        '64-bit RGBA Half' => '64-bit RGBA mezzi toni',
        '72-bit 8 Channels Alpha' => '72-bit 8 canali trasparenza',
        '8-bit Gray' => '8-bit grigio',
        '80-bit 4 Channels Alpha' => '80-bit 4 canali trasparenza',
        '80-bit 5 Channels' => '80-bit 5 canali',
        '80-bit CMYK Alpha' => '80-bit CMYK trasparenza',
        '96-bit 5 Channels Alpha' => '96-bit 5 canali trasparenza',
        '96-bit 6 Channels' => '96-bit 6 canali',
        '96-bit RGB Fixed Point' => '96-bit RGB virgola fissa',
        'Black & White' => 'Bianco e nero',
      },
    },
   'PixelIntensityRange' => 'Intervallo intensità pixel',
   'PixelMagicJBIGOptions' => 'Opzioni Pixel Magic JBIG',
   'PixelScale' => 'Scala pixel',
   'PixelUnits' => {
      PrintConv => {
        'Unknown' => 'Sconosciuto',
      },
    },
   'PlanarConfiguration' => {
      Description => 'Configurazione planare',
      PrintConv => {
        'Chunky' => 'Spezzettato',
        'Planar' => 'Planare',
      },
    },
   'PlayCounter' => 'Conteggio esecuzioni',
   'PlayGap' => {
      PrintConv => {
        'No Gap' => 'Nessun salto',
      },
    },
   'PlaylistDelay' => 'Attesa playlist',
   'PortraitRefiner' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Position' => 'Posizione',
   'PostScriptFontName' => 'Nome carattere PostScript',
   'PostprocessingFunction' => 'Funzione post-processamento',
   'PowerSource' => 'Fonte alimentazione',
   'PowerUpTime' => 'Ora accensione',
   'Predictor' => {
      Description => 'Predittore',
      PrintConv => {
        'Horizontal differencing' => 'Differenziazione orizzontale',
        'None' => 'Nessuno',
      },
    },
   'PredictorColumns' => 'Colonne predittore',
   'PredictorConstants' => 'Costanti predittore',
   'PredictorRows' => 'Righe predittore',
   'PreferredFamily' => 'Famiglia preferita',
   'PreferredSubfamily' => 'Sotto-famiglia preferita',
   'PreferredVolume' => 'Volume preferito',
   'Prefs' => 'Preferenze',
   'PresetWhiteBalance' => {
      PrintConv => {
        'Camera Setting' => 'Impostazioni fotocamera',
        'Fluorescent' => 'Fluorescente',
        'Shade' => 'Ombrato',
      },
    },
   'PrevFileName' => 'Nome file precedente',
   'PrevSize' => 'Dimensione precedente',
   'PrevUID' => 'UID precedente',
   'Preview' => 'Anteprima',
   'Preview0' => 'Anteprima 0 ',
   'Preview1' => 'Anteprima 1 ',
   'Preview2' => 'Anteprima 2',
   'PreviewApplicationName' => 'Nome applicazione anteprima ',
   'PreviewApplicationVersion' => 'Versione applicazione anteprima ',
   'PreviewButton' => {
      Description => 'Pulsante anteprima',
      PrintConv => {
        'Flash Off' => 'Flash spento',
        'None' => 'Nessuno',
        'Preview' => 'Anteprima',
        'Virtual Horizon' => 'Orizzonte virtuale',
      },
    },
   'PreviewButtonPlusDials' => {
      PrintConv => {
        'Choose Image Area' => 'Seleziona area immagine',
        'None' => 'Nessuno',
      },
    },
   'PreviewColorSpace' => {
      Description => 'Spazio colore anteprima',
      PrintConv => {
        'Gray Gamma 2.2' => 'Gamma grigio 2.2',
        'Unknown' => 'Sconosciuto',
      },
    },
   'PreviewDate' => 'Data anteprima',
   'PreviewDateTime' => 'Data ora anteprima',
   'PreviewDuration' => 'Durata anteprima',
   'PreviewIFD' => 'Anteprima Puntatore IFD',
   'PreviewImage' => 'Immagine anteprima',
   'PreviewImageBorders' => 'Bordi immagine anteprima',
   'PreviewImageData' => 'Altezza immagine anteprima',
   'PreviewImageHeight' => 'Altezza immagine anteprima',
   'PreviewImageLength' => 'Lunghezza immagine anteprima',
   'PreviewImageName' => 'Nome immagine anteprima',
   'PreviewImageSize' => 'Dimensioni immagine anteprima',
   'PreviewImageStart' => 'Inizio immagine anteprima',
   'PreviewImageType' => 'Tipo immagine anteprima',
   'PreviewImageValid' => {
      Description => 'Immagine anteprima valida',
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'PreviewImageWidth' => 'Larghezza immagine anteprima',
   'PreviewInfo' => 'Info anteprima',
   'PreviewQuality' => {
      Description => 'Qualtià anteprima',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'PreviewSettingsDigest' => 'Sommario impostazioni anteprima',
   'PreviewSettingsName' => 'Nome impostazioni anteprima',
   'PrimaryAFPoint' => {
      PrintConv => {
        '(none)' => '(nessuno)',
        'Bottom' => 'Basso',
        'C6 (Center)' => 'C6 (Centro)',
        'Center' => 'Centro',
        'Far Left' => 'Tutto a sinistra',
        'Far Right' => 'Tutto a destra',
        'Lower-left' => 'Inferiore sinistro',
        'Lower-right' => 'Inferiore destro',
        'Mid-left' => 'Centro/sinistra',
        'Mid-right' => 'Centro/destra',
        'Top' => 'Alto',
        'Upper-left' => 'Superiore sinistro',
        'Upper-right' => 'Superiore destro',
      },
    },
   'PrimaryChromaticities' => 'Cromatismo dei colori primari',
   'PrimaryPlatform' => 'Piattaforma primaria',
   'PrintIM' => 'Stampa Image Matching',
   'PrintIMVersion' => 'Versione PrintIM',
   'PrintPriority' => 'Priorità stampa',
   'PrintQuality' => 'Qualità stampa',
   'PrintScale' => 'Scala stampa',
   'PrinterName' => 'Nome stampante',
   'Priority' => 'Priorità',
   'PrivateBuild' => 'Compilazione privata',
   'ProcessingSoftware' => 'Software di elaborazione',
   'ProducedNotice' => 'Note prodotte',
   'Product' => 'Prodotto',
   'ProductDescription' => 'Descrizione prodotto',
   'ProductID' => 'ID prodotto',
   'ProductName' => 'Nome prodotto',
   'ProductVersion' => 'Versione prodotto',
   'ProductVersionNumber' => 'Numero di versione prodotto',
   'Profession' => 'Professione',
   'Profile' => 'Profilo',
   'ProfileAndLevel' => 'Profilo e livello',
   'ProfileCMMType' => 'Tipo profilo CMM',
   'ProfileCalibrationSig' => 'Segn calibrazione profilo',
   'ProfileClass' => {
      Description => 'Classe profilo',
      PrintConv => {
        'Abstract Profile' => 'Profilo Astratto',
        'ColorSpace Conversion Profile' => 'Profilo Conversione Spazio Colore',
        'DeviceLink Profile' => 'Profilo Dispositivo di Collegamento',
        'Display Device Profile' => 'Profilo Dispositivo Visualizzazione',
        'Input Device Profile' => 'Profilo dispositivo di Input',
        'NamedColor Profile' => 'Profilo Colore Chiamato',
        'Nikon Input Device Profile (NON-STANDARD!)' => 'Profilo Nikon ("nkpf")',
        'Output Device Profile' => 'Profilo Dispositivo Output',
      },
    },
   'ProfileConnectionSpace' => 'Spazio connessione profilo',
   'ProfileCopyright' => 'Copyright profilo',
   'ProfileCreator' => 'Autore profilo',
   'ProfileDateTime' => 'Data/ora profilo',
   'ProfileDescription' => 'Descrizione del Profilo',
   'ProfileDescriptionML' => 'Descrizione profilo multilinguaggio.',
   'ProfileEmbedPolicy' => {
      Description => 'Politica incorporamento profilo',
      PrintConv => {
        'Allow Copying' => 'Permetti la copia',
        'Embed if Used' => 'Incorpora se usato',
        'Never Embed' => 'Non incorporare mai',
        'No Restrictions' => 'Nessuna restrizione',
      },
    },
   'ProfileFileSignature' => 'Firma file profilo',
   'ProfileID' => {
      Description => 'ID profilo',
      PrintConv => {
        'Not Specified' => 'Non specificato',
      },
    },
   'ProfileName' => 'Nome profilo',
   'ProfileSequenceDesc' => 'Descrizione della Sequenza del Profilo',
   'ProfileType' => {
      Description => 'Tipo profilo',
      PrintConv => {
        'Unspecified' => 'Non specificato',
      },
    },
   'ProfileVersion' => 'Versione profilo',
   'ProgID' => 'ID programma',
   'ProgramISO' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ProgramKind' => 'Tipo di programma',
   'ProgramLine' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'ProgramMode' => {
      Description => 'Modo programma',
      PrintConv => {
        'None' => 'Nessuno',
        'Portrait' => 'Verticale',
        'Sunset' => 'Tramonto',
        'Text' => 'Testo',
      },
    },
   'ProgramName' => 'Nome programma',
   'ProgramNumber' => 'Numero programma',
   'ProgramVersion' => 'Versione programma',
   'ProgrammingGroupKind' => 'Tipo gruppo programmazione',
   'ProgrammingGroupTitle' => 'Titolo gruppo programmazione',
   'ProgressiveScans' => 'Scansioni progressive',
   'Project' => 'Progetto',
   'ProjectName' => 'Nome progetto',
   'ProjectNumber' => 'Numero progetto',
   'ProjectRef' => 'Rif progetto',
   'ProjectSet' => 'Set progetto',
   'ProjectedCSType' => {
      Description => 'Tipo CS proiettato',
      PrintConv => {
        'ETRS89 Poland CS2000 zone 5' => 'ETRS89 Poland CS2000 zona 5',
        'ETRS89 Poland CS2000 zone 7' => 'ETRS89 Poland CS2000 zona 7',
        'ETRS89 Poland CS2000 zone 8' => 'ETRS89 Poland CS2000 zona 8',
        'PSAD56 UTM zone 17S' => 'PSAD56 UTM zona 17S',
        'PSAD56 UTM zone 18N' => 'PSAD56 UTM zona 18N',
        'PSAD56 UTM zone 18S' => 'PSAD56 UTM zona 18S',
        'PSAD56 UTM zone 19N' => 'PSAD56 UTM zona 19N',
        'PSAD56 UTM zone 19S' => 'PSAD56 UTM zona 19S',
        'PSAD56 UTM zone 20N' => 'PSAD56 UTM zona 20N',
        'PSAD56 UTM zone 20S' => 'PSAD56 UTM zona 20S',
        'PSAD56 UTM zone 21N' => 'PSAD56 UTM zona 21N',
        'Pulkovo Gauss zone 10' => 'Pulkovo Gauss zona 10',
        'Pulkovo Gauss zone 11' => 'Pulkovo Gauss zona 11',
        'Pulkovo Gauss zone 12' => 'Pulkovo Gauss zona 12',
        'Pulkovo Gauss zone 13' => 'Pulkovo Gauss zona 13',
        'Pulkovo Gauss zone 14' => 'Pulkovo Gauss zona 14',
        'Pulkovo Gauss zone 15' => 'Pulkovo Gauss zona 15',
        'Pulkovo Gauss zone 16' => 'Pulkovo Gauss zona 16',
        'Pulkovo Gauss zone 17' => 'Pulkovo Gauss zona 17',
        'Pulkovo Gauss zone 18' => 'Pulkovo Gauss zona 18',
        'Pulkovo Gauss zone 19' => 'Pulkovo Gauss zona 19',
        'Pulkovo Gauss zone 20' => 'Pulkovo Gauss zona 20',
        'Pulkovo Gauss zone 21' => 'Pulkovo Gauss zona 21',
        'Pulkovo Gauss zone 22' => 'Pulkovo Gauss zona 22',
        'Pulkovo Gauss zone 23' => 'Pulkovo Gauss zona 23',
        'Pulkovo Gauss zone 24' => 'Pulkovo Gauss zona 24',
        'Pulkovo Gauss zone 25' => 'Pulkovo Gauss zona 25',
        'Pulkovo Gauss zone 26' => 'Pulkovo Gauss zona 26',
        'Pulkovo Gauss zone 27' => 'Pulkovo Gauss zona 27',
        'Pulkovo Gauss zone 28' => 'Pulkovo Gauss zona 28',
        'Pulkovo Gauss zone 29' => 'Pulkovo Gauss zona 29',
        'Pulkovo Gauss zone 30' => 'Pulkovo Gauss zona 30',
        'Pulkovo Gauss zone 31' => 'Pulkovo Gauss zona 31',
        'Pulkovo Gauss zone 32' => 'Pulkovo Gauss zona 32',
        'Pulkovo Gauss zone 4' => 'Pulkovo Gauss zona 4',
        'Pulkovo Gauss zone 5' => 'Pulkovo Gauss zona 5',
        'Pulkovo Gauss zone 6' => 'Pulkovo Gauss zona 6',
        'Pulkovo Gauss zone 7' => 'Pulkovo Gauss zona 7',
        'Pulkovo Gauss zone 8' => 'Pulkovo Gauss zona 8',
        'Pulkovo Gauss zone 9' => 'Pulkovo Gauss zona 9',
        'Sudan UTM zone 35N' => 'Sudan UTM zona 35N',
        'Sudan UTM zone 36N' => 'Sudan UTM zona 36N',
      },
    },
   'Projection' => 'Proiezione',
   'ProjectionAlgorithm' => 'Algoritmo proiezione',
   'ProjectionAngle' => 'Angolo proiezione',
   'Projects' => 'Progetti',
   'Properties' => 'Proprietà',
   'PropertyReleaseStatus' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Not Applicable' => 'Non applicabile',
      },
    },
   'Protect' => 'Proteggi',
   'Protected' => 'Protetto',
   'ProtocolName' => 'Nome protocollo',
   'Province-State' => 'Provincia/Stato',
   'PublicationDate' => 'Data pubblicazione',
   'PublicationName' => 'Nome pubblicazione',
   'PublicationSets' => 'Set pupplicazione',
   'Publisher' => 'Editore',
   'PublisherURL' => 'URL editore',
   'PurchaseDate' => 'Data acquisto',
   'PurchaserAccountName' => 'Nome account acquirente',
   'PurchaserAccountNumber' => 'Numero account acquirente',
   'PurchaserIdentificationKind' => 'Tipo identificazione acquirente',
   'PurchaserIdentificationValue' => 'Valore identificazione acquirente',
   'PurchasingDepartment' => 'Reparto acquirente',
   'PurchasingOrganizationName' => 'Nome organizzazione acquirente',
   'Purpose' => 'Scopo',
   'PurposeOfReferenceCodeSequence' => 'Scopo sequenza codici riferimento',
   'PyramidLevels' => 'Livelli piramite',
   'Quality' => {
      Description => 'Qualità',
      PrintConv => {
        'Compressed RAW' => 'RAW compresso',
        'Compressed RAW + JPEG' => 'RAW+JPEG compresso',
        'Extra Fine' => 'Extra fine',
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'RAW + JPEG' => 'RAW+JPEG',
        'n/a' => 'n/d',
      },
    },
   'QualityControlImage' => 'Immagine controllo qualità',
   'QualityFlag' => 'Indicatore qualità',
   'QualityMode' => {
      Description => 'Modo qualità',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'Quantity' => 'Quantità',
   'QuantitySequence' => 'Sequenza quantità',
   'QuantizationDefault' => 'Default quantizzazione',
   'QuantizationMethod' => 'Metodo quantizzazione',
   'QueueStatus' => 'Stato coda',
   'QuickAdjust' => 'Regolazione rapida',
   'QuickControlDialInMeter' => {
      PrintConv => {
        'ISO speed' => 'Velocità ISO',
      },
    },
   'QuickEdit' => 'Modifica velote',
   'QuickMaskInfo' => 'Info maschera veloce',
   'QuickShot' => {
      Description => 'Scatto veloce',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'R2ABlueCtbl00' => 'R2 A blu Stbl 30',
   'R2ABlueCtbl01' => 'R2 A blu Stbl 29',
   'R2ABlueCtbl02' => 'R2 A blu Stbl 28',
   'R2ABlueCtbl03' => 'R2 A blu Stbl 27',
   'R2ABlueCtbl04' => 'R2 A blu Stbl 26',
   'R2ABlueCtbl05' => 'R2 A blu Stbl 25',
   'R2ABlueCtbl06' => 'R2 A blu Stbl 24',
   'R2ABlueCtbl07' => 'R2 A blu Stbl 23',
   'R2ABlueCtbl08' => 'R2 A blu Stbl 22',
   'R2ABlueCtbl09' => 'R2 A blu Stbl 21',
   'R2ABlueCtbl10' => 'R2 A blu Stbl 20',
   'R2ABlueCtbl11' => 'R2 A blu Stbl 19',
   'R2ABlueCtbl12' => 'R2 A blu Stbl 18',
   'R2ABlueCtbl13' => 'R2 A blu Stbl 17',
   'R2ABlueCtbl14' => 'R2 A blu Stbl 16',
   'R2ABlueCtbl15' => 'R2 A blu Stbl 15',
   'R2ABlueCtbl16' => 'R2 A blu Stbl 14',
   'R2ABlueCtbl17' => 'R2 A blu Stbl 13',
   'R2ABlueCtbl18' => 'R2 A blu Stbl 12',
   'R2ABlueCtbl19' => 'R2 A blu Stbl 11',
   'R2ABlueCtbl20' => 'R2 A blu Stbl 10',
   'R2ABlueCtbl21' => 'R2 A blu Stbl 09',
   'R2ABlueCtbl22' => 'R2 A blu Stbl 08',
   'R2ABlueCtbl23' => 'R2 A blu Stbl 07',
   'R2ABlueCtbl24' => 'R2 A blu Stbl 06',
   'R2ABlueCtbl25' => 'R2 A blu Stbl 05',
   'R2ABlueCtbl26' => 'R2 A blu Stbl 04',
   'R2ABlueCtbl27' => 'R2 A blu Stbl 03',
   'R2ABlueCtbl28' => 'R2 A blu Stbl 02',
   'R2ABlueCtbl29' => 'R2 A blu Stbl 01',
   'R2ABlueCtbl30' => 'R2 A blu Stbl 00',
   'R2ABlueCtbl31' => 'R2 A blu Ctbl 31',
   'R2ABlueStbl00' => 'R2 A blu Ctbl 30',
   'R2ABlueStbl01' => 'R2 A blu Ctbl 29',
   'R2ABlueStbl02' => 'R2 A blu Ctbl 28',
   'R2ABlueStbl03' => 'R2 A blu Ctbl 27',
   'R2ABlueStbl04' => 'R2 A blu Ctbl 26',
   'R2ABlueStbl05' => 'R2 A blu Ctbl 25',
   'R2ABlueStbl06' => 'R2 A blu Ctbl 24',
   'R2ABlueStbl07' => 'R2 A blu Ctbl 23',
   'R2ABlueStbl08' => 'R2 A blu Ctbl 22',
   'R2ABlueStbl09' => 'R2 A blu Ctbl 21',
   'R2ABlueStbl10' => 'R2 A blu Ctbl 20',
   'R2ABlueStbl11' => 'R2 A blu Ctbl 19',
   'R2ABlueStbl12' => 'R2 A blu Ctbl 18',
   'R2ABlueStbl13' => 'R2 A blu Ctbl 17',
   'R2ABlueStbl14' => 'R2 A blu Ctbl 16',
   'R2ABlueStbl15' => 'R2 A blu Ctbl 15',
   'R2ABlueStbl16' => 'R2 A blu Ctbl 14',
   'R2ABlueStbl17' => 'R2 A blu Ctbl 13',
   'R2ABlueStbl18' => 'R2 A blu Ctbl 12',
   'R2ABlueStbl19' => 'R2 A blu Ctbl 11',
   'R2ABlueStbl20' => 'R2 A blu Ctbl 10',
   'R2ABlueStbl21' => 'R2 A blu Ctbl 09',
   'R2ABlueStbl22' => 'R2 A blu Ctbl 08',
   'R2ABlueStbl23' => 'R2 A blu Ctbl 07',
   'R2ABlueStbl24' => 'R2 A blu Ctbl 06',
   'R2ABlueStbl25' => 'R2 A blu Ctbl 05',
   'R2ABlueStbl26' => 'R2 A blu Ctbl 04',
   'R2ABlueStbl27' => 'R2 A blu Ctbl 03',
   'R2ABlueStbl28' => 'R2 A blu Ctbl 02',
   'R2ABlueStbl29' => 'R2 A blu Ctbl 01',
   'R2ABlueStbl30' => 'R2 A blu Ctbl 00',
   'R2ABlueStbl31' => 'R2 A blu Stbl 31',
   'R2AGreenCtbl00' => 'R2 A verde Ctbl 00',
   'R2AGreenCtbl01' => 'R2 A verde Ctbl 01',
   'R2AGreenCtbl02' => 'R2 A verde Ctbl 02',
   'R2AGreenCtbl03' => 'R2 A verde Ctbl 03',
   'R2AGreenCtbl04' => 'R2 A verde Ctbl 04',
   'R2AGreenCtbl05' => 'R2 A verde Ctbl 05',
   'R2AGreenCtbl06' => 'R2 A verde Ctbl 06',
   'R2AGreenCtbl07' => 'R2 A verde Ctbl 07',
   'R2AGreenCtbl08' => 'R2 A verde Ctbl 08',
   'R2AGreenCtbl09' => 'R2 A verde Ctbl 09',
   'R2AGreenCtbl10' => 'R2 A verde Ctbl 10',
   'R2AGreenCtbl11' => 'R2 A verde Ctbl 11',
   'R2AGreenCtbl12' => 'R2 A verde Ctbl 12',
   'R2AGreenCtbl13' => 'R2 A verde Ctbl 13',
   'R2AGreenCtbl14' => 'R2 A verde Ctbl 14',
   'R2AGreenCtbl15' => 'R2 A verde Ctbl 15',
   'R2AGreenCtbl16' => 'R2 A verde Ctbl 16',
   'R2AGreenCtbl17' => 'R2 A verde Ctbl 17',
   'R2AGreenCtbl18' => 'R2 A verde Ctbl 18',
   'R2AGreenCtbl19' => 'R2 A verde Ctbl 19',
   'R2AGreenCtbl20' => 'R2 A verde Ctbl 20',
   'R2AGreenCtbl21' => 'R2 A verde Ctbl 21',
   'R2AGreenCtbl22' => 'R2 A verde Ctbl 22',
   'R2AGreenCtbl23' => 'R2 A verde Ctbl 23',
   'R2AGreenCtbl24' => 'R2 A verde Ctbl 24',
   'R2AGreenCtbl25' => 'R2 A verde Ctbl 25',
   'R2AGreenCtbl26' => 'R2 A verde Ctbl 26',
   'R2AGreenCtbl27' => 'R2 A verde Ctbl 27',
   'R2AGreenCtbl28' => 'R2 A verde Ctbl 28',
   'R2AGreenCtbl29' => 'R2 A verde Ctbl 29',
   'R2AGreenCtbl30' => 'R2 A verde Ctbl 30',
   'R2AGreenCtbl31' => 'R2 A verde Ctbl 31',
   'R2AGreenStbl00' => 'R2 A verde Stbl 00',
   'R2AGreenStbl01' => 'R2 A verde Stbl 01',
   'R2AGreenStbl02' => 'R2 A verde Stbl 02',
   'R2AGreenStbl03' => 'R2 A verde Stbl 03',
   'R2AGreenStbl04' => 'R2 A verde Stbl 04',
   'R2AGreenStbl05' => 'R2 A verde Stbl 05',
   'R2AGreenStbl06' => 'R2 A verde Stbl 06',
   'R2AGreenStbl07' => 'R2 A verde Stbl 07',
   'R2AGreenStbl08' => 'R2 A verde Stbl 08',
   'R2AGreenStbl09' => 'R2 A verde Stbl 09',
   'R2AGreenStbl10' => 'R2 A verde Stbl 10',
   'R2AGreenStbl11' => 'R2 A verde Stbl 11',
   'R2AGreenStbl12' => 'R2 A verde Stbl 12',
   'R2AGreenStbl13' => 'R2 A verde Stbl 13',
   'R2AGreenStbl14' => 'R2 A verde Stbl 14',
   'R2AGreenStbl15' => 'R2 A verde Stbl 15',
   'R2AGreenStbl16' => 'R2 A verde Stbl 16',
   'R2AGreenStbl17' => 'R2 A verde Stbl 17',
   'R2AGreenStbl18' => 'R2 A verde Stbl 18',
   'R2AGreenStbl19' => 'R2 A verde Stbl 19',
   'R2AGreenStbl20' => 'R2 A verde Stbl 20',
   'R2AGreenStbl21' => 'R2 A verde Stbl 21',
   'R2AGreenStbl22' => 'R2 A verde Stbl 22',
   'R2AGreenStbl23' => 'R2 A verde Stbl 23',
   'R2AGreenStbl24' => 'R2 A verde Stbl 24',
   'R2AGreenStbl25' => 'R2 A verde Stbl 25',
   'R2AGreenStbl26' => 'R2 A verde Stbl 26',
   'R2AGreenStbl27' => 'R2 A verde Stbl 27',
   'R2AGreenStbl28' => 'R2 A verde Stbl 28',
   'R2AGreenStbl29' => 'R2 A verde Stbl 29',
   'R2AGreenStbl30' => 'R2 A verde Stbl 30',
   'R2AGreenStbl31' => 'R2 A verde Stbl 31',
   'R2AHeight' => 'R2 A altezza',
   'R2AIntervals' => 'R2 A intervalli',
   'R2ARedCtbl00' => 'R2 A rosso Ctbl 00',
   'R2ARedCtbl01' => 'R2 A rosso Ctbl 01',
   'R2ARedCtbl02' => 'R2 A rosso Ctbl 02',
   'R2ARedCtbl03' => 'R2 A rosso Ctbl 03',
   'R2ARedCtbl04' => 'R2 A rosso Ctbl 04',
   'R2ARedCtbl05' => 'R2 A rosso Ctbl 05',
   'R2ARedCtbl06' => 'R2 A rosso Ctbl 06',
   'R2ARedCtbl07' => 'R2 A rosso Ctbl 07',
   'R2ARedCtbl08' => 'R2 A rosso Ctbl 08',
   'R2ARedCtbl09' => 'R2 A rosso Ctbl 09',
   'R2ARedCtbl10' => 'R2 A rosso Ctbl 10',
   'R2ARedCtbl11' => 'R2 A rosso Ctbl 11',
   'R2ARedCtbl12' => 'R2 A rosso Ctbl 12',
   'R2ARedCtbl13' => 'R2 A rosso Ctbl 13',
   'R2ARedCtbl14' => 'R2 A rosso Ctbl 14',
   'R2ARedCtbl15' => 'R2 A rosso Ctbl 15',
   'R2ARedCtbl16' => 'R2 A rosso Ctbl 16',
   'R2ARedCtbl17' => 'R2 A rosso Ctbl 17',
   'R2ARedCtbl18' => 'R2 A rosso Ctbl 18',
   'R2ARedCtbl19' => 'R2 A rosso Ctbl 19',
   'R2ARedCtbl20' => 'R2 A rosso Ctbl 20',
   'R2ARedCtbl21' => 'R2 A rosso Ctbl 21',
   'R2ARedCtbl22' => 'R2 A rosso Ctbl 22',
   'R2ARedCtbl23' => 'R2 A rosso Ctbl 23',
   'R2ARedCtbl24' => 'R2 A rosso Ctbl 24',
   'R2ARedCtbl25' => 'R2 A rosso Ctbl 25',
   'R2ARedCtbl26' => 'R2 A rosso Ctbl 26',
   'R2ARedCtbl27' => 'R2 A rosso Ctbl 27',
   'R2ARedCtbl28' => 'R2 A rosso Ctbl 28',
   'R2ARedCtbl29' => 'R2 A rosso Ctbl 29',
   'R2ARedCtbl30' => 'R2 A rosso Ctbl 30',
   'R2ARedCtbl31' => 'R2 A rosso Ctbl 31',
   'R2ARedStbl00' => 'R2 A rosso Stbl 00',
   'R2ARedStbl01' => 'R2 A rosso Stbl 01',
   'R2ARedStbl02' => 'R2 A rosso Stbl 02',
   'R2ARedStbl03' => 'R2 A rosso Stbl 03',
   'R2ARedStbl04' => 'R2 A rosso Stbl 04',
   'R2ARedStbl05' => 'R2 A rosso Stbl 05',
   'R2ARedStbl06' => 'R2 A rosso Stbl 06',
   'R2ARedStbl07' => 'R2 A rosso Stbl 07',
   'R2ARedStbl08' => 'R2 A rosso Stbl 08',
   'R2ARedStbl09' => 'R2 A rosso Stbl 09',
   'R2ARedStbl10' => 'R2 A rosso Stbl 10',
   'R2ARedStbl11' => 'R2 A rosso Stbl 11',
   'R2ARedStbl12' => 'R2 A rosso Stbl 12',
   'R2ARedStbl13' => 'R2 A rosso Stbl 13',
   'R2ARedStbl14' => 'R2 A rosso Stbl 14',
   'R2ARedStbl15' => 'R2 A rosso Stbl 15',
   'R2ARedStbl16' => 'R2 A rosso Stbl 16',
   'R2ARedStbl17' => 'R2 A rosso Stbl 17',
   'R2ARedStbl18' => 'R2 A rosso Stbl 18',
   'R2ARedStbl19' => 'R2 A rosso Stbl 19',
   'R2ARedStbl20' => 'R2 A rosso Stbl 20',
   'R2ARedStbl21' => 'R2 A rosso Stbl 21',
   'R2ARedStbl22' => 'R2 A rosso Stbl 22',
   'R2ARedStbl23' => 'R2 A rosso Stbl 23',
   'R2ARedStbl24' => 'R2 A rosso Stbl 24',
   'R2ARedStbl25' => 'R2 A rosso Stbl 25',
   'R2ARedStbl26' => 'R2 A rosso Stbl 26',
   'R2ARedStbl27' => 'R2 A rosso Stbl 27',
   'R2ARedStbl28' => 'R2 A rosso Stbl 28',
   'R2ARedStbl29' => 'R2 A rosso Stbl 29',
   'R2ARedStbl30' => 'R2 A rosso Stbl 30',
   'R2ARedStbl31' => 'R2 A rosso Stbl 31',
   'R2AWidth' => 'R2 A larghezza',
   'R2D65BlueCtbl00' => 'R2 D65 blu Ctbl 00',
   'R2D65BlueCtbl01' => 'R2 D65 blu Ctbl 01',
   'R2D65BlueCtbl02' => 'R2 D65 blu Ctbl 02',
   'R2D65BlueCtbl03' => 'R2 D65 blu Ctbl 03',
   'R2D65BlueCtbl04' => 'R2 D65 blu Ctbl 04',
   'R2D65BlueCtbl05' => 'R2 D65 blu Ctbl 05',
   'R2D65BlueCtbl06' => 'R2 D65 blu Ctbl 06',
   'R2D65BlueCtbl07' => 'R2 D65 blu Ctbl 07',
   'R2D65BlueCtbl08' => 'R2 D65 blu Ctbl 08',
   'R2D65BlueCtbl09' => 'R2 D65 blu Ctbl 09',
   'R2D65BlueCtbl10' => 'R2 D65 blu Ctbl 10',
   'R2D65BlueCtbl11' => 'R2 D65 blu Ctbl 11',
   'R2D65BlueCtbl12' => 'R2 D65 blu Ctbl 12',
   'R2D65BlueCtbl13' => 'R2 D65 blu Ctbl 13',
   'R2D65BlueCtbl14' => 'R2 D65 blu Ctbl 14',
   'R2D65BlueCtbl15' => 'R2 D65 blu Ctbl 15',
   'R2D65BlueCtbl16' => 'R2 D65 blu Ctbl 16',
   'R2D65BlueCtbl17' => 'R2 D65 blu Ctbl 17',
   'R2D65BlueCtbl18' => 'R2 D65 blu Ctbl 18',
   'R2D65BlueCtbl19' => 'R2 D65 blu Ctbl 19',
   'R2D65BlueCtbl20' => 'R2 D65 blu Ctbl 20',
   'R2D65BlueCtbl21' => 'R2 D65 blu Ctbl 21',
   'R2D65BlueCtbl22' => 'R2 D65 blu Ctbl 22',
   'R2D65BlueCtbl23' => 'R2 D65 blu Ctbl 23',
   'R2D65BlueCtbl24' => 'R2 D65 blu Ctbl 24',
   'R2D65BlueCtbl25' => 'R2 D65 blu Ctbl 25',
   'R2D65BlueCtbl26' => 'R2 D65 blu Ctbl 26',
   'R2D65BlueCtbl27' => 'R2 D65 blu Ctbl 27',
   'R2D65BlueCtbl28' => 'R2 D65 blu Ctbl 28',
   'R2D65BlueCtbl29' => 'R2 D65 blu Ctbl 29',
   'R2D65BlueCtbl30' => 'R2 D65 blu Ctbl 30',
   'R2D65BlueCtbl31' => 'R2 D65 blu Ctbl 31',
   'R2D65BlueStbl00' => 'R2 D65 blu Stbl 00',
   'R2D65BlueStbl01' => 'R2 D65 blu Stbl 01',
   'R2D65BlueStbl02' => 'R2 D65 blu Stbl 02',
   'R2D65BlueStbl03' => 'R2 D65 blu Stbl 03',
   'R2D65BlueStbl04' => 'R2 D65 blu Stbl 04',
   'R2D65BlueStbl05' => 'R2 D65 blu Stbl 05',
   'R2D65BlueStbl06' => 'R2 D65 blu Stbl 06',
   'R2D65BlueStbl07' => 'R2 D65 blu Stbl 07',
   'R2D65BlueStbl08' => 'R2 D65 blu Stbl 08',
   'R2D65BlueStbl09' => 'R2 D65 blu Stbl 09',
   'R2D65BlueStbl10' => 'R2 D65 blu Stbl 10',
   'R2D65BlueStbl11' => 'R2 D65 blu Stbl 11',
   'R2D65BlueStbl12' => 'R2 D65 blu Stbl 12',
   'R2D65BlueStbl13' => 'R2 D65 blu Stbl 13',
   'R2D65BlueStbl14' => 'R2 D65 blu Stbl 14',
   'R2D65BlueStbl15' => 'R2 D65 blu Stbl 15',
   'R2D65BlueStbl16' => 'R2 D65 blu Stbl 16',
   'R2D65BlueStbl17' => 'R2 D65 blu Stbl 17',
   'R2D65BlueStbl18' => 'R2 D65 blu Stbl 18',
   'R2D65BlueStbl19' => 'R2 D65 blu Stbl 19',
   'R2D65BlueStbl20' => 'R2 D65 blu Stbl 20',
   'R2D65BlueStbl21' => 'R2 D65 blu Stbl 21',
   'R2D65BlueStbl22' => 'R2 D65 blu Stbl 22',
   'R2D65BlueStbl23' => 'R2 D65 blu Stbl 23',
   'R2D65BlueStbl24' => 'R2 D65 blu Stbl 24',
   'R2D65BlueStbl25' => 'R2 D65 blu Stbl 25',
   'R2D65BlueStbl26' => 'R2 D65 blu Stbl 26',
   'R2D65BlueStbl27' => 'R2 D65 blu Stbl 27',
   'R2D65BlueStbl28' => 'R2 D65 blu Stbl 28',
   'R2D65BlueStbl29' => 'R2 D65 blu Stbl 29',
   'R2D65BlueStbl30' => 'R2 D65 blu Stbl 30',
   'R2D65BlueStbl31' => 'R2 D65 blu Stbl 31',
   'R2D65GreenCtbl00' => 'R2 D65 verde Ctbl 00',
   'R2D65GreenCtbl01' => 'R2 D65 verde Ctbl 01',
   'R2D65GreenCtbl02' => 'R2 D65 verde Ctbl 02',
   'R2D65GreenCtbl03' => 'R2 D65 verde Ctbl 03',
   'R2D65GreenCtbl04' => 'R2 D65 verde Ctbl 04',
   'R2D65GreenCtbl05' => 'R2 D65 verde Ctbl 05',
   'R2D65GreenCtbl06' => 'R2 D65 verde Ctbl 06',
   'R2D65GreenCtbl07' => 'R2 D65 verde Ctbl 07',
   'R2D65GreenCtbl08' => 'R2 D65 verde Ctbl 08',
   'R2D65GreenCtbl09' => 'R2 D65 verde Ctbl 09',
   'R2D65GreenCtbl10' => 'R2 D65 verde Ctbl 10',
   'R2D65GreenCtbl11' => 'R2 D65 verde Ctbl 11',
   'R2D65GreenCtbl12' => 'R2 D65 verde Ctbl 12',
   'R2D65GreenCtbl13' => 'R2 D65 verde Ctbl 13',
   'R2D65GreenCtbl14' => 'R2 D65 verde Ctbl 14',
   'R2D65GreenCtbl15' => 'R2 D65 verde Ctbl 15',
   'R2D65GreenCtbl16' => 'R2 D65 verde Ctbl 16',
   'R2D65GreenCtbl17' => 'R2 D65 verde Ctbl 17',
   'R2D65GreenCtbl18' => 'R2 D65 verde Ctbl 18',
   'R2D65GreenCtbl19' => 'R2 D65 verde Ctbl 19',
   'R2D65GreenCtbl20' => 'R2 D65 verde Ctbl 20',
   'R2D65GreenCtbl21' => 'R2 D65 verde Ctbl 21',
   'R2D65GreenCtbl22' => 'R2 D65 verde Ctbl 22',
   'R2D65GreenCtbl23' => 'R2 D65 verde Ctbl 23',
   'R2D65GreenCtbl24' => 'R2 D65 verde Ctbl 24',
   'R2D65GreenCtbl25' => 'R2 D65 verde Ctbl 25',
   'R2D65GreenCtbl26' => 'R2 D65 verde Ctbl 26',
   'R2D65GreenCtbl27' => 'R2 D65 verde Ctbl 27',
   'R2D65GreenCtbl28' => 'R2 D65 verde Ctbl 28',
   'R2D65GreenCtbl29' => 'R2 D65 verde Ctbl 29',
   'R2D65GreenCtbl30' => 'R2 D65 verde Ctbl 30',
   'R2D65GreenCtbl31' => 'R2 D65 verde Ctbl 31',
   'R2D65GreenStbl00' => 'R2 D65 verde Stbl 00',
   'R2D65GreenStbl01' => 'R2 D65 verde Stbl 01',
   'R2D65GreenStbl02' => 'R2 D65 verde Stbl 02',
   'R2D65GreenStbl03' => 'R2 D65 verde Stbl 03',
   'R2D65GreenStbl04' => 'R2 D65 verde Stbl 04',
   'R2D65GreenStbl05' => 'R2 D65 verde Stbl 05',
   'R2D65GreenStbl06' => 'R2 D65 verde Stbl 06',
   'R2D65GreenStbl07' => 'R2 D65 verde Stbl 07',
   'R2D65GreenStbl08' => 'R2 D65 verde Stbl 08',
   'R2D65GreenStbl09' => 'R2 D65 verde Stbl 09',
   'R2D65GreenStbl10' => 'R2 D65 verde Stbl 10',
   'R2D65GreenStbl11' => 'R2 D65 verde Stbl 11',
   'R2D65GreenStbl12' => 'R2 D65 verde Stbl 12',
   'R2D65GreenStbl13' => 'R2 D65 verde Stbl 13',
   'R2D65GreenStbl14' => 'R2 D65 verde Stbl 14',
   'R2D65GreenStbl15' => 'R2 D65 verde Stbl 15',
   'R2D65GreenStbl16' => 'R2 D65 verde Stbl 16',
   'R2D65GreenStbl17' => 'R2 D65 verde Stbl 17',
   'R2D65GreenStbl18' => 'R2 D65 verde Stbl 18',
   'R2D65GreenStbl19' => 'R2 D65 verde Stbl 19',
   'R2D65GreenStbl20' => 'R2 D65 verde Stbl 20',
   'R2D65GreenStbl21' => 'R2 D65 verde Stbl 21',
   'R2D65GreenStbl22' => 'R2 D65 verde Stbl 22',
   'R2D65GreenStbl23' => 'R2 D65 verde Stbl 23',
   'R2D65GreenStbl24' => 'R2 D65 verde Stbl 24',
   'R2D65GreenStbl25' => 'R2 D65 verde Stbl 25',
   'R2D65GreenStbl26' => 'R2 D65 verde Stbl 26',
   'R2D65GreenStbl27' => 'R2 D65 verde Stbl 27',
   'R2D65GreenStbl28' => 'R2 D65 verde Stbl 28',
   'R2D65GreenStbl29' => 'R2 D65 verde Stbl 29',
   'R2D65GreenStbl30' => 'R2 D65 verde Stbl 30',
   'R2D65GreenStbl31' => 'R2 D65 verde Stbl 31',
   'R2D65Height' => 'R2 D65 altezza',
   'R2D65Intervals' => 'R2 D65 intervalli',
   'R2D65RedCtbl00' => 'R2 D65 rosso Ctbl 00',
   'R2D65RedCtbl01' => 'R2 D65 rosso Ctbl 01',
   'R2D65RedCtbl02' => 'R2 D65 rosso Ctbl 02',
   'R2D65RedCtbl03' => 'R2 D65 rosso Ctbl 03',
   'R2D65RedCtbl04' => 'R2 D65 rosso Ctbl 04',
   'R2D65RedCtbl05' => 'R2 D65 rosso Ctbl 05',
   'R2D65RedCtbl06' => 'R2 D65 rosso Ctbl 06',
   'R2D65RedCtbl07' => 'R2 D65 rosso Ctbl 07',
   'R2D65RedCtbl08' => 'R2 D65 rosso Ctbl 08',
   'R2D65RedCtbl09' => 'R2 D65 rosso Ctbl 09',
   'R2D65RedCtbl10' => 'R2 D65 rosso Ctbl 10',
   'R2D65RedCtbl11' => 'R2 D65 rosso Ctbl 11',
   'R2D65RedCtbl12' => 'R2 D65 rosso Ctbl 12',
   'R2D65RedCtbl13' => 'R2 D65 rosso Ctbl 13',
   'R2D65RedCtbl14' => 'R2 D65 rosso Ctbl 14',
   'R2D65RedCtbl15' => 'R2 D65 rosso Ctbl 15',
   'R2D65RedCtbl16' => 'R2 D65 rosso Ctbl 16',
   'R2D65RedCtbl17' => 'R2 D65 rosso Ctbl 17',
   'R2D65RedCtbl18' => 'R2 D65 rosso Ctbl 18',
   'R2D65RedCtbl19' => 'R2 D65 rosso Ctbl 19',
   'R2D65RedCtbl20' => 'R2 D65 rosso Ctbl 20',
   'R2D65RedCtbl21' => 'R2 D65 rosso Ctbl 21',
   'R2D65RedCtbl22' => 'R2 D65 rosso Ctbl 22',
   'R2D65RedCtbl23' => 'R2 D65 rosso Ctbl 23',
   'R2D65RedCtbl24' => 'R2 D65 rosso Ctbl 24',
   'R2D65RedCtbl25' => 'R2 D65 rosso Ctbl 25',
   'R2D65RedCtbl26' => 'R2 D65 rosso Ctbl 26',
   'R2D65RedCtbl27' => 'R2 D65 rosso Ctbl 27',
   'R2D65RedCtbl28' => 'R2 D65 rosso Ctbl 28',
   'R2D65RedCtbl29' => 'R2 D65 rosso Ctbl 29',
   'R2D65RedCtbl30' => 'R2 D65 rosso Ctbl 30',
   'R2D65RedCtbl31' => 'R2 D65 rosso Ctbl 31',
   'R2D65RedStbl00' => 'R2 D65 rosso Stbl 00',
   'R2D65RedStbl01' => 'R2 D65 rosso Stbl 01',
   'R2D65RedStbl02' => 'R2 D65 rosso Stbl 02',
   'R2D65RedStbl03' => 'R2 D65 rosso Stbl 03',
   'R2D65RedStbl04' => 'R2 D65 rosso Stbl 04',
   'R2D65RedStbl05' => 'R2 D65 rosso Stbl 05',
   'R2D65RedStbl06' => 'R2 D65 rosso Stbl 06',
   'R2D65RedStbl07' => 'R2 D65 rosso Stbl 07',
   'R2D65RedStbl08' => 'R2 D65 rosso Stbl 08',
   'R2D65RedStbl09' => 'R2 D65 rosso Stbl 09',
   'R2D65RedStbl10' => 'R2 D65 rosso Stbl 10',
   'R2D65RedStbl11' => 'R2 D65 rosso Stbl 11',
   'R2D65RedStbl12' => 'R2 D65 rosso Stbl 12',
   'R2D65RedStbl13' => 'R2 D65 rosso Stbl 13',
   'R2D65RedStbl14' => 'R2 D65 rosso Stbl 14',
   'R2D65RedStbl15' => 'R2 D65 rosso Stbl 15',
   'R2D65RedStbl16' => 'R2 D65 rosso Stbl 16',
   'R2D65RedStbl17' => 'R2 D65 rosso Stbl 17',
   'R2D65RedStbl18' => 'R2 D65 rosso Stbl 18',
   'R2D65RedStbl19' => 'R2 D65 rosso Stbl 19',
   'R2D65RedStbl20' => 'R2 D65 rosso Stbl 20',
   'R2D65RedStbl21' => 'R2 D65 rosso Stbl 21',
   'R2D65RedStbl22' => 'R2 D65 rosso Stbl 22',
   'R2D65RedStbl23' => 'R2 D65 rosso Stbl 23',
   'R2D65RedStbl24' => 'R2 D65 rosso Stbl 24',
   'R2D65RedStbl25' => 'R2 D65 rosso Stbl 25',
   'R2D65RedStbl26' => 'R2 D65 rosso Stbl 26',
   'R2D65RedStbl27' => 'R2 D65 rosso Stbl 27',
   'R2D65RedStbl28' => 'R2 D65 rosso Stbl 28',
   'R2D65RedStbl29' => 'R2 D65 rosso Stbl 29',
   'R2D65RedStbl30' => 'R2 D65 rosso Stbl 30',
   'R2D65RedStbl31' => 'R2 D65 rosso Stbl 31',
   'R2D65Width' => 'R2 D65 larghezza',
   'R2TL84BlueCtbl00' => 'R2 TL84 blu Ctbl 00',
   'R2TL84BlueCtbl01' => 'R2 TL84 blu Ctbl 01',
   'R2TL84BlueCtbl02' => 'R2 TL84 blu Ctbl 02',
   'R2TL84BlueCtbl03' => 'R2 TL84 blu Ctbl 03',
   'R2TL84BlueCtbl04' => 'R2 TL84 blu Ctbl 04',
   'R2TL84BlueCtbl05' => 'R2 TL84 blu Ctbl 05',
   'R2TL84BlueCtbl06' => 'R2 TL84 blu Ctbl 06',
   'R2TL84BlueCtbl07' => 'R2 TL84 blu Ctbl 07',
   'R2TL84BlueCtbl08' => 'R2 TL84 blu Ctbl 08',
   'R2TL84BlueCtbl09' => 'R2 TL84 blu Ctbl 09',
   'R2TL84BlueCtbl10' => 'R2 TL84 blu Ctbl 10',
   'R2TL84BlueCtbl11' => 'R2 TL84 blu Ctbl 11',
   'R2TL84BlueCtbl12' => 'R2 TL84 blu Ctbl 12',
   'R2TL84BlueCtbl13' => 'R2 TL84 blu Ctbl 13',
   'R2TL84BlueCtbl14' => 'R2 TL84 blu Ctbl 14',
   'R2TL84BlueCtbl15' => 'R2 TL84 blu Ctbl 15',
   'R2TL84BlueCtbl16' => 'R2 TL84 blu Ctbl 16',
   'R2TL84BlueCtbl17' => 'R2 TL84 blu Ctbl 17',
   'R2TL84BlueCtbl18' => 'R2 TL84 blu Ctbl 18',
   'R2TL84BlueCtbl19' => 'R2 TL84 blu Ctbl 19',
   'R2TL84BlueCtbl20' => 'R2 TL84 blu Ctbl 20',
   'R2TL84BlueCtbl21' => 'R2 TL84 blu Ctbl 21',
   'R2TL84BlueCtbl22' => 'R2 TL84 blu Ctbl 22',
   'R2TL84BlueCtbl23' => 'R2 TL84 blu Ctbl 23',
   'R2TL84BlueCtbl24' => 'R2 TL84 blu Ctbl 24',
   'R2TL84BlueCtbl25' => 'R2 TL84 blu Ctbl 25',
   'R2TL84BlueCtbl26' => 'R2 TL84 blu Ctbl 26',
   'R2TL84BlueCtbl27' => 'R2 TL84 blu Ctbl 27',
   'R2TL84BlueCtbl28' => 'R2 TL84 blu Ctbl 28',
   'R2TL84BlueCtbl29' => 'R2 TL84 blu Ctbl 29',
   'R2TL84BlueCtbl30' => 'R2 TL84 blu Ctbl 30',
   'R2TL84BlueCtbl31' => 'R2 TL84 blu Ctbl 31',
   'R2TL84BlueStbl00' => 'R2 TL84 blu Stbl 00',
   'R2TL84BlueStbl01' => 'R2 TL84 blu Stbl 01',
   'R2TL84BlueStbl02' => 'R2 TL84 blu Stbl 02',
   'R2TL84BlueStbl03' => 'R2 TL84 blu Stbl 03',
   'R2TL84BlueStbl04' => 'R2 TL84 blu Stbl 04',
   'R2TL84BlueStbl05' => 'R2 TL84 blu Stbl 05',
   'R2TL84BlueStbl06' => 'R2 TL84 blu Stbl 06',
   'R2TL84BlueStbl07' => 'R2 TL84 blu Stbl 07',
   'R2TL84BlueStbl08' => 'R2 TL84 blu Stbl 08',
   'R2TL84BlueStbl09' => 'R2 TL84 blu Stbl 09',
   'R2TL84BlueStbl10' => 'R2 TL84 blu Stbl 10',
   'R2TL84BlueStbl11' => 'R2 TL84 blu Stbl 11',
   'R2TL84BlueStbl12' => 'R2 TL84 blu Stbl 12',
   'R2TL84BlueStbl13' => 'R2 TL84 blu Stbl 13',
   'R2TL84BlueStbl14' => 'R2 TL84 blu Stbl 14',
   'R2TL84BlueStbl15' => 'R2 TL84 blu Stbl 15',
   'R2TL84BlueStbl16' => 'R2 TL84 blu Stbl 16',
   'R2TL84BlueStbl17' => 'R2 TL84 blu Stbl 17',
   'R2TL84BlueStbl18' => 'R2 TL84 blu Stbl 18',
   'R2TL84BlueStbl19' => 'R2 TL84 blu Stbl 19',
   'R2TL84BlueStbl20' => 'R2 TL84 blu Stbl 20',
   'R2TL84BlueStbl21' => 'R2 TL84 blu Stbl 21',
   'R2TL84BlueStbl22' => 'R2 TL84 blu Stbl 22',
   'R2TL84BlueStbl23' => 'R2 TL84 blu Stbl 23',
   'R2TL84BlueStbl24' => 'R2 TL84 blu Stbl 24',
   'R2TL84BlueStbl25' => 'R2 TL84 blu Stbl 25',
   'R2TL84BlueStbl26' => 'R2 TL84 blu Stbl 26',
   'R2TL84BlueStbl27' => 'R2 TL84 blu Stbl 27',
   'R2TL84BlueStbl28' => 'R2 TL84 blu Stbl 28',
   'R2TL84BlueStbl29' => 'R2 TL84 blu Stbl 29',
   'R2TL84BlueStbl30' => 'R2 TL84 blu Stbl 30',
   'R2TL84BlueStbl31' => 'R2 TL84 blu Stbl 31',
   'R2TL84GreenCtbl00' => 'R2 TL84 verde Ctbl 00',
   'R2TL84GreenCtbl01' => 'R2 TL84 verde Ctbl 01',
   'R2TL84GreenCtbl02' => 'R2 TL84 verde Ctbl 02',
   'R2TL84GreenCtbl03' => 'R2 TL84 verde Ctbl 03',
   'R2TL84GreenCtbl04' => 'R2 TL84 verde Ctbl 04',
   'R2TL84GreenCtbl05' => 'R2 TL84 verde Ctbl 05',
   'R2TL84GreenCtbl06' => 'R2 TL84 verde Ctbl 06',
   'R2TL84GreenCtbl07' => 'R2 TL84 verde Ctbl 07',
   'R2TL84GreenCtbl08' => 'R2 TL84 verde Ctbl 08',
   'R2TL84GreenCtbl09' => 'R2 TL84 verde Ctbl 09',
   'R2TL84GreenCtbl10' => 'R2 TL84 verde Ctbl 10',
   'R2TL84GreenCtbl11' => 'R2 TL84 verde Ctbl 11',
   'R2TL84GreenCtbl12' => 'R2 TL84 verde Ctbl 12',
   'R2TL84GreenCtbl13' => 'R2 TL84 verde Ctbl 13',
   'R2TL84GreenCtbl14' => 'R2 TL84 verde Ctbl 14',
   'R2TL84GreenCtbl15' => 'R2 TL84 verde Ctbl 15',
   'R2TL84GreenCtbl16' => 'R2 TL84 verde Ctbl 16',
   'R2TL84GreenCtbl17' => 'R2 TL84 verde Ctbl 17',
   'R2TL84GreenCtbl18' => 'R2 TL84 verde Ctbl 18',
   'R2TL84GreenCtbl19' => 'R2 TL84 verde Ctbl 19',
   'R2TL84GreenCtbl20' => 'R2 TL84 verde Ctbl 20',
   'R2TL84GreenCtbl21' => 'R2 TL84 verde Ctbl 21',
   'R2TL84GreenCtbl22' => 'R2 TL84 verde Ctbl 22',
   'R2TL84GreenCtbl23' => 'R2 TL84 verde Ctbl 23',
   'R2TL84GreenCtbl24' => 'R2 TL84 verde Ctbl 24',
   'R2TL84GreenCtbl25' => 'R2 TL84 verde Ctbl 25',
   'R2TL84GreenCtbl26' => 'R2 TL84 verde Ctbl 26',
   'R2TL84GreenCtbl27' => 'R2 TL84 verde Ctbl 27',
   'R2TL84GreenCtbl28' => 'R2 TL84 verde Ctbl 28',
   'R2TL84GreenCtbl29' => 'R2 TL84 verde Ctbl 29',
   'R2TL84GreenCtbl30' => 'R2 TL84 verde Ctbl 30',
   'R2TL84GreenCtbl31' => 'R2 TL84 verde Ctbl 31',
   'R2TL84GreenStbl00' => 'R2 TL84 verde Stbl 00',
   'R2TL84GreenStbl01' => 'R2 TL84 verde Stbl 01',
   'R2TL84GreenStbl02' => 'R2 TL84 verde Stbl 02',
   'R2TL84GreenStbl03' => 'R2 TL84 verde Stbl 03',
   'R2TL84GreenStbl04' => 'R2 TL84 verde Stbl 04',
   'R2TL84GreenStbl05' => 'R2 TL84 verde Stbl 05',
   'R2TL84GreenStbl06' => 'R2 TL84 verde Stbl 06',
   'R2TL84GreenStbl07' => 'R2 TL84 verde Stbl 07',
   'R2TL84GreenStbl08' => 'R2 TL84 verde Stbl 08',
   'R2TL84GreenStbl09' => 'R2 TL84 verde Stbl 09',
   'R2TL84GreenStbl10' => 'R2 TL84 verde Stbl 10',
   'R2TL84GreenStbl11' => 'R2 TL84 verde Stbl 11',
   'R2TL84GreenStbl12' => 'R2 TL84 verde Stbl 12',
   'R2TL84GreenStbl13' => 'R2 TL84 verde Stbl 13',
   'R2TL84GreenStbl14' => 'R2 TL84 verde Stbl 14',
   'R2TL84GreenStbl15' => 'R2 TL84 verde Stbl 15',
   'R2TL84GreenStbl16' => 'R2 TL84 verde Stbl 16',
   'R2TL84GreenStbl17' => 'R2 TL84 verde Stbl 17',
   'R2TL84GreenStbl18' => 'R2 TL84 verde Stbl 18',
   'R2TL84GreenStbl19' => 'R2 TL84 verde Stbl 19',
   'R2TL84GreenStbl20' => 'R2 TL84 verde Stbl 20',
   'R2TL84GreenStbl21' => 'R2 TL84 verde Stbl 21',
   'R2TL84GreenStbl22' => 'R2 TL84 verde Stbl 22',
   'R2TL84GreenStbl23' => 'R2 TL84 verde Stbl 23',
   'R2TL84GreenStbl24' => 'R2 TL84 verde Stbl 24',
   'R2TL84GreenStbl25' => 'R2 TL84 verde Stbl 25',
   'R2TL84GreenStbl26' => 'R2 TL84 verde Stbl 26',
   'R2TL84GreenStbl27' => 'R2 TL84 verde Stbl 27',
   'R2TL84GreenStbl28' => 'R2 TL84 verde Stbl 28',
   'R2TL84GreenStbl29' => 'R2 TL84 verde Stbl 29',
   'R2TL84GreenStbl30' => 'R2 TL84 verde Stbl 30',
   'R2TL84GreenStbl31' => 'R2 TL84 verde Stbl 31',
   'R2TL84Height' => 'R2 TL84 altezza',
   'R2TL84Intervals' => 'R2 TL84 intervalli',
   'R2TL84RedCtbl00' => 'R2 TL84 rosso Ctbl 00',
   'R2TL84RedCtbl01' => 'R2 TL84 rosso Ctbl 01',
   'R2TL84RedCtbl02' => 'R2 TL84 rosso Ctbl 02',
   'R2TL84RedCtbl03' => 'R2 TL84 rosso Ctbl 03',
   'R2TL84RedCtbl04' => 'R2 TL84 rosso Ctbl 04',
   'R2TL84RedCtbl05' => 'R2 TL84 rosso Ctbl 05',
   'R2TL84RedCtbl06' => 'R2 TL84 rosso Ctbl 06',
   'R2TL84RedCtbl07' => 'R2 TL84 rosso Ctbl 07',
   'R2TL84RedCtbl08' => 'R2 TL84 rosso Ctbl 08',
   'R2TL84RedCtbl09' => 'R2 TL84 rosso Ctbl 09',
   'R2TL84RedCtbl10' => 'R2 TL84 rosso Ctbl 10',
   'R2TL84RedCtbl11' => 'R2 TL84 rosso Ctbl 11',
   'R2TL84RedCtbl12' => 'R2 TL84 rosso Ctbl 12',
   'R2TL84RedCtbl13' => 'R2 TL84 rosso Ctbl 13',
   'R2TL84RedCtbl14' => 'R2 TL84 rosso Ctbl 14',
   'R2TL84RedCtbl15' => 'R2 TL84 rosso Ctbl 15',
   'R2TL84RedCtbl16' => 'R2 TL84 rosso Ctbl 16',
   'R2TL84RedCtbl17' => 'R2 TL84 rosso Ctbl 17',
   'R2TL84RedCtbl18' => 'R2 TL84 rosso Ctbl 18',
   'R2TL84RedCtbl19' => 'R2 TL84 rosso Ctbl 19',
   'R2TL84RedCtbl20' => 'R2 TL84 rosso Ctbl 20',
   'R2TL84RedCtbl21' => 'R2 TL84 rosso Ctbl 21',
   'R2TL84RedCtbl22' => 'R2 TL84 rosso Ctbl 22',
   'R2TL84RedCtbl23' => 'R2 TL84 rosso Ctbl 23',
   'R2TL84RedCtbl24' => 'R2 TL84 rosso Ctbl 24',
   'R2TL84RedCtbl25' => 'R2 TL84 rosso Ctbl 25',
   'R2TL84RedCtbl26' => 'R2 TL84 rosso Ctbl 26',
   'R2TL84RedCtbl27' => 'R2 TL84 rosso Ctbl 27',
   'R2TL84RedCtbl28' => 'R2 TL84 rosso Ctbl 28',
   'R2TL84RedCtbl29' => 'R2 TL84 rosso Ctbl 29',
   'R2TL84RedCtbl30' => 'R2 TL84 rosso Ctbl 30',
   'R2TL84RedCtbl31' => 'R2 TL84 rosso Ctbl 31',
   'R2TL84RedStbl00' => 'R2 TL84 rosso Stbl 00',
   'R2TL84RedStbl01' => 'R2 TL84 rosso Stbl 01',
   'R2TL84RedStbl02' => 'R2 TL84 rosso Stbl 02',
   'R2TL84RedStbl03' => 'R2 TL84 rosso Stbl 03',
   'R2TL84RedStbl04' => 'R2 TL84 rosso Stbl 04',
   'R2TL84RedStbl05' => 'R2 TL84 rosso Stbl 05',
   'R2TL84RedStbl06' => 'R2 TL84 rosso Stbl 06',
   'R2TL84RedStbl07' => 'R2 TL84 rosso Stbl 07',
   'R2TL84RedStbl08' => 'R2 TL84 rosso Stbl 08',
   'R2TL84RedStbl09' => 'R2 TL84 rosso Stbl 09',
   'R2TL84RedStbl10' => 'R2 TL84 rosso Stbl 10',
   'R2TL84RedStbl11' => 'R2 TL84 rosso Stbl 11',
   'R2TL84RedStbl12' => 'R2 TL84 rosso Stbl 12',
   'R2TL84RedStbl13' => 'R2 TL84 rosso Stbl 13',
   'R2TL84RedStbl14' => 'R2 TL84 rosso Stbl 14',
   'R2TL84RedStbl15' => 'R2 TL84 rosso Stbl 15',
   'R2TL84RedStbl16' => 'R2 TL84 rosso Stbl 16',
   'R2TL84RedStbl17' => 'R2 TL84 rosso Stbl 17',
   'R2TL84RedStbl18' => 'R2 TL84 rosso Stbl 18',
   'R2TL84RedStbl19' => 'R2 TL84 rosso Stbl 19',
   'R2TL84RedStbl20' => 'R2 TL84 rosso Stbl 20',
   'R2TL84RedStbl21' => 'R2 TL84 rosso Stbl 21',
   'R2TL84RedStbl22' => 'R2 TL84 rosso Stbl 22',
   'R2TL84RedStbl23' => 'R2 TL84 rosso Stbl 23',
   'R2TL84RedStbl24' => 'R2 TL84 rosso Stbl 24',
   'R2TL84RedStbl25' => 'R2 TL84 rosso Stbl 25',
   'R2TL84RedStbl26' => 'R2 TL84 rosso Stbl 26',
   'R2TL84RedStbl27' => 'R2 TL84 rosso Stbl 27',
   'R2TL84RedStbl28' => 'R2 TL84 rosso Stbl 28',
   'R2TL84RedStbl29' => 'R2 TL84 rosso Stbl 29',
   'R2TL84RedStbl30' => 'R2 TL84 rosso Stbl 30',
   'R2TL84RedStbl31' => 'R2 TL84 rosso Stbl 31',
   'R2TL84Width' => 'R2 TL84 larghezza',
   'RAFVersion' => 'Versione RAF',
   'RGBCurveLimits' => 'Limiti curva RGB',
   'RGBCurvePoints' => 'Punti curva RGB',
   'RadialPosition' => 'Posizione radiale',
   'RandomIndexMetadata' => 'Indice casuale metadati',
   'RandomIndexMetadataV10' => 'Indice casuale metadati V10',
   'RangeFinder' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'RasterPadding' => {
      PrintConv => {
        'Long Sector' => 'Settore lungo',
        'Long Word' => 'Parola lunga',
        'Sector' => 'Settore',
        'Word' => 'Parola',
      },
    },
   'RasterizedCaption' => 'Didascalia rasterizzata',
   'Rating' => {
      Description => 'Valutazione',
      PrintConv => {
        'Explicit' => 'Esplicito',
      },
    },
   'RatingPercent' => 'Valutazione percentuale',
   'RawAndJpgRecording' => {
      Description => 'Registrazione raw e jpg',
      PrintConv => {
        'Off' => 'Spento',
        'RAW (DNG, Best)' => 'RAW (DNG, migliore)',
        'RAW (DNG, Better)' => 'RAW (DNG, più buona)',
        'RAW (DNG, Good)' => 'RAW (DNG, buona)',
        'RAW (PEF, Best)' => 'RAW (PEF, migliore)',
        'RAW (PEF, Better)' => 'RAW (PEF, più buona)',
        'RAW (PEF, Good)' => 'RAW (PEF, buona)',
        'RAW+JPEG (DNG, Best)' => 'RAW+JPEG (DNG, migliore)',
        'RAW+JPEG (DNG, Better)' => 'RAW+JPEG (DNG, più buona)',
        'RAW+JPEG (DNG, Good)' => 'RAW+JPEG (DNG, buona)',
        'RAW+JPEG (PEF, Best)' => 'RAW+JPEG (PEF, migliore)',
        'RAW+JPEG (PEF, Better)' => 'RAW+JPEG (PEF, più buona)',
        'RAW+JPEG (PEF, Good)' => 'RAW+JPEG (PEF, buona)',
      },
    },
   'RawColorAdj' => {
      PrintConv => {
        'Shot Settings' => 'Impostazioni scatto',
      },
    },
   'RawDataOffset' => 'Offset dati raw',
   'RawDataUniqueID' => 'ID unico dati raw',
   'RawDepth' => 'Profondità raw',
   'RawDevAutoGradation' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'RawDevEditStatus' => {
      PrintConv => {
        'Original' => 'Originale',
      },
    },
   'RawDevNoiseReduction' => {
      PrintConv => {
        'Noise Filter' => 'Filtro rumore',
        'Noise Reduction' => 'Riduzione rumore',
      },
    },
   'RawDevPMPictureTone' => {
      PrintConv => {
        'Green' => 'Verde',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
      },
    },
   'RawDevPM_BWFilter' => {
      PrintConv => {
        'Green' => 'Verde',
        'Orange' => 'Arancio',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'RawDevPictureMode' => {
      PrintConv => {
        'Sepia' => 'Seppia',
        'Vivid' => 'Vivace',
      },
    },
   'RawDevSettings' => {
      PrintConv => {
        'Noise Reduction' => 'Riduzione rumore',
        'Saturation' => 'Saturazione',
        'Sharpness' => 'Nitidezza',
      },
    },
   'RawFile' => 'File raw',
   'RawFileName' => 'Nome file raw',
   'RawImageCenter' => 'Centro immagine raw',
   'RawImageDigest' => 'Sommario file raw',
   'RawImageFullSize' => 'Dimensione finale immagine raw',
   'RawImageHeight' => 'Altezza immagine raw',
   'RawImageMode' => 'Modo immagine raw',
   'RawImageSegmentation' => 'Segmentazione file raw',
   'RawImageSize' => 'Dimensione immagine raw',
   'RawImageWidth' => 'Larghezza immagine raw',
   'RawInfoVersion' => 'Info versione raw',
   'RawJpgHeight' => 'Altezza jpg raw',
   'RawJpgQuality' => {
      Description => 'Qualità jpg raw',
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'RawJpgSize' => {
      Description => 'Dimensione jpg raw',
      PrintConv => {
        'Postcard' => 'Cartolina',
      },
    },
   'RawJpgWidth' => 'Larghezza jpg raw',
   'ReadStatus' => 'Stato lettura',
   'RecommendedExposureIndex' => 'Indice esposizione raccomandato',
   'RecordDisplay' => {
      PrintConv => {
        'Horizontal' => 'Orizzontale',
      },
    },
   'RecordMode' => {
      Description => 'Modo registrazione',
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
        'Manual' => 'Manuale',
        'Program AE' => 'Programma AE',
        'Shutter Priority' => 'Priorità otturatore',
      },
    },
   'RecordShutterRelease' => {
      PrintConv => {
        'Press start, press stop' => 'Premi start, premi stop',
      },
    },
   'RecordedFormat' => 'Formato registrato',
   'RecordingDates' => 'Date registrazioni',
   'RecordingLabelName' => 'Nome etichetta registrazione',
   'RecordingMode' => {
      Description => 'Modo registrazione',
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Portrait' => 'Verticale',
      },
    },
   'RecordingTime' => 'Tempo registrazione',
   'RedAdjust' => 'Correzione rosso',
   'RedBalance' => 'Bilanciamento del rosso',
   'RedCurveLimits' => 'Limiti curva rosso',
   'RedCurvePoints' => 'Punti curva rosso',
   'RedEyeCorrection' => {
      Description => 'Correzione occhi rossi',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'RedEyeInfo' => 'Info occhi rossi',
   'RedEyeReduction' => {
      Description => 'Riduzione occhi rossi',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'RedGain' => 'Guadagno rosso',
   'RedHue' => 'Tinta rosso',
   'RedMatrixColumn' => 'Colonna matrice rosso',
   'RedPaletteColorTableData' => 'Dati tabella tavolozza colori rosso',
   'RedPaletteColorTableDescriptor' => 'Descrittore tabella tavolozza colori rosso',
   'RedPrimary' => 'Rosso primario',
   'RedSample' => 'Campione rosso',
   'RedSaturation' => 'Saturazione rosso',
   'RedTRC' => 'Curva riproduzione tono rosso',
   'ReductionMatrix1' => 'Matrice di riduzione 1',
   'ReductionMatrix2' => 'Matrice di riduzione 2',
   'Reference' => 'Riferimento',
   'Reference1' => 'Riferimento 1',
   'Reference2' => 'Riferimento 2',
   'Reference3' => 'Riferimento 3',
   'Reference4' => 'Riferimento 4',
   'Reference5' => 'Riferimento 5',
   'Reference6' => 'Riferimento 6',
   'ReferenceBlackWhite' => 'Coppia valori riferimento di bianco e nero',
   'ReferenceBlock' => 'Blocco di riferimento',
   'ReferenceChannels' => 'Canali di riferimento',
   'ReferenceCoordinates' => 'Coordinate di riferimento',
   'ReferenceDate' => 'Data di riferimento',
   'ReferenceNumber' => 'Numero di riferimento',
   'ReferencePixelPhysicalValueX' => 'Valore fisico valore X pixel di riferimento',
   'ReferencePixelPhysicalValueY' => 'Valore fisico valore Y pixel di riferimento',
   'ReferencePixelX0' => 'Pixel X0 di riferimento',
   'ReferencePixelY0' => 'Pixel Y0 di riferimento',
   'ReferencePriority' => 'Priorità riferimento',
   'ReferenceService' => 'Riferimento di servizio',
   'ReferenceToRecordedSound' => 'Riferimento a suono registrato',
   'ReferenceVirtual' => 'Riferimento virtuale',
   'Refresh' => 'Aggiorna',
   'RegionCode' => 'Codice regione',
   'RegionInfo' => 'Info regione',
   'RegionList' => 'Elenco regioni',
   'RegionName' => 'Nome regione',
   'RegionOfResidence' => 'Regione di residenza',
   'RelatedImageFileFormat' => 'Formato file immagine correlato',
   'RelatedImageHeight' => 'Numero delle righe dei dati immagine',
   'RelatedImageWidth' => 'Larghezza immagine correlata',
   'RelatedSoundFile' => 'File audio relativo',
   'Relation' => 'Relazione',
   'RelativeVolumeAdjustment' => 'Correzione relativa volume',
   'ReleaseButtonToUseDial' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ReleaseDate' => 'Data di rilascio',
   'ReleaseMode' => {
      PrintConv => {
        'Normal' => 'Normale',
        'n/a' => 'n/d',
      },
    },
   'ReleaseTime' => 'Ora di rilascio',
   'RenderingIntent' => {
      PrintConv => {
        'ICC-Absolute Colorimetric' => 'Colorimetrico Assoluto',
        'Media-Relative Colorimetric' => 'Colorimetrico Relativo',
        'Perceptual' => 'Percentuale',
        'Saturation' => 'Saturazione',
      },
    },
   'RenderingType3D' => 'Tipo resa 3D',
   'RepeatingFlashCount' => 'Conteggio ripetizione flash',
   'RepeatingFlashOutput' => 'Uscita ripetizione flash',
   'RepeatingFlashRate' => 'Frequenza ripetizione flash',
   'ReplyTo' => 'Rispondi a',
   'Resaved' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'Reserved' => 'Riservato',
   'Reserved1' => 'Riservato 1',
   'Resolution' => 'Risoluzione',
   'ResolutionMode' => 'Modo risoluzione',
   'ResolutionUnit' => {
      Description => 'Unità risoluzione',
      PrintConv => {
        'None' => 'Nessuno',
        'inches' => 'Pollici',
      },
    },
   'ResonantNucleus' => 'Nucleo risonante',
   'ResourceID' => 'ID risorsa',
   'ResourceType' => 'Tipo risorsa',
   'Resources' => 'Risorse',
   'ResourcesNeeded' => 'Risorse richieste',
   'Restrictions' => 'Restrizioni',
   'ResultsID' => 'ID risultati',
   'RetouchHistory' => {
      Description => 'Cronologia ritocco',
      PrintConv => {
        'Fisheye' => 'Fish-eye',
        'None' => 'Nessuno',
        'Quick Retouch' => 'Ritocco veloce',
        'Red Eye' => 'Occhi rossi',
        'Red Intensifier' => 'Intensificatore rosso',
        'Resize' => 'Ridimensione',
        'Sepia' => 'Seppia',
        'Straighten' => 'Raddrizza',
      },
    },
   'RetouchInfo' => 'Info ritocco',
   'Reuse' => {
      Description => 'Riutilizza',
      PrintConv => {
        'Not Applicable' => 'Non applicabile',
      },
    },
   'ReverseIndicators' => 'Inverti indicatori',
   'ReversedByteOrder' => 'Ordine byte invertito',
   'Revision' => 'Revisione',
   'RevisionDate' => 'Data revisione',
   'RevisionNumber' => 'Numero revisione',
   'RicohDate' => 'Data Ricoh',
   'RicohImageHeight' => 'Altezza immagine Ricoh',
   'RicohImageWidth' => 'Larghezza immagine Ricoh',
   'RicohRDC2' => 'RDC2 Ricoh',
   'RightAscension' => 'Ascensione retta',
   'Robots' => 'Robot',
   'RoleName' => 'Nome ruolo',
   'RoomNumber' => 'Numero stanza',
   'RoomOrSuiteName' => 'Numero stanza/suite',
   'RootFormatVersion' => 'Versione formato radice',
   'Rotation' => {
      Description => 'Rotazione',
      PrintConv => {
        'Horizontal' => 'Orizzontale',
        'Horizontal (Normal)' => 'Orizzontale (normale)',
        'Horizontal (normal)' => 'Orizzontale (normale)',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
        'Rotated 180' => 'Ruotato di 180°',
        'Rotated 270 CW' => 'Ruotato di 270° in senso orario',
        'Rotated 90 CW' => 'Ruotato di 90° in senso orario',
      },
    },
   'RotationAngle' => 'Angolo rotazione',
   'RotationDirection' => 'Direzione rotazione',
   'RowsPerStrip' => 'Righe per striscia',
   'RunWindow' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Restore' => 'Ripristina',
      },
    },
   'SEMInfo' => 'Info SEM',
   'SPIFFVersion' => 'Versione SPIFF',
   'SRAWQuality' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SRActive' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'SRGBRendering' => {
      PrintConv => {
        'Saturation' => 'Saturazione',
      },
    },
   'SRResult' => {
      PrintConv => {
        'Not ready' => 'Non pronto',
      },
    },
   'SVGVersion' => 'Versione SVG',
   'SafetyShiftInAvOrTv' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'SampleBits' => 'Bit campione',
   'SampleFormat' => {
      Description => 'Formato campioni',
      PrintConv => {
        'Complex float' => 'Virgola mobile immaginario',
        'Complex int' => 'Intero immaginario',
        'Float' => 'Virgola mobile',
        'Signed' => 'Con segno',
        'Undefined' => 'Non definito',
        'Unsigned' => 'Senza segno',
      },
    },
   'SampleIndex' => 'Indice campione',
   'SampleRate' => 'Frequenza campionamento',
   'SampleRate2' => 'Frequenza campionamento 2',
   'SampleSize' => 'Dimensione campione',
   'SampleSizes' => 'Dimensioni campione',
   'SampleStructure' => 'Struttura d\'esempio',
   'SampleText' => 'Testo d\'esempio',
   'SamplesPerPixel' => 'Campioni per pixel',
   'SamplesPerPixelUsed' => 'Campioni per pixel',
   'SamplingFrequency' => 'Frequenza campionamento',
   'Saturation' => {
      Description => 'Saturazione',
      PrintConv => {
        'High' => 'Alta',
        'Low' => 'Basso',
        'None' => 'Nessuno',
        'None (B&W)' => 'Nessuno (B&N)',
        'Normal' => 'Normale',
        'Vivid' => 'Vivace',
      },
    },
   'SaturationAdj' => 'Correzione saturazione',
   'SaturationFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationMonochrome' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationPortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationUnknown' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationUserDef1' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationUserDef2' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SaturationUserDef3' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ScanImageEnhancer' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ScanningDirection' => {
      PrintConv => {
        'R-L, Bottom-Top' => 'D-S, basso-alto',
        'R-L, Top-Bottom' => 'D-S, alto-basso',
      },
    },
   'Scene' => 'Scena',
   'SceneArea' => 'Area scena',
   'SceneCaptureType' => {
      Description => 'Tipo cattura scena',
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Night' => 'Notte',
        'Portrait' => 'Verticale',
      },
    },
   'SceneMode' => {
      Description => 'Modo scena',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => 'Riduz. sfocat. movim.',
        'Aperture Priority' => 'Priorità diaframma',
        'Auto' => 'Automatico',
        'Children' => 'Bambini',
        'Cont. Priority AE' => 'AE prior. avan.cont.',
        'Fireworks' => 'Fuochi artificiali',
        'Handheld Night Shot' => 'Foto nott. senza trepp.',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Night Portrait' => 'Rit. notturno',
        'Night Scene' => 'Visione notturna',
        'Night View/Portrait' => 'Visione/Ritratto notturni',
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Scenery' => 'Paesaggio',
        'Shutter Priority' => 'Priorità otturatore',
        'Sports' => 'Evento sportivo',
        'Sunset' => 'Tramonto',
        'Sweep Panorama' => 'Panoramica ad arco',
        'Text' => 'Testo',
        'Vivid' => 'Vivace',
        'n/a' => 'n/d',
      },
    },
   'SceneModeUsed' => {
      Description => 'Modo scena usato',
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
        'Children' => 'Bambini',
        'Fireworks' => 'Fuochi artificiali',
        'Landscape' => 'Orizzontale',
        'Manual' => 'Manuale',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Shutter Priority' => 'Priorità otturatore',
        'Sunset' => 'Tramonto',
        'Text' => 'Testo',
      },
    },
   'SceneNumber' => 'Numero scena',
   'SceneSelect' => {
      PrintConv => {
        'Night' => 'Scena notturna',
        'Off' => 'Spento',
      },
    },
   'SceneType' => {
      Description => 'Tipo scena',
      PrintConv => {
        'Directly photographed' => 'Immagine fotografata direttamente',
      },
    },
   'School' => 'Scuola',
   'ScreenTips' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Section' => 'Sezione',
   'SectorSize' => 'Dimensione settore',
   'Security' => {
      Description => 'Sicurezza',
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'SecurityClassification' => {
      Description => 'Classificazione sicurezza',
      PrintConv => {
        'Confidential' => 'Confidenziale',
        'Restricted' => 'Riservato',
        'Secret' => 'Segreto',
        'Top Secret' => 'Massima segretezza',
        'Unclassified' => 'Non classificato',
      },
    },
   'SelectAFAreaSelectMode' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'SelectableAFPoint' => {
      PrintConv => {
        '11 points' => '11 punti',
      },
    },
   'Selected' => 'Selezionato',
   'SelfTimer' => {
      Description => 'Autoscatto',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SelfTimer2' => 'Autoscatto 2',
   'SelfTimerInterval' => 'Intervallo autoscatto',
   'SelfTimerMode' => 'Modo autoscatto',
   'SelfTimerTime' => 'Tempo autoscatto',
   'SenderAddress' => 'Indirizzo mittente',
   'SenderName' => 'Nome mittente',
   'SensingMethod' => {
      Description => 'Metodo misurazione esposimetrica',
      PrintConv => {
        'Color sequential area' => 'Area sequenziale a colori',
        'Color sequential linear' => 'Lineare sequenziale a colori',
        'Not defined' => 'Non definito',
        'One-chip color area' => 'Sensore area a colori a un chip',
        'Three-chip color area' => 'Sensore area a colori a tre chip',
        'Trilinear' => 'Trilineare',
        'Two-chip color area' => 'Sensore area a colori a due chip',
      },
    },
   'Sensitivity' => 'Sensibilità',
   'SensitivityCalibrated' => 'Sensibilità calibrata',
   'SensitivitySteps' => {
      Description => 'Passi sensibilità',
      PrintConv => {
        '1 EV Steps' => 'Step 1 EV',
      },
    },
   'SensitivityType' => {
      Description => 'Tipo sensibilità',
      PrintConv => {
        'ISO Speed' => 'Velocità ISO',
        'Recommended Exposure Index' => 'Indice esposizione raccomandato',
        'Recommended Exposure Index and ISO Speed' => 'Indice esposizione raccomandato e velocità ISO',
        'Standard Output Sensitivity' => 'Sensibilità predefinita uscita',
        'Standard Output Sensitivity and ISO Speed' => 'Sensibilità predefinita uscita e velocità ISO',
        'Standard Output Sensitivity and Recommended Exposure Index' => 'Sensibilità predefinita uscita e indice esposizione consigliato',
        'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed' => 'Sensibilità predefinita uscita, indice esposizione consigliato e velocità ISO',
        'Unknown' => 'Sconosciuto',
      },
    },
   'SensorAreas' => 'Aree sensore',
   'SensorCleaning' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'SensorHeight' => 'Altezza sensore',
   'SensorID' => 'ID sensore',
   'SensorLeftBorder' => 'Bordo sinistro sensore',
   'SensorMode' => 'Modo sensore',
   'SensorPixelSize' => 'Dimensione pixel sensore',
   'SensorRedLevel' => 'Livello rosso sensore',
   'SensorSize' => 'Dimensione sensore',
   'SensorTemperature' => 'Temperatura sensore',
   'SensorTopBorder' => 'Bordo superiore sensore',
   'SensorType' => 'Tipo sensore',
   'SensorTypeCode' => 'Codice tipo sensore',
   'SensorWidth' => 'Larghezza sensore',
   'Sequence' => 'Sequenza',
   'SequenceName' => 'Nome sequenza',
   'SequenceNumber' => {
      Description => 'Numero sequenza',
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SequenceShotInterval' => {
      PrintConv => {
        '10 frames/s' => '10 frame/s',
      },
    },
   'SequentialShot' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'SerialNumber' => 'Numero di serie',
   'SerialNumberFormat' => {
      Description => 'Formato numero di serie',
      PrintConv => {
        'Format 1' => 'Formato 1',
        'Format 2' => 'Formato 2',
      },
    },
   'ServiceID' => 'ID servizio',
   'ServiceIdentifier' => 'Identificativo servizio',
   'ServiceOrganizationName' => 'Nome organizzazione servizio',
   'SetButtonCrossKeysFunc' => {
      PrintConv => {
        'Normal' => 'Normale',
      },
    },
   'SetButtonWhenShooting' => {
      PrintConv => {
        'Change ISO speed' => 'Cambia velocità ISO',
        'Change parameters' => 'Cambia parametri',
        'ISO speed' => 'Velocità ISO',
        'Normal (disabled)' => 'Normale (disabilitato)',
        'White balance' => 'Bilanciamento del bianco',
      },
    },
   'SetCookie' => 'Imposta cookie',
   'SetFunctionWhenShooting' => {
      PrintConv => {
        'Change Parameters' => 'Cambia parametri',
      },
    },
   'SetInfo' => 'Imposta info',
   'SetSubtitle' => 'Imposta sottotitoli',
   'ShadingCompensation' => {
      Description => 'Compensazione ombreggiatura',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ShadingCompensation2' => {
      Description => 'Compensazione ombreggiatura 2',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Shadow' => 'Ombra',
   'ShadowCorrection' => {
      PrintConv => {
        'Normal' => 'Normale',
        'Off' => 'Spento',
        'Strong' => 'Forte',
      },
    },
   'ShadowScale' => 'Scala ombre',
   'Shadows' => 'Ombre',
   'ShakeReduction' => {
      PrintConv => {
        'Off' => 'Spento',
        'Off (4)' => 'Spento (4)',
      },
    },
   'SharedData' => 'Dati condivisi',
   'Sharpening' => {
      PrintConv => {
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Off' => 'Spento',
      },
    },
   'Sharpness' => {
      Description => 'Nitidezza',
      PrintConv => {
        'Hard' => 'Forte',
        'Normal' => 'Normale',
        'Soft' => 'Leggera',
        'n/a' => 'n/d',
      },
    },
   'SharpnessFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessFrequency' => {
      PrintConv => {
        'Low' => 'Basso',
        'n/a' => 'n/d',
      },
    },
   'SharpnessLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessMonochrome' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessPortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessUnknown' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessUserDef1' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessUserDef2' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'SharpnessUserDef3' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ShootingMode' => {
      Description => 'Modo scatto',
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
        'Fireworks' => 'Fuochi artificiali',
        'Manual' => 'Manuale',
        'Normal' => 'Normale',
        'Portrait' => 'Verticale',
        'Program' => 'Programma',
        'Scenery' => 'Paesaggio',
        'Shutter Priority' => 'Priorità otturatore',
        'Sunset' => 'Tramonto',
      },
    },
   'ShootingModeSetting' => {
      Description => 'Impostazione modo scatto',
      PrintConv => {
        'Self-timer' => 'Autoscatto',
      },
    },
   'ShortComment' => 'Commento breve',
   'ShortDescription' => 'Descrizione breve',
   'ShortDocumentID' => 'ID documento breve',
   'ShortReleaseTimeLag' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ShotComment' => 'Commento scatto',
   'ShotCommentKind' => 'Tipo commento scatto',
   'ShotDate' => 'Data scatto',
   'ShotDay' => 'Giorno scatto',
   'ShotDescription' => 'Descrizione scatto',
   'ShotDuration' => 'Durata scatto',
   'ShotInfoVersion' => 'Info versione scatto',
   'ShotList' => 'Elenco scatti',
   'ShotLocation' => 'Posizione scatto',
   'ShotLocationSets' => 'Insiemi posizione scatto',
   'ShotName' => 'Nome scatto',
   'ShotNumber' => 'Numero scatto',
   'ShotSize' => 'Dimensione scatto',
   'ShutterCount' => 'Conteggio scatti',
   'ShutterMode' => {
      PrintConv => {
        'Aperture Priority' => 'Priorità diaframma',
      },
    },
   'ShutterReleaseButtonAE-L' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ShutterReleaseNoCFCard' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ShutterSpeed' => 'Tempo esposizione',
   'ShutterSpeedRange' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ShutterSpeedValue' => 'Velocità otturatore',
   'SideNumber' => 'Numero lato',
   'Sidebars' => 'Barre laterali',
   'SimilarityIndex' => 'Indice di Somiglianza',
   'SingleFrameBracketing' => {
      PrintConv => {
        'Low' => 'Basso',
      },
    },
   'Site' => 'Sito',
   'Size' => 'Dimensioni',
   'SlideShow' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'SlowShutter' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'SlowSync' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SmartRange' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SmileShutter' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Smoothness' => 'Arrotondamento',
   'Software' => 'Software utilizzato',
   'Source' => 'Origine',
   'SourceTitle' => 'Titolo fonte',
   'SourceTrackID' => 'ID traccia fonte',
   'SourceTrackIDs' => 'ID traccia fonte',
   'SourceType' => 'Tipo fonte',
   'SourceURL' => 'URL fonte',
   'SourceValue' => 'Valore fonte',
   'SpatialFrequencyResponse' => 'Risposta in frequenza spaziale',
   'SpecialBuild' => 'Compilazione speciale',
   'SpecialEffectMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SpecialEffectSetting' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SpecialEffectsOpticalFilter' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Star' => 'Stella',
      },
    },
   'SpecialInstructions' => 'Istruzioni',
   'SpectralSensitivity' => 'Sensibilità spettro',
   'Speed' => {
      Description => 'Velocità',
      PrintConv => {
        'Fast' => 'Veloce',
        'Medium' => 'Medio',
        'Slow' => 'Lento',
      },
    },
   'SpotMeteringMode' => {
      PrintConv => {
        'Center' => 'Centro',
      },
    },
   'StandardOutputSensitivity' => 'Sensibilità predefinita uscita',
   'StartTime' => 'Ora inizio',
   'State' => 'Stato',
   'StorageMethod' => {
      PrintConv => {
        'Linear' => 'Lineare',
      },
    },
   'StreamType' => {
      PrintConv => {
        'File Transfer' => 'Trasferimento file',
        'Text' => 'Testo',
      },
    },
   'StretchMode' => {
      PrintConv => {
        'Fixed length' => 'Lunghezza fissa',
      },
    },
   'StripByteCounts' => 'Byte per striscia',
   'StripOffsets' => 'Offset striscia',
   'StripRowCounts' => 'Numero righe striscia',
   'Sub-location' => 'Località',
   'SubSecCreateDate' => 'Data di creazione',
   'SubSecDateTimeOriginal' => 'Data/ora originali',
   'SubSecModifyDate' => 'Data modifica',
   'SubSecTime' => 'Sottosecondi ora',
   'SubSecTimeDigitized' => 'Sottosecondi ora digitalizzazione',
   'SubSecTimeOriginal' => 'Sottosecondi ora originale',
   'SubTileBlockSize' => 'Dimensione blocco sotto-tasselli',
   'SubfileType' => {
      Description => 'Tipo sotto-file',
      PrintConv => {
        'Full-resolution image' => 'Immagine con risoluzione originale',
        'Reduced-resolution image' => 'Immagine a risoluzione ridotta',
        'Single page of multi-page image' => 'Singola pagina di un\'immagine multi-pagina',
        'Single page of multi-page reduced-resolution image' => 'Singola pagina di un\'immagine multi-pagina a risoluzione ridotta',
        'TIFF-FX mixed raster content' => 'Contenuto raster misto TIFF-FX',
        'TIFF/IT final page' => 'Pagina finale TIFF/IT',
        'Thumbnail image' => 'Miniatura',
        'Transparency mask' => 'Maschera trasparenza',
        'Transparency mask of multi-page image' => 'Maschera trasparenza di immagine multi-pagina',
        'Transparency mask of reduced-resolution image' => 'Maschera trasparenza di immagine a risoluzione ridotta',
        'Transparency mask of reduced-resolution multi-page image' => 'Maschera trasparenza di immagine multi-pagina a risoluzione ridotta',
        'invalid' => 'non valido',
      },
    },
   'SubimageColor' => {
      PrintConv => {
        'RGB (uncalibrated)' => 'RGB (non calibrato)',
        'RGB with Opacity' => 'RGB con opacità',
        'RGB with Opacity (uncalibrated)' => 'RGB con opacità (non calibrato)',
      },
    },
   'Subject' => 'Soggetto',
   'SubjectArea' => 'Area soggetto',
   'SubjectCode' => 'Codice sottetto',
   'SubjectDistance' => 'Distanza soggetto',
   'SubjectDistanceRange' => {
      Description => 'Intervallo distanza soggetto',
      PrintConv => {
        'Close' => 'Vicino',
        'Distant' => 'Lontano',
        'Unknown' => 'Sconosciuto',
      },
    },
   'SubjectLocation' => 'Posizione soggetto',
   'SubjectName' => 'Nome soggetto',
   'SubjectProgram' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Portrait' => 'Verticale',
        'Sunset' => 'Tramonto',
        'Text' => 'Testo',
      },
    },
   'SubjectReference' => 'Codice Soggetto',
   'SubjectUnits' => {
      PrintConv => {
        'radians' => 'Radianti',
      },
    },
   'Subsystem' => {
      Description => 'Sottosistema',
      PrintConv => {
        'EFI ROM' => 'ROM EFI',
        'EFI application' => 'Applicazione EFI',
        'EFI boot service' => 'Servizio di avvio EFI',
        'EFI runtime driver' => 'Driver a runtine EFI',
        'Native' => 'Nativo',
        'OS/2 command line' => 'Linea di comando OS/2',
        'POSIX command line' => 'Linea di comando POSIX',
        'Unknown' => 'Sconosciuto',
        'Windows command line' => 'Linea di comando Windows',
      },
    },
   'SubsystemVersion' => 'Versione sottosistema',
   'Subtitle' => 'Sottotitolo',
   'Suffix' => 'Suffisso',
   'SuggestedPalette' => 'Tavolozza suggerita',
   'Summary' => 'Sommario',
   'SuperMacro' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SuperimposedDisplay' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'SupplementalCategories' => 'Categoria Supplementare',
   'SupplementalType' => {
      PrintConv => {
        'Main Image' => 'Non impostato',
        'Rasterized Caption' => 'Didascalia rasterizzata',
        'Reduced Resolution Image' => 'Risoluzione immagine ridotta',
      },
    },
   'SurroundMode' => {
      PrintConv => {
        'Not Dolby surround' => 'Non Dolby surround',
        'Not indicated' => 'Non indicato',
      },
    },
   'SweepPanoramaDirection' => {
      Description => 'Direzione panoramica ad arco',
      PrintConv => {
        'Left' => 'Sinistra',
        'Right' => 'Destra',
      },
    },
   'SweepPanoramaSize' => 'Dimensione panoramica ad arco',
   'SwitchToRegisteredAFPoint' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'SynchronizedLyricText' => 'Testo sincronizzato',
   'SystemNameOrNumber' => 'Nome/numero sistema',
   'T4Options' => {
      Description => 'Opzioni T4',
      PrintConv => {
        '2-Dimensional encoding' => 'Codifica 2D',
        'Fill bits added' => 'Bit di riempimento aggiunti',
        'Uncompressed' => 'Non compresso',
      },
    },
   'T6Options' => {
      Description => 'Opzioni T6',
      PrintConv => {
        'Uncompressed' => 'Non compresso',
      },
    },
   'T82Options' => 'Opzioni T82',
   'T88Options' => 'Opzioni T88',
   'TIFFPreview' => 'Anteprima TIFF',
   'TIFFSummary' => 'Sommario TIFF',
   'TIFF_FXExtensions' => {
      Description => 'Estensioni TIFF FX',
      PrintConv => {
        'B&W JBIG2' => 'JBIG2 bianco e nero',
        'JBIG2 Profile M' => 'JBIG2 TIFF FX',
        'N Layer Profile M' => 'Livello N profilo M',
        'Resolution/Image Width' => 'Risoluzione/larghezza immagine',
        'Shared Data' => 'Dati condivisi',
      },
    },
   'Tagged' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'TaggingTime' => 'Ora tag',
   'TargetDeltaType' => {
      PrintConv => {
        'Absolute' => 'Assoluto',
      },
    },
   'TargetPrinter' => 'Stampante di destinazione',
   'Technology' => {
      Description => 'Tecnologia',
      PrintConv => {
        'Active Matrix Display' => 'Display a matrice attiva',
        'Cathode Ray Tube Display' => 'Monitor con Tubo a Raggi Catodici',
        'Digital Camera' => 'Fotocamera Digitale',
        'Dye Sublimation Printer' => 'Stampante a Sublimazione Termica',
        'Electrophotographic Printer' => 'Stampante Laser',
        'Electrostatic Printer' => 'Stampante Elettrostatica',
        'Film Scanner' => 'Scanner per Pellicola',
        'Ink Jet Printer' => 'Stampante Ink Jet',
        'Offset Lithography' => 'Litografía Offset',
        'Passive Matrix Display' => 'Display a Matrice passiva',
        'Photographic Paper Printer' => 'Stampante per Carta Fotografica',
        'Projection Television' => 'Proiettore televisivo',
        'Reflective Scanner' => 'Scanner a riflessione',
        'Thermal Wax Printer' => 'Stampante Thermal Wax',
      },
    },
   'Teleconverter' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'Temperature' => 'Temperatura',
   'Template' => 'Modello',
   'TermsOfUse' => 'Termini di utilizzo',
   'TestTarget' => {
      PrintConv => {
        'Grayscale' => 'Scala di grigi',
      },
    },
   'Text' => 'Testo',
   'TextColor' => 'Colore testo',
   'TextComments' => 'Testo commenti',
   'TextEncoding' => {
      Description => 'Codifica testo',
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'TextFace' => {
      Description => 'Aspetto testo',
      PrintConv => {
        'Extend' => 'Estendi',
        'Italic' => 'Corsivo',
        'Shadow' => 'Ombra',
      },
    },
   'TextFont' => {
      Description => 'Carattere testo',
      PrintConv => {
        'System' => 'Sistema',
      },
    },
   'TextLayers' => 'Livelli testo',
   'TextSize' => 'Dimenione testo',
   'TextStamp' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'TextString' => 'Stringa testo',
   'TextValue' => 'Valore testo',
   'Thresholding' => {
      Description => 'Soglia',
      PrintConv => {
        'No dithering or halftoning' => 'Nessun dithering o resa a mezzi toni',
        'Randomized dither' => 'Dither casuale',
      },
    },
   'ThumbnailFileName' => 'Nome file miniatura',
   'ThumbnailFormat' => 'Formato miniatura',
   'ThumbnailHeight' => 'Miniatura',
   'ThumbnailImage' => 'Miniatura',
   'ThumbnailImageName' => 'Nome miniatura',
   'ThumbnailImageSize' => 'Dimensione miniatura',
   'ThumbnailImageType' => 'Tipo miniatura',
   'ThumbnailImageValidArea' => 'Area valida miniatura',
   'ThumbnailLength' => 'Dimensioni miniatura',
   'ThumbnailOffset' => 'Offset miniatura',
   'ThumbnailWidth' => 'Larghezza miniatura',
   'Thumbnails' => 'Miniature',
   'TileByteCounts' => 'Byte tassello',
   'TileDepth' => 'Profondità tassello',
   'TileLength' => 'Lunghezza tassello',
   'TileOffsets' => 'Offset tasselli',
   'TileWidth' => 'Larghezza tassello',
   'Tiles' => 'Tasselli',
   'Time' => 'Ora',
   'TimeCreated' => 'Ora di Creazione',
   'TimeSent' => 'Ora d\'invio',
   'TimeStamp' => 'Marcatura oraria',
   'TimeStamp1' => 'Marcatura oraria 1',
   'TimeZone' => 'Fuso orario',
   'TimeZoneCity' => {
      Description => 'Città fuso orario',
      PrintConv => {
        'London' => 'Londra',
        'Tehran' => 'Teheran',
        'n/a' => 'n/d',
      },
    },
   'TimeZoneCode' => 'Codice fuso orario',
   'TimeZoneInfo' => 'Info fuso orario',
   'TimeZoneOffset' => 'Scostamento fuso orario',
   'TimerFunctionButton' => {
      PrintConv => {
        'Self-timer' => 'Autoscatto',
        'Shooting Mode' => 'Modo scatto',
        'White Balance' => 'Bilanciamento del bianco',
      },
    },
   'TimerLength' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'Tint' => 'Tinta',
   'Title' => 'Titolo',
   'Title2' => 'Titolo 2',
   'TitleKind' => 'Tipo titolo',
   'TitleLen' => 'Lungh titolo',
   'TitleNum' => 'Num titolo',
   'TitleOfParts' => 'Titolo parti',
   'TitleSortOrder' => 'Ordinamento titolo',
   'TitlesOfParts' => 'Titoli parti',
   'TitlesSets' => 'Insiemi di titoli',
   'ToDoTitle' => 'Titolo da fare',
   'ToneComp' => 'Compensazione Tono',
   'ToneCurve' => {
      PrintConv => {
        'Manual' => 'Manuale',
      },
    },
   'ToneCurveActive' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'ToneCurveName' => {
      PrintConv => {
        'Linear' => 'Lineare',
        'Strong Contrast' => 'Contrasto elevato',
      },
    },
   'ToneCurveProperty' => {
      PrintConv => {
        'Linear' => 'Lineare',
        'Shot Settings' => 'Impostazioni scatto',
      },
    },
   'ToningEffect' => {
      PrintConv => {
        'Blue-green' => 'Blu-Verde',
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Purple-blue' => 'Porpora-blu',
        'Red' => 'Rosso',
        'Red-purple' => 'Rosso-porpora',
        'Sepia' => 'Seppia',
        'Yellow' => 'Giallo',
        'n/a' => 'n/d',
      },
    },
   'ToningEffectFaithful' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ToningEffectLandscape' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ToningEffectMonochrome' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'ToningEffectNeutral' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ToningEffectPortrait' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ToningEffectStandard' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'ToningEffectUnknown' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'ToningEffectUserDef1' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'ToningEffectUserDef2' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'ToningEffectUserDef3' => {
      PrintConv => {
        'Green' => 'Verde',
        'None' => 'Nessuno',
        'Purple' => 'Porpora',
        'Sepia' => 'Seppia',
        'n/a' => 'n/d',
      },
    },
   'ToolName' => 'Nome strumento',
   'ToolVersion' => 'Versione strumento',
   'TotalFrames' => 'Frame totali',
   'Track' => 'Traccia',
   'TrackProperty' => {
      PrintConv => {
        'Read only' => 'Sola lettura',
      },
    },
   'TransferFunction' => 'Funzione di trasferimento',
   'TransferRange' => 'Intervallo di trasferimento',
   'Transform' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Transformation' => {
      Description => 'Trasformazione',
      PrintConv => {
        'Horizontal (normal)' => 'Orizzontale (normale)',
        'Mirror horizontal' => 'Rifletti orizzontalmente',
        'Mirror horizontal and rotate 270 CW' => 'Rifletti orizzontalmente e ruota di 270° in senso orario',
        'Mirror horizontal and rotate 90 CW' => 'Rifletti orizzontalmente e ruota di 90° in senso orario',
        'Mirror vertical' => 'Rifletti verticalmente',
        'Rotate 180' => 'Ruota di 180°',
        'Rotate 270 CW' => 'Ruota di 270° in senso orario',
        'Rotate 90 CW' => 'Ruota di 90° senso orario',
      },
    },
   'TransmissionReference' => 'Riferimento trasmissione',
   'TransparencyIndicator' => 'Indicatore trasparenza',
   'TrapIndicator' => 'Indicatore trap',
   'Trapped' => {
      PrintConv => {
        'False' => 'Falso',
        'Unknown' => 'Sconosciuto',
      },
    },
   'TungstenAWB' => {
      PrintConv => {
        'Strong Correction' => 'Correzione elevata',
      },
    },
   'TxFace' => 'Stile carattere testo',
   'TxFont' => 'Numero carattere',
   'TxSize' => 'Dimenione testo',
   'UIC1Tag' => 'Tag UIC1',
   'UIC2Tag' => 'Tag UIC2',
   'UIC3Tag' => 'Tag UIC3',
   'UIC4Tag' => 'Tag UIC4',
   'USPTOOriginalContentType' => {
      PrintConv => {
        'Color' => 'Colore',
        'Grayscale' => 'Scala di grigi',
        'Text or Drawing' => 'Testo o disegno',
      },
    },
   'UV-IRFilterCorrection' => {
      PrintConv => {
        'Not Active' => 'Non attivo',
      },
    },
   'Uncompressed' => {
      Description => 'Non compresso',
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'UninitializedDataSize' => 'Dimensione dati non inizializzati',
   'UniqueCameraModel' => 'Modello unico fotocamera',
   'UniqueDocumentID' => 'ID Unico del Documento',
   'Units' => 'Unità',
   'Unknown' => 'Sconosciuto',
   'Unsharp1Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'Unsharp2Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'Unsharp3Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'Unsharp4Color' => {
      PrintConv => {
        'Green' => 'Verde',
        'Red' => 'Rosso',
        'Yellow' => 'Giallo',
      },
    },
   'UnsharpMask' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Urgency' => {
      Description => 'Urgenza',
      PrintConv => {
        '0 (reserved)' => '0 (riservato)',
        '1 (most urgent)' => '1 (molto urgente)',
        '5 (normal urgency)' => '5 (urgenza normale)',
        '8 (least urgent)' => '8 (meno urgent)e',
        '9 (user-defined priority)' => '9 (riservato per usi futuri)',
      },
    },
   'UsableMeteringModes' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'UsableShootingModes' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'UserAccess' => {
      PrintConv => {
        'Print' => 'Stampa',
      },
    },
   'UserComment' => 'Commento utente',
   'UserDef1PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Portrait' => 'Verticale',
      },
    },
   'UserDef2PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Portrait' => 'Verticale',
      },
    },
   'UserDef3PictureStyle' => {
      PrintConv => {
        'Landscape' => 'Orizzontale',
        'Portrait' => 'Verticale',
      },
    },
   'UserDefinedText' => 'Testo personalizzato',
   'UserDefinedURL' => 'URL personalizzato',
   'VFDisplayIllumination' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'VRDVersion' => 'Versione VRD',
   'VRInfo' => 'Info Riduzione Vibrazione',
   'VRInfoVersion' => 'Info Versione VR',
   'VR_0x66' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'Value' => 'Valore',
   'VariousModes' => 'Modi vari',
   'VariousModes2' => 'Modi vari 2',
   'Version' => 'Numero versione immagine',
   'Version2' => 'Versione 2',
   'VersionBF' => 'Versione BF',
   'VersionCreateDate' => 'Data creazione versione',
   'VersionID' => 'ID versione',
   'VersionIdentifier' => 'Identificativo versione',
   'VersionInfo' => 'Info versione',
   'VersionModifyDate' => 'Data modifica versione',
   'VersionNumber' => 'Numero versione',
   'VersionNumberString' => 'Stringa numero versione',
   'VersionTitle' => 'Titolo versione',
   'VersionYear' => 'Anno versione',
   'Versions' => 'Versioni',
   'VerticalCSType' => {
      PrintConv => {
        'Caspian Sea' => 'Mar Caspio',
        'WGS 84 ellipsoid' => 'Ellissoide WGS 84',
      },
    },
   'VerticalUnits' => 'Unità verticali',
   'VibrationReduction' => {
      Description => 'Riduzione vibrazione',
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'VideoAlphaMode' => {
      PrintConv => {
        'None' => 'Nessuno',
      },
    },
   'VideoAttributes' => {
      PrintConv => {
        'Encrypted' => 'Crittografato',
      },
    },
   'VideoBitrate' => 'Bitrate video',
   'VideoCardGamma' => 'Gamma della Scheda Video',
   'VideoCodec' => 'Codec video',
   'VideoCodecDescription' => 'Descrizione codec video',
   'VideoCodecID' => 'ID codec video',
   'VideoCodecInfo' => 'Info codec video',
   'VideoCodecName' => 'Nome codec video',
   'VideoFieldOrder' => {
      PrintConv => {
        'Progressive' => 'Progressivo',
      },
    },
   'VideoQuality' => {
      PrintConv => {
        'Low' => 'Basso',
      },
    },
   'VideoScanType' => {
      PrintConv => {
        'Progressive' => 'Progressivo',
      },
    },
   'VideoStreamType' => {
      PrintConv => {
        'Reserved' => 'Riservato',
      },
    },
   'ViewInfoDuringExposure' => {
      PrintConv => {
        'Enable' => 'Abilita',
      },
    },
   'ViewfinderWarning' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ViewingCondDesc' => 'Descrizione Condizioni di Visualizzazione',
   'ViewingMode2' => {
      PrintConv => {
        'n/a' => 'n/d',
      },
    },
   'VignetteControl' => {
      PrintConv => {
        'Low' => 'Basso',
        'Normal' => 'Normale',
        'Off' => 'Spento',
      },
    },
   'VignettingCorrection' => {
      PrintConv => {
        'Off' => 'Spento',
        'n/a' => 'n/d',
      },
    },
   'VirtualImageHeight' => 'Altezza immagine virtuale',
   'VirtualImageWidth' => 'Larghezza immagine virtuale',
   'VirtualPageUnits' => 'Unità pagina virtuale',
   'VoiceMemo' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'WBAdjLighting' => {
      PrintConv => {
        'Daylight (cloudy)' => 'Luce del giorno (2)',
        'Daylight (direct sunlight)' => 'Luce del giorno (0)',
        'Daylight (shade)' => 'Luce del giorno (1)',
        'None' => 'Nessuno',
      },
    },
   'WBBracketMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'WBFineTuneActive' => {
      PrintConv => {
        'Yes' => 'Sì',
      },
    },
   'WCSProfiles' => 'Profilo Windows Color System',
   'WhiteBalance' => {
      Description => 'Bilanciamento del bianco',
      PrintConv => {
        'Auto' => 'Equilibrio del bianco automatico',
        'Black & White' => 'Monocromatico',
        'Cloudy' => 'Nuvoloso',
        'Color Temperature/Color Filter' => 'Temperatura colore / Filtro colore',
        'Cool White Fluorescent' => 'Fluorescente bianca fredda',
        'Custom' => 'Personalizzato',
        'Custom 1' => 'PERSONAL.1',
        'Custom 2' => 'PERSONAL.2',
        'Custom 3' => 'PERSONAL.3',
        'Custom 4' => 'PERSONAL.4',
        'Day White Fluorescent' => 'Fluorescente a luce del giorno bianca',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Fluorescent' => 'Fluorescente',
        'Manual' => 'Manuale',
        'Shade' => 'Ombrato',
        'Tungsten' => 'Tungsteno (luce incandescente)',
        'Unknown' => 'Sconosciuto',
        'Warm White Fluorescent' => 'Luce fluorescente bianca calda',
        'White Fluorescent' => 'Fluorescente bianca',
      },
    },
   'WhiteBalance2' => 'Bilanciamento del bianco 2',
   'WhiteBalanceAdj' => {
      Description => 'Adattamento bilanciamento bianco',
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Daylight' => 'Luce del giorno',
        'Fluorescent' => 'Fluorescente',
        'Off' => 'Spento',
        'Shade' => 'Ombrato',
        'Shot Settings' => 'Impostazioni scatto',
        'Tungsten' => 'Tungsteno (luce incandescente)',
      },
    },
   'WhiteBalanceAutoAdjustment' => {
      Description => 'Adattamento automatico bilanciamento bianco',
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'WhiteBalanceBracketing' => {
      PrintConv => {
        'Low' => 'Basso',
        'Off' => 'Spento',
      },
    },
   'WhiteBalanceFineTune' => 'Regolazione Fine Bilanciamento Bianco',
   'WhiteBalanceMode' => {
      PrintConv => {
        'Unknown' => 'Sconosciuto',
      },
    },
   'WhiteBalanceSet' => {
      PrintConv => {
        'Cloudy' => 'Nuvoloso',
        'Daylight' => 'Luce del giorno',
        'Daylight Fluorescent' => 'Fluorescente a luce del giorno',
        'Manual' => 'Manuale',
        'Shade' => 'Ombrato',
        'Tungsten' => 'Tungsteno (luce incandescente)',
        'White Fluorescent' => 'Fluorescente bianca',
      },
    },
   'WhiteBalanceSetting' => {
      PrintConv => {
        'Fluorescent (+1)' => 'Fluorescente (+1)',
        'Fluorescent (+2)' => 'Fluorescente (+2)',
        'Fluorescent (+3)' => 'Fluorescente (+3)',
        'Fluorescent (-1)' => 'Fluorescente (-1)',
        'Fluorescent (-2)' => 'Fluorescente (-2)',
        'Fluorescent (-3)' => 'Fluorescente (-3)',
        'Fluorescent (0)' => 'Fluorescente (0)',
        'Shade (+1)' => 'Ombrato (+1)',
        'Shade (+2)' => 'Ombrato (+2)',
        'Shade (+3)' => 'Ombrato (+3)',
        'Shade (-1)' => 'Ombrato (-1)',
        'Shade (-2)' => 'Ombrato (-2)',
        'Shade (-3)' => 'Ombrato (-3)',
        'Shade (0)' => 'Ombrato (0)',
      },
    },
   'WhiteLevel' => 'Livello bianco',
   'WhitePoint' => 'Punto bianco',
   'WideRange' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'WidthResolution' => 'Risoluzione larghezza',
   'Writer-Editor' => 'Autore Didascalia/Descrizione',
   'XMethod' => {
      PrintConv => {
        'No Magnification' => 'Nessun ingrandimento',
      },
    },
   'XPAuthor' => 'Autore',
   'XPComment' => 'Commento XP',
   'XPKeywords' => 'Parole chiave XP',
   'XPSubject' => 'Soggetto XP',
   'XPTitle' => 'Titolo XP',
   'XPosition' => 'Posizione X',
   'XResolution' => 'Risoluzione orizzontale immagine',
   'YCbCrCoefficients' => 'Coefficienti matrice trasformazione spazio colori',
   'YCbCrPositioning' => {
      Description => 'Posizionamento Y e C',
      PrintConv => {
        'Centered' => 'Centrato',
        'Co-sited' => 'Affiancato',
      },
    },
   'YCbCrSubSampling' => 'Indice sottocampionamento da Y a C',
   'YMethod' => {
      PrintConv => {
        'No Magnification' => 'Nessun ingrandimento',
      },
    },
   'YPosition' => 'Posizione Y',
   'YResolution' => 'Risoluzione verticale immagine',
   'Year' => 'Anno',
   'ZipCompression' => {
      PrintConv => {
        'None' => 'Nessuno',
        'Reduced with compression factor 1' => 'Ridotto con fattore compressione 1',
        'Reduced with compression factor 2' => 'Ridotto con fattore compressione 2',
        'Reduced with compression factor 3' => 'Ridotto con fattore compressione 3',
        'Reduced with compression factor 4' => 'Ridotto con fattore compressione 4',
      },
    },
   'ZoneMatching' => {
      Description => 'Adeguamento zona',
      PrintConv => {
        'High Key' => 'Hi',
        'ISO Setting Used' => 'Impostazione ISO usata',
        'Low Key' => 'Lo',
      },
    },
   'ZoneMatchingMode' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ZoneMatchingOn' => {
      PrintConv => {
        'Off' => 'Spento',
      },
    },
   'ZoomCenter' => 'Centro zoom',
   'ZoomFactor' => 'Fattore di zoom',
   'ZoomPos' => 'Posizione zoom',
   'iTunesU' => 'ITunes U',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::it.pm - ExifTool Italian language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Ferdinando Agovino, Emilio Dati and Michele Locati
for providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
