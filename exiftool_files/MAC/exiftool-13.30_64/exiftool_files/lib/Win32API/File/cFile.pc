# Generated by cFile_pc.cxx.
# Package Win32API::File with options:
#    CPLUSPLUS => q[1]
#    IFDEF => q[!/[a-z\d]/]
#    IMPORT_LIST => [q[/._/], q[!/[a-z]/], q[:MEDIA_TYPE]]
#    WRITE_PERL => q[1]
# Perl files eval'd:
#    File.pm => last if /^\s*(bootstrap|XSLoader::load)\b/
# C files included:
#    File.xs => last if m#/[/*]\s*CONSTS_DEFINED\b|^\s*MODULE\b#
sub CREATE_ALWAYS () { 2 }
sub CREATE_NEW () { 1 }
sub DDD_EXACT_MATCH_ON_REMOVE () { 4 }
sub DDD_RAW_TARGET_PATH () { 1 }
sub DDD_REMOVE_DEFINITION () { 2 }
sub DRIVE_CDROM () { 5 }
sub DRIVE_FIXED () { 3 }
sub DRIVE_NO_ROOT_DIR () { 1 }
sub DRIVE_RAMDISK () { 6 }
sub DRIVE_REMOTE () { 4 }
sub DRIVE_REMOVABLE () { 2 }
sub DRIVE_UNKNOWN () { 0 }
sub F3_120M_512 () { 13 }
sub F3_1Pt44_512 () { 2 }
sub F3_20Pt8_512 () { 4 }
sub F3_2Pt88_512 () { 3 }
sub F3_720_512 () { 5 }
sub F5_160_512 () { 10 }
sub F5_180_512 () { 9 }
sub F5_1Pt2_512 () { 1 }
sub F5_320_1024 () { 8 }
sub F5_320_512 () { 7 }
sub F5_360_512 () { 6 }
sub FILE_ADD_FILE () { 2 }
sub FILE_ADD_SUBDIRECTORY () { 4 }
sub FILE_ALL_ACCESS () { 2032127 }
sub FILE_APPEND_DATA () { 4 }
sub FILE_ATTRIBUTE_ARCHIVE () { 32 }
sub FILE_ATTRIBUTE_COMPRESSED () { 2048 }
sub FILE_ATTRIBUTE_DEVICE () { 0x00000040 }
sub FILE_ATTRIBUTE_DIRECTORY () { 0x00000010 }
sub FILE_ATTRIBUTE_ENCRYPTED () { 0x00004000 }
sub FILE_ATTRIBUTE_HIDDEN () { 2 }
sub FILE_ATTRIBUTE_NORMAL () { 128 }
sub FILE_ATTRIBUTE_NOT_CONTENT_INDEXED () { 0x00002000 }
sub FILE_ATTRIBUTE_OFFLINE () { 4096 }
sub FILE_ATTRIBUTE_READONLY () { 1 }
sub FILE_ATTRIBUTE_REPARSE_POINT () { 0x00000400 }
sub FILE_ATTRIBUTE_SPARSE_FILE () { 0x00000200 }
sub FILE_ATTRIBUTE_SYSTEM () { 4 }
sub FILE_ATTRIBUTE_TEMPORARY () { 256 }
sub FILE_BEGIN () { 0 }
sub FILE_CREATE_PIPE_INSTANCE () { 4 }
sub FILE_CURRENT () { 1 }
sub FILE_DELETE_CHILD () { 64 }
sub FILE_END () { 2 }
sub FILE_EXECUTE () { 32 }
sub FILE_FLAG_BACKUP_SEMANTICS () { 33554432 }
sub FILE_FLAG_DELETE_ON_CLOSE () { 67108864 }
sub FILE_FLAG_NO_BUFFERING () { 536870912 }
sub FILE_FLAG_OPEN_REPARSE_POINT () { 0x200000 }
sub FILE_FLAG_OVERLAPPED () { 1073741824 }
sub FILE_FLAG_POSIX_SEMANTICS () { 16777216 }
sub FILE_FLAG_RANDOM_ACCESS () { 268435456 }
sub FILE_FLAG_SEQUENTIAL_SCAN () { 134217728 }
sub FILE_FLAG_WRITE_THROUGH () { 0x80000000 }
sub FILE_GENERIC_EXECUTE () { 1179808 }
sub FILE_GENERIC_READ () { 1179785 }
sub FILE_GENERIC_WRITE () { 1179926 }
sub FILE_LIST_DIRECTORY () { 1 }
sub FILE_READ_ATTRIBUTES () { 128 }
sub FILE_READ_DATA () { 1 }
sub FILE_READ_EA () { 8 }
sub FILE_SHARE_DELETE () { 4 }
sub FILE_SHARE_READ () { 1 }
sub FILE_SHARE_WRITE () { 2 }
sub FILE_TRAVERSE () { 32 }
sub FILE_TYPE_CHAR () { 2 }
sub FILE_TYPE_DISK () { 1 }
sub FILE_TYPE_PIPE () { 3 }
sub FILE_TYPE_UNKNOWN () { 0 }
sub FILE_WRITE_ATTRIBUTES () { 256 }
sub FILE_WRITE_DATA () { 2 }
sub FILE_WRITE_EA () { 16 }
sub FS_CASE_IS_PRESERVED () { 2 }
sub FS_CASE_SENSITIVE () { 1 }
sub FS_FILE_COMPRESSION () { 16 }
sub FS_PERSISTENT_ACLS () { 8 }
sub FS_UNICODE_STORED_ON_DISK () { 4 }
sub FS_VOL_IS_COMPRESSED () { 32768 }
sub FSCTL_SET_REPARSE_POINT () { (9 << 16 | 0 << 14 | 41 << 2 | 0) }
sub FSCTL_GET_REPARSE_POINT () { (9 << 16 | 0 << 14 | 42 << 2 | 0) }
sub FSCTL_DELETE_REPARSE_POINT () { (9 << 16 | 0 << 14 | 43 << 2 | 0) }
sub FixedMedia () { 12 }
sub GENERIC_ALL () { 268435456 }
sub GENERIC_EXECUTE () { 536870912 }
sub GENERIC_READ () { 0x80000000 }
sub GENERIC_WRITE () { 1073741824 }
sub HANDLE_FLAG_INHERIT () { 1 }
sub HANDLE_FLAG_PROTECT_FROM_CLOSE () { 2 }
sub INVALID_FILE_ATTRIBUTES () { 0xFFFFFFFF }
sub INVALID_HANDLE_VALUE () { 0xffffffff }
sub IOCTL_DISK_FORMAT_TRACKS () { 507928 }
sub IOCTL_DISK_FORMAT_TRACKS_EX () { 507948 }
sub IOCTL_DISK_GET_DRIVE_GEOMETRY () { 458752 }
sub IOCTL_DISK_GET_DRIVE_LAYOUT () { 475148 }
sub IOCTL_DISK_GET_MEDIA_TYPES () { 461824 }
sub IOCTL_DISK_GET_PARTITION_INFO () { 475140 }
sub IOCTL_DISK_HISTOGRAM_DATA () { 458804 }
sub IOCTL_DISK_HISTOGRAM_RESET () { 458808 }
sub IOCTL_DISK_HISTOGRAM_STRUCTURE () { 458800 }
sub IOCTL_DISK_IS_WRITABLE () { 458788 }
sub IOCTL_DISK_LOGGING () { 458792 }
sub IOCTL_DISK_PERFORMANCE () { 458784 }
sub IOCTL_DISK_REASSIGN_BLOCKS () { 507932 }
sub IOCTL_DISK_REQUEST_DATA () { 458816 }
sub IOCTL_DISK_REQUEST_STRUCTURE () { 458812 }
sub IOCTL_DISK_SET_DRIVE_LAYOUT () { 507920 }
sub IOCTL_DISK_SET_PARTITION_INFO () { 507912 }
sub IOCTL_DISK_VERIFY () { 458772 }
sub IOCTL_STORAGE_CHECK_VERIFY () { 2967552 }
sub IOCTL_STORAGE_EJECT_MEDIA () { 2967560 }
sub IOCTL_STORAGE_FIND_NEW_DEVICES () { 2967576 }
sub IOCTL_STORAGE_GET_MEDIA_TYPES () { 2952192 }
sub IOCTL_STORAGE_LOAD_MEDIA () { 2967564 }
sub IOCTL_STORAGE_MEDIA_REMOVAL () { 2967556 }
sub IOCTL_STORAGE_RELEASE () { 2967572 }
sub IOCTL_STORAGE_RESERVE () { 2967568 }
sub MOVEFILE_COPY_ALLOWED () { 2 }
sub MOVEFILE_DELAY_UNTIL_REBOOT () { 4 }
sub MOVEFILE_REPLACE_EXISTING () { 1 }
sub MOVEFILE_WRITE_THROUGH () { 8 }
sub OPEN_ALWAYS () { 4 }
sub OPEN_EXISTING () { 3 }
sub PARTITION_ENTRY_UNUSED () { 0 }
sub PARTITION_EXTENDED () { 5 }
sub PARTITION_FAT32 () { 11 }
sub PARTITION_FAT32_XINT13 () { 12 }
sub PARTITION_FAT_12 () { 1 }
sub PARTITION_FAT_16 () { 4 }
sub PARTITION_HUGE () { 6 }
sub PARTITION_IFS () { 7 }
sub PARTITION_NTFT () { 128 }
sub PARTITION_PREP () { 65 }
sub PARTITION_UNIX () { 99 }
sub PARTITION_XENIX_1 () { 2 }
sub PARTITION_XENIX_2 () { 3 }
sub PARTITION_XINT13 () { 14 }
sub PARTITION_XINT13_EXTENDED () { 15 }
sub RemovableMedia () { 11 }
sub SECURITY_ANONYMOUS () { 0 }
sub SECURITY_CONTEXT_TRACKING () { 262144 }
sub SECURITY_DELEGATION () { 196608 }
sub SECURITY_EFFECTIVE_ONLY () { 524288 }
sub SECURITY_IDENTIFICATION () { 65536 }
sub SECURITY_IMPERSONATION () { 131072 }
sub SECURITY_SQOS_PRESENT () { 1048576 }
sub SEM_FAILCRITICALERRORS () { 1 }
sub SEM_NOALIGNMENTFAULTEXCEPT () { 4 }
sub SEM_NOGPFAULTERRORBOX () { 2 }
sub SEM_NOOPENFILEERRORBOX () { 32768 }
sub TRUNCATE_EXISTING () { 5 }
sub Unknown () { 0 }
sub VALID_NTFT () { 192 }
sub STD_ERROR_HANDLE () { 0xfffffff4 }
sub STD_INPUT_HANDLE () { 0xfffffff6 }
sub STD_OUTPUT_HANDLE () { 0xfffffff5 }
1;
