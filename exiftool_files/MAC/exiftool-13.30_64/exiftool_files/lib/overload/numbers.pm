# -*- buffer-read-only: t -*-
#
#    lib/overload/numbers.pm
#
#    Copyright (C) 2008 by <PERSON> and others
#
#    You may distribute under the terms of either the GNU General Public
#    License or the Artistic License, as specified in the README file.
#
# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is built by regen/overload.pl.
# Any changes made here will be lost!

package overload::numbers;

our @names = qw#
    ()
    (${}
    (@{}
    (%{}
    (*{}
    (&{}
    (++
    (--
    (bool
    (0+
    (""
    (!
    (=
    (abs
    (neg
    (<>
    (int
    (<
    (<=
    (>
    (>=
    (==
    (!=
    (lt
    (le
    (gt
    (ge
    (eq
    (ne
    (nomethod
    (+
    (+=
    (-
    (-=
    (*
    (*=
    (/
    (/=
    (%
    (%=
    (**
    (**=
    (<<
    (<<=
    (>>
    (>>=
    (&
    (&=
    (&.
    (&.=
    (|
    (|=
    (|.
    (|.=
    (^
    (^=
    (^.
    (^.=
    (<=>
    (cmp
    (~
    (~.
    (atan2
    (cos
    (sin
    (exp
    (log
    (sqrt
    (x
    (x=
    (.
    (.=
    (~~
    (-X
    (qr
#;

our @enums = qw#
    fallback
    to_sv
    to_av
    to_hv
    to_gv
    to_cv
    inc
    dec
    bool_
    numer
    string
    not
    copy
    abs
    neg
    iter
    int
    lt
    le
    gt
    ge
    eq
    ne
    slt
    sle
    sgt
    sge
    seq
    sne
    nomethod
    add
    add_ass
    subtr
    subtr_ass
    mult
    mult_ass
    div
    div_ass
    modulo
    modulo_ass
    pow
    pow_ass
    lshift
    lshift_ass
    rshift
    rshift_ass
    band
    band_ass
    sband
    sband_ass
    bor
    bor_ass
    sbor
    sbor_ass
    bxor
    bxor_ass
    sbxor
    sbxor_ass
    ncmp
    scmp
    compl
    scompl
    atan2
    cos
    sin
    exp
    log
    sqrt
    repeat
    repeat_ass
    concat
    concat_ass
    smart
    ftest
    regexp
#;

{ my $i = 0; our %names = map { $_ => $i++ } @names }

{ my $i = 0; our %enums = map { $_ => $i++ } @enums }

# ex: set ro:
