#!/usr/bin/env python3
"""
Demo script to showcase the new modern interface design inspired by the domotic assistant.
This demonstrates the card-based UI components and the new dark blue theme.
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QGridLayout, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# Add the project root to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from metadata_genie.ui.custom_widgets import CardWidget, StatusCard, ActionButton
from metadata_genie.utils.theme import apply_theme


class DemoWindow(QMainWindow):
    """
    Demo window showcasing the new interface design.
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Metadata Genie AI - Modern Interface Demo")
        self.setMinimumSize(1000, 700)
        
        # Apply the dark theme
        apply_theme("dark")
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the demo UI."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Title
        title = QLabel("Modern Interface Design Demo")
        title.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(18)
        font.setWeight(QFont.Bold)
        title.setFont(font)
        title.setStyleSheet("color: #5dade2; margin-bottom: 20px;")
        main_layout.addWidget(title)
        
        # Create scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.NoFrame)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(30)
        
        # Status Cards Section
        self.create_status_section(content_layout)
        
        # Feature Cards Section
        self.create_feature_section(content_layout)
        
        # Action Buttons Section
        self.create_action_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def create_status_section(self, layout):
        """Create the status cards section."""
        section_title = QLabel("Status Overview")
        section_title.setStyleSheet("color: #e8eaed; font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(section_title)
        
        status_layout = QHBoxLayout()
        status_layout.setSpacing(15)
        
        # Create status cards
        processed_card = StatusCard("Images Processed", "247", "", "success")
        pending_card = StatusCard("Pending", "12", "", "normal")
        errors_card = StatusCard("Errors", "3", "", "error")
        success_rate = StatusCard("Success Rate", "95", "%", "success")
        
        status_layout.addWidget(processed_card)
        status_layout.addWidget(pending_card)
        status_layout.addWidget(errors_card)
        status_layout.addWidget(success_rate)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
    
    def create_feature_section(self, layout):
        """Create the feature cards section."""
        section_title = QLabel("Features")
        section_title.setStyleSheet("color: #e8eaed; font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(section_title)
        
        # Create grid layout for feature cards
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)
        
        # Feature cards
        features = [
            ("AI Analysis", "Automatic metadata generation using advanced vision models"),
            ("Batch Processing", "Process multiple images simultaneously with progress tracking"),
            ("IPTC Support", "Full support for IPTC metadata standards and embedding"),
            ("Smart Keywords", "Intelligent keyword extraction and categorization"),
            ("Export Options", "Multiple export formats and metadata preservation"),
            ("Quality Control", "Built-in validation and error checking")
        ]
        
        for i, (title, subtitle) in enumerate(features):
            card = CardWidget(title, subtitle)
            card.clicked.connect(lambda t=title: self.on_feature_clicked(t))
            row = i // 3
            col = i % 3
            grid_layout.addWidget(card, row, col)
        
        layout.addLayout(grid_layout)
    
    def create_action_section(self, layout):
        """Create the action buttons section."""
        section_title = QLabel("Actions")
        section_title.setStyleSheet("color: #e8eaed; font-size: 14pt; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(section_title)
        
        action_layout = QHBoxLayout()
        action_layout.setSpacing(15)
        
        # Primary actions
        start_btn = ActionButton("Start Processing", primary=True)
        start_btn.clicked.connect(lambda: self.show_message("Starting processing..."))
        
        # Secondary actions
        settings_btn = ActionButton("Settings", primary=False)
        settings_btn.clicked.connect(lambda: self.show_message("Opening settings..."))
        
        export_btn = ActionButton("Export Results", primary=False)
        export_btn.clicked.connect(lambda: self.show_message("Exporting results..."))
        
        action_layout.addWidget(start_btn)
        action_layout.addWidget(settings_btn)
        action_layout.addWidget(export_btn)
        action_layout.addStretch()
        
        layout.addLayout(action_layout)
    
    def on_feature_clicked(self, feature_name):
        """Handle feature card clicks."""
        self.show_message(f"Feature clicked: {feature_name}")
    
    def show_message(self, message):
        """Show a message in the status bar."""
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage(message, 3000)
        else:
            status_bar = self.statusBar()
            status_bar.showMessage(message, 3000)


def main():
    """Run the demo application."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Metadata Genie AI Demo")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Metadata Genie")
    
    # Create and show the demo window
    window = DemoWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
