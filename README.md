weaks to # Metadata Genie AI

Metadata Genie AI is a powerful application that analyzes images using advanced vision models and automatically embeds detailed descriptions into the images' IPTC metadata fields.

## Features

- **Image Analysis**: Utilizes state-of-the-art vision models to generate detailed descriptions of images
- **Metadata Embedding**: Writes generated descriptions to IPTC metadata fields (Description, Caption/Abstract, Keywords, Headline)
- **Batch Processing**: Process multiple images at once
- **Metadata Preview**: Preview and edit metadata before saving
- **Offline Operation**: All models and dependencies are stored locally after initial setup

## Requirements

- Python 3.7 or higher
- ExifTool (for metadata handling)
- Required Python packages (installed automatically on first run):
  - PyQt5
  - Pillow
  - pyexiftool
  - transformers
  - torch
  - ultralytics
  - requests
  - tqdm

## Installation

1. Clone or download this repository
2. Run the application:
   ```
   python MetadataGenie.py
   ```

   For development and troubleshooting with detailed logging:
   ```
   python run_with_debug.py
   ```

## First Run

On the first run, the application will:
1. Check for required dependencies and offer to install any missing packages
2. Check for ExifTool and offer to download and install it automatically if not found
3. Download the required vision models (this may take some time depending on your internet connection)
4. Set up the application environment

All dependencies, including ExifTool, are managed automatically by the application.

## Usage

1. Launch the application by running `python MetadataGenie.py`
2. Click "Add Images" to select images for processing
3. Select an image to preview its analysis
4. Edit metadata if desired
5. Click "Update Metadata" to save changes to the selected image
6. Use "Process Selected" or "Process All" to batch process multiple images

## Project Structure

```
MetadataGenieAI/
├── MetadataGenie.py         # Main entry point
├── run_with_debug.py        # Debug entry point with enhanced logging
├── requirements.txt         # Dependencies
├── README.md                # Documentation
├── metadata_genie/          # Main package
│   ├── gui/                 # GUI components
│   ├── models/              # Vision models and image analysis
│   ├── metadata/            # Metadata handling
│   └── utils/               # Utilities (logging, configuration)
└── models/                  # Directory for downloaded models
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- ExifTool by Phil Harvey
- Hugging Face Transformers
- Ultralytics YOLO (PyTorch version)
- PyQt5
