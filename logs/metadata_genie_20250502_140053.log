2025-05-02 14:00:53,607 - LogThread - INFO - <PERSON><PERSON> initialized with level INFO
2025-05-02 14:00:53,609 - LogThread - INFO - Log file: logs\metadata_genie_20250502_140053.log
2025-05-02 14:00:57,448 - LogThread - INFO - Configuration loaded from config.json
2025-05-02 14:01:06,117 - LogThread - INFO - Applying theme: dark
2025-05-02 14:01:06,118 - LogThread - INFO - Applied dark theme
2025-05-02 14:01:06,500 - LogThread - INFO - ExifTool found in PATH: version 13.29
2025-05-02 14:01:06,501 - LogThread - INFO - Loading vision model from local directory
2025-05-02 14:01:07,584 - LogThread - INFO - Vision model loaded successfully
2025-05-02 14:01:07,585 - LogThread - INFO - Forcing CPU mode for object detection model
2025-05-02 14:01:07,587 - LogThread - INFO - Loading object detection model from local directory
2025-05-02 14:01:07,652 - LogThread - INFO - Successfully loaded YOLOv8 model in PyTorch format
2025-05-02 14:01:07,658 - LogThread - INFO - Verifying model is using CPU...
2025-05-02 14:01:07,659 - LogThread - INFO - Object detection model loaded successfully in CPU mode
2025-05-02 14:01:07,747 - LogThread - INFO - Applying theme: dark
2025-05-02 14:01:07,893 - LogThread - INFO - Applied dark theme
2025-05-02 14:17:06,431 - LogThread - INFO - Applying theme: light
2025-05-02 14:17:06,433 - LogThread - INFO - Applied light theme
2025-05-02 14:17:06,434 - LogThread - INFO - Configuration saved to config.json
2025-05-02 14:17:06,435 - LogThread - ERROR - Error toggling theme: 'Config' object has no attribute 'save'
NoneType: None
2025-05-02 14:17:14,141 - LogThread - INFO - Applying theme: dark
2025-05-02 14:17:14,143 - LogThread - INFO - Applied dark theme
2025-05-02 14:17:14,143 - LogThread - INFO - Configuration saved to config.json
2025-05-02 14:17:14,144 - LogThread - ERROR - Error toggling theme: 'Config' object has no attribute 'save'
NoneType: None
2025-05-02 14:17:28,051 - LogThread - INFO - Downloading models...
2025-05-02 14:17:28,053 - LogThread - INFO - Downloading vision model: microsoft/git-base
2025-05-02 14:17:53,856 - LogThread - INFO - Backed up existing model to models\vision_model_backup_1746213473
2025-05-02 14:18:36,842 - LogThread - ERROR - Error downloading vision model: 'GitConfig' object has no attribute 'get'
NoneType: None
2025-05-02 14:18:36,864 - LogThread - INFO - Downloading object detection model: yolov8n.pt
2025-05-02 14:18:36,906 - LogThread - WARNING - Checksum verification failed for yolov8n.pt
2025-05-02 14:18:36,907 - LogThread - WARNING - Expected: b8f1b33d8c5a4d7e4bd1f1a2d6588b9c, Got: 95a2449609c73cd69a072b09daaff0cc
2025-05-02 14:18:36,907 - LogThread - WARNING - Object detection model verification failed, but continuing
2025-05-02 14:18:36,914 - LogThread - INFO - Object detection model downloaded and saved to models\yolov8n.pt in 0.04 seconds
2025-05-02 14:18:37,382 - LogThread - ERROR - Error downloading models
NoneType: None
2025-05-02 14:18:51,206 - MainThread - INFO - Shutting down logger
