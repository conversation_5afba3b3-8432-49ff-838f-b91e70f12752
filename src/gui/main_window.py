from PyQt5.QtWidgets import (
    QMain<PERSON><PERSON>ow, QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QFileDialog, QProgressBar, QTabWidget, QSplitter,
    QMessageBox, QAction, QStatusBar, QToolBar
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QPixmap
import os
import threading
import time
import logging

from src.gui.widgets import ImageListWidget, MetadataPreviewWidget, LogViewerWidget
from src.models.image_analyzer import ImageAnalyzer
from src.utils.logger import logger
from src.utils.config import config

class WorkerThread(QThread):
    """
    Worker thread for processing images in the background.
    Handles batch processing of multiple images with progress reporting.
    """
    progress_updated = pyqtSignal(int, int)  # Current progress, total
    analysis_completed = pyqtSignal(str, dict)  # Image path, analysis results
    processing_completed = pyqtSignal()  # All processing completed
    error_occurred = pyqtSignal(str, str)  # Image path, error message
    status_update = pyqtSignal(str)  # Status message for status bar

    def __init__(self, image_analyzer, image_paths):
        super().__init__()
        self.image_analyzer = image_analyzer
        self.image_paths = image_paths
        self.stop_requested = False
        self.paused = False
        self.pause_condition = threading.Condition()

        # Track processing statistics
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.start_time = 0

    def run(self):
        """Run the worker thread to process all images."""
        total = len(self.image_paths)
        self.start_time = time.time()

        self.status_update.emit(f"Starting processing of {total} images...")

        for i, image_path in enumerate(self.image_paths):
            # Check if stop was requested
            if self.stop_requested:
                self.status_update.emit("Processing stopped by user")
                break

            # Handle pause if requested
            with self.pause_condition:
                while self.paused and not self.stop_requested:
                    self.status_update.emit("Processing paused...")
                    self.pause_condition.wait()

            # Skip if stop was requested during pause
            if self.stop_requested:
                break

            try:
                # Update progress
                current = i + 1
                self.progress_updated.emit(current, total)
                self.status_update.emit(f"Processing image {current} of {total}: {os.path.basename(image_path)}")

                # Process image (analyze and update metadata)
                success = self.image_analyzer.process_image(image_path)

                if success:
                    self.success_count += 1
                    # Get the analysis results to emit
                    analysis_results = self.image_analyzer.analyze_image(image_path)
                    if analysis_results:
                        # Emit signal with analysis results
                        self.analysis_completed.emit(image_path, analysis_results)
                else:
                    self.error_count += 1
                    self.error_occurred.emit(image_path, "Failed to process image")

                # Update processed count
                self.processed_count += 1

            except Exception as e:
                self.error_count += 1
                self.processed_count += 1
                self.error_occurred.emit(image_path, str(e))

        # Calculate processing time
        elapsed_time = time.time() - self.start_time

        # Emit signal when processing is completed
        self.status_update.emit(
            f"Processing completed: {self.success_count} succeeded, {self.error_count} failed, "
            f"in {elapsed_time:.1f} seconds"
        )
        self.processing_completed.emit()

    def stop(self):
        """Request the thread to stop."""
        self.stop_requested = True

        # If paused, wake up the thread to check stop condition
        with self.pause_condition:
            self.paused = False
            self.pause_condition.notify_all()

    def pause(self):
        """Pause the thread."""
        self.paused = True

    def resume(self):
        """Resume the thread."""
        with self.pause_condition:
            self.paused = False
            self.pause_condition.notify_all()

    def get_stats(self):
        """Get processing statistics."""
        elapsed_time = time.time() - self.start_time if self.start_time > 0 else 0
        return {
            'total': len(self.image_paths),
            'processed': self.processed_count,
            'success': self.success_count,
            'error': self.error_count,
            'elapsed_time': elapsed_time,
            'is_running': self.isRunning(),
            'is_paused': self.paused
        }


class MainWindow(QMainWindow):
    """
    Main window for the Metadata Genie AI application.
    """
    def __init__(self):
        super().__init__()
        self.image_analyzer = ImageAnalyzer()
        self.current_image_path = None
        self.current_metadata = None
        self.worker_thread = None

        self.initUI()

        # Check if this is the first run
        if config.is_first_run():
            self.first_run_setup()

    def initUI(self):
        """Initialize the UI."""
        self.setWindowTitle("Metadata Genie AI")
        self.setMinimumSize(1000, 700)

        # Create central widget
        central_widget = QWidget()
        main_layout = QVBoxLayout()

        # Create splitter for main layout
        splitter = QSplitter(Qt.Horizontal)

        # Left panel - Image list
        left_panel = QWidget()
        left_layout = QVBoxLayout()

        # Image list
        self.image_list = ImageListWidget()
        self.image_list.itemSelectionChanged.connect(self.on_image_selection_changed)

        # Buttons for image selection
        button_layout = QHBoxLayout()
        self.add_images_button = QPushButton("Add Images")
        self.add_images_button.clicked.connect(self.add_images)
        self.remove_images_button = QPushButton("Remove Selected")
        self.remove_images_button.clicked.connect(self.remove_selected_images)
        self.clear_images_button = QPushButton("Clear All")
        self.clear_images_button.clicked.connect(self.clear_all_images)

        button_layout.addWidget(self.add_images_button)
        button_layout.addWidget(self.remove_images_button)
        button_layout.addWidget(self.clear_images_button)

        # Process buttons
        process_layout = QHBoxLayout()
        self.process_selected_button = QPushButton("Process Selected")
        self.process_selected_button.clicked.connect(self.process_selected_images)
        self.process_all_button = QPushButton("Process All")
        self.process_all_button.clicked.connect(self.process_all_images)

        process_layout.addWidget(self.process_selected_button)
        process_layout.addWidget(self.process_all_button)

        # Process control buttons (initially hidden)
        control_layout = QHBoxLayout()

        self.pause_button = QPushButton("Pause")
        self.pause_button.clicked.connect(self.pause_processing)
        self.pause_button.setVisible(False)

        self.resume_button = QPushButton("Resume")
        self.resume_button.clicked.connect(self.resume_processing)
        self.resume_button.setVisible(False)

        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setVisible(False)

        # Add a distinctive style to the stop button
        self.stop_button.setStyleSheet("QPushButton { background-color: #ffcccc; }")

        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.resume_button)
        control_layout.addWidget(self.stop_button)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)

        # Add widgets to left layout
        left_layout.addWidget(QLabel("Selected Images:"))
        left_layout.addWidget(self.image_list)
        left_layout.addLayout(button_layout)
        left_layout.addLayout(process_layout)
        left_layout.addLayout(control_layout)
        left_layout.addWidget(self.progress_bar)

        left_panel.setLayout(left_layout)

        # Right panel - Tabs for metadata preview and logs
        right_panel = QTabWidget()

        # Metadata preview tab
        self.metadata_preview = MetadataPreviewWidget()
        self.metadata_preview.metadataUpdated.connect(self.on_metadata_updated)

        # Log viewer tab
        self.log_viewer = LogViewerWidget()

        # Add tabs
        right_panel.addTab(self.metadata_preview, "Metadata Preview")
        right_panel.addTab(self.log_viewer, "Logs")

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # Set initial sizes
        splitter.setSizes([400, 600])

        # Add splitter to main layout
        main_layout.addWidget(splitter)

        # Set central widget
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

        # Create status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready")

        # Create menu bar
        self.create_menu_bar()

        # Connect logger to log viewer
        self.connect_logger()

    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("File")

        # Add images action
        add_images_action = QAction("Add Images", self)
        add_images_action.triggered.connect(self.add_images)
        file_menu.addAction(add_images_action)

        # Exit action
        exit_action = QAction("Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu("Tools")

        # Download models action
        download_models_action = QAction("Download Models", self)
        download_models_action.triggered.connect(self.download_models)
        tools_menu.addAction(download_models_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        # About action
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def connect_logger(self):
        """Connect the logger to the log viewer."""
        # Create a custom handler that sends logs to the log viewer
        class LogHandler(logging.Handler):
            def __init__(self, log_viewer):
                super().__init__()
                self.log_viewer = log_viewer

            def emit(self, record):
                msg = self.format(record)
                self.log_viewer.add_log(msg)

        # Create and add the handler
        handler = LogHandler(self.log_viewer)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.logger.addHandler(handler)

    def first_run_setup(self):
        """Perform first run setup."""
        logger.info("First run detected. Performing initial setup...")

        # Show welcome message
        QMessageBox.information(
            self,
            "Welcome to Metadata Genie AI",
            "Welcome to Metadata Genie AI!\n\n"
            "This appears to be your first time running the application. "
            "We'll now download the required models and set up the environment.\n\n"
            "This may take a few minutes depending on your internet connection."
        )

        # Download models
        self.download_models()

        # Mark first run as completed
        config.set_first_run_completed()

    def download_models(self):
        """Download the required models."""
        logger.info("Downloading models...")
        self.statusBar.showMessage("Downloading models...")

        # Disable UI during download
        self.setEnabled(False)

        # Create a worker thread for downloading models
        class DownloadThread(QThread):
            download_completed = pyqtSignal(bool)

            def __init__(self, model_manager):
                super().__init__()
                self.model_manager = model_manager

            def run(self):
                success = self.model_manager.download_models(force=True)
                self.download_completed.emit(success)

        # Create and start the thread
        self.download_thread = DownloadThread(self.image_analyzer.model_manager)

        # Connect signals
        self.download_thread.download_completed.connect(self.on_download_completed)

        # Make sure thread is properly cleaned up
        self.download_thread.finished.connect(self.download_thread.deleteLater)

        # Start the thread
        self.download_thread.start()

    def on_download_completed(self, success):
        """Handle download completed event."""
        # Re-enable UI
        self.setEnabled(True)

        if success:
            self.statusBar.showMessage("Models downloaded successfully")
            logger.info("Models downloaded successfully")
            QMessageBox.information(
                self,
                "Download Complete",
                "Models downloaded successfully."
            )
        else:
            self.statusBar.showMessage("Error downloading models")
            logger.error("Error downloading models")
            QMessageBox.critical(
                self,
                "Download Error",
                "There was an error downloading the models. "
                "Please check the logs for more information."
            )

    def add_images(self):
        """Add images to the list."""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Images (*.jpg *.jpeg *.png *.bmp *.gif *.tiff)")

        if file_dialog.exec_():
            file_paths = file_dialog.selectedFiles()
            self.image_list.addItems(file_paths)
            self.statusBar.showMessage(f"Added {len(file_paths)} images")

    def remove_selected_images(self):
        """Remove selected images from the list."""
        selected_items = self.image_list.selectedItems()

        if not selected_items:
            return

        for item in selected_items:
            row = self.image_list.row(item)
            self.image_list.takeItem(row)

        self.statusBar.showMessage(f"Removed {len(selected_items)} images")

    def clear_all_images(self):
        """Clear all images from the list."""
        self.image_list.clear()
        self.statusBar.showMessage("Cleared all images")

    def on_image_selection_changed(self):
        """Handle image selection changed event."""
        selected_items = self.image_list.selectedItems()

        if not selected_items:
            self.current_image_path = None
            self.current_metadata = None
            self.metadata_preview.set_metadata({})
            return

        # Get the first selected image
        self.current_image_path = selected_items[0].data(Qt.UserRole)

        # Analyze the image
        self.statusBar.showMessage(f"Analyzing {os.path.basename(self.current_image_path)}...")

        # Create a worker thread for analysis
        class AnalysisThread(QThread):
            analysis_completed = pyqtSignal(dict)
            analysis_failed = pyqtSignal(str)

            def __init__(self, image_analyzer, image_path):
                super().__init__()
                self.image_analyzer = image_analyzer
                self.image_path = image_path

            def run(self):
                try:
                    # Analyze image
                    metadata = self.image_analyzer.analyze_image(self.image_path)

                    if metadata:
                        self.analysis_completed.emit(metadata)
                    else:
                        self.analysis_failed.emit(f"Failed to analyze {os.path.basename(self.image_path)}")
                except Exception as e:
                    logger.error(f"Error analyzing image: {str(e)}")
                    self.analysis_failed.emit(f"Error analyzing {os.path.basename(self.image_path)}: {str(e)}")

        # Create and start the thread
        self.analysis_thread = AnalysisThread(self.image_analyzer, self.current_image_path)

        # Connect signals
        self.analysis_thread.analysis_completed.connect(
            lambda metadata: (
                self.metadata_preview.set_metadata(metadata),
                self.statusBar.showMessage(f"Analysis complete for {os.path.basename(self.current_image_path)}"),
                setattr(self, 'current_metadata', metadata)
            )
        )
        self.analysis_thread.analysis_failed.connect(self.statusBar.showMessage)

        # Make sure thread is properly cleaned up
        self.analysis_thread.finished.connect(self.analysis_thread.deleteLater)

        # Start the thread
        self.analysis_thread.start()

    def on_metadata_updated(self, metadata):
        """Handle metadata updated event."""
        if not self.current_image_path:
            return

        # Update metadata
        self.statusBar.showMessage(f"Updating metadata for {os.path.basename(self.current_image_path)}...")

        # Create a worker thread for metadata update
        class UpdateThread(QThread):
            update_completed = pyqtSignal(bool)
            update_failed = pyqtSignal(str)

            def __init__(self, image_analyzer, image_path, metadata):
                super().__init__()
                self.image_analyzer = image_analyzer
                self.image_path = image_path
                self.metadata = metadata

            def run(self):
                try:
                    # Update metadata
                    success = self.image_analyzer.update_metadata(self.image_path, self.metadata)

                    if success:
                        self.update_completed.emit(True)
                    else:
                        self.update_failed.emit(f"Failed to update metadata for {os.path.basename(self.image_path)}")
                except Exception as e:
                    logger.error(f"Error updating metadata: {str(e)}")
                    self.update_failed.emit(f"Error updating metadata for {os.path.basename(self.image_path)}: {str(e)}")

        # Create and start the thread
        self.update_thread = UpdateThread(self.image_analyzer, self.current_image_path, metadata)

        # Connect signals
        self.update_thread.update_completed.connect(
            lambda _: self.statusBar.showMessage(f"Metadata updated for {os.path.basename(self.current_image_path)}")
        )
        self.update_thread.update_failed.connect(self.statusBar.showMessage)

        # Make sure thread is properly cleaned up
        self.update_thread.finished.connect(self.update_thread.deleteLater)

        # Start the thread
        self.update_thread.start()

    def process_selected_images(self):
        """Process selected images."""
        selected_paths = self.image_list.get_selected_items()

        if not selected_paths:
            QMessageBox.warning(
                self,
                "No Images Selected",
                "Please select one or more images to process."
            )
            return

        self.process_images(selected_paths)

    def process_all_images(self):
        """Process all images."""
        all_paths = self.image_list.get_all_items()

        if not all_paths:
            QMessageBox.warning(
                self,
                "No Images",
                "Please add one or more images to process."
            )
            return

        self.process_images(all_paths)

    def process_images(self, image_paths):
        """
        Process a list of images.

        Args:
            image_paths (list): List of paths to images to process
        """
        # Check if a worker thread is already running
        if self.worker_thread and self.worker_thread.isRunning():
            # If paused, offer to resume
            if hasattr(self.worker_thread, 'paused') and self.worker_thread.paused:
                reply = QMessageBox.question(
                    self,
                    "Processing Paused",
                    "Processing is currently paused. Would you like to resume?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    self.resume_processing()
                    return
            else:
                QMessageBox.warning(
                    self,
                    "Processing in Progress",
                    "Please wait for the current processing to complete or use the pause/stop buttons."
                )
                return

        # Create and start worker thread
        self.worker_thread = WorkerThread(self.image_analyzer, image_paths)

        # Connect signals
        self.worker_thread.progress_updated.connect(self.update_progress)
        self.worker_thread.analysis_completed.connect(self.on_analysis_completed)
        self.worker_thread.processing_completed.connect(self.on_processing_completed)
        self.worker_thread.error_occurred.connect(self.on_processing_error)
        self.worker_thread.status_update.connect(self.statusBar.showMessage)

        # Make sure thread is properly cleaned up
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Update UI for processing state
        self.update_ui_for_processing(True)

        # Start worker thread
        self.worker_thread.start()

        # Show initial status message
        self.statusBar.showMessage(f"Processing {len(image_paths)} images...")

    def update_ui_for_processing(self, is_processing):
        """
        Update UI elements based on processing state.

        Args:
            is_processing (bool): Whether processing is active
        """
        # Enable/disable main UI
        self.image_list.setEnabled(not is_processing)
        self.add_images_button.setEnabled(not is_processing)
        self.remove_images_button.setEnabled(not is_processing)
        self.clear_images_button.setEnabled(not is_processing)
        self.process_selected_button.setEnabled(not is_processing)
        self.process_all_button.setEnabled(not is_processing)

        # Show/hide processing control buttons
        if hasattr(self, 'pause_button'):
            self.pause_button.setVisible(is_processing)
            self.resume_button.setVisible(is_processing and
                                         hasattr(self.worker_thread, 'paused') and
                                         self.worker_thread.paused)
            self.stop_button.setVisible(is_processing)

        # Reset progress bar if processing is done
        if not is_processing:
            self.progress_bar.setValue(0)

    def pause_processing(self):
        """Pause the current processing."""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.pause()
            self.statusBar.showMessage("Processing paused")

            # Update UI
            if hasattr(self, 'pause_button') and hasattr(self, 'resume_button'):
                self.pause_button.setVisible(False)
                self.resume_button.setVisible(True)

    def resume_processing(self):
        """Resume the paused processing."""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.resume()
            self.statusBar.showMessage("Processing resumed")

            # Update UI
            if hasattr(self, 'pause_button') and hasattr(self, 'resume_button'):
                self.pause_button.setVisible(True)
                self.resume_button.setVisible(False)

    def stop_processing(self):
        """Stop the current processing."""
        if self.worker_thread and self.worker_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Stop Processing",
                "Are you sure you want to stop the current processing?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.statusBar.showMessage("Stopping processing...")
                self.worker_thread.stop()

                # Update UI after a short delay to allow thread to clean up
                QTimer.singleShot(500, lambda: self.update_ui_for_processing(False))

    def update_progress(self, current, total):
        """Update the progress bar."""
        progress = int((current / total) * 100)
        self.progress_bar.setValue(progress)
        self.statusBar.showMessage(f"Processing image {current} of {total}...")

    def on_analysis_completed(self, image_path, metadata):
        """Handle analysis completed event."""
        logger.info(f"Analysis completed for {os.path.basename(image_path)}")

        # Update metadata
        try:
            success = self.image_analyzer.update_metadata(image_path, metadata)

            if success:
                logger.info(f"Metadata updated for {os.path.basename(image_path)}")
            else:
                logger.error(f"Failed to update metadata for {os.path.basename(image_path)}")
        except Exception as e:
            logger.error(f"Error updating metadata: {str(e)}")

    def on_processing_completed(self):
        """Handle processing completed event."""
        # Get processing statistics if available
        stats = None
        if self.worker_thread and hasattr(self.worker_thread, 'get_stats'):
            stats = self.worker_thread.get_stats()

        # Update UI for processing state
        self.update_ui_for_processing(False)

        # Log completion
        if stats:
            message = (f"Processing completed: {stats['success']} succeeded, "
                      f"{stats['error']} failed, in {stats['elapsed_time']:.1f} seconds")
            logger.info(message)
            self.statusBar.showMessage(message)
        else:
            self.statusBar.showMessage("Processing completed")
            logger.info("Processing completed")

        # Show completion message
        if stats and stats['error'] > 0:
            QMessageBox.information(
                self,
                "Processing Complete",
                f"Image processing completed with some errors.\n\n"
                f"Successfully processed: {stats['success']}\n"
                f"Failed: {stats['error']}\n"
                f"Total time: {stats['elapsed_time']:.1f} seconds\n\n"
                f"Check the logs for more details."
            )
        else:
            QMessageBox.information(
                self,
                "Processing Complete",
                "Image processing completed successfully."
            )

    def on_processing_error(self, image_path, error_message):
        """Handle processing error event."""
        logger.error(f"Error processing {os.path.basename(image_path)}: {error_message}")

    def show_about(self):
        """Show the about dialog."""
        QMessageBox.about(
            self,
            "About Metadata Genie AI",
            "Metadata Genie AI\n\n"
            "A tool for automatically generating and embedding metadata into images "
            "using advanced vision models.\n\n"
            "© 2024"
        )

    def closeEvent(self, event):
        """
        Handle application close event.
        Ensures all threads are properly stopped and resources are cleaned up.
        """
        # Check if a worker thread is running
        try:
            if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
                # If paused, ask differently
                if hasattr(self.worker_thread, 'paused') and self.worker_thread.paused:
                    reply = QMessageBox.question(
                        self,
                        "Processing Paused",
                        "Processing is currently paused. Are you sure you want to exit?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                else:
                    # Ask user if they want to cancel processing
                    reply = QMessageBox.question(
                        self,
                        "Processing in Progress",
                        "Processing is still in progress. Are you sure you want to exit?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                if reply == QMessageBox.Yes:
                    # Stop worker thread
                    logger.info("Stopping worker thread before exit")
                    self.worker_thread.stop()

                    # Wait briefly for the thread to clean up
                    if not self.worker_thread.wait(2000):  # Wait up to 2 seconds
                        logger.warning("Worker thread did not exit cleanly, forcing termination")
                        self.worker_thread.terminate()

                    event.accept()
                else:
                    event.ignore()
                    return
        except (RuntimeError, AttributeError) as e:
            # Thread might have been deleted already
            logger.debug(f"Error checking worker thread: {str(e)}")
            pass

        # Clean up any other threads
        thread_names = ['analysis_thread', 'update_thread', 'download_thread']
        for thread_name in thread_names:
            try:
                if hasattr(self, thread_name):
                    thread = getattr(self, thread_name)
                    if thread and thread.isRunning():
                        logger.info(f"Stopping {thread_name} before exit")
                        thread.quit()

                        # Wait briefly for the thread to clean up
                        if not thread.wait(1000):  # Wait up to 1 second
                            logger.warning(f"{thread_name} did not exit cleanly, forcing termination")
                            thread.terminate()
            except (RuntimeError, AttributeError) as e:
                logger.debug(f"Error cleaning up {thread_name}: {str(e)}")
                pass

        # Shutdown logger
        try:
            if hasattr(logger, 'shutdown'):
                logger.shutdown()
        except Exception as e:
            print(f"Error shutting down logger: {str(e)}")

        event.accept()
