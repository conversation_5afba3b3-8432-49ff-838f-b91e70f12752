from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QProgressBar, QVBoxLayout, QHBoxLayout,
    QListWidget, QListWidgetItem, QFileDialog, QTextEdit, QCheckBox,
    QGroupBox, QSplitter, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QIcon
import os
from src.utils.logger import logger

# Register QTextCursor for thread-safe signal/slot connections
# This is done differently depending on PyQt5 version
try:
    from PyQt5.QtCore import qRegisterMetaType
    qRegisterMetaType("QTextCursor")
except (ImportError, AttributeError):
    pass

class ImageListWidget(QListWidget):
    """
    Custom list widget for displaying selected images.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setIconSize(QSize(100, 100))
        self.setSelectionMode(QListWidget.ExtendedSelection)

    def dragEnterEvent(self, event):
        """Handle drag enter events for drag and drop functionality."""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move events for drag and drop functionality."""
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.CopyAction)
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop events for drag and drop functionality."""
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.CopyAction)
            event.accept()

            # Process the dropped files
            links = []
            for url in event.mimeData().urls():
                links.append(str(url.toLocalFile()))

            # Emit signal to add the dropped files
            self.addItems(links)
        else:
            event.ignore()

    def addItems(self, items):
        """Add items to the list widget with thumbnails."""
        for item_path in items:
            if os.path.isfile(item_path) and self._is_image_file(item_path):
                # Create a list item with the image path
                item = QListWidgetItem(os.path.basename(item_path))
                item.setData(Qt.UserRole, item_path)

                # Try to create a thumbnail
                try:
                    pixmap = QPixmap(item_path)
                    if not pixmap.isNull():
                        pixmap = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        item.setIcon(QIcon(pixmap))
                except Exception as e:
                    logger.error(f"Error creating thumbnail for {item_path}: {str(e)}")

                # Add the item to the list
                super().addItem(item)

    def _is_image_file(self, file_path):
        """Check if a file is an image file based on its extension."""
        extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff']
        return any(file_path.lower().endswith(ext) for ext in extensions)

    def get_selected_items(self):
        """Get the paths of the selected items."""
        return [item.data(Qt.UserRole) for item in self.selectedItems()]

    def get_all_items(self):
        """Get the paths of all items in the list."""
        return [self.item(i).data(Qt.UserRole) for i in range(self.count())]


class MetadataPreviewWidget(QWidget):
    """
    Widget for previewing and editing metadata before saving.
    """
    metadataUpdated = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        """Initialize the UI."""
        layout = QVBoxLayout()

        # Description
        description_group = QGroupBox("Description")
        description_layout = QVBoxLayout()
        self.description_edit = QTextEdit()
        description_layout.addWidget(self.description_edit)
        description_group.setLayout(description_layout)

        # Caption
        caption_group = QGroupBox("Caption/Abstract")
        caption_layout = QVBoxLayout()
        self.caption_edit = QTextEdit()
        caption_layout.addWidget(self.caption_edit)
        caption_group.setLayout(caption_layout)

        # Headline
        headline_group = QGroupBox("Headline")
        headline_layout = QVBoxLayout()
        self.headline_edit = QTextEdit()
        headline_layout.addWidget(self.headline_edit)
        headline_group.setLayout(headline_layout)

        # Keywords
        keywords_group = QGroupBox("Keywords")
        keywords_layout = QVBoxLayout()
        self.keywords_edit = QTextEdit()
        keywords_layout.addWidget(self.keywords_edit)
        keywords_group.setLayout(keywords_layout)

        # IPTC Fields to update
        fields_group = QGroupBox("Fields to Update")
        fields_layout = QVBoxLayout()
        self.description_check = QCheckBox("Description")
        self.caption_check = QCheckBox("Caption/Abstract")
        self.headline_check = QCheckBox("Headline")
        self.keywords_check = QCheckBox("Keywords")

        # Set all checkboxes checked by default
        self.description_check.setChecked(True)
        self.caption_check.setChecked(True)
        self.headline_check.setChecked(True)
        self.keywords_check.setChecked(True)

        fields_layout.addWidget(self.description_check)
        fields_layout.addWidget(self.caption_check)
        fields_layout.addWidget(self.headline_check)
        fields_layout.addWidget(self.keywords_check)
        fields_group.setLayout(fields_layout)

        # Update button
        self.update_button = QPushButton("Update Metadata")
        self.update_button.clicked.connect(self.update_metadata)

        # Add all widgets to the layout
        layout.addWidget(description_group)
        layout.addWidget(caption_group)
        layout.addWidget(headline_group)
        layout.addWidget(keywords_group)
        layout.addWidget(fields_group)
        layout.addWidget(self.update_button)

        self.setLayout(layout)

    def set_metadata(self, metadata):
        """Set the metadata in the preview widget."""
        if metadata:
            self.description_edit.setText(metadata.get('description', ''))
            self.caption_edit.setText(metadata.get('caption', ''))
            self.headline_edit.setText(metadata.get('headline', ''))
            self.keywords_edit.setText('\n'.join(metadata.get('keywords', [])))

    def update_metadata(self):
        """Update the metadata based on the user's edits."""
        metadata = {
            'description': self.description_edit.toPlainText() if self.description_check.isChecked() else None,
            'caption': self.caption_edit.toPlainText() if self.caption_check.isChecked() else None,
            'headline': self.headline_edit.toPlainText() if self.headline_check.isChecked() else None,
            'keywords': self.keywords_edit.toPlainText().split('\n') if self.keywords_check.isChecked() else None
        }

        # Remove None values
        metadata = {k: v for k, v in metadata.items() if v is not None}

        # Emit signal with updated metadata
        self.metadataUpdated.emit(metadata)


class LogViewerWidget(QWidget):
    """
    Widget for displaying application logs.
    """
    # Signal for thread-safe log updates
    log_received = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

        # Connect the signal to the slot for thread-safe updates
        self.log_received.connect(self._append_log)

    def initUI(self):
        """Initialize the UI."""
        layout = QVBoxLayout()

        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        # Clear button
        self.clear_button = QPushButton("Clear Logs")
        self.clear_button.clicked.connect(self.clear_logs)

        layout.addWidget(self.log_text)
        layout.addWidget(self.clear_button)

        self.setLayout(layout)

    def add_log(self, message):
        """Add a log message to the log viewer in a thread-safe way."""
        # Emit signal instead of directly modifying the UI
        self.log_received.emit(message)

    def _append_log(self, message):
        """Slot that actually appends the log message (called in the UI thread)."""
        self.log_text.append(message)
        # Scroll to the bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def clear_logs(self):
        """Clear all logs from the log viewer."""
        self.log_text.clear()
