import os
import torch
import hashlib
import json
import time
import traceback
import shutil
from transformers import AutoProcessor, AutoModelForCausalLM
from ultralytics import YOLO
import requests
from tqdm import tqdm
from src.utils.logger import logger
from src.utils.config import config

class ModelManager:
    """
    Manages downloading, loading, and using vision models.
    Handles model verification, caching, and error recovery.
    """
    # Model checksums for verification
    MODEL_CHECKSUMS = {
        "yolov8n.pt": "b8f1b33d8c5a4d7e4bd1f1a2d6588b9c",  # MD5 checksum for YOLOv8n
    }

    def __init__(self):
        """Initialize the model manager."""
        # Get configuration values
        self.models_dir = config.get("models_dir", "models")
        self.vision_model_name = config.get("vision_model", "microsoft/git-base")
        self.object_detection_model_name = config.get("object_detection_model", "yolov8n.pt")
        self.temp_dir = config.get("temp_dir", "temp")

        # Create required directories
        for directory in [self.models_dir, self.temp_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)

        # Initialize models to None
        self.vision_model = None
        self.vision_processor = None
        self.object_detection_model = None

        # Track model status
        self.model_status = {
            "vision_model": {
                "loaded": False,
                "local": self._check_vision_model_exists(),
                "last_error": None
            },
            "object_detection_model": {
                "loaded": False,
                "local": self._check_object_detection_model_exists(),
                "last_error": None
            }
        }

    def download_models(self, force=False):
        """
        Download all required models.

        Args:
            force (bool): Force download even if models exist locally

        Returns:
            bool: True if all downloads were successful, False otherwise
        """
        success = True

        # Create a metadata file to track model information
        metadata_path = os.path.join(self.models_dir, "metadata.json")
        metadata = self._load_metadata(metadata_path)

        # Download vision model
        if force or not self._check_vision_model_exists():
            logger.info(f"Downloading vision model: {self.vision_model_name}")
            try:
                # This will download the model to the cache directory
                start_time = time.time()

                # Download to a temporary directory first
                temp_model_path = os.path.join(self.temp_dir, "vision_model_temp")
                if os.path.exists(temp_model_path):
                    shutil.rmtree(temp_model_path)
                os.makedirs(temp_model_path)

                # Download the model and processor
                processor = AutoProcessor.from_pretrained(self.vision_model_name)
                model = AutoModelForCausalLM.from_pretrained(self.vision_model_name)

                # Save model to temporary directory
                processor.save_pretrained(temp_model_path)
                model.save_pretrained(temp_model_path)

                # Verify the model was downloaded correctly
                if not os.path.exists(os.path.join(temp_model_path, "config.json")):
                    raise Exception("Model files are incomplete")

                # Move to final location
                model_path = os.path.join(self.models_dir, "vision_model")
                if os.path.exists(model_path):
                    # Backup the old model
                    backup_path = f"{model_path}_backup_{int(time.time())}"
                    shutil.move(model_path, backup_path)
                    logger.info(f"Backed up existing model to {backup_path}")

                # Move the temporary model to the final location
                shutil.move(temp_model_path, model_path)

                # Update metadata
                download_time = time.time() - start_time
                metadata["vision_model"] = {
                    "name": self.vision_model_name,
                    "download_date": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "download_time": download_time,
                    "version": getattr(model, "config", {}).get("_name_or_path", "unknown")
                }
                self._save_metadata(metadata_path, metadata)

                # Update status
                self.model_status["vision_model"]["local"] = True
                self.model_status["vision_model"]["last_error"] = None

                logger.info(f"Vision model downloaded and saved to {model_path} in {download_time:.2f} seconds")
            except Exception as e:
                logger.error(f"Error downloading vision model: {str(e)}")
                logger.debug(traceback.format_exc())
                self.model_status["vision_model"]["last_error"] = str(e)
                success = False

        # Download object detection model
        if force or not self._check_object_detection_model_exists():
            logger.info(f"Downloading object detection model: {self.object_detection_model_name}")
            try:
                start_time = time.time()

                # Determine if it's a local file or a model name
                if os.path.exists(self.object_detection_model_name):
                    # It's a local file, just copy it
                    model_path = os.path.join(self.models_dir, "yolov8n.pt")
                    shutil.copy2(self.object_detection_model_name, model_path)
                else:
                    # It's a model name, download it
                    # Use PyTorch format instead of ONNX to avoid compatibility issues
                    model = YOLO(self.object_detection_model_name)
                    model_path = os.path.join(self.models_dir, "yolov8n.pt")

                    # Save the model in PyTorch format
                    model.save(model_path)

                # Verify the model file
                if self._verify_model_file(model_path, "yolov8n.pt"):
                    logger.info("Object detection model verified successfully")
                else:
                    logger.warning("Object detection model verification failed, but continuing")

                # Update metadata
                download_time = time.time() - start_time
                metadata["object_detection_model"] = {
                    "name": self.object_detection_model_name,
                    "download_date": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "download_time": download_time,
                    "file_size": os.path.getsize(model_path)
                }
                self._save_metadata(metadata_path, metadata)

                # Update status
                self.model_status["object_detection_model"]["local"] = True
                self.model_status["object_detection_model"]["last_error"] = None

                logger.info(f"Object detection model downloaded and saved to {model_path} in {download_time:.2f} seconds")
            except Exception as e:
                logger.error(f"Error downloading object detection model: {str(e)}")
                logger.debug(traceback.format_exc())
                self.model_status["object_detection_model"]["last_error"] = str(e)
                success = False

        return success

    def _load_metadata(self, metadata_path):
        """Load model metadata from file."""
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Error loading model metadata: {str(e)}")
        return {}

    def _save_metadata(self, metadata_path, metadata):
        """Save model metadata to file."""
        try:
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)
        except Exception as e:
            logger.warning(f"Error saving model metadata: {str(e)}")

    def _verify_model_file(self, file_path, model_name):
        """
        Verify a model file using checksums.

        Args:
            file_path (str): Path to the model file
            model_name (str): Name of the model for checksum lookup

        Returns:
            bool: True if verification passed, False otherwise
        """
        if model_name not in self.MODEL_CHECKSUMS:
            logger.warning(f"No checksum available for {model_name}")
            return True  # Skip verification if no checksum is available

        expected_checksum = self.MODEL_CHECKSUMS[model_name]

        try:
            # Calculate MD5 checksum
            md5_hash = hashlib.md5()
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)

            actual_checksum = md5_hash.hexdigest()

            if actual_checksum != expected_checksum:
                logger.warning(f"Checksum verification failed for {model_name}")
                logger.warning(f"Expected: {expected_checksum}, Got: {actual_checksum}")
                return False

            return True
        except Exception as e:
            logger.warning(f"Error verifying model file: {str(e)}")
            return False

    def _check_vision_model_exists(self):
        """Check if the vision model exists locally."""
        model_path = os.path.join(self.models_dir, "vision_model")
        config_file = os.path.join(model_path, "config.json")
        return os.path.exists(model_path) and os.path.exists(config_file)

    def _check_object_detection_model_exists(self):
        """Check if the object detection model exists locally."""
        model_path = os.path.join(self.models_dir, "yolov8n.pt")
        return os.path.exists(model_path) and os.path.getsize(model_path) > 0

    def load_models(self):
        """
        Load all models.

        Returns:
            bool: True if all models were loaded successfully, False otherwise
        """
        success = True

        # Load vision model
        try:
            model_path = os.path.join(self.models_dir, "vision_model")
            if os.path.exists(model_path):
                logger.info("Loading vision model from local directory")
                self.vision_processor = AutoProcessor.from_pretrained(model_path)
                self.vision_model = AutoModelForCausalLM.from_pretrained(model_path)
            else:
                logger.info(f"Loading vision model from {self.vision_model_name}")
                self.vision_processor = AutoProcessor.from_pretrained(self.vision_model_name)
                self.vision_model = AutoModelForCausalLM.from_pretrained(self.vision_model_name)

            # Update status
            self.model_status["vision_model"]["loaded"] = True
            self.model_status["vision_model"]["last_error"] = None

            logger.info("Vision model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading vision model: {str(e)}")
            logger.debug(traceback.format_exc())
            self.model_status["vision_model"]["loaded"] = False
            self.model_status["vision_model"]["last_error"] = str(e)
            success = False

        # Load object detection model
        try:
            model_path = os.path.join(self.models_dir, "yolov8n.pt")

            # Force CPU mode to avoid CUDA issues
            logger.info("Forcing CPU mode for object detection model")
            os.environ["CUDA_VISIBLE_DEVICES"] = "-1"  # Disable CUDA
            torch.set_num_threads(1)  # Limit CPU threads to avoid threading issues

            if os.path.exists(model_path):
                logger.info("Loading object detection model from local directory")
                self.object_detection_model = YOLO(model_path, task='detect')
            else:
                logger.info(f"Loading object detection model from {self.object_detection_model_name}")
                self.object_detection_model = YOLO(self.object_detection_model_name, task='detect')

            # Explicitly set the model to CPU mode
            if hasattr(self.object_detection_model, 'to'):
                self.object_detection_model.to('cpu')

            # Update status
            self.model_status["object_detection_model"]["loaded"] = True
            self.model_status["object_detection_model"]["last_error"] = None

            logger.info("Object detection model loaded successfully in CPU mode")
        except Exception as e:
            logger.error(f"Error loading object detection model: {str(e)}")
            logger.debug(traceback.format_exc())
            self.model_status["object_detection_model"]["loaded"] = False
            self.model_status["object_detection_model"]["last_error"] = str(e)
            success = False

        return success

    def get_model_status(self):
        """
        Get the status of all models.

        Returns:
            dict: Dictionary containing model status information
        """
        # Update local status
        self.model_status["vision_model"]["local"] = self._check_vision_model_exists()
        self.model_status["object_detection_model"]["local"] = self._check_object_detection_model_exists()

        # Add model paths
        self.model_status["vision_model"]["path"] = os.path.join(self.models_dir, "vision_model")
        self.model_status["object_detection_model"]["path"] = os.path.join(self.models_dir, "yolov8n.pt")

        return self.model_status

    def download_file(self, url, destination):
        """
        Download a file with progress bar.

        Args:
            url (str): URL to download from
            destination (str): Path to save the file to

        Returns:
            bool: True if download was successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(destination), exist_ok=True)

            # Download to a temporary file first
            temp_destination = f"{destination}.download"

            # Make the request with timeout and retries
            session = requests.Session()
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

            response = session.get(url, stream=True, timeout=30)
            response.raise_for_status()  # Raise an exception for HTTP errors

            total_size = int(response.headers.get('content-length', 0))
            block_size = 8192  # 8 KB

            # Download with progress bar
            with open(temp_destination, 'wb') as file, tqdm(
                desc=os.path.basename(destination),
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as bar:
                for data in response.iter_content(block_size):
                    size = file.write(data)
                    bar.update(size)

            # Move the temporary file to the final destination
            if os.path.exists(destination):
                os.remove(destination)
            shutil.move(temp_destination, destination)

            logger.info(f"Downloaded {url} to {destination} ({total_size} bytes)")
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading file: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error downloading file: {str(e)}")
            logger.debug(traceback.format_exc())

            # Clean up temporary file
            if os.path.exists(temp_destination):
                try:
                    os.remove(temp_destination)
                except:
                    pass

            return False
