<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- (this file generated automatically by Image::ExifTool::BuildTagLookup) -->
<head>
<title>Sony Tags</title>
<link rel=stylesheet type='text/css' href='style.css' title='Style'>
</head>
<body>
<h2 class=top>Sony Tags</h2>
<p>The following information has been decoded from the MakerNotes of Sony
cameras.  Some of these tags have been inherited from the Minolta
MakerNotes.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0010 = 16'>0x0010</td>
<td>CameraInfo
  <br>CameraInfo2
  <br>CameraInfo3
  <br>CameraInfoUnknown</td>
<td class=c>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#CameraInfo'>Sony CameraInfo Tags</a>
  <br>--&gt; <a href='Sony.html#CameraInfo2'>Sony CameraInfo2 Tags</a>
  <br>--&gt; <a href='Sony.html#CameraInfo3'>Sony CameraInfo3 Tags</a>
  <br>--&gt; <a href='Sony.html#CameraInfoUnknown'>Sony CameraInfoUnknown Tags</a></td></tr>
<tr class=b>
<td title='0x0020 = 32'>0x0020</td>
<td>FocusInfo
  <br>MoreInfo</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#FocusInfo'>Sony FocusInfo Tags</a>
  <br>--&gt; <a href='Sony.html#MoreInfo'>Sony MoreInfo Tags</a></td></tr>
<tr>
<td title='0x0102 = 258'>0x0102</td>
<td>Quality</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0 = RAW
  <br>1 = Super Fine
  <br>2 = Fine
  <br>3 = Standard
  <br>4 = Economy
  <br>5 = Extra Fine
  <br>6 = RAW + JPEG/HEIF
  <br>7 = Compressed RAW
  <br>8 = Compressed RAW + JPEG
  <br>9 = Light
  <br>4294967295 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x0104 = 260'>0x0104</td>
<td>FlashExposureComp</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0105 = 261'>0x0105</td>
<td>Teleconverter</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0x0 = None
  <br>0x4 = Minolta/Sony AF 1.4x APO (D) (0x04)
  <br>0x5 = Minolta/Sony AF 2x APO (D) (0x05)
  <br>0x48 = Minolta/Sony AF 2x APO (D)
  <br>0x50 = Minolta AF 2x APO II
  <br>0x60 = Minolta AF 2x APO
  <br>0x88 = Minolta/Sony AF 1.4x APO (D)
  <br>0x90 = Minolta AF 1.4x APO II
  <br>0xa0 = Minolta AF 1.4x APO</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x0112 = 274'>0x0112</td>
<td>WhiteBalanceFineTune</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0114 = 276'>0x0114</td>
<td>CameraSettings
  <br>CameraSettings2
  <br>CameraSettings3
  <br>CameraSettingsUnknown</td>
<td class=c>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#CameraSettings'>Sony CameraSettings Tags</a>
  <br>--&gt; <a href='Sony.html#CameraSettings2'>Sony CameraSettings2 Tags</a>
  <br>--&gt; <a href='Sony.html#CameraSettings3'>Sony CameraSettings3 Tags</a>
  <br>--&gt; <a href='Sony.html#CameraSettingsUnknown'>Sony CameraSettingsUnknown Tags</a></td></tr>
<tr class=b>
<td title='0x0115 = 277'>0x0115</td>
<td>WhiteBalance</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0x0 = Auto
  <br>0x1 = Color Temperature/Color Filter
  <br>0x10 = Daylight
  <br>0x20 = Cloudy
  <br>0x30 = Shade
  <br>0x40 = Tungsten
  <br>0x50 = Flash
  <br>0x60 = Fluorescent
  <br>0x70 = Custom
  <br>0x80 = Underwater</td></tr></table>
</td></tr>
<tr>
<td title='0x0116 = 278'>0x0116</td>
<td>ExtraInfo
  <br>ExtraInfo2
  <br>ExtraInfo3</td>
<td class=c>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#ExtraInfo'>Sony ExtraInfo Tags</a>
  <br>--&gt; <a href='Sony.html#ExtraInfo2'>Sony ExtraInfo2 Tags</a>
  <br>--&gt; <a href='Sony.html#ExtraInfo3'>Sony ExtraInfo3 Tags</a></td></tr>
<tr class=b>
<td title='0x0e00 = 3584'>0x0e00</td>
<td>PrintIM</td>
<td class=c>-</td>
<td>--&gt; <a href='PrintIM.html'>PrintIM Tags</a></td></tr>
<tr>
<td title='0x1000 = 4096'>0x1000</td>
<td>MultiBurstMode</td>
<td class=c>undef</td>
<td><span class=s><span class=n>(MultiBurst tags valid only for models with this feature, like the F88)</span>
  <br>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td title='0x1001 = 4097'>0x1001</td>
<td>MultiBurstImageWidth</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x1002 = 4098'>0x1002</td>
<td>MultiBurstImageHeight</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x1003 = 4099'>0x1003</td>
<td>Panorama</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Panorama'>Sony Panorama Tags</a></td></tr>
<tr>
<td title='0x2001 = 8193'>0x2001</td>
<td>PreviewImage</td>
<td class=c>undef</td>
<td><span class=s><span class=n>(HD-size preview in JPEG images from almost all DSLR/SLT/ILCA/NEX/ILCE.)</span></span></td></tr>
<tr class=b>
<td title='0x2002 = 8194'>0x2002</td>
<td>Rating</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2004 = 8196'>0x2004</td>
<td>Contrast</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x2005 = 8197'>0x2005</td>
<td>Saturation</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2006 = 8198'>0x2006</td>
<td>Sharpness</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x2007 = 8199'>0x2007</td>
<td>Brightness</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2008 = 8200'>0x2008</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0x0 = Off
  <br>0x1 = On (unused)
  <br>0x10001 = On (dark subtracted)
  <br>0xffff0000 = Off (65535)
  <br>0xffff0001 = On (65535)
  <br>0xffffffff = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x2009 = 8201'>0x2009</td>
<td>HighISONoiseReduction</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Low
  <br>2 = Normal</td><td>&nbsp;&nbsp;</td>
  <td>3 = High
  <br>256 = Auto
  <br>65535 = n/a</td></tr></table>
</td></tr>
<tr>
<td title='0x200a = 8202'>0x200a</td>
<td>HDR</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(stored as a 32-bit integer, but read as two 16-bit integers)</span>
  <br>[Value 0]</span><table class=cols><tr>
  <td>0x0 = Off
  <br>0x1 = Auto
  <br>0x10 = 1.0 EV
  <br>0x11 = 1.5 EV
  <br>0x12 = 2.0 EV</td><td>&nbsp;&nbsp;</td>
  <td>0x13 = 2.5 EV
  <br>0x14 = 3.0 EV
  <br>0x15 = 3.5 EV
  <br>0x16 = 4.0 EV
  <br>0x17 = 4.5 EV</td><td>&nbsp;&nbsp;</td>
  <td>0x18 = 5.0 EV
  <br>0x19 = 5.5 EV
  <br>0x1a = 6.0 EV</td></tr></table>
<span class=s>[Value 1]
  <br>0x0 = Uncorrected image
  <br>0x1 = HDR image (good)
  <br>0x2 = HDR image (fail 1)
  <br>0x3 = HDR image (fail 2)</span></td></tr>
<tr class=b>
<td title='0x200b = 8203'>0x200b</td>
<td>MultiFrameNoiseReduction</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(may not be valid for RS100)</span>
  <br>0 = Off
  <br>1 = On
  <br>255 = n/a</span></td></tr>
<tr>
<td title='0x200e = 8206'>0x200e</td>
<td>PictureEffect</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Toy Camera
  <br>2 = Pop Color
  <br>3 = Posterization
  <br>4 = Posterization B/W
  <br>5 = Retro Photo
  <br>6 = Soft High Key
  <br>7 = Partial Color (red)
  <br>8 = Partial Color (green)
  <br>9 = Partial Color (blue)
  <br>10 = Partial Color (yellow)
  <br>13 = High Contrast Monochrome
  <br>16 = Toy Camera (normal)
  <br>17 = Toy Camera (cool)
  <br>18 = Toy Camera (warm)
  <br>19 = Toy Camera (green)
  <br>20 = Toy Camera (magenta)
  <br>32 = Soft Focus (low)
  <br>33 = Soft Focus
  <br>34 = Soft Focus (high)
  <br>48 = Miniature (auto)
  <br>49 = Miniature (top)
  <br>50 = Miniature (middle horizontal)
  <br>51 = Miniature (bottom)
  <br>52 = Miniature (left)
  <br>53 = Miniature (middle vertical)
  <br>54 = Miniature (right)
  <br>64 = HDR Painting (low)
  <br>65 = HDR Painting
  <br>66 = HDR Painting (high)
  <br>80 = Rich-tone Monochrome
  <br>97 = Water Color
  <br>98 = Water Color 2
  <br>112 = Illustration (low)
  <br>113 = Illustration
  <br>114 = Illustration (high)</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x200f = 8207'>0x200f</td>
<td>SoftSkinEffect</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>1 = Low
  <br>2 = Mid
  <br>3 = High
  <br>4294967295 = n/a</span></td></tr>
<tr>
<td title='0x2010 = 8208'>0x2010</td>
<td>Tag2010a
  <br>Tag2010b
  <br>Tag2010c
  <br>Tag2010d
  <br>Tag2010e
  <br>Tag2010f
  <br>Tag2010g
  <br>Tag2010h
  <br>Tag2010i</td>
<td class=c>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag2010a'>Sony Tag2010a Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010b'>Sony Tag2010b Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010c'>Sony Tag2010c Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010d'>Sony Tag2010d Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010e'>Sony Tag2010e Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010f'>Sony Tag2010f Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010g'>Sony Tag2010g Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010h'>Sony Tag2010h Tags</a>
  <br>--&gt; <a href='Sony.html#Tag2010i'>Sony Tag2010i Tags</a></td></tr>
<tr class=b>
<td title='0x2011 = 8209'>0x2011</td>
<td>VignettingCorrection</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>2 = Auto
  <br>4294967295 = n/a</span></td></tr>
<tr>
<td title='0x2012 = 8210'>0x2012</td>
<td>LateralChromaticAberration</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>2 = Auto
  <br>4294967295 = n/a</span></td></tr>
<tr class=b>
<td title='0x2013 = 8211'>0x2013</td>
<td>DistortionCorrectionSetting</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>2 = Auto
  <br>4294967295 = n/a</span></td></tr>
<tr>
<td title='0x2014 = 8212'>0x2014</td>
<td>WBShiftAB_GM</td>
<td class=c>int32s[2]</td>
<td><span class=s><span class=n>(2 numbers: 1. positive is a shift toward amber, 2. positive is a shift
toward magenta)</span></span></td></tr>
<tr class=b>
<td title='0x2016 = 8214'>0x2016</td>
<td>AutoPortraitFramed</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(&quot;Yes&quot; if this image was created by the Auto Portrait Framing feature)</span>
  <br>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td title='0x2017 = 8215'>0x2017</td>
<td>FlashAction</td>
<td class=c>int32u</td>
<td><span class=s>0 = Did not fire
  <br>1 = Flash Fired
  <br>2 = External Flash Fired
  <br>3 = Wireless Controlled Flash Fired</span></td></tr>
<tr class=b>
<td title='0x201a = 8218'>0x201a</td>
<td>ElectronicFrontCurtainShutter</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td title='0x201b = 8219'>0x201b</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Manual
  <br>2 = AF-S
  <br>3 = AF-C</td><td>&nbsp;&nbsp;</td>
  <td>4 = AF-A
  <br>6 = DMF
  <br>7 = AF-D</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x201c = 8220'>0x201c</td>
<td>AFAreaModeSetting</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(SLT models)</span>
  <br>0 = Wide
  <br>4 = Local
  <br>8 = Zone
  <br>9 = Spot
  <br><span class=n>(NEX, ILCE and some DSC models)</span></span><table class=cols><tr>
  <td>0 = Wide
  <br>1 = Center
  <br>3 = Flexible Spot
  <br>4 = Flexible Spot (LA-EA4)
  <br>9 = Center (LA-EA4)
  <br>11 = Zone
  <br>12 = Expanded Flexible Spot
  <br>13 = Custom AF Area</td></tr></table>
<span class=s><span class=n>(ILCA models)</span>
  <br>0 = Wide
  <br>4 = Flexible Spot
  <br>8 = Zone
  <br>9 = Center
  <br>12 = Expanded Flexible Spot</span></td></tr>
<tr>
<td title='0x201d = 8221'>0x201d</td>
<td>FlexibleSpotPosition</td>
<td class=c>int16u[2]</td>
<td><span class=s><span class=n>(X and Y coordinates of the AF point, valid only when AFAreaMode is Flexible
Spot)</span></span></td></tr>
<tr class=b>
<td title='0x201e = 8222'>0x201e</td>
<td>AFPointSelected</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(SLT models or ILCE with LA-EA2/EA4)</span></span><table class=cols><tr>
  <td>0 = Auto
  <br>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right
  <br>5 = Lower-right
  <br>6 = Bottom
  <br>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left</td><td>&nbsp;&nbsp;</td>
  <td>10 = Far Right
  <br>11 = Far Left
  <br>12 = Upper-middle
  <br>13 = Near Right
  <br>14 = Lower-middle
  <br>15 = Near Left
  <br>16 = Upper Far Right
  <br>17 = Lower Far Right
  <br>18 = Lower Far Left
  <br>19 = Upper Far Left</td></tr></table>
<span class=s><span class=n>(ILCA-68 and ILCA-77M2)</span></span><table class=cols><tr>
  <td>-1 = Auto
  <br>0 = A5
  <br>1 = A6
  <br>2 = A7
  <br>3 = B2
  <br>4 = B3
  <br>5 = B4
  <br>6 = B5
  <br>7 = B6
  <br>8 = B7
  <br>9 = B8
  <br>10 = B9
  <br>11 = B10
  <br>12 = C1
  <br>13 = C2
  <br>14 = C3</td><td>&nbsp;&nbsp;</td>
  <td>15 = C4
  <br>16 = C5
  <br>17 = C6
  <br>18 = C7
  <br>19 = C8
  <br>20 = C9
  <br>21 = C10
  <br>22 = C11
  <br>23 = D1
  <br>24 = D2
  <br>25 = D3
  <br>26 = D4
  <br>27 = D5
  <br>28 = D6
  <br>29 = D7
  <br>30 = D8</td><td>&nbsp;&nbsp;</td>
  <td>31 = D9
  <br>32 = D10
  <br>33 = D11
  <br>34 = E1
  <br>35 = E2
  <br>36 = E3
  <br>37 = E4
  <br>38 = E5
  <br>39 = E6 (Center)
  <br>40 = E7
  <br>41 = E8
  <br>42 = E9
  <br>43 = E10
  <br>44 = E11
  <br>45 = F1
  <br>46 = F2</td><td>&nbsp;&nbsp;</td>
  <td>47 = F3
  <br>48 = F4
  <br>49 = F5
  <br>50 = F6
  <br>51 = F7
  <br>52 = F8
  <br>53 = F9
  <br>54 = F10
  <br>55 = F11
  <br>56 = G1
  <br>57 = G2
  <br>58 = G3
  <br>59 = G4
  <br>60 = G5
  <br>61 = G6
  <br>62 = G7</td><td>&nbsp;&nbsp;</td>
  <td>63 = G8
  <br>64 = G9
  <br>65 = G10
  <br>66 = G11
  <br>67 = H2
  <br>68 = H3
  <br>69 = H4
  <br>70 = H5
  <br>71 = H6
  <br>72 = H7
  <br>73 = H8
  <br>74 = H9
  <br>75 = H10
  <br>76 = I5
  <br>77 = I6
  <br>78 = I7</td></tr></table>
<span class=s><span class=n>(ILCA-99M2 when AFAreaModeSetting is not Zone)</span></span><table class=cols><tr>
  <td>0 = Auto
  <br>93 = A5 (93)
  <br>94 = A6 (94)
  <br>95 = A7 (95)
  <br>106 = B2 (106)
  <br>107 = B3 (107)
  <br>108 = B4 (108)
  <br>110 = B5 (110)
  <br>111 = B6 (111)
  <br>112 = B7 (112)
  <br>114 = B8 (114)
  <br>115 = B9 (115)
  <br>116 = B10 (116)
  <br>122 = C1 (122)
  <br>123 = C2 (123)
  <br>124 = C3 (124)
  <br>125 = C4 (125)
  <br>127 = C5 (127)
  <br>128 = C6 (128)
  <br>129 = C7 (129)</td><td>&nbsp;&nbsp;</td>
  <td>131 = C8 (131)
  <br>132 = C9 (132)
  <br>133 = C10 (133)
  <br>134 = C11 (134)
  <br>139 = D1 (139)
  <br>140 = D2 (140)
  <br>141 = D3 (141)
  <br>142 = D4 (142)
  <br>144 = D5 (144)
  <br>145 = D6 (145)
  <br>146 = D7 (146)
  <br>148 = D8 (148)
  <br>149 = D9 (149)
  <br>150 = D10 (150)
  <br>151 = D11 (151)
  <br>156 = E1 (156)
  <br>157 = E2 (157)
  <br>158 = E3 (158)
  <br>159 = E4 (159)
  <br>161 = E5 (161)</td><td>&nbsp;&nbsp;</td>
  <td>162 = E6 (162, Center)
  <br>163 = E7 (163)
  <br>165 = E8 (165)
  <br>166 = E9 (166)
  <br>167 = E10 (167)
  <br>168 = E11 (168)
  <br>173 = F1 (173)
  <br>174 = F2 (174)
  <br>175 = F3 (175)
  <br>176 = F4 (176)
  <br>178 = F5 (178)
  <br>179 = F6 (179)
  <br>180 = F7 (180)
  <br>182 = F8 (182)
  <br>183 = F9 (183)
  <br>184 = F10 (184)
  <br>185 = F11 (185)
  <br>190 = G1 (190)
  <br>191 = G2 (191)
  <br>192 = G3 (192)</td><td>&nbsp;&nbsp;</td>
  <td>193 = G4 (193)
  <br>195 = G5 (195)
  <br>196 = G6 (196)
  <br>197 = G7 (197)
  <br>199 = G8 (199)
  <br>200 = G9 (200)
  <br>201 = G10 (201)
  <br>202 = G11 (202)
  <br>208 = H2 (208)
  <br>209 = H3 (209)
  <br>210 = H4 (210)
  <br>212 = H5 (212)
  <br>213 = H6 (213)
  <br>214 = H7 (214)
  <br>216 = H8 (216)
  <br>217 = H9 (217)
  <br>218 = H10 (218)
  <br>229 = I5 (229)
  <br>230 = I6 (230)
  <br>231 = I7 (231)</td></tr></table>
<span class=s><span class=n>(ILCA models when AFAreaModeSetting is set to Zone)</span></span><table class=cols><tr>
  <td>0 = n/a
  <br>1 = Top Left Zone
  <br>2 = Top Zone
  <br>3 = Top Right Zone
  <br>4 = Left Zone</td><td>&nbsp;&nbsp;</td>
  <td>5 = Center Zone
  <br>6 = Right Zone
  <br>7 = Bottom Left Zone
  <br>8 = Bottom Zone
  <br>9 = Bottom Right Zone</td></tr></table>
<span class=s><span class=n>(NEX and ILCE models)</span></span><table class=cols><tr>
  <td>0 = n/a
  <br>1 = Center Zone
  <br>2 = Top Zone
  <br>3 = Right Zone
  <br>4 = Left Zone</td><td>&nbsp;&nbsp;</td>
  <td>5 = Bottom Zone
  <br>6 = Bottom Right Zone
  <br>7 = Bottom Left Zone
  <br>8 = Top Left Zone
  <br>9 = Top Right Zone</td></tr></table>
</td></tr>
<tr>
<td title='0x2020 = 8224'>0x2020</td>
<td>AFPointsUsed</td>
<td class=c>no</td>
<td><span class=s><span class=n>(SLT models, or NEX/ILCE with A-mount lenses)</span></span><table class=cols><tr>
  <td>0x0 = (none)
  <br>Bit 0 = Center
  <br>Bit 1 = Top
  <br>Bit 2 = Upper-right
  <br>Bit 3 = Right
  <br>Bit 4 = Lower-right
  <br>Bit 5 = Bottom
  <br>Bit 6 = Lower-left
  <br>Bit 7 = Left
  <br>Bit 8 = Upper-left</td><td>&nbsp;&nbsp;</td>
  <td>Bit 9 = Far Right
  <br>Bit 10 = Far Left
  <br>Bit 11 = Upper-middle
  <br>Bit 12 = Near Right
  <br>Bit 13 = Lower-middle
  <br>Bit 14 = Near Left
  <br>Bit 15 = Upper Far Right
  <br>Bit 16 = Lower Far Right
  <br>Bit 17 = Lower Far Left
  <br>Bit 18 = Upper Far Left</td></tr></table>
<span class=s><span class=n>(ILCA models)</span></span><table class=cols><tr>
  <td>0x0 = (none)
  <br>Bit 0 = A5
  <br>Bit 1 = A6
  <br>Bit 2 = A7
  <br>Bit 3 = B2
  <br>Bit 4 = B3
  <br>Bit 5 = B4
  <br>Bit 6 = B5
  <br>Bit 7 = B6
  <br>Bit 8 = B7
  <br>Bit 9 = B8
  <br>Bit 10 = B9
  <br>Bit 11 = B10
  <br>Bit 12 = C1
  <br>Bit 13 = C2
  <br>Bit 14 = C3
  <br>Bit 15 = C4
  <br>Bit 16 = C5
  <br>Bit 17 = C6
  <br>Bit 18 = C7</td><td>&nbsp;&nbsp;</td>
  <td>Bit 19 = C8
  <br>Bit 20 = C9
  <br>Bit 21 = C10
  <br>Bit 22 = C11
  <br>Bit 23 = D1
  <br>Bit 24 = D2
  <br>Bit 25 = D3
  <br>Bit 26 = D4
  <br>Bit 27 = D5
  <br>Bit 28 = D6
  <br>Bit 29 = D7
  <br>Bit 30 = D8
  <br>Bit 31 = D9
  <br>Bit 32 = D10
  <br>Bit 33 = D11
  <br>Bit 34 = E1
  <br>Bit 35 = E2
  <br>Bit 36 = E3
  <br>Bit 37 = E4
  <br>Bit 38 = E5</td><td>&nbsp;&nbsp;</td>
  <td>Bit 39 = E6
  <br>Bit 40 = E7
  <br>Bit 41 = E8
  <br>Bit 42 = E9
  <br>Bit 43 = E10
  <br>Bit 44 = E11
  <br>Bit 45 = F1
  <br>Bit 46 = F2
  <br>Bit 47 = F3
  <br>Bit 48 = F4
  <br>Bit 49 = F5
  <br>Bit 50 = F6
  <br>Bit 51 = F7
  <br>Bit 52 = F8
  <br>Bit 53 = F9
  <br>Bit 54 = F10
  <br>Bit 55 = F11
  <br>Bit 56 = G1
  <br>Bit 57 = G2
  <br>Bit 58 = G3</td><td>&nbsp;&nbsp;</td>
  <td>Bit 59 = G4
  <br>Bit 60 = G5
  <br>Bit 61 = G6
  <br>Bit 62 = G7
  <br>Bit 63 = G8
  <br>Bit 64 = G9
  <br>Bit 65 = G10
  <br>Bit 66 = G11
  <br>Bit 67 = H2
  <br>Bit 68 = H3
  <br>Bit 69 = H4
  <br>Bit 70 = H5
  <br>Bit 71 = H6
  <br>Bit 72 = H7
  <br>Bit 73 = H8
  <br>Bit 74 = H9
  <br>Bit 75 = H10
  <br>Bit 76 = I5
  <br>Bit 77 = I6
  <br>Bit 78 = I7</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x2021 = 8225'>0x2021</td>
<td>AFTracking</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Face tracking
  <br>2 = Lock On AF</span></td></tr>
<tr>
<td title='0x2022 = 8226'>0x2022</td>
<td>FocalPlaneAFPointsUsed</td>
<td class=c>no</td>
<td><span class=s><span class=n>(On-sensor/focal-plane phase AF points for ILCE with hybrid AF)</span>
  <br>0x0 = (none)
  <br>0x0 = (none)</span></td></tr>
<tr class=b>
<td title='0x2023 = 8227'>0x2023</td>
<td>MultiFrameNREffect</td>
<td class=c>int32u</td>
<td><span class=s>0 = Normal
  <br>1 = High</span></td></tr>
<tr>
<td title='0x2026 = 8230'>0x2026</td>
<td>WBShiftAB_GM_Precise</td>
<td class=c title=' ~ = Writable only with -n'>int32s[2]~</td>
<td><span class=s><span class=n>(2 numbers: 1. positive is a shift toward amber, 2. positive is a shift
toward magenta)</span></span></td></tr>
<tr class=b>
<td title='0x2027 = 8231'>0x2027</td>
<td>FocusLocation</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2028 = 8232'>0x2028</td>
<td>VariableLowPassFilter</td>
<td class=c>int16u[2]</td>
<td><span class=s>&#39;0 0&#39; = n/a
  <br>&#39;1 0&#39; = Off
  <br>&#39;1 1&#39; = Standard
  <br>&#39;1 2&#39; = High
  <br>&#39;65535 65535&#39; = n/a</span></td></tr>
<tr class=b>
<td title='0x2029 = 8233'>0x2029</td>
<td>RAWFileType</td>
<td class=c>int16u</td>
<td><span class=s>0 = Compressed RAW
  <br>1 = Uncompressed RAW
  <br>2 = Lossless Compressed RAW
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0x202a = 8234'>0x202a</td>
<td>Tag202a</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag202a'>Sony Tag202a Tags</a></td></tr>
<tr class=b>
<td title='0x202b = 8235'>0x202b</td>
<td>PrioritySetInAWB</td>
<td class=c>int8u</td>
<td><span class=s>0 = Standard
  <br>1 = Ambience
  <br>2 = White</span></td></tr>
<tr>
<td title='0x202c = 8236'>0x202c</td>
<td>MeteringMode2</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0x100 = Multi-segment
  <br>0x200 = Center-weighted average
  <br>0x301 = Spot (Standard)
  <br>0x302 = Spot (Large)
  <br>0x400 = Average
  <br>0x500 = Highlight</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x202d = 8237'>0x202d</td>
<td>ExposureStandardAdjustment</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x202e = 8238'>0x202e</td>
<td>Quality</td>
<td class=c>int16u[2]</td>
<td><table class=cols><tr>
  <td>&#39;0 0&#39; = n/a
  <br>&#39;0 1&#39; = Standard
  <br>&#39;0 2&#39; = Fine
  <br>&#39;0 3&#39; = Extra Fine
  <br>&#39;0 4&#39; = Light
  <br>&#39;1 0&#39; = RAW
  <br>&#39;1 1&#39; = RAW + Standard
  <br>&#39;1 2&#39; = RAW + Fine
  <br>&#39;1 3&#39; = RAW + Extra Fine
  <br>&#39;1 4&#39; = RAW + Light
  <br>&#39;2 0&#39; = S-size RAW
  <br>&#39;3 0&#39; = M-size RAW
  <br>&#39;3 2&#39; = M-size RAW + Fine
  <br>&#39;3 3&#39; = M-size RAW + Extra Fine</td></tr></table>
</td></tr>
<tr class=b>
<td title='0x202f = 8239'>0x202f</td>
<td>PixelShiftInfo</td>
<td class=c>undef</td>
<td><span class=s>&#39;00000000 0 0 0x0&#39; = n/a</span></td></tr>
<tr>
<td title='0x2031 = 8241'>0x2031</td>
<td>SerialNumber</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x2032 = 8242'>0x2032</td>
<td>Shadows</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2033 = 8243'>0x2033</td>
<td>Highlights</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x2034 = 8244'>0x2034</td>
<td>Fade</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2035 = 8245'>0x2035</td>
<td>SharpnessRange</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x2036 = 8246'>0x2036</td>
<td>Clarity</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2037 = 8247'>0x2037</td>
<td>FocusFrameSize</td>
<td class=c>no</td>
<td><span class=s><span class=n>(width and height of FocusFrame, centered on FocusLocation)</span></span></td></tr>
<tr class=b>
<td title='0x2039 = 8249'>0x2039</td>
<td>JPEG-HEIFSwitch</td>
<td class=c>int16u</td>
<td><span class=s>0 = JPEG
  <br>1 = HEIF
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0x2044 = 8260'>0x2044</td>
<td>HiddenInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#HiddenInfo'>Sony HiddenInfo Tags</a></td></tr>
<tr class=b>
<td title='0x204a = 8266'>0x204a</td>
<td>FocusLocation2</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x3000 = 12288'>0x3000</td>
<td>ShotInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ShotInfo'>Sony ShotInfo Tags</a></td></tr>
<tr class=b>
<td title='0x900b = 36875'>0x900b</td>
<td>Tag900b</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag900b'>Sony Tag900b Tags</a></td></tr>
<tr>
<td title='0x9050 = 36944'>0x9050</td>
<td>Tag9050a
  <br>Tag9050b
  <br>Tag9050c
  <br>Tag9050d</td>
<td class=c>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag9050a'>Sony Tag9050a Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9050b'>Sony Tag9050b Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9050c'>Sony Tag9050c Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9050d'>Sony Tag9050d Tags</a></td></tr>
<tr class=b>
<td title='0x9400 = 37888'>0x9400</td>
<td>Tag9400a
  <br>Tag9400b
  <br>Tag9400c</td>
<td class=c>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag9400a'>Sony Tag9400a Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9400b'>Sony Tag9400b Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9400c'>Sony Tag9400c Tags</a></td></tr>
<tr>
<td title='0x9401 = 37889'>0x9401</td>
<td>Tag9401</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag9401'>Sony Tag9401 Tags</a></td></tr>
<tr class=b>
<td title='0x9402 = 37890'>0x9402</td>
<td>Tag9402</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag9402'>Sony Tag9402 Tags</a></td></tr>
<tr>
<td title='0x9403 = 37891'>0x9403</td>
<td>Tag9403</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag9403'>Sony Tag9403 Tags</a></td></tr>
<tr class=b>
<td title='0x9404 = 37892'>0x9404</td>
<td>Tag9404a
  <br>Tag9404b
  <br>Tag9404c</td>
<td class=c>-<br>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag9404a'>Sony Tag9404a Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9404b'>Sony Tag9404b Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9404c'>Sony Tag9404c Tags</a></td></tr>
<tr>
<td title='0x9405 = 37893'>0x9405</td>
<td>Tag9405a
  <br>Tag9405b</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag9405a'>Sony Tag9405a Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9405b'>Sony Tag9405b Tags</a></td></tr>
<tr class=b>
<td title='0x9406 = 37894'>0x9406</td>
<td>Tag9406
  <br>Tag9406b</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#Tag9406'>Sony Tag9406 Tags</a>
  <br>--&gt; <a href='Sony.html#Tag9406b'>Sony Tag9406b Tags</a></td></tr>
<tr>
<td title='0x940a = 37898'>0x940a</td>
<td>Tag940a</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag940a'>Sony Tag940a Tags</a></td></tr>
<tr class=b>
<td title='0x940c = 37900'>0x940c</td>
<td>Tag940c</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag940c'>Sony Tag940c Tags</a></td></tr>
<tr>
<td title='0x940e = 37902'>0x940e</td>
<td>AFInfo
  <br>Tag940e</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#AFInfo'>Sony AFInfo Tags</a>
  <br>--&gt; <a href='Sony.html#Tag940e'>Sony Tag940e Tags</a></td></tr>
<tr class=b>
<td title='0x9416 = 37910'>0x9416</td>
<td>Sony_0x9416</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#Tag9416'>Sony Tag9416 Tags</a></td></tr>
<tr>
<td title='0xb000 = 45056'>0xb000</td>
<td>FileFormat</td>
<td class=c>int8u[4]</td>
<td><table class=cols><tr>
  <td>&#39;0 0 0 2&#39; = JPEG
  <br>&#39;1 0 0 0&#39; = SR2
  <br>&#39;2 0 0 0&#39; = ARW 1.0
  <br>&#39;3 0 0 0&#39; = ARW 2.0
  <br>&#39;3 1 0 0&#39; = ARW 2.1
  <br>&#39;3 2 0 0&#39; = ARW 2.2
  <br>&#39;3 3 0 0&#39; = ARW 2.3
  <br>&#39;3 3 1 0&#39; = ARW 2.3.1</td><td>&nbsp;&nbsp;</td>
  <td>&#39;3 3 2 0&#39; = ARW 2.3.2
  <br>&#39;3 3 3 0&#39; = ARW 2.3.3
  <br>&#39;3 3 5 0&#39; = ARW 2.3.5
  <br>&#39;4 0 0 0&#39; = ARW 4.0
  <br>&#39;4 0 1 0&#39; = ARW 4.0.1
  <br>&#39;5 0 0 0&#39; = ARW 5.0
  <br>&#39;5 0 1 0&#39; = ARW 5.0.1</td></tr></table>
</td></tr>
<tr class=b>
<td title='0xb001 = 45057'>0xb001</td>
<td>SonyModelID</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>2 = DSC-R1
  <br>256 = DSLR-A100
  <br>257 = DSLR-A900
  <br>258 = DSLR-A700
  <br>259 = DSLR-A200
  <br>260 = DSLR-A350
  <br>261 = DSLR-A300
  <br>262 = DSLR-A900 (APS-C mode)
  <br>263 = DSLR-A380/A390
  <br>264 = DSLR-A330
  <br>265 = DSLR-A230
  <br>266 = DSLR-A290
  <br>269 = DSLR-A850
  <br>270 = DSLR-A850 (APS-C mode)
  <br>273 = DSLR-A550
  <br>274 = DSLR-A500
  <br>275 = DSLR-A450
  <br>278 = NEX-5
  <br>279 = NEX-3
  <br>280 = SLT-A33
  <br>281 = SLT-A55 / SLT-A55V
  <br>282 = DSLR-A560
  <br>283 = DSLR-A580
  <br>284 = NEX-C3
  <br>285 = SLT-A35
  <br>286 = SLT-A65 / SLT-A65V
  <br>287 = SLT-A77 / SLT-A77V
  <br>288 = NEX-5N
  <br>289 = NEX-7
  <br>290 = NEX-VG20E
  <br>291 = SLT-A37
  <br>292 = SLT-A57
  <br>293 = NEX-F3
  <br>294 = SLT-A99 / SLT-A99V
  <br>295 = NEX-6
  <br>296 = NEX-5R
  <br>297 = DSC-RX100
  <br>298 = DSC-RX1
  <br>299 = NEX-VG900
  <br>300 = NEX-VG30E
  <br>302 = ILCE-3000 / ILCE-3500
  <br>303 = SLT-A58
  <br>305 = NEX-3N
  <br>306 = ILCE-7
  <br>307 = NEX-5T
  <br>308 = DSC-RX100M2
  <br>309 = DSC-RX10
  <br>310 = DSC-RX1R
  <br>311 = ILCE-7R
  <br>312 = ILCE-6000
  <br>313 = ILCE-5000
  <br>317 = DSC-RX100M3
  <br>318 = ILCE-7S</td><td>&nbsp;&nbsp;</td>
  <td>319 = ILCA-77M2
  <br>339 = ILCE-5100
  <br>340 = ILCE-7M2
  <br>341 = DSC-RX100M4
  <br>342 = DSC-RX10M2
  <br>344 = DSC-RX1RM2
  <br>346 = ILCE-QX1
  <br>347 = ILCE-7RM2
  <br>350 = ILCE-7SM2
  <br>353 = ILCA-68
  <br>354 = ILCA-99M2
  <br>355 = DSC-RX10M3
  <br>356 = DSC-RX100M5
  <br>357 = ILCE-6300
  <br>358 = ILCE-9
  <br>360 = ILCE-6500
  <br>362 = ILCE-7RM3
  <br>363 = ILCE-7M3
  <br>364 = DSC-RX0
  <br>365 = DSC-RX10M4
  <br>366 = DSC-RX100M6
  <br>367 = DSC-HX99
  <br>369 = DSC-RX100M5A
  <br>371 = ILCE-6400
  <br>372 = DSC-RX0M2
  <br>373 = DSC-HX95
  <br>374 = DSC-RX100M7
  <br>375 = ILCE-7RM4
  <br>376 = ILCE-9M2
  <br>378 = ILCE-6600
  <br>379 = ILCE-6100
  <br>380 = ZV-1
  <br>381 = ILCE-7C
  <br>382 = ZV-E10
  <br>383 = ILCE-7SM3
  <br>384 = ILCE-1
  <br>385 = ILME-FX3
  <br>386 = ILCE-7RM3A
  <br>387 = ILCE-7RM4A
  <br>388 = ILCE-7M4
  <br>389 = ZV-1F
  <br>390 = ILCE-7RM5
  <br>391 = ILME-FX30
  <br>392 = ILCE-9M3
  <br>393 = ZV-E1
  <br>394 = ILCE-6700
  <br>395 = ZV-1M2
  <br>396 = ILCE-7CR
  <br>397 = ILCE-7CM2
  <br>398 = ILX-LR1
  <br>399 = ZV-E10M2
  <br>400 = ILCE-1M2</td></tr></table>
</td></tr>
<tr>
<td title='0xb020 = 45088'>0xb020</td>
<td>CreativeStyle</td>
<td class=c>string</td>
<td><table class=cols><tr>
  <td>&#39;AdobeRGB&#39; = Adobe RGB
  <br>&#39;Autumnleaves&#39; = Autumn Leaves
  <br>&#39;BW&#39; = B&amp;W
  <br>&#39;Clear&#39; = Clear
  <br>&#39;Deep&#39; = Deep
  <br>&#39;FL&#39; = FL
  <br>&#39;IN&#39; = IN
  <br>&#39;Landscape&#39; = Landscape
  <br>&#39;Light&#39; = Light
  <br>&#39;Neutral&#39; = Neutral
  <br>&#39;Nightview&#39; = Night View/Portrait
  <br>&#39;None&#39; = None
  <br>&#39;Portrait&#39; = Portrait
  <br>&#39;Real&#39; = Real
  <br>&#39;SH&#39; = SH
  <br>&#39;Sepia&#39; = Sepia
  <br>&#39;Standard&#39; = Standard
  <br>&#39;Sunset&#39; = Sunset
  <br>&#39;VV2&#39; = Vivid 2
  <br>&#39;Vivid&#39; = Vivid</td></tr></table>
</td></tr>
<tr class=b>
<td title='0xb021 = 45089'>0xb021</td>
<td>ColorTemperature</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0xb022 = 45090'>0xb022</td>
<td>ColorCompensationFilter</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td title='0xb023 = 45091'>0xb023</td>
<td>SceneMode</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0 = Standard
  <br>1 = Portrait
  <br>2 = Text
  <br>3 = Night Scene
  <br>4 = Sunset
  <br>5 = Sports
  <br>6 = Landscape
  <br>7 = Night Portrait
  <br>8 = Macro
  <br>9 = Super Macro
  <br>16 = Auto
  <br>17 = Night View/Portrait
  <br>18 = Sweep Panorama</td><td>&nbsp;&nbsp;</td>
  <td>19 = Handheld Night Shot
  <br>20 = Anti Motion Blur
  <br>21 = Cont. Priority AE
  <br>22 = Auto+
  <br>23 = 3D Sweep Panorama
  <br>24 = Superior Auto
  <br>25 = High Sensitivity
  <br>26 = Fireworks
  <br>27 = Food
  <br>28 = Pet
  <br>33 = HDR
  <br>65535 = n/a</td></tr></table>
</td></tr>
<tr>
<td title='0xb024 = 45092'>0xb024</td>
<td>ZoneMatching</td>
<td class=c>int32u</td>
<td><span class=s>0 = ISO Setting Used
  <br>1 = High Key
  <br>2 = Low Key</span></td></tr>
<tr class=b>
<td title='0xb025 = 45093'>0xb025</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Standard
  <br>2 = Advanced Auto
  <br>3 = Auto
  <br>8 = Advanced Lv1
  <br>9 = Advanced Lv2
  <br>10 = Advanced Lv3</td><td>&nbsp;&nbsp;</td>
  <td>11 = Advanced Lv4
  <br>12 = Advanced Lv5
  <br>16 = Lv1
  <br>17 = Lv2
  <br>18 = Lv3
  <br>19 = Lv4
  <br>20 = Lv5</td></tr></table>
</td></tr>
<tr>
<td title='0xb026 = 45094'>0xb026</td>
<td>ImageStabilization</td>
<td class=c>int32u</td>
<td><span class=s>0 = Off
  <br>1 = On
  <br>4294967295 = n/a</span></td></tr>
<tr class=b>
<td title='0xb027 = 45095'>0xb027</td>
<td>LensType</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr>
<td title='0xb028 = 45096'>0xb028</td>
<td>MinoltaMakerNote</td>
<td class=c>-</td>
<td>--&gt; <a href='Minolta.html'>Minolta Tags</a></td></tr>
<tr class=b>
<td title='0xb029 = 45097'>0xb029</td>
<td>ColorMode</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0 = Standard
  <br>1 = Vivid
  <br>2 = Portrait
  <br>3 = Landscape
  <br>4 = Sunset
  <br>5 = Night View/Portrait
  <br>6 = B&amp;W
  <br>7 = Adobe RGB
  <br>12 = Neutral
  <br>13 = Clear
  <br>14 = Deep
  <br>15 = Light
  <br>16 = Autumn Leaves</td><td>&nbsp;&nbsp;</td>
  <td>17 = Sepia
  <br>18 = FL
  <br>19 = Vivid 2
  <br>20 = IN
  <br>21 = SH
  <br>100 = Neutral
  <br>101 = Clear
  <br>102 = Deep
  <br>103 = Light
  <br>104 = Night View
  <br>105 = Autumn Leaves
  <br>255 = Off
  <br>4294967295 = n/a</td></tr></table>
</td></tr>
<tr>
<td title='0xb02a = 45098'>0xb02a</td>
<td>LensSpec</td>
<td class=c>int8u[8]</td>
<td><span class=s><span class=n>(like LensInfo, but also specifies lens features: DT, E, ZA, G, SSM, SAM,
OSS, STF, Reflex, Macro and Fisheye)</span></span></td></tr>
<tr class=b>
<td title='0xb02b = 45099'>0xb02b</td>
<td>FullImageSize</td>
<td class=c>int32u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0xb02c = 45100'>0xb02c</td>
<td>PreviewImageSize</td>
<td class=c>int32u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0xb040 = 45120'>0xb040</td>
<td>Macro</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On
  <br>2 = Close Focus
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0xb041 = 45121'>0xb041</td>
<td>ExposureMode</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Program AE
  <br>1 = Portrait
  <br>2 = Beach
  <br>3 = Sports
  <br>4 = Snow
  <br>5 = Landscape
  <br>6 = Auto
  <br>7 = Aperture-priority AE
  <br>8 = Shutter speed priority AE
  <br>9 = Night Scene / Twilight
  <br>10 = Hi-Speed Shutter
  <br>11 = Twilight Portrait
  <br>12 = Soft Snap/Portrait
  <br>13 = Fireworks
  <br>14 = Smile Shutter
  <br>15 = Manual</td><td>&nbsp;&nbsp;</td>
  <td>18 = High Sensitivity
  <br>19 = Macro
  <br>20 = Advanced Sports Shooting
  <br>29 = Underwater
  <br>33 = Food
  <br>34 = Sweep Panorama
  <br>35 = Handheld Night Shot
  <br>36 = Anti Motion Blur
  <br>37 = Pet
  <br>38 = Backlight Correction HDR
  <br>39 = Superior Auto
  <br>40 = Background Defocus
  <br>41 = Soft Skin
  <br>42 = 3D Image
  <br>65535 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td title='0xb042 = 45122'>0xb042</td>
<td>FocusMode</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(not valid for all models)</span>
  <br>1 = AF-S
  <br>2 = AF-C
  <br>4 = Permanent-AF
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0xb043 = 45123'>0xb043</td>
<td>AFAreaMode</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(older models)</span></span><table class=cols><tr>
  <td>0 = Default
  <br>1 = Multi
  <br>2 = Center
  <br>3 = Spot
  <br>4 = Flexible Spot</td><td>&nbsp;&nbsp;</td>
  <td>6 = Touch
  <br>14 = Tracking
  <br>15 = Face Tracking
  <br>65535 = n/a</td></tr></table>
<span class=s><span class=n>(DSC-HX9V generation cameras)</span></span><table class=cols><tr>
  <td>0 = Multi
  <br>1 = Center
  <br>2 = Spot
  <br>3 = Flexible Spot
  <br>10 = Selective (for Miniature effect)
  <br>14 = Tracking
  <br>15 = Face Tracking
  <br>255 = Manual</td></tr></table>
</td></tr>
<tr class=b>
<td title='0xb044 = 45124'>0xb044</td>
<td>AFIlluminator</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = Auto
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0xb047 = 45127'>0xb047</td>
<td>JPEGQuality</td>
<td class=c>int16u</td>
<td><span class=s>0 = Standard
  <br>1 = Fine
  <br>2 = Extra Fine
  <br>65535 = n/a</span></td></tr>
<tr class=b>
<td title='0xb048 = 45128'>0xb048</td>
<td>FlashLevel</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Low
  <br>-9 = -9/3
  <br>-8 = -8/3
  <br>-7 = -7/3
  <br>-6 = -6/3
  <br>-5 = -5/3
  <br>-4 = -4/3</td><td>&nbsp;&nbsp;</td>
  <td>-3 = -3/3
  <br>-2 = -2/3
  <br>-1 = -1/3
  <br>0 = Normal
  <br>1 = +1/3
  <br>2 = +2/3
  <br>3 = +3/3</td><td>&nbsp;&nbsp;</td>
  <td>4 = +4/3
  <br>5 = +5/3
  <br>6 = +6/3
  <br>9 = +9/3
  <br>128 = n/a
  <br>32767 = High</td></tr></table>
</td></tr>
<tr>
<td title='0xb049 = 45129'>0xb049</td>
<td>ReleaseMode</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>2 = Continuous
  <br>5 = Exposure Bracketing
  <br>6 = White Balance Bracketing
  <br>8 = DRO Bracketing
  <br>65535 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td title='0xb04a = 45130'>0xb04a</td>
<td>SequenceNumber</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(shot number in continuous burst)</span>
  <br>0 = Single
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0xb04b = 45131'>0xb04b</td>
<td>Anti-Blur</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On (Continuous)
  <br>2 = On (Shooting)
  <br>65535 = n/a</span></td></tr>
<tr class=b>
<td title='0xb04e = 45134'>0xb04e</td>
<td>FocusMode</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(valid for DSC-HX9V generation and newer)</span>
  <br>0 = Manual
  <br>2 = AF-S
  <br>3 = AF-C
  <br>5 = Semi-manual
  <br>6 = DMF</span></td></tr>
<tr>
<td title='0xb04f = 45135'>0xb04f</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = Standard
  <br>2 = Plus</span></td></tr>
<tr class=b>
<td title='0xb050 = 45136'>0xb050</td>
<td>HighISONoiseReduction2</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(DSC models only)</span>
  <br>0 = Normal
  <br>1 = High
  <br>2 = Low
  <br>3 = Off
  <br>65535 = n/a</span></td></tr>
<tr>
<td title='0xb052 = 45138'>0xb052</td>
<td>IntelligentAuto</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On
  <br>2 = Advanced</span></td></tr>
<tr class=b>
<td title='0xb054 = 45140'>0xb054</td>
<td>WhiteBalance</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(decoding of the Fluorescent settings matches the EXIF standard, which is
different than the names used by Sony for some models)</span></span><table class=cols><tr>
  <td>0 = Auto
  <br>4 = Custom
  <br>5 = Daylight
  <br>6 = Cloudy
  <br>7 = Cool White Fluorescent
  <br>8 = Day White Fluorescent
  <br>9 = Daylight Fluorescent
  <br>10 = Incandescent2
  <br>11 = Warm White Fluorescent
  <br>14 = Incandescent
  <br>15 = Flash
  <br>17 = Underwater 1 (Blue Water)
  <br>18 = Underwater 2 (Green Water)
  <br>19 = Underwater Auto</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='LensType'>Sony LensType Values</a></h2>
<p>&quot;New&quot; or &quot;II&quot; appear in brackets if the original version of the lens has the
same LensType.  Special logic is employed to identify the attached lens when
a Metabones Canon EF adapter is used.</p>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>LensType</th></tr>
<tr><td>0</td><td>= Minolta AF 28-85mm F3.5-4.5 New</td>
</tr><tr><td>1</td><td>= Minolta AF 80-200mm F2.8 HS-APO G</td>
</tr><tr><td>2</td><td>= Minolta AF 28-70mm F2.8 G</td>
</tr><tr><td>3</td><td>= Minolta AF 28-80mm F4-5.6</td>
</tr><tr><td>4</td><td>= Minolta AF 85mm F1.4G</td>
</tr><tr><td>5</td><td>= Minolta AF 35-70mm F3.5-4.5 [II]</td>
</tr><tr><td>6</td><td>= Minolta AF 24-85mm F3.5-4.5 [New]</td>
</tr><tr><td>7</td><td>= Minolta AF 100-300mm F4.5-5.6 APO [New] or 100-400mm or Sigma Lens</td>
</tr><tr><td>7</td><td>= Minolta AF 100-400mm F4.5-6.7 APO</td>
</tr><tr><td>7</td><td>= Sigma AF 100-300mm F4 EX DG IF</td>
</tr><tr><td>8</td><td>= Minolta AF 70-210mm F4.5-5.6 [II]</td>
</tr><tr><td>9</td><td>= Minolta AF 50mm F3.5 Macro</td>
</tr><tr><td>10</td><td>= Minolta AF 28-105mm F3.5-4.5 [New]</td>
</tr><tr><td>11</td><td>= Minolta AF 300mm F4 HS-APO G</td>
</tr><tr><td>12</td><td>= Minolta AF 100mm F2.8 Soft Focus</td>
</tr><tr><td>13</td><td>= Minolta AF 75-300mm F4.5-5.6 (New or II)</td>
</tr><tr><td>14</td><td>= Minolta AF 100-400mm F4.5-6.7 APO</td>
</tr><tr><td>15</td><td>= Minolta AF 400mm F4.5 HS-APO G</td>
</tr><tr><td>16</td><td>= Minolta AF 17-35mm F3.5 G</td>
</tr><tr><td>17</td><td>= Minolta AF 20-35mm F3.5-4.5</td>
</tr><tr><td>18</td><td>= Minolta AF 28-80mm F3.5-5.6 II</td>
</tr><tr><td>19</td><td>= Minolta AF 35mm F1.4 G</td>
</tr><tr><td>20</td><td>= Minolta/Sony 135mm F2.8 [T4.5] STF</td>
</tr><tr><td>22</td><td>= Minolta AF 35-80mm F4-5.6 II</td>
</tr><tr><td>23</td><td>= Minolta AF 200mm F4 Macro APO G</td>
</tr><tr><td>24</td><td>= Minolta/Sony AF 24-105mm F3.5-4.5 (D) or Sigma or Tamron Lens</td>
</tr><tr><td>24</td><td>= Sigma 18-50mm F2.8</td>
</tr><tr><td>24</td><td>= Sigma 17-70mm F2.8-4.5 DC Macro</td>
</tr><tr><td>24</td><td>= Sigma 20-40mm F2.8 EX DG Aspherical IF</td>
</tr><tr><td>24</td><td>= Sigma 18-200mm F3.5-6.3 DC</td>
</tr><tr><td>24</td><td>= Sigma DC 18-125mm F4-5,6 D</td>
</tr><tr><td>24</td><td>= Tamron SP AF 28-75mm F2.8 XR Di LD Aspherical [IF] Macro</td>
</tr><tr><td>24</td><td>= Sigma 15-30mm F3.5-4.5 EX DG Aspherical</td>
</tr><tr><td>25</td><td>= Minolta AF 100-300mm F4.5-5.6 APO (D) or Sigma Lens</td>
</tr><tr><td>25</td><td>= Sigma 100-300mm F4 EX (APO (D) or D IF)</td>
</tr><tr><td>25</td><td>= Sigma 70mm F2.8 EX DG Macro</td>
</tr><tr><td>25</td><td>= Sigma 20mm F1.8 EX DG Aspherical RF</td>
</tr><tr><td>25</td><td>= Sigma 30mm F1.4 EX DC</td>
</tr><tr><td>25</td><td>= Sigma 24mm F1.8 EX DG ASP Macro</td>
</tr><tr><td>27</td><td>= Minolta AF 85mm F1.4 G (D)</td>
</tr><tr><td>28</td><td>= Minolta/Sony AF 100mm F2.8 Macro (D) or Tamron Lens</td>
</tr><tr><td>28</td><td>= Tamron SP AF 90mm F2.8 Di Macro</td>
</tr><tr><td>28</td><td>= Tamron SP AF 180mm F3.5 Di LD [IF] Macro</td>
</tr><tr><td>29</td><td>= Minolta/Sony AF 75-300mm F4.5-5.6 (D)</td>
</tr><tr><td>30</td><td>= Minolta AF 28-80mm F3.5-5.6 (D) or Sigma Lens</td>
</tr><tr><td>30</td><td>= Sigma AF 10-20mm F4-5.6 EX DC</td>
</tr><tr><td>30</td><td>= Sigma AF 12-24mm F4.5-5.6 EX DG</td>
</tr><tr><td>30</td><td>= Sigma 28-70mm EX DG F2.8</td>
</tr><tr><td>30</td><td>= Sigma 55-200mm F4-5.6 DC</td>
</tr><tr><td>31</td><td>= Minolta/Sony AF 50mm F2.8 Macro (D) or F3.5</td>
</tr><tr><td>31</td><td>= Minolta/Sony AF 50mm F3.5 Macro</td>
</tr><tr><td>32</td><td>= Minolta/Sony AF 300mm F2.8 G or 1.5x Teleconverter</td>
</tr><tr><td>33</td><td>= Minolta/Sony AF 70-200mm F2.8 G</td>
</tr><tr><td>35</td><td>= Minolta AF 85mm F1.4 G (D) Limited</td>
</tr><tr><td>36</td><td>= Minolta AF 28-100mm F3.5-5.6 (D)</td>
</tr><tr><td>38</td><td>= Minolta AF 17-35mm F2.8-4 (D)</td>
</tr><tr><td>39</td><td>= Minolta AF 28-75mm F2.8 (D)</td>
</tr><tr><td>40</td><td>= Minolta/Sony AF DT 18-70mm F3.5-5.6 (D)</td>
</tr><tr><td>41</td><td>= Minolta/Sony AF DT 11-18mm F4.5-5.6 (D) or Tamron Lens</td>
</tr><tr><td>41</td><td>= Tamron SP AF 11-18mm F4.5-5.6 Di II LD Aspherical IF</td>
</tr><tr><td>42</td><td>= Minolta/Sony AF DT 18-200mm F3.5-6.3 (D)</td>
</tr><tr><td>43</td><td>= Sony 35mm F1.4 G (SAL35F14G)</td>
</tr><tr><td>44</td><td>= Sony 50mm F1.4 (SAL50F14)</td>
</tr><tr><td>45</td><td>= Carl Zeiss Planar T* 85mm F1.4 ZA (SAL85F14Z)</td>
</tr><tr><td>46</td><td>= Carl Zeiss Vario-Sonnar T* DT 16-80mm F3.5-4.5 ZA (SAL1680Z)</td>
</tr><tr><td>47</td><td>= Carl Zeiss Sonnar T* 135mm F1.8 ZA (SAL135F18Z)</td>
</tr><tr><td>48</td><td>= Carl Zeiss Vario-Sonnar T* 24-70mm F2.8 ZA SSM (SAL2470Z) or Other Lens</td>
</tr><tr><td>48</td><td>= Carl Zeiss Vario-Sonnar T* 24-70mm F2.8 ZA SSM II (SAL2470Z2)</td>
</tr><tr><td>48</td><td>= Tamron SP 24-70mm F2.8 Di USD</td>
</tr><tr><td>49</td><td>= Sony DT 55-200mm F4-5.6 (SAL55200)</td>
</tr><tr><td>50</td><td>= Sony DT 18-250mm F3.5-6.3 (SAL18250)</td>
</tr><tr><td>51</td><td>= Sony DT 16-105mm F3.5-5.6 (SAL16105)</td>
</tr><tr><td>52</td><td>= Sony 70-300mm F4.5-5.6 G SSM (SAL70300G) or G SSM II or Tamron Lens</td>
</tr><tr><td>52</td><td>= Sony 70-300mm F4.5-5.6 G SSM II (SAL70300G2)</td>
</tr><tr><td>52</td><td>= Tamron SP 70-300mm F4-5.6 Di USD</td>
</tr><tr><td>53</td><td>= Sony 70-400mm F4-5.6 G SSM (SAL70400G)</td>
</tr><tr><td>54</td><td>= Carl Zeiss Vario-Sonnar T* 16-35mm F2.8 ZA SSM (SAL1635Z) or ZA SSM II</td>
</tr><tr><td>54</td><td>= Carl Zeiss Vario-Sonnar T* 16-35mm F2.8 ZA SSM II (SAL1635Z2)</td>
</tr><tr><td>55</td><td>= Sony DT 18-55mm F3.5-5.6 SAM (SAL1855) or SAM II</td>
</tr><tr><td>55</td><td>= Sony DT 18-55mm F3.5-5.6 SAM II (SAL18552)</td>
</tr><tr><td>56</td><td>= Sony DT 55-200mm F4-5.6 SAM (SAL55200-2)</td>
</tr><tr><td>57</td><td>= Sony DT 50mm F1.8 SAM (SAL50F18) or Tamron Lens or Commlite CM-EF-NEX adapter</td>
</tr><tr><td>57</td><td>= Tamron SP AF 60mm F2 Di II LD [IF] Macro 1:1</td>
</tr><tr><td>57</td><td>= Tamron 18-270mm F3.5-6.3 Di II PZD</td>
</tr><tr><td>58</td><td>= Sony DT 30mm F2.8 Macro SAM (SAL30M28)</td>
</tr><tr><td>59</td><td>= Sony 28-75mm F2.8 SAM (SAL2875)</td>
</tr><tr><td>60</td><td>= Carl Zeiss Distagon T* 24mm F2 ZA SSM (SAL24F20Z)</td>
</tr><tr><td>61</td><td>= Sony 85mm F2.8 SAM (SAL85F28)</td>
</tr><tr><td>62</td><td>= Sony DT 35mm F1.8 SAM (SAL35F18)</td>
</tr><tr><td>63</td><td>= Sony DT 16-50mm F2.8 SSM (SAL1650)</td>
</tr><tr><td>64</td><td>= Sony 500mm F4 G SSM (SAL500F40G)</td>
</tr><tr><td>65</td><td>= Sony DT 18-135mm F3.5-5.6 SAM (SAL18135)</td>
</tr><tr><td>66</td><td>= Sony 300mm F2.8 G SSM II (SAL300F28G2)</td>
</tr><tr><td>67</td><td>= Sony 70-200mm F2.8 G SSM II (SAL70200G2)</td>
</tr><tr><td>68</td><td>= Sony DT 55-300mm F4.5-5.6 SAM (SAL55300)</td>
</tr><tr><td>69</td><td>= Sony 70-400mm F4-5.6 G SSM II (SAL70400G2)</td>
</tr><tr><td>70</td><td>= Carl Zeiss Planar T* 50mm F1.4 ZA SSM (SAL50F14Z)</td>
</tr><tr><td>128</td><td>= Tamron or Sigma Lens (128)</td>
</tr><tr><td>128</td><td>= Tamron AF 18-200mm F3.5-6.3 XR Di II LD Aspherical [IF] Macro</td>
</tr><tr><td>128</td><td>= Tamron AF 28-300mm F3.5-6.3 XR Di LD Aspherical [IF] Macro</td>
</tr><tr><td>128</td><td>= Tamron AF 28-200mm F3.8-5.6 XR Di Aspherical [IF] Macro</td>
</tr><tr><td>128</td><td>= Tamron SP AF 17-35mm F2.8-4 Di LD Aspherical IF</td>
</tr><tr><td>128</td><td>= Sigma AF 50-150mm F2.8 EX DC APO HSM II</td>
</tr><tr><td>128</td><td>= Sigma 10-20mm F3.5 EX DC HSM</td>
</tr><tr><td>128</td><td>= Sigma 70-200mm F2.8 II EX DG APO MACRO HSM</td>
</tr><tr><td>128</td><td>= Sigma 10mm F2.8 EX DC HSM Fisheye</td>
</tr><tr><td>128</td><td>= Sigma 50mm F1.4 EX DG HSM</td>
</tr><tr><td>128</td><td>= Sigma 85mm F1.4 EX DG HSM</td>
</tr><tr><td>128</td><td>= Sigma 24-70mm F2.8 IF EX DG HSM</td>
</tr><tr><td>128</td><td>= Sigma 18-250mm F3.5-6.3 DC OS HSM</td>
</tr><tr><td>128</td><td>= Sigma 17-50mm F2.8 EX DC HSM</td>
</tr><tr><td>128</td><td>= Sigma 17-70mm F2.8-4 DC Macro HSM</td>
</tr><tr><td>128</td><td>= Sigma 150mm F2.8 EX DG OS HSM APO Macro</td>
</tr><tr><td>128</td><td>= Sigma 150-500mm F5-6.3 APO DG OS HSM</td>
</tr><tr><td>128</td><td>= Tamron AF 28-105mm F4-5.6 [IF]</td>
</tr><tr><td>128</td><td>= Sigma 35mm F1.4 DG HSM</td>
</tr><tr><td>128</td><td>= Sigma 18-35mm F1.8 DC HSM</td>
</tr><tr><td>128</td><td>= Sigma 50-500mm F4.5-6.3 APO DG OS HSM</td>
</tr><tr><td>128</td><td>= Sigma 24-105mm F4 DG HSM | A</td>
</tr><tr><td>128</td><td>= Sigma 30mm F1.4</td>
</tr><tr><td>128</td><td>= Sigma 35mm F1.4 DG HSM | A</td>
</tr><tr><td>128</td><td>= Sigma 105mm F2.8 EX DG OS HSM Macro</td>
</tr><tr><td>128</td><td>= Sigma 180mm F2.8 EX DG OS HSM APO Macro</td>
</tr><tr><td>128</td><td>= Sigma 18-300mm F3.5-6.3 DC Macro HSM | C</td>
</tr><tr><td>128</td><td>= Sigma 18-50mm F2.8-4.5 DC HSM</td>
</tr><tr><td>129</td><td>= Tamron Lens (129)</td>
</tr><tr><td>129</td><td>= Tamron 200-400mm F5.6 LD</td>
</tr><tr><td>129</td><td>= Tamron 70-300mm F4-5.6 LD</td>
</tr><tr><td>131</td><td>= Tamron 20-40mm F2.7-3.5 SP Aspherical IF</td>
</tr><tr><td>135</td><td>= Vivitar 28-210mm F3.5-5.6</td>
</tr><tr><td>136</td><td>= Tokina EMZ M100 AF 100mm F3.5</td>
</tr><tr><td>137</td><td>= Cosina 70-210mm F2.8-4 AF</td>
</tr><tr><td>138</td><td>= Soligor 19-35mm F3.5-4.5</td>
</tr><tr><td>139</td><td>= Tokina AF 28-300mm F4-6.3</td>
</tr><tr><td>142</td><td>= Cosina AF 70-300mm F4.5-5.6 MC</td>
</tr><tr><td>146</td><td>= Voigtlander Macro APO-Lanthar 125mm F2.5 SL</td>
</tr><tr><td>194</td><td>= Tamron SP AF 17-50mm F2.8 XR Di II LD Aspherical [IF]</td>
</tr><tr><td>202</td><td>= Tamron SP AF 70-200mm F2.8 Di LD [IF] Macro</td>
</tr><tr><td>203</td><td>= Tamron SP 70-200mm F2.8 Di USD</td>
</tr><tr><td>204</td><td>= Tamron SP 24-70mm F2.8 Di USD</td>
</tr><tr><td>212</td><td>= Tamron 28-300mm F3.5-6.3 Di PZD</td>
</tr><tr><td>213</td><td>= Tamron 16-300mm F3.5-6.3 Di II PZD Macro</td>
</tr><tr><td>214</td><td>= Tamron SP 150-600mm F5-6.3 Di USD</td>
</tr><tr><td>215</td><td>= Tamron SP 15-30mm F2.8 Di USD</td>
</tr><tr><td>216</td><td>= Tamron SP 45mm F1.8 Di USD</td>
</tr><tr><td>217</td><td>= Tamron SP 35mm F1.8 Di USD</td>
</tr><tr><td>218</td><td>= Tamron SP 90mm F2.8 Di Macro 1:1 USD (F017)</td>
</tr><tr><td>220</td><td>= Tamron SP 150-600mm F5-6.3 Di USD G2</td>
</tr><tr><td>224</td><td>= Tamron SP 90mm F2.8 Di Macro 1:1 USD (F004)</td>
</tr><tr><td>255</td><td>= Tamron Lens (255)</td>
</tr><tr><td>255</td><td>= Tamron SP AF 17-50mm F2.8 XR Di II LD Aspherical</td>
</tr><tr><td>255</td><td>= Tamron AF 18-250mm F3.5-6.3 XR Di II LD</td>
</tr><tr><td>255</td><td>= Tamron AF 55-200mm F4-5.6 Di II LD Macro</td>
</tr><tr><td>255</td><td>= Tamron AF 70-300mm F4-5.6 Di LD Macro 1:2</td>
</tr><tr><td>255</td><td>= Tamron SP AF 200-500mm F5.0-6.3 Di LD IF</td>
</tr><tr><td>255</td><td>= Tamron SP AF 10-24mm F3.5-4.5 Di II LD Aspherical IF</td>
</tr><tr><td>255</td><td>= Tamron SP AF 70-200mm F2.8 Di LD IF Macro</td>
</tr><tr><td>255</td><td>= Tamron SP AF 28-75mm F2.8 XR Di LD Aspherical IF</td>
</tr><tr><td>255</td><td>= Tamron AF 90-300mm F4.5-5.6 Telemacro</td>
</tr><tr><td>1868</td><td>= Sigma MC-11 SA-E Mount Converter with not-supported Sigma lens</td>
</tr><tr><td>2550</td><td>= Minolta AF 50mm F1.7</td>
</tr><tr><td>2551</td><td>= Minolta AF 35-70mm F4 or Other Lens</td>
</tr><tr><td>2551</td><td>= Sigma UC AF 28-70mm F3.5-4.5</td>
</tr><tr><td>2551</td><td>= Sigma AF 28-70mm F2.8</td>
</tr><tr><td>2551</td><td>= Sigma M-AF 70-200mm F2.8 EX Aspherical</td>
</tr><tr><td>2551</td><td>= Quantaray M-AF 35-80mm F4-5.6</td>
</tr><tr><td>2551</td><td>= Tokina 28-70mm F2.8-4.5 AF</td>
</tr><tr><td>2552</td><td>= Minolta AF 28-85mm F3.5-4.5 or Other Lens</td>
</tr><tr><td>2552</td><td>= Tokina 19-35mm F3.5-4.5</td>
</tr><tr><td>2552</td><td>= Tokina 28-70mm F2.8 AT-X</td>
</tr><tr><td>2552</td><td>= Tokina 80-400mm F4.5-5.6 AT-X AF II 840</td>
</tr><tr><td>2552</td><td>= Tokina AF PRO 28-80mm F2.8 AT-X 280</td>
</tr><tr><td>2552</td><td>= Tokina AT-X PRO [II] AF 28-70mm F2.6-2.8 270</td>
</tr><tr><td>2552</td><td>= Tamron AF 19-35mm F3.5-4.5</td>
</tr><tr><td>2552</td><td>= Angenieux AF 28-70mm F2.6</td>
</tr><tr><td>2552</td><td>= Tokina AT-X 17 AF 17mm F3.5</td>
</tr><tr><td>2552</td><td>= Tokina 20-35mm F3.5-4.5 II AF</td>
</tr><tr><td>2553</td><td>= Minolta AF 28-135mm F4-4.5 or Other Lens</td>
</tr><tr><td>2553</td><td>= Sigma ZOOM-alpha 35-135mm F3.5-4.5</td>
</tr><tr><td>2553</td><td>= Sigma 28-105mm F2.8-4 Aspherical</td>
</tr><tr><td>2553</td><td>= Sigma 28-105mm F4-5.6 UC</td>
</tr><tr><td>2553</td><td>= Tokina AT-X 242 AF 24-200mm F3.5-5.6</td>
</tr><tr><td>2554</td><td>= Minolta AF 35-105mm F3.5-4.5</td>
</tr><tr><td>2555</td><td>= Minolta AF 70-210mm F4 Macro or Sigma Lens</td>
</tr><tr><td>2555</td><td>= Sigma 70-210mm F4-5.6 APO</td>
</tr><tr><td>2555</td><td>= Sigma M-AF 70-200mm F2.8 EX APO</td>
</tr><tr><td>2555</td><td>= Sigma 75-200mm F2.8-3.5</td>
</tr><tr><td>2556</td><td>= Minolta AF 135mm F2.8</td>
</tr><tr><td>2557</td><td>= Minolta/Sony AF 28mm F2.8</td>
</tr><tr><td>2558</td><td>= Minolta AF 24-50mm F4</td>
</tr><tr><td>2560</td><td>= Minolta AF 100-200mm F4.5</td>
</tr><tr><td>2561</td><td>= Minolta AF 75-300mm F4.5-5.6 or Sigma Lens</td>
</tr><tr><td>2561</td><td>= Sigma 70-300mm F4-5.6 DL Macro</td>
</tr><tr><td>2561</td><td>= Sigma 300mm F4 APO Macro</td>
</tr><tr><td>2561</td><td>= Sigma AF 500mm F4.5 APO</td>
</tr><tr><td>2561</td><td>= Sigma AF 170-500mm F5-6.3 APO Aspherical</td>
</tr><tr><td>2561</td><td>= Tokina AT-X AF 300mm F4</td>
</tr><tr><td>2561</td><td>= Tokina AT-X AF 400mm F5.6 SD</td>
</tr><tr><td>2561</td><td>= Tokina AF 730 II 75-300mm F4.5-5.6</td>
</tr><tr><td>2561</td><td>= Sigma 800mm F5.6 APO</td>
</tr><tr><td>2561</td><td>= Sigma AF 400mm F5.6 APO Macro</td>
</tr><tr><td>2561</td><td>= Sigma 1000mm F8 APO</td>
</tr><tr><td>2562</td><td>= Minolta AF 50mm F1.4 [New]</td>
</tr><tr><td>2563</td><td>= Minolta AF 300mm F2.8 APO or Sigma Lens</td>
</tr><tr><td>2563</td><td>= Sigma AF 50-500mm F4-6.3 EX DG APO</td>
</tr><tr><td>2563</td><td>= Sigma AF 170-500mm F5-6.3 APO Aspherical</td>
</tr><tr><td>2563</td><td>= Sigma AF 500mm F4.5 EX DG APO</td>
</tr><tr><td>2563</td><td>= Sigma 400mm F5.6 APO</td>
</tr><tr><td>2564</td><td>= Minolta AF 50mm F2.8 Macro or Sigma Lens</td>
</tr><tr><td>2564</td><td>= Sigma 50mm F2.8 EX Macro</td>
</tr><tr><td>2565</td><td>= Minolta AF 600mm F4 APO</td>
</tr><tr><td>2566</td><td>= Minolta AF 24mm F2.8 or Sigma Lens</td>
</tr><tr><td>2566</td><td>= Sigma 17-35mm F2.8-4 EX Aspherical</td>
</tr><tr><td>2572</td><td>= Minolta/Sony AF 500mm F8 Reflex</td>
</tr><tr><td>2578</td><td>= Minolta/Sony AF 16mm F2.8 Fisheye or Sigma Lens</td>
</tr><tr><td>2578</td><td>= Sigma 8mm F4 EX [DG] Fisheye</td>
</tr><tr><td>2578</td><td>= Sigma 14mm F3.5</td>
</tr><tr><td>2578</td><td>= Sigma 15mm F2.8 Fisheye</td>
</tr><tr><td>2579</td><td>= Minolta/Sony AF 20mm F2.8 or Tokina Lens</td>
</tr><tr><td>2579</td><td>= Tokina AT-X Pro DX 11-16mm F2.8</td>
</tr><tr><td>2581</td><td>= Minolta AF 100mm F2.8 Macro [New] or Sigma or Tamron Lens</td>
</tr><tr><td>2581</td><td>= Sigma AF 90mm F2.8 Macro</td>
</tr><tr><td>2581</td><td>= Sigma AF 105mm F2.8 EX [DG] Macro</td>
</tr><tr><td>2581</td><td>= Sigma 180mm F5.6 Macro</td>
</tr><tr><td>2581</td><td>= Sigma 180mm F3.5 EX DG Macro</td>
</tr><tr><td>2581</td><td>= Tamron 90mm F2.8 Macro</td>
</tr><tr><td>2585</td><td>= Minolta AF 35-105mm F3.5-4.5 New or Tamron Lens</td>
</tr><tr><td>2585</td><td>= Beroflex 35-135mm F3.5-4.5</td>
</tr><tr><td>2585</td><td>= Tamron 24-135mm F3.5-5.6</td>
</tr><tr><td>2588</td><td>= Minolta AF 70-210mm F3.5-4.5</td>
</tr><tr><td>2589</td><td>= Minolta AF 80-200mm F2.8 APO or Tokina Lens</td>
</tr><tr><td>2589</td><td>= Tokina 80-200mm F2.8</td>
</tr><tr><td>2590</td><td>= Minolta AF 200mm F2.8 G APO + Minolta AF 1.4x APO or Other Lens + 1.4x</td>
</tr><tr><td>2590</td><td>= Minolta AF 600mm F4 HS-APO G + Minolta AF 1.4x APO</td>
</tr><tr><td>2591</td><td>= Minolta AF 35mm F1.4</td>
</tr><tr><td>2592</td><td>= Minolta AF 85mm F1.4 G (D)</td>
</tr><tr><td>2593</td><td>= Minolta AF 200mm F2.8 APO</td>
</tr><tr><td>2594</td><td>= Minolta AF 3x-1x F1.7-2.8 Macro</td>
</tr><tr><td>2596</td><td>= Minolta AF 28mm F2</td>
</tr><tr><td>2597</td><td>= Minolta AF 35mm F2 [New]</td>
</tr><tr><td>2598</td><td>= Minolta AF 100mm F2</td>
</tr><tr><td>2601</td><td>= Minolta AF 200mm F2.8 G APO + Minolta AF 2x APO or Other Lens + 2x</td>
</tr><tr><td>2601</td><td>= Minolta AF 600mm F4 HS-APO G + Minolta AF 2x APO</td>
</tr><tr><td>2604</td><td>= Minolta AF 80-200mm F4.5-5.6</td>
</tr><tr><td>2605</td><td>= Minolta AF 35-80mm F4-5.6</td>
</tr><tr><td>2606</td><td>= Minolta AF 100-300mm F4.5-5.6</td>
</tr><tr><td>2607</td><td>= Minolta AF 35-80mm F4-5.6</td>
</tr><tr><td>2608</td><td>= Minolta AF 300mm F2.8 HS-APO G</td>
</tr><tr><td>2609</td><td>= Minolta AF 600mm F4 HS-APO G</td>
</tr><tr><td>2612</td><td>= Minolta AF 200mm F2.8 HS-APO G</td>
</tr><tr><td>2613</td><td>= Minolta AF 50mm F1.7 New</td>
</tr><tr><td>2615</td><td>= Minolta AF 28-105mm F3.5-4.5 xi</td>
</tr><tr><td>2616</td><td>= Minolta AF 35-200mm F4.5-5.6 xi</td>
</tr><tr><td>2618</td><td>= Minolta AF 28-80mm F4-5.6 xi</td>
</tr><tr><td>2619</td><td>= Minolta AF 80-200mm F4.5-5.6 xi</td>
</tr><tr><td>2620</td><td>= Minolta AF 28-70mm F2.8 G</td>
</tr><tr><td>2621</td><td>= Minolta AF 100-300mm F4.5-5.6 xi</td>
</tr><tr><td>2624</td><td>= Minolta AF 35-80mm F4-5.6 Power Zoom</td>
</tr><tr><td>2628</td><td>= Minolta AF 80-200mm F2.8 HS-APO G</td>
</tr><tr><td>2629</td><td>= Minolta AF 85mm F1.4 New</td>
</tr><tr><td>2631</td><td>= Minolta AF 100-300mm F4.5-5.6 APO</td>
</tr><tr><td>2632</td><td>= Minolta AF 24-50mm F4 New</td>
</tr><tr><td>2638</td><td>= Minolta AF 50mm F2.8 Macro New</td>
</tr><tr><td>2639</td><td>= Minolta AF 100mm F2.8 Macro</td>
</tr><tr><td>2641</td><td>= Minolta/Sony AF 20mm F2.8 New</td>
</tr><tr><td>2642</td><td>= Minolta AF 24mm F2.8 New</td>
</tr><tr><td>2644</td><td>= Minolta AF 100-400mm F4.5-6.7 APO</td>
</tr><tr><td>2662</td><td>= Minolta AF 50mm F1.4 New</td>
</tr><tr><td>2667</td><td>= Minolta AF 35mm F2 New</td>
</tr><tr><td>2668</td><td>= Minolta AF 28mm F2 New</td>
</tr><tr><td>2672</td><td>= Minolta AF 24-105mm F3.5-4.5 (D)</td>
</tr><tr><td>3046</td><td>= Metabones Canon EF Speed Booster</td>
</tr><tr><td>4567</td><td>= Tokina 70-210mm F4-5.6</td>
</tr><tr><td>4568</td><td>= Tokina AF 35-200mm F4-5.6 Zoom SD</td>
</tr><tr><td>4570</td><td>= Tamron AF 35-135mm F3.5-4.5</td>
</tr><tr><td>4571</td><td>= Vivitar 70-210mm F4.5-5.6</td>
</tr><tr><td>4574</td><td>= 2x Teleconverter or Tamron or Tokina Lens</td>
</tr><tr><td>4574</td><td>= Tamron SP AF 90mm F2.5</td>
</tr><tr><td>4574</td><td>= Tokina RF 500mm F8.0 x2</td>
</tr><tr><td>4574</td><td>= Tokina 300mm F2.8 x2</td>
</tr><tr><td>4575</td><td>= 1.4x Teleconverter</td>
</tr><tr><td>4585</td><td>= Tamron SP AF 300mm F2.8 LD IF</td>
</tr><tr><td>4586</td><td>= Tamron SP AF 35-105mm F2.8 LD Aspherical IF</td>
</tr><tr><td>4587</td><td>= Tamron AF 70-210mm F2.8 SP LD</td>
</tr><tr><td>4812</td><td>= Metabones Canon EF Speed Booster Ultra</td>
</tr><tr><td>6118</td><td>= Canon EF Adapter</td>
</tr><tr><td>6528</td><td>= Sigma 16mm F2.8 Filtermatic Fisheye</td>
</tr><tr><td>6553</td><td>= E-Mount, T-Mount, Other Lens or no lens</td>
</tr><tr><td>6553</td><td>= Arax MC 35mm F2.8 Tilt+Shift</td>
</tr><tr><td>6553</td><td>= Arax MC 80mm F2.8 Tilt+Shift</td>
</tr><tr><td>6553</td><td>= Zenitar MF 16mm F2.8 Fisheye M42</td>
</tr><tr><td>6553</td><td>= Samyang 500mm Mirror F8.0</td>
</tr><tr><td>6553</td><td>= Pentacon Auto 135mm F2.8</td>
</tr><tr><td>6553</td><td>= Pentacon Auto 29mm F2.8</td>
</tr><tr><td>6553</td><td>= Helios 44-2 58mm F2.0</td>
</tr><tr><td>18688</td><td>= Sigma MC-11 SA-E Mount Converter with not-supported Sigma lens</td>
</tr><tr><td>25501</td><td>= Minolta AF 50mm F1.7</td>
</tr><tr><td>25511</td><td>= Minolta AF 35-70mm F4 or Other Lens</td>
</tr><tr><td>25511</td><td>= Sigma UC AF 28-70mm F3.5-4.5</td>
</tr><tr><td>25511</td><td>= Sigma AF 28-70mm F2.8</td>
</tr><tr><td>25511</td><td>= Sigma M-AF 70-200mm F2.8 EX Aspherical</td>
</tr><tr><td>25511</td><td>= Quantaray M-AF 35-80mm F4-5.6</td>
</tr><tr><td>25511</td><td>= Tokina 28-70mm F2.8-4.5 AF</td>
</tr><tr><td>25521</td><td>= Minolta AF 28-85mm F3.5-4.5 or Other Lens</td>
</tr><tr><td>25521</td><td>= Tokina 19-35mm F3.5-4.5</td>
</tr><tr><td>25521</td><td>= Tokina 28-70mm F2.8 AT-X</td>
</tr><tr><td>25521</td><td>= Tokina 80-400mm F4.5-5.6 AT-X AF II 840</td>
</tr><tr><td>25521</td><td>= Tokina AF PRO 28-80mm F2.8 AT-X 280</td>
</tr><tr><td>25521</td><td>= Tokina AT-X PRO [II] AF 28-70mm F2.6-2.8 270</td>
</tr><tr><td>25521</td><td>= Tamron AF 19-35mm F3.5-4.5</td>
</tr><tr><td>25521</td><td>= Angenieux AF 28-70mm F2.6</td>
</tr><tr><td>25521</td><td>= Tokina AT-X 17 AF 17mm F3.5</td>
</tr><tr><td>25521</td><td>= Tokina 20-35mm F3.5-4.5 II AF</td>
</tr><tr><td>25531</td><td>= Minolta AF 28-135mm F4-4.5 or Other Lens</td>
</tr><tr><td>25531</td><td>= Sigma ZOOM-alpha 35-135mm F3.5-4.5</td>
</tr><tr><td>25531</td><td>= Sigma 28-105mm F2.8-4 Aspherical</td>
</tr><tr><td>25531</td><td>= Sigma 28-105mm F4-5.6 UC</td>
</tr><tr><td>25531</td><td>= Tokina AT-X 242 AF 24-200mm F3.5-5.6</td>
</tr><tr><td>25541</td><td>= Minolta AF 35-105mm F3.5-4.5</td>
</tr><tr><td>25551</td><td>= Minolta AF 70-210mm F4 Macro or Sigma Lens</td>
</tr><tr><td>25551</td><td>= Sigma 70-210mm F4-5.6 APO</td>
</tr><tr><td>25551</td><td>= Sigma M-AF 70-200mm F2.8 EX APO</td>
</tr><tr><td>25551</td><td>= Sigma 75-200mm F2.8-3.5</td>
</tr><tr><td>25561</td><td>= Minolta AF 135mm F2.8</td>
</tr><tr><td>25571</td><td>= Minolta/Sony AF 28mm F2.8</td>
</tr><tr><td>25581</td><td>= Minolta AF 24-50mm F4</td>
</tr><tr><td>25601</td><td>= Minolta AF 100-200mm F4.5</td>
</tr><tr><td>25611</td><td>= Minolta AF 75-300mm F4.5-5.6 or Sigma Lens</td>
</tr><tr><td>25611</td><td>= Sigma 70-300mm F4-5.6 DL Macro</td>
</tr><tr><td>25611</td><td>= Sigma 300mm F4 APO Macro</td>
</tr><tr><td>25611</td><td>= Sigma AF 500mm F4.5 APO</td>
</tr><tr><td>25611</td><td>= Sigma AF 170-500mm F5-6.3 APO Aspherical</td>
</tr><tr><td>25611</td><td>= Tokina AT-X AF 300mm F4</td>
</tr><tr><td>25611</td><td>= Tokina AT-X AF 400mm F5.6 SD</td>
</tr><tr><td>25611</td><td>= Tokina AF 730 II 75-300mm F4.5-5.6</td>
</tr><tr><td>25611</td><td>= Sigma 800mm F5.6 APO</td>
</tr><tr><td>25611</td><td>= Sigma AF 400mm F5.6 APO Macro</td>
</tr><tr><td>25611</td><td>= Sigma 1000mm F8 APO</td>
</tr><tr><td>25621</td><td>= Minolta AF 50mm F1.4 [New]</td>
</tr><tr><td>25631</td><td>= Minolta AF 300mm F2.8 APO or Sigma Lens</td>
</tr><tr><td>25631</td><td>= Sigma AF 50-500mm F4-6.3 EX DG APO</td>
</tr><tr><td>25631</td><td>= Sigma AF 170-500mm F5-6.3 APO Aspherical</td>
</tr><tr><td>25631</td><td>= Sigma AF 500mm F4.5 EX DG APO</td>
</tr><tr><td>25631</td><td>= Sigma 400mm F5.6 APO</td>
</tr><tr><td>25641</td><td>= Minolta AF 50mm F2.8 Macro or Sigma Lens</td>
</tr><tr><td>25641</td><td>= Sigma 50mm F2.8 EX Macro</td>
</tr><tr><td>25651</td><td>= Minolta AF 600mm F4 APO</td>
</tr><tr><td>25661</td><td>= Minolta AF 24mm F2.8 or Sigma Lens</td>
</tr><tr><td>25661</td><td>= Sigma 17-35mm F2.8-4 EX Aspherical</td>
</tr><tr><td>25721</td><td>= Minolta/Sony AF 500mm F8 Reflex</td>
</tr><tr><td>25781</td><td>= Minolta/Sony AF 16mm F2.8 Fisheye or Sigma Lens</td>
</tr><tr><td>25781</td><td>= Sigma 8mm F4 EX [DG] Fisheye</td>
</tr><tr><td>25781</td><td>= Sigma 14mm F3.5</td>
</tr><tr><td>25781</td><td>= Sigma 15mm F2.8 Fisheye</td>
</tr><tr><td>25791</td><td>= Minolta/Sony AF 20mm F2.8 or Tokina Lens</td>
</tr><tr><td>25791</td><td>= Tokina AT-X Pro DX 11-16mm F2.8</td>
</tr><tr><td>25811</td><td>= Minolta AF 100mm F2.8 Macro [New] or Sigma or Tamron Lens</td>
</tr><tr><td>25811</td><td>= Sigma AF 90mm F2.8 Macro</td>
</tr><tr><td>25811</td><td>= Sigma AF 105mm F2.8 EX [DG] Macro</td>
</tr><tr><td>25811</td><td>= Sigma 180mm F5.6 Macro</td>
</tr><tr><td>25811</td><td>= Sigma 180mm F3.5 EX DG Macro</td>
</tr><tr><td>25811</td><td>= Tamron 90mm F2.8 Macro</td>
</tr><tr><td>25851</td><td>= Beroflex 35-135mm F3.5-4.5</td>
</tr><tr><td>25858</td><td>= Minolta AF 35-105mm F3.5-4.5 New or Tamron Lens</td>
</tr><tr><td>25858</td><td>= Tamron 24-135mm F3.5-5.6</td>
</tr><tr><td>25881</td><td>= Minolta AF 70-210mm F3.5-4.5</td>
</tr><tr><td>25891</td><td>= Minolta AF 80-200mm F2.8 APO or Tokina Lens</td>
</tr><tr><td>25891</td><td>= Tokina 80-200mm F2.8</td>
</tr><tr><td>25901</td><td>= Minolta AF 200mm F2.8 G APO + Minolta AF 1.4x APO or Other Lens + 1.4x</td>
</tr><tr><td>25901</td><td>= Minolta AF 600mm F4 HS-APO G + Minolta AF 1.4x APO</td>
</tr><tr><td>25911</td><td>= Minolta AF 35mm F1.4</td>
</tr><tr><td>25921</td><td>= Minolta AF 85mm F1.4 G (D)</td>
</tr><tr><td>25931</td><td>= Minolta AF 200mm F2.8 APO</td>
</tr><tr><td>25941</td><td>= Minolta AF 3x-1x F1.7-2.8 Macro</td>
</tr><tr><td>25961</td><td>= Minolta AF 28mm F2</td>
</tr><tr><td>25971</td><td>= Minolta AF 35mm F2 [New]</td>
</tr><tr><td>25981</td><td>= Minolta AF 100mm F2</td>
</tr><tr><td>26011</td><td>= Minolta AF 200mm F2.8 G APO + Minolta AF 2x APO or Other Lens + 2x</td>
</tr><tr><td>26011</td><td>= Minolta AF 600mm F4 HS-APO G + Minolta AF 2x APO</td>
</tr><tr><td>26041</td><td>= Minolta AF 80-200mm F4.5-5.6</td>
</tr><tr><td>26051</td><td>= Minolta AF 35-80mm F4-5.6</td>
</tr><tr><td>26061</td><td>= Minolta AF 100-300mm F4.5-5.6</td>
</tr><tr><td>26071</td><td>= Minolta AF 35-80mm F4-5.6</td>
</tr><tr><td>26081</td><td>= Minolta AF 300mm F2.8 HS-APO G</td>
</tr><tr><td>26091</td><td>= Minolta AF 600mm F4 HS-APO G</td>
</tr><tr><td>26121</td><td>= Minolta AF 200mm F2.8 HS-APO G</td>
</tr><tr><td>26131</td><td>= Minolta AF 50mm F1.7 New</td>
</tr><tr><td>26151</td><td>= Minolta AF 28-105mm F3.5-4.5 xi</td>
</tr><tr><td>26161</td><td>= Minolta AF 35-200mm F4.5-5.6 xi</td>
</tr><tr><td>26181</td><td>= Minolta AF 28-80mm F4-5.6 xi</td>
</tr><tr><td>26191</td><td>= Minolta AF 80-200mm F4.5-5.6 xi</td>
</tr><tr><td>26201</td><td>= Minolta AF 28-70mm F2.8 G</td>
</tr><tr><td>26211</td><td>= Minolta AF 100-300mm F4.5-5.6 xi</td>
</tr><tr><td>26241</td><td>= Minolta AF 35-80mm F4-5.6 Power Zoom</td>
</tr><tr><td>26281</td><td>= Minolta AF 80-200mm F2.8 HS-APO G</td>
</tr><tr><td>26291</td><td>= Minolta AF 85mm F1.4 New</td>
</tr><tr><td>26311</td><td>= Minolta AF 100-300mm F4.5-5.6 APO</td>
</tr><tr><td>26321</td><td>= Minolta AF 24-50mm F4 New</td>
</tr><tr><td>26381</td><td>= Minolta AF 50mm F2.8 Macro New</td>
</tr><tr><td>26391</td><td>= Minolta AF 100mm F2.8 Macro</td>
</tr><tr><td>26411</td><td>= Minolta/Sony AF 20mm F2.8 New</td>
</tr><tr><td>26421</td><td>= Minolta AF 24mm F2.8 New</td>
</tr><tr><td>26441</td><td>= Minolta AF 100-400mm F4.5-6.7 APO</td>
</tr><tr><td>26621</td><td>= Minolta AF 50mm F1.4 New</td>
</tr><tr><td>26671</td><td>= Minolta AF 35mm F2 New</td>
</tr><tr><td>26681</td><td>= Minolta AF 28mm F2 New</td>
</tr><tr><td>26721</td><td>= Minolta AF 24-105mm F3.5-4.5 (D)</td>
</tr><tr><td>30464</td><td>= Metabones Canon EF Speed Booster</td>
</tr><tr><td>45671</td><td>= Tokina 70-210mm F4-5.6</td>
</tr><tr><td>45681</td><td>= Tokina AF 35-200mm F4-5.6 Zoom SD</td>
</tr><tr><td>45701</td><td>= Tamron AF 35-135mm F3.5-4.5</td>
</tr><tr><td>45711</td><td>= Vivitar 70-210mm F4.5-5.6</td>
</tr><tr><td>45741</td><td>= 2x Teleconverter or Tamron or Tokina Lens</td>
</tr><tr><td>45741</td><td>= Tamron SP AF 90mm F2.5</td>
</tr><tr><td>45741</td><td>= Tokina RF 500mm F8.0 x2</td>
</tr><tr><td>45741</td><td>= Tokina 300mm F2.8 x2</td>
</tr><tr><td>45751</td><td>= 1.4x Teleconverter</td>
</tr><tr><td>45851</td><td>= Tamron SP AF 300mm F2.8 LD IF</td>
</tr><tr><td>45861</td><td>= Tamron SP AF 35-105mm F2.8 LD Aspherical IF</td>
</tr><tr><td>45871</td><td>= Tamron AF 70-210mm F2.8 SP LD</td>
</tr><tr><td>48128</td><td>= Metabones Canon EF Speed Booster Ultra</td>
</tr><tr><td>61184</td><td>= Canon EF Adapter</td>
</tr><tr><td>65280</td><td>= Sigma 16mm F2.8 Filtermatic Fisheye</td>
</tr><tr><td>65535</td><td>= E-Mount, T-Mount, Other Lens or no lens</td>
</tr><tr><td>65535</td><td>= Arax MC 35mm F2.8 Tilt+Shift</td>
</tr><tr><td>65535</td><td>= Arax MC 80mm F2.8 Tilt+Shift</td>
</tr><tr><td>65535</td><td>= Zenitar MF 16mm F2.8 Fisheye M42</td>
</tr><tr><td>65535</td><td>= Samyang 500mm Mirror F8.0</td>
</tr><tr><td>65535</td><td>= Pentacon Auto 135mm F2.8</td>
</tr><tr><td>65535</td><td>= Pentacon Auto 29mm F2.8</td>
</tr><tr><td>65535</td><td>= Helios 44-2 58mm F2.0</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='CameraInfo'>Sony CameraInfo Tags</a></h2>
<p>Camera information for the A700, A850 and A900.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>LensSpec</td>
<td class=c>undef[8]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>FocusModeSetting</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(FocusModeSetting for the A700, A850 and A900)</span>
  <br>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A
  <br>4 = DMF</span></td></tr>
<tr>
<td class=r title='21 = 0x15'>21</td>
<td>AFPointSelected</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Auto
  <br>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right
  <br>5 = Lower-right</td><td>&nbsp;&nbsp;</td>
  <td>6 = Bottom
  <br>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left
  <br>10 = Far Right
  <br>11 = Far Left</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='25 = 0x19'>25</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Upper-left
  <br>1 = Left
  <br>2 = Lower-left
  <br>3 = Far Left
  <br>4 = Bottom Assist-left
  <br>5 = Bottom
  <br>6 = Bottom Assist-right
  <br>7 = Center (7)
  <br>8 = Center (horizontal)
  <br>9 = Center (9)
  <br>10 = Center (10)
  <br>11 = Center (11)</td><td>&nbsp;&nbsp;</td>
  <td>12 = Center (12)
  <br>13 = Center (vertical)
  <br>14 = Center (14)
  <br>15 = Top Assist-left
  <br>16 = Top
  <br>17 = Top Assist-right
  <br>18 = Far Right
  <br>19 = Upper-right
  <br>20 = Right
  <br>21 = Lower-right
  <br>22 = Center F2.8</td></tr></table>
</td></tr>
<tr>
<td class=r title='30 = 0x1e'>30</td>
<td>AFStatusActiveSensor</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='32 = 0x20'>32</td>
<td>AFStatusUpper-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='34 = 0x22'>34</td>
<td>AFStatusLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='36 = 0x24'>36</td>
<td>AFStatusLower-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='38 = 0x26'>38</td>
<td>AFStatusFarLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='40 = 0x28'>40</td>
<td>AFStatusBottomAssist-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='42 = 0x2a'>42</td>
<td>AFStatusBottom</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='44 = 0x2c'>44</td>
<td>AFStatusBottomAssist-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='46 = 0x2e'>46</td>
<td>AFStatusCenter-7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='48 = 0x30'>48</td>
<td>AFStatusCenter-horizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='50 = 0x32'>50</td>
<td>AFStatusCenter-9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='52 = 0x34'>52</td>
<td>AFStatusCenter-10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='54 = 0x36'>54</td>
<td>AFStatusCenter-11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='56 = 0x38'>56</td>
<td>AFStatusCenter-12</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='58 = 0x3a'>58</td>
<td>AFStatusCenter-vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='60 = 0x3c'>60</td>
<td>AFStatusCenter-14</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='62 = 0x3e'>62</td>
<td>AFStatusTopAssist-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='64 = 0x40'>64</td>
<td>AFStatusTop</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='66 = 0x42'>66</td>
<td>AFStatusTopAssist-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='68 = 0x44'>68</td>
<td>AFStatusFarRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='70 = 0x46'>70</td>
<td>AFStatusUpper-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='72 = 0x48'>72</td>
<td>AFStatusRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='74 = 0x4a'>74</td>
<td>AFStatusLower-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='76 = 0x4c'>76</td>
<td>AFStatusCenterF2-8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='304 = 0x130'>304</td>
<td>AFMicroAdjValue</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='305 = 0x131'>305</td>
<td>AFMicroAdjMode</td>
<td class=c>int8u</td>
<td><span class=s>[val &gt;&gt; 7 &amp; 0x1]
  <br>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='305 = 0x131'>305.1</td>
<td>AFMicroAdjRegisteredLenses</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(number of registered lenses with a non-zero AFMicroAdjValue)</span>
  <br>[val &amp; 0x7f]</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraInfo2'>Sony CameraInfo2 Tags</a></h2>
<p>Camera information for the DSLR-A200, A230, A290, A300, A330, A350, A380 and
A390.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>LensSpec</td>
<td class=c>undef[8]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>AFPointSelected</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Auto
  <br>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lower-right
  <br>6 = Bottom
  <br>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left</td></tr></table>
</td></tr>
<tr>
<td class=r title='21 = 0x15'>21</td>
<td>FocusModeSetting</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(FocusModeSetting for other models)</span>
  <br>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A
  <br>4 = DMF</span></td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Top-right
  <br>1 = Bottom-right
  <br>2 = Bottom
  <br>3 = Middle Horizontal</td><td>&nbsp;&nbsp;</td>
  <td>4 = Center Vertical
  <br>5 = Top
  <br>6 = Top-left
  <br>7 = Bottom-left</td></tr></table>
</td></tr>
<tr>
<td class=r title='27 = 0x1b'>27</td>
<td>AFStatusActiveSensor</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='29 = 0x1d'>29</td>
<td>AFStatusTop-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='31 = 0x1f'>31</td>
<td>AFStatusBottom-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='33 = 0x21'>33</td>
<td>AFStatusBottom</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='35 = 0x23'>35</td>
<td>AFStatusMiddleHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='37 = 0x25'>37</td>
<td>AFStatusCenterVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='39 = 0x27'>39</td>
<td>AFStatusTop</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='41 = 0x29'>41</td>
<td>AFStatusTop-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='43 = 0x2b'>43</td>
<td>AFStatusBottom-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='45 = 0x2d'>45</td>
<td>AFStatusLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='47 = 0x2f'>47</td>
<td>AFStatusCenterHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='49 = 0x31'>49</td>
<td>AFStatusRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraInfo3'>Sony CameraInfo3 Tags</a></h2>
<p>Camera information stored by the A33, A35, A55, A450, A500, A550, A560,
A580, NEX-3/5/5C/C3 and VG10E.  Some tags are valid only for some of these
models.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>LensSpec</td>
<td class=c>undef[8]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>FocalLengthTeleZoom</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>AFPointSelected</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Auto
  <br>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lower-right
  <br>6 = Bottom
  <br>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left</td></tr></table>
</td></tr>
<tr>
<td class=r title='21 = 0x15'>21</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A</span></td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Top-right
  <br>1 = Bottom-right
  <br>2 = Bottom
  <br>3 = Middle Horizontal</td><td>&nbsp;&nbsp;</td>
  <td>4 = Center Vertical
  <br>5 = Top
  <br>6 = Top-left
  <br>7 = Bottom-left</td></tr></table>
</td></tr>
<tr>
<td class=r title='25 = 0x19'>25</td>
<td>FocusStatus</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(not valid with Contrast AF or for NEX models)</span>
  <br>0 = Manual - Not confirmed (0)
  <br>4 = Manual - Not confirmed (4)
  <br>16 = AF-C - Confirmed
  <br>24 = AF-C - Not Confirmed
  <br>64 = AF-S - Confirmed</span></td></tr>
<tr class=b>
<td class=r title='27 = 0x1b'>27</td>
<td>AFStatusActiveSensor</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>AFPointSelected</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(not valid for Contrast AF)</span></span><table class=cols><tr>
  <td>0 = Auto
  <br>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right
  <br>5 = Lower-right
  <br>6 = Bottom
  <br>7 = Lower-left</td><td>&nbsp;&nbsp;</td>
  <td>8 = Left
  <br>9 = Upper-left
  <br>10 = Far Right
  <br>11 = Far Left
  <br>12 = Upper-middle
  <br>13 = Near Right
  <br>14 = Lower-middle
  <br>15 = Near Left</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='29 = 0x1d'>29</td>
<td>FocusMode
  <br>AFStatusTop-right</td>
<td class=c>int8u<br>int16s</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A</span><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='31 = 0x1f'>31</td>
<td>AFStatusBottom-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='32 = 0x20'>32</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(the AF sensor used for focusing. Not valid for Contrast AF)</span></span><table class=cols><tr>
  <td>0 = Upper-left
  <br>1 = Left
  <br>2 = Lower-left
  <br>3 = Far Left
  <br>4 = Top (horizontal)
  <br>5 = Near Right
  <br>6 = Center (horizontal)
  <br>7 = Near Left
  <br>8 = Bottom (horizontal)
  <br>9 = Top (vertical)</td><td>&nbsp;&nbsp;</td>
  <td>10 = Center (vertical)
  <br>11 = Bottom (vertical)
  <br>12 = Far Right
  <br>13 = Upper-right
  <br>14 = Right
  <br>15 = Lower-right
  <br>16 = Upper-middle
  <br>17 = Lower-middle
  <br>255 = (none)</td></tr></table>
</td></tr>
<tr>
<td class=r title='33 = 0x21'>33</td>
<td>AFStatusActiveSensor
  <br>AFStatusBottom</td>
<td class=c>int16s<br>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
<table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='35 = 0x23'>35</td>
<td>AFStatus15
  <br>AFStatusMiddleHorizontal</td>
<td class=c>-<br>int16s</td>
<td>--&gt; <a href='Sony.html#AFStatus15'>Sony AFStatus15 Tags</a>
  <br><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='37 = 0x25'>37</td>
<td>AFStatusCenterVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='39 = 0x27'>39</td>
<td>AFStatusTop</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='41 = 0x29'>41</td>
<td>AFStatusTop-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='43 = 0x2b'>43</td>
<td>AFStatusBottom-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='45 = 0x2d'>45</td>
<td>AFStatusLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='47 = 0x2f'>47</td>
<td>AFStatusCenterHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='49 = 0x31'>49</td>
<td>AFStatusRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AFStatus15'>Sony AFStatus15 Tags</a></h2>
<p>AF Status information for models with 15-point AF.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>AFStatusUpper-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>AFStatusLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AFStatusLower-left</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AFStatusFarLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AFStatusTopHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>AFStatusNearRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>AFStatusCenterHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>AFStatusNearLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>AFStatusBottomHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>AFStatusTopVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>AFStatusCenterVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>AFStatusBottomVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>AFStatusFarRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>AFStatusUpper-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>AFStatusRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>AFStatusLower-right</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>AFStatusUpper-middle</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>AFStatusLower-middle</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraInfoUnknown'>Sony CameraInfoUnknown Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr><td colspan=4 class=c><i>[no tags known]</i></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FocusInfo'>Sony FocusInfo Tags</a></h2>
<p>More camera settings and focus information decoded for models such as the
A200, A230, A290, A300, A330, A350, A380, A390, A700, A850 and A900.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='14 = 0xe'>14</td>
<td>DriveMode2</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(A230, A290, A330, A380 and A390)</span></span><table class=cols><tr>
  <td>0x1 = Single Frame
  <br>0x2 = Continuous High
  <br>0x4 = Self-timer 10 sec
  <br>0x5 = Self-timer 2 sec, Mirror Lock-up
  <br>0x7 = Continuous Bracketing
  <br>0xa = Remote Commander
  <br>0xb = Continuous Self-timer</td></tr></table>
<span class=s><span class=n>(A200, A300, A350, A700, A850 and A900)</span></span><table class=cols><tr>
  <td>0x1 = Single Frame
  <br>0x2 = Continuous High
  <br>0x4 = Self-timer 10 sec
  <br>0x5 = Self-timer 2 sec, Mirror Lock-up
  <br>0x6 = Single-frame Bracketing
  <br>0x7 = Continuous Bracketing
  <br>0xa = Remote Commander
  <br>0xb = Mirror Lock-up
  <br>0x12 = Continuous Low
  <br>0x18 = White Balance Bracketing Low
  <br>0x19 = D-Range Optimizer Bracketing Low
  <br>0x28 = White Balance Bracketing High
  <br>0x29 = D-Range Optimizer Bracketing High</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>Rotation</td>
<td class=c>int8u</td>
<td><span class=s>0 = Horizontal (normal)
  <br>1 = Rotate 270 CW
  <br>2 = Rotate 90 CW</span></td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>ImageStabilizationSetting</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>DynamicRangeOptimizerMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Standard
  <br>2 = Advanced Auto
  <br>3 = Advanced Level</span></td></tr>
<tr>
<td class=r title='43 = 0x2b'>43</td>
<td>BracketShotNumber</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(WB and DRO bracketing)</span></span></td></tr>
<tr class=b>
<td class=r title='44 = 0x2c'>44</td>
<td>WhiteBalanceBracketing</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Low
  <br>2 = High</span></td></tr>
<tr>
<td class=r title='45 = 0x2d'>45</td>
<td>BracketShotNumber2</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='46 = 0x2e'>46</td>
<td>DynamicRangeOptimizerBracket</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Low
  <br>2 = High</span></td></tr>
<tr>
<td class=r title='47 = 0x2f'>47</td>
<td>ExposureBracketShotNumber</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='63 = 0x3f'>63</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram'>Sony ExposureProgram Values</a></td></tr>
<tr>
<td class=r title='65 = 0x41'>65</td>
<td>CreativeStyle</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>1 = Standard
  <br>2 = Vivid
  <br>3 = Portrait
  <br>4 = Landscape
  <br>5 = Sunset
  <br>6 = Night View/Portrait
  <br>8 = B&amp;W</td><td>&nbsp;&nbsp;</td>
  <td>9 = Adobe RGB
  <br>11 = Neutral
  <br>12 = Clear
  <br>13 = Deep
  <br>14 = Light
  <br>15 = Autumn Leaves
  <br>16 = Sepia</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='109 = 0x6d'>109</td>
<td>ISOSetting</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='111 = 0x6f'>111</td>
<td>ISO</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='119 = 0x77'>119</td>
<td>DynamicRangeOptimizerMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Standard
  <br>2 = Advanced Auto
  <br>3 = Advanced Level</span></td></tr>
<tr>
<td class=r title='121 = 0x79'>121</td>
<td>DynamicRangeOptimizerLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2118 = 0x846'>2118</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(only valid for some DSLR models)</span></span></td></tr>
<tr>
<td class=r title='2491 = 0x9bb'>2491</td>
<td>FocusPosition</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(only valid for some DSLR models)</span></span></td></tr>
<tr class=b>
<td class=r title='4368 = 0x1110'>4368</td>
<td>TiffMeteringImage</td>
<td class=c>no</td>
<td><span class=s><span class=n>(13-bit RBGG (?) 40x30 pixels, presumably metering info, extracted as a
16-bit TIFF image;)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ExposureProgram'>Sony ExposureProgram Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>ExposureProgram</th><th>Value</th><th>ExposureProgram</th><th>Value</th><th>ExposureProgram</th></tr>
<tr><td class=r>0</td><td>= Auto</td>
<td class='r b'>8</td><td class=b>= Program Shift A</td>
<td class=r>19</td><td>= Night Portrait</td>
</tr><tr><td class=r>1</td><td>= Manual</td>
<td class='r b'>9</td><td class=b>= Program Shift S</td>
<td class=r>20</td><td>= Landscape</td>
</tr><tr><td class=r>2</td><td>= Program AE</td>
<td class='r b'>16</td><td class=b>= Portrait</td>
<td class=r>21</td><td>= Macro</td>
</tr><tr><td class=r>3</td><td>= Aperture-priority AE</td>
<td class='r b'>17</td><td class=b>= Sports</td>
<td class=r>35</td><td>= Auto No Flash</td>
</tr><tr><td class=r>4</td><td>= Shutter speed priority AE</td>
<td class='r b'>18</td><td class=b>= Sunset</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='MoreInfo'>Sony MoreInfo Tags</a></h2>
<p>More camera settings information decoded for the A450, A500, A550, A560,
A580, A33, A35, A55, NEX-3/5/C3 and VG10E.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0001 = 1'>0x0001</td>
<td>MoreSettings</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MoreSettings'>Sony MoreSettings Tags</a></td></tr>
<tr class=b>
<td title='0x0002 = 2'>0x0002</td>
<td>FaceInfo
  <br>FaceInfoA</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#FaceInfo'>Sony FaceInfo Tags</a>
  <br>--&gt; <a href='Sony.html#FaceInfoA'>Sony FaceInfoA Tags</a></td></tr>
<tr>
<td title='0x0107 = 263'>0x0107</td>
<td>TiffMeteringImage</td>
<td class=c>no</td>
<td><span class=s><span class=n>(10-bit RGB data from the 1200 AE metering segments, extracted as a 16-bit
TIFF image)</span></span></td></tr>
<tr class=b>
<td title='0x0201 = 513'>0x0201</td>
<td>MoreInfo0201</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MoreInfo0201'>Sony MoreInfo0201 Tags</a></td></tr>
<tr>
<td title='0x0401 = 1025'>0x0401</td>
<td>MoreInfo0401</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MoreInfo0401'>Sony MoreInfo0401 Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MoreSettings'>Sony MoreSettings Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>DriveMode2</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0x10 = Single Frame
  <br>0x21 = Continuous High
  <br>0x22 = Continuous Low
  <br>0x30 = Speed Priority Continuous
  <br>0x51 = Self-timer 10 sec
  <br>0x52 = Self-timer 2 sec, Mirror Lock-up
  <br>0x71 = Continuous Bracketing 0.3 EV
  <br>0x75 = Continuous Bracketing 0.7 EV
  <br>0x91 = White Balance Bracketing Low
  <br>0x92 = White Balance Bracketing High
  <br>0xc0 = Remote Commander</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram2'>Sony ExposureProgram2 Values</a></td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>1 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot</span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>DynamicRangeOptimizerSetting</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On (Auto)
  <br>17 = On (Manual)</span></td></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>DynamicRangeOptimizerLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>ColorSpace</td>
<td class=c>int8u</td>
<td><span class=s>1 = sRGB
  <br>2 = Adobe RGB</span></td></tr>
<tr>
<td class=r title='7 = 0x7'>7</td>
<td>CreativeStyleSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>16 = Standard
  <br>32 = Vivid
  <br>64 = Portrait</td><td>&nbsp;&nbsp;</td>
  <td>80 = Landscape
  <br>96 = B&amp;W
  <br>160 = Sunset</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>ContrastSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='9 = 0x9'>9</td>
<td>SaturationSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>SharpnessSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='13 = 0xd'>13</td>
<td>WhiteBalanceSetting</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#WhiteBalanceSetting'>Sony WhiteBalanceSetting Values</a></td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>ColorTemperatureSetting</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='15 = 0xf'>15</td>
<td>ColorCompensationFilterSet</td>
<td class=c>int8s</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>1 = Flash Off
  <br>16 = Autoflash
  <br>17 = Fill-flash</td><td>&nbsp;&nbsp;</td>
  <td>18 = Slow Sync
  <br>19 = Rear Sync
  <br>20 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='17 = 0x11'>17</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>HighISONoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>16 = Low
  <br>17 = High
  <br>19 = Auto</span></td></tr>
<tr>
<td class=r title='19 = 0x13'>19</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><span class=s>17 = AF-S
  <br>18 = AF-C
  <br>19 = AF-A
  <br>32 = Manual
  <br>48 = DMF</span></td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>MultiFrameNoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = Off
  <br>16 = On
  <br>255 = None</span></td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On (Auto)
  <br>17 = On (Manual)</span></td></tr>
<tr class=b>
<td class=r title='23 = 0x17'>23</td>
<td>HDRLevel</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>33 = 1 EV
  <br>34 = 1.5 EV
  <br>35 = 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>36 = 2.5 EV
  <br>37 = 3 EV
  <br>38 = 3.5 EV</td><td>&nbsp;&nbsp;</td>
  <td>39 = 4 EV
  <br>40 = 5 EV
  <br>41 = 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>ViewingMode</td>
<td class=c>int8u</td>
<td><span class=s>16 = ViewFinder
  <br>33 = Focus Check Live View
  <br>34 = Quick AF Live View</span></td></tr>
<tr class=b>
<td class=r title='25 = 0x19'>25</td>
<td>FaceDetection</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>CustomWB_RBLevels</td>
<td class=c>int16uRev[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>BrightnessValue
  <br>ExposureCompensationSet</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(other models)</span></span></td></tr>
<tr>
<td class=r title='31 = 0x1f'>31</td>
<td>ISO
  <br>FlashExposureCompSet</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(other models)</span></span></td></tr>
<tr class=b>
<td class=r title='32 = 0x20'>32</td>
<td>FNumber
  <br>LiveViewAFMethod</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(other models except the NEX-3/5/5C)</span>
  <br>0 = n/a
  <br>1 = Phase-detect AF
  <br>2 = Contrast AF</span></td></tr>
<tr>
<td class=r title='33 = 0x21'>33</td>
<td>ExposureTime
  <br>ISO</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(NEX-3/5/5C)</span></span></td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>FNumber</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(NEX-3/5/5C only)</span></span></td></tr>
<tr>
<td class=r title='35 = 0x23'>35</td>
<td>FocalLength2
  <br>ExposureTime</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(NEX-3/5/5C)</span></span></td></tr>
<tr class=b>
<td class=r title='36 = 0x24'>36</td>
<td>ExposureCompensation2</td>
<td class=c>int16s</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span></span></td></tr>
<tr>
<td class=r title='37 = 0x25'>37</td>
<td>FocalLength2
  <br>ISO</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(NEX-3/5/5C)</span>
  <br><span class=n>(other models except the A450, A500 and A550)</span></span></td></tr>
<tr class=b>
<td class=r title='38 = 0x26'>38</td>
<td>FlashExposureCompSet2
  <br>ExposureCompensation2
  <br>FNumber</td>
<td class=c>int16s<br>int16s<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(NEX-3/5/5C)</span>
  <br><span class=n>(other models)</span></span></td></tr>
<tr>
<td class=r title='39 = 0x27'>39</td>
<td>ExposureTime</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models other than the A450, A500, A550 and NEX-3/5/5C)</span></span></td></tr>
<tr class=b>
<td class=r title='40 = 0x28'>40</td>
<td>Orientation2</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>1 = Horizontal (normal)
  <br>2 = Rotate 180
  <br>6 = Rotate 90 CW
  <br>8 = Rotate 270 CW</span></td></tr>
<tr>
<td class=r title='41 = 0x29'>41</td>
<td>FocusPosition2
  <br>FocalLength2</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br><span class=n>(other models except the NEX-3/5/5C)</span></span></td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>FlashAction
  <br>ExposureCompensation2</td>
<td class=c>int8u<br>int16s</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>0 = Did not fire
  <br>1 = Fired
  <br><span class=n>(other models except the NEX-3/5/5C)</span></span></td></tr>
<tr>
<td class=r title='43 = 0x2b'>43</td>
<td>FocusPosition2</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(NEX-3/5/5C only)</span></span></td></tr>
<tr class=b>
<td class=r title='44 = 0x2c'>44</td>
<td>FocusMode2
  <br>FlashAction
  <br>FlashExposureCompSet2</td>
<td class=c>int8u<br>int8u<br>int16s</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>0 = AF
  <br>1 = MF
  <br><span class=n>(NEX-3/5/5C FlashAction2)</span>
  <br>0 = Did not fire
  <br>1 = Fired
  <br><span class=n>(other models FlashExposureCompSet2)</span></span></td></tr>
<tr>
<td class=r title='46 = 0x2e'>46</td>
<td>FocusMode2
  <br>Orientation2</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s><span class=n>(NEX-3/5/5C)</span>
  <br>0 = AF
  <br>1 = MF
  <br><span class=n>(other models except the A450, A500 and A550)</span>
  <br>1 = Horizontal (normal)
  <br>2 = Rotate 180
  <br>6 = Rotate 90 CW
  <br>8 = Rotate 270 CW</span></td></tr>
<tr class=b>
<td class=r title='47 = 0x2f'>47</td>
<td>FocusPosition2</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models other than the A450, A500, A550 and NEX-3/5/5C)</span></span></td></tr>
<tr>
<td class=r title='48 = 0x30'>48</td>
<td>FlashAction</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models other than the A450, A500, A550 and NEX-3/5/5C)</span>
  <br>0 = Did not fire
  <br>1 = Fired</span></td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>FocusMode2</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models other than the A450, A500, A550 and NEX-3/5/5C)</span>
  <br>0 = AF
  <br>1 = MF</span></td></tr>
<tr>
<td class=r title='119 = 0x77'>119</td>
<td>FlashAction2</td>
<td class=c>int8u</td>
<td><span class=s>0 = Did not fire
  <br>2 = External Flash fired (2)
  <br>3 = Built-in Flash fired
  <br>4 = External Flash fired (4)</span></td></tr>
<tr class=b>
<td class=r title='120 = 0x78'>120</td>
<td>FlashActionExternal</td>
<td class=c>int8u</td>
<td><span class=s>121 = Fired
  <br>122 = Fired
  <br>136 = Did not fire</span></td></tr>
<tr>
<td class=r title='124 = 0x7c'>124</td>
<td>FlashActionExternal</td>
<td class=c>int8u</td>
<td><span class=s>136 = Did not fire
  <br>167 = Fired
  <br>182 = Fired, HSS</span></td></tr>
<tr class=b>
<td class=r title='130 = 0x82'>130</td>
<td>FlashStatus</td>
<td class=c>int8u</td>
<td><span class=s>0 = None
  <br>2 = External</span></td></tr>
<tr>
<td class=r title='134 = 0x86'>134</td>
<td>FlashStatus</td>
<td class=c>int8u</td>
<td><span class=s>0 = None
  <br>1 = Built-in
  <br>2 = External</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ExposureProgram2'>Sony ExposureProgram2 Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>ExposureProgram2</th><th>Value</th><th>ExposureProgram2</th><th>Value</th><th>ExposureProgram2</th></tr>
<tr><td class=r>1</td><td>= Program AE</td>
<td class='r b'>52</td><td class=b>= Sports</td>
<td class=r>129</td><td>= Pop Color</td>
</tr><tr><td class=r>2</td><td>= Aperture-priority AE</td>
<td class='r b'>53</td><td class=b>= Sunset</td>
<td class=r>130</td><td>= Posterization</td>
</tr><tr><td class=r>3</td><td>= Shutter speed priority AE</td>
<td class='r b'>54</td><td class=b>= Night view</td>
<td class=r>131</td><td>= Posterization B/W</td>
</tr><tr><td class=r>4</td><td>= Manual</td>
<td class='r b'>55</td><td class=b>= Night view/portrait</td>
<td class=r>132</td><td>= Retro Photo</td>
</tr><tr><td class=r>5</td><td>= Cont. Priority AE</td>
<td class='r b'>56</td><td class=b>= Handheld Night Shot</td>
<td class=r>133</td><td>= High-key</td>
</tr><tr><td class=r>16</td><td>= Auto</td>
<td class='r b'>57</td><td class=b>= 3D Sweep Panorama</td>
<td class=r>134</td><td>= Partial Color Red</td>
</tr><tr><td class=r>17</td><td>= Auto (no flash)</td>
<td class='r b'>64</td><td class=b>= Auto 2</td>
<td class=r>135</td><td>= Partial Color Green</td>
</tr><tr><td class=r>18</td><td>= Auto+</td>
<td class='r b'>65</td><td class=b>= Auto 2 (no flash)</td>
<td class=r>136</td><td>= Partial Color Blue</td>
</tr><tr><td class=r>49</td><td>= Portrait</td>
<td class='r b'>80</td><td class=b>= Sweep Panorama</td>
<td class=r>137</td><td>= Partial Color Yellow</td>
</tr><tr><td class=r>50</td><td>= Landscape</td>
<td class='r b'>96</td><td class=b>= Anti Motion Blur</td>
<td class=r>138</td><td>= High Contrast Monochrome</td>
</tr><tr><td class=r>51</td><td>= Macro</td>
<td class='r b'>128</td><td class=b>= Toy Camera</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='WhiteBalanceSetting'>Sony WhiteBalanceSetting Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>WhiteBalanceSetting</th><th>Value</th><th>WhiteBalanceSetting</th></tr>
<tr><td class=r>0x10</td><td>= Auto (-3)</td>
<td class='r b'>0x45</td><td class=b>= Cloudy (+2)</td>
</tr><tr><td class=r>0x11</td><td>= Auto (-2)</td>
<td class='r b'>0x46</td><td class=b>= Cloudy (+3)</td>
</tr><tr><td class=r>0x12</td><td>= Auto (-1)</td>
<td class='r b'>0x50</td><td class=b>= Tungsten (-3)</td>
</tr><tr><td class=r>0x13</td><td>= Auto (0)</td>
<td class='r b'>0x51</td><td class=b>= Tungsten (-2)</td>
</tr><tr><td class=r>0x14</td><td>= Auto (+1)</td>
<td class='r b'>0x52</td><td class=b>= Tungsten (-1)</td>
</tr><tr><td class=r>0x15</td><td>= Auto (+2)</td>
<td class='r b'>0x53</td><td class=b>= Tungsten (0)</td>
</tr><tr><td class=r>0x16</td><td>= Auto (+3)</td>
<td class='r b'>0x54</td><td class=b>= Tungsten (+1)</td>
</tr><tr><td class=r>0x20</td><td>= Daylight (-3)</td>
<td class='r b'>0x55</td><td class=b>= Tungsten (+2)</td>
</tr><tr><td class=r>0x21</td><td>= Daylight (-2)</td>
<td class='r b'>0x56</td><td class=b>= Tungsten (+3)</td>
</tr><tr><td class=r>0x22</td><td>= Daylight (-1)</td>
<td class='r b'>0x60</td><td class=b>= Fluorescent (-3)</td>
</tr><tr><td class=r>0x23</td><td>= Daylight (0)</td>
<td class='r b'>0x61</td><td class=b>= Fluorescent (-2)</td>
</tr><tr><td class=r>0x24</td><td>= Daylight (+1)</td>
<td class='r b'>0x62</td><td class=b>= Fluorescent (-1)</td>
</tr><tr><td class=r>0x25</td><td>= Daylight (+2)</td>
<td class='r b'>0x63</td><td class=b>= Fluorescent (0)</td>
</tr><tr><td class=r>0x26</td><td>= Daylight (+3)</td>
<td class='r b'>0x64</td><td class=b>= Fluorescent (+1)</td>
</tr><tr><td class=r>0x30</td><td>= Shade (-3)</td>
<td class='r b'>0x65</td><td class=b>= Fluorescent (+2)</td>
</tr><tr><td class=r>0x31</td><td>= Shade (-2)</td>
<td class='r b'>0x66</td><td class=b>= Fluorescent (+3)</td>
</tr><tr><td class=r>0x32</td><td>= Shade (-1)</td>
<td class='r b'>0x70</td><td class=b>= Flash (-3)</td>
</tr><tr><td class=r>0x33</td><td>= Shade (0)</td>
<td class='r b'>0x71</td><td class=b>= Flash (-2)</td>
</tr><tr><td class=r>0x34</td><td>= Shade (+1)</td>
<td class='r b'>0x72</td><td class=b>= Flash (-1)</td>
</tr><tr><td class=r>0x35</td><td>= Shade (+2)</td>
<td class='r b'>0x73</td><td class=b>= Flash (0)</td>
</tr><tr><td class=r>0x36</td><td>= Shade (+3)</td>
<td class='r b'>0x74</td><td class=b>= Flash (+1)</td>
</tr><tr><td class=r>0x40</td><td>= Cloudy (-3)</td>
<td class='r b'>0x75</td><td class=b>= Flash (+2)</td>
</tr><tr><td class=r>0x41</td><td>= Cloudy (-2)</td>
<td class='r b'>0x76</td><td class=b>= Flash (+3)</td>
</tr><tr><td class=r>0x42</td><td>= Cloudy (-1)</td>
<td class='r b'>0xa3</td><td class=b>= Custom</td>
</tr><tr><td class=r>0x43</td><td>= Cloudy (0)</td>
<td class='r b'>0xf3</td><td class=b>= Color Temperature/Color Filter</td>
</tr><tr><td class=r>0x44</td><td>= Cloudy (+1)</td>
<td class='r b'>&nbsp;</td><td class=b>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='FaceInfo'>Sony FaceInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>FacesDetected</td>
<td class=c>int16s</td>
<td><span class=s>-1 = n/a</span></td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>Face1Position</td>
<td class=c>int16u[4]</td>
<td><span class=s><span class=n>(re-ordered and scaled to return the top, left, height and width of detected
face, with coordinates relative to the full-sized unrotated image and
increasing Y downwards)</span></span></td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>Face2Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='11 = 0xb'>11</td>
<td>Face3Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>Face4Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>Face5Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>Face6Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='31 = 0x1f'>31</td>
<td>Face7Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='36 = 0x24'>36</td>
<td>Face8Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FaceInfoA'>Sony FaceInfoA Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>FacesDetected</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='11 = 0xb'>11</td>
<td>PotentialFace1Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='21 = 0x15'>21</td>
<td>PotentialFace2Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='31 = 0x1f'>31</td>
<td>PotentialFace3Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='41 = 0x29'>41</td>
<td>PotentialFace4Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='51 = 0x33'>51</td>
<td>PotentialFace5Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='61 = 0x3d'>61</td>
<td>PotentialFace6Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='71 = 0x47'>71</td>
<td>PotentialFace7Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='81 = 0x51'>81</td>
<td>PotentialFace8Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='91 = 0x5b'>91</td>
<td>Face1Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='101 = 0x65'>101</td>
<td>Face2Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='111 = 0x6f'>111</td>
<td>Face3Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='121 = 0x79'>121</td>
<td>Face4Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MoreInfo0201'>Sony MoreInfo0201 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='283 = 0x11b'>283</td>
<td>ImageCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(not valid for the A450, A500 or A550)</span></span></td></tr>
<tr class=b>
<td class=r title='293 = 0x125'>293</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(not valid for the A450, A500 or A550)</span></span></td></tr>
<tr>
<td class=r title='330 = 0x14a'>330</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(A450, A500 and A550 only)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MoreInfo0401'>Sony MoreInfo0401 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1102 = 0x44e'>1102</td>
<td>ShotNumberSincePowerUp</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(Not valid for the NEX-3 or NEX-5)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraSettings'>Sony CameraSettings Tags</a></h2>
<p>Camera settings for the A200, A300, A350, A700, A850 and A900.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>ExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>FNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>HighSpeedSync</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>ExposureCompensationSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>DriveMode</td>
<td class=c>int16u</td>
<td><span class=s>[val &amp; 0xff]</span><table class=cols><tr>
  <td>0x1 = Single Frame
  <br>0x2 = Continuous High
  <br>0x4 = Self-timer 10 sec
  <br>0x5 = Self-timer 2 sec, Mirror Lock-up
  <br>0x6 = Single-frame Bracketing
  <br>0x7 = Continuous Bracketing
  <br>0xa = Remote Commander
  <br>0xb = Mirror Lock-up
  <br>0x12 = Continuous Low
  <br>0x18 = White Balance Bracketing Low
  <br>0x19 = D-Range Optimizer Bracketing Low
  <br>0x28 = White Balance Bracketing High
  <br>0x29 = D-Range Optimizer Bracketing High</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>WhiteBalanceSetting</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>2 = Auto
  <br>4 = Daylight
  <br>5 = Fluorescent
  <br>6 = Tungsten
  <br>7 = Flash
  <br>16 = Cloudy
  <br>17 = Shade
  <br>18 = Color Temperature/Color Filter
  <br>32 = Custom 1
  <br>33 = Custom 2
  <br>34 = Custom 3</td></tr></table>
</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>WhiteBalanceFineTune</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>ColorTemperatureSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>ColorCompensationFilterSet</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>ColorTemperatureCustom</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='13 = 0xd'>13</td>
<td>ColorCompensationFilterCustom</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td class=r title='15 = 0xf'>15</td>
<td>WhiteBalance</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>2 = Auto
  <br>4 = Daylight
  <br>5 = Fluorescent
  <br>6 = Tungsten
  <br>7 = Flash</td><td>&nbsp;&nbsp;</td>
  <td>12 = Color Temperature
  <br>13 = Color Filter
  <br>14 = Custom
  <br>16 = Cloudy
  <br>17 = Shade</td></tr></table>
</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>FocusModeSetting</td>
<td class=c>int16u</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A
  <br>4 = DMF</span></td></tr>
<tr class=b>
<td class=r title='17 = 0x11'>17</td>
<td>AFAreaMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Wide
  <br>1 = Local
  <br>2 = Spot</span></td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>AFPointSetting</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right
  <br>5 = Lower-right
  <br>6 = Bottom</td><td>&nbsp;&nbsp;</td>
  <td>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left
  <br>10 = Far Right
  <br>11 = Far Left</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='19 = 0x13'>19</td>
<td>FlashMode</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>2 = Rear Sync
  <br>3 = Wireless</td><td>&nbsp;&nbsp;</td>
  <td>4 = Fill-flash
  <br>5 = Flash Off
  <br>6 = Slow Sync</td></tr></table>
</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>FlashExposureCompSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>MeteringMode</td>
<td class=c>int16u</td>
<td><span class=s>1 = Multi-segment
  <br>2 = Center-weighted average
  <br>4 = Spot</span></td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>ISOSetting</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>DynamicRangeOptimizerMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = Standard
  <br>2 = Advanced Auto
  <br>3 = Advanced Level</span></td></tr>
<tr>
<td class=r title='25 = 0x19'>25</td>
<td>DynamicRangeOptimizerLevel</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>CreativeStyle</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Standard
  <br>2 = Vivid
  <br>3 = Portrait
  <br>4 = Landscape
  <br>5 = Sunset
  <br>6 = Night View/Portrait
  <br>8 = B&amp;W</td><td>&nbsp;&nbsp;</td>
  <td>9 = Adobe RGB
  <br>11 = Neutral
  <br>12 = Clear
  <br>13 = Deep
  <br>14 = Light
  <br>15 = Autumn Leaves
  <br>16 = Sepia</td></tr></table>
</td></tr>
<tr>
<td class=r title='27 = 0x1b'>27</td>
<td>ColorSpace</td>
<td class=c>int16u</td>
<td><span class=s>0 = sRGB
  <br>1 = Adobe RGB
  <br>5 = Adobe RGB (A700)</span></td></tr>
<tr class=b>
<td class=r title='28 = 0x1c'>28</td>
<td>Sharpness</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='29 = 0x1d'>29</td>
<td>Contrast</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>Saturation</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='31 = 0x1f'>31</td>
<td>ZoneMatchingValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>Brightness</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='35 = 0x23'>35</td>
<td>FlashControl</td>
<td class=c>int16u</td>
<td><span class=s>0 = ADI
  <br>1 = Pre-flash TTL
  <br>2 = Manual</span></td></tr>
<tr class=b>
<td class=r title='40 = 0x28'>40</td>
<td>PrioritySetupShutterRelease</td>
<td class=c>int16u</td>
<td><span class=s>0 = AF
  <br>1 = Release</span></td></tr>
<tr>
<td class=r title='41 = 0x29'>41</td>
<td>AFIlluminator</td>
<td class=c>int16u</td>
<td><span class=s>0 = Auto
  <br>1 = Off</span></td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>AFWithShutter</td>
<td class=c>int16u</td>
<td><span class=s>0 = On
  <br>1 = Off</span></td></tr>
<tr>
<td class=r title='43 = 0x2b'>43</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='44 = 0x2c'>44</td>
<td>HighISONoiseReduction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Normal
  <br>1 = Low
  <br>2 = High
  <br>3 = Off</span></td></tr>
<tr>
<td class=r title='45 = 0x2d'>45</td>
<td>ImageStyle</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Standard
  <br>2 = Vivid
  <br>3 = Portrait
  <br>4 = Landscape
  <br>5 = Sunset
  <br>7 = Night View/Portrait
  <br>8 = B&amp;W
  <br>9 = Adobe RGB</td><td>&nbsp;&nbsp;</td>
  <td>11 = Neutral
  <br>129 = StyleBox1
  <br>130 = StyleBox2
  <br>131 = StyleBox3
  <br>132 = StyleBox4
  <br>133 = StyleBox5
  <br>134 = StyleBox6</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='46 = 0x2e'>46</td>
<td>FocusModeSwitch</td>
<td class=c>int16u</td>
<td><span class=s>0 = AF
  <br>1 = Manual</span></td></tr>
<tr>
<td class=r title='47 = 0x2f'>47</td>
<td>ShutterSpeedSetting</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(used in M, S and Program Shift S modes)</span></span></td></tr>
<tr class=b>
<td class=r title='48 = 0x30'>48</td>
<td>ApertureSetting</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(used in M, A and Program Shift A modes)</span></span></td></tr>
<tr>
<td class=r title='60 = 0x3c'>60</td>
<td>ExposureProgram</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram'>Sony ExposureProgram Values</a></td></tr>
<tr class=b>
<td class=r title='61 = 0x3d'>61</td>
<td>ImageStabilizationSetting</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='62 = 0x3e'>62</td>
<td>FlashAction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Did not fire
  <br>1 = Fired
  <br>2 = External Flash, Did not fire
  <br>3 = External Flash, Fired</span></td></tr>
<tr class=b>
<td class=r title='63 = 0x3f'>63</td>
<td>Rotation</td>
<td class=c>int16u</td>
<td><span class=s>0 = Horizontal (normal)
  <br>1 = Rotate 90 CW
  <br>2 = Rotate 270 CW</span></td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>AELock</td>
<td class=c>int16u</td>
<td><span class=s>1 = Off
  <br>2 = On</span></td></tr>
<tr class=b>
<td class=r title='76 = 0x4c'>76</td>
<td>FlashAction2</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Fired, Autoflash
  <br>2 = Fired, Fill-flash
  <br>3 = Fired, Rear Sync
  <br>4 = Fired, Wireless
  <br>5 = Did not fire
  <br>6 = Fired, Slow Sync
  <br>17 = Fired, Autoflash, Red-eye reduction
  <br>18 = Fired, Fill-flash, Red-eye reduction
  <br>34 = Fired, Fill-flash, HSS</td></tr></table>
</td></tr>
<tr>
<td class=r title='77 = 0x4d'>77</td>
<td>FocusMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A
  <br>4 = DMF</span></td></tr>
<tr class=b>
<td class=r title='80 = 0x50'>80</td>
<td>BatteryState</td>
<td class=c>int16u</td>
<td><span class=s>2 = Empty
  <br>3 = Very Low
  <br>4 = Low
  <br>5 = Sufficient
  <br>6 = Full</span></td></tr>
<tr>
<td class=r title='81 = 0x51'>81</td>
<td>BatteryLevel</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='83 = 0x53'>83</td>
<td>FocusStatus</td>
<td class=c>int16u</td>
<td><span class=s>0x0 = Not confirmed
  <br>0x4 = Not confirmed, Tracking
  <br>Bit 0 = Confirmed
  <br>Bit 1 = Failed
  <br>Bit 2 = Tracking</span></td></tr>
<tr>
<td class=r title='84 = 0x54'>84</td>
<td>SonyImageSize</td>
<td class=c>int16u</td>
<td><span class=s>1 = Large
  <br>2 = Medium
  <br>3 = Small</span></td></tr>
<tr class=b>
<td class=r title='85 = 0x55'>85</td>
<td>AspectRatio</td>
<td class=c>int16u</td>
<td><span class=s>1 = 3:2
  <br>2 = 16:9</span></td></tr>
<tr>
<td class=r title='86 = 0x56'>86</td>
<td>Quality</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = RAW
  <br>2 = CRAW
  <br>16 = Extra Fine
  <br>32 = Fine</td><td>&nbsp;&nbsp;</td>
  <td>34 = RAW + JPEG
  <br>35 = CRAW + JPEG
  <br>48 = Standard</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='88 = 0x58'>88</td>
<td>ExposureLevelIncrements</td>
<td class=c>int16u</td>
<td><span class=s>33 = 1/3 EV
  <br>50 = 1/2 EV</span></td></tr>
<tr>
<td class=r title='106 = 0x6a'>106</td>
<td>RedEyeReduction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='154 = 0x9a'>154</td>
<td>FolderNumber</td>
<td class=c>int16u</td>
<td><span class=s>[val &amp; 0x3ff]</span></td></tr>
<tr>
<td class=r title='155 = 0x9b'>155</td>
<td>ImageNumber</td>
<td class=c>int16u</td>
<td><span class=s>[val &amp; 0x3fff]</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraSettings2'>Sony CameraSettings2 Tags</a></h2>
<p>Camera settings for the A230, A290, A330, A380 and A390.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>ExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>FNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>HighSpeedSync</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>ExposureCompensationSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>WhiteBalanceSetting</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>2 = Auto
  <br>4 = Daylight
  <br>5 = Fluorescent
  <br>6 = Tungsten
  <br>7 = Flash
  <br>16 = Cloudy
  <br>17 = Shade
  <br>18 = Color Temperature/Color Filter
  <br>32 = Custom 1
  <br>33 = Custom 2
  <br>34 = Custom 3</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>WhiteBalanceFineTune</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>ColorTemperatureSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>ColorCompensationFilterSet</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>CustomWB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='11 = 0xb'>11</td>
<td>ColorTemperatureCustom</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>ColorCompensationFilterCustom</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>WhiteBalance</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>2 = Auto
  <br>4 = Daylight
  <br>5 = Fluorescent
  <br>6 = Tungsten
  <br>7 = Flash</td><td>&nbsp;&nbsp;</td>
  <td>12 = Color Temperature
  <br>13 = Color Filter
  <br>14 = Custom
  <br>16 = Cloudy
  <br>17 = Shade</td></tr></table>
</td></tr>
<tr>
<td class=r title='15 = 0xf'>15</td>
<td>FocusModeSetting</td>
<td class=c>int16u</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A</span></td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>AFAreaMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Wide
  <br>1 = Local
  <br>2 = Spot</span></td></tr>
<tr>
<td class=r title='17 = 0x11'>17</td>
<td>AFPointSetting</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Center
  <br>2 = Top
  <br>3 = Upper-right
  <br>4 = Right
  <br>5 = Lower-right</td><td>&nbsp;&nbsp;</td>
  <td>6 = Bottom
  <br>7 = Lower-left
  <br>8 = Left
  <br>9 = Upper-left</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>FlashExposureCompSet</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='19 = 0x13'>19</td>
<td>MeteringMode</td>
<td class=c>int16u</td>
<td><span class=s>1 = Multi-segment
  <br>2 = Center-weighted average
  <br>4 = Spot</span></td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>ISOSetting</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>DynamicRangeOptimizerMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = Standard
  <br>2 = Advanced Auto
  <br>3 = Advanced Level</span></td></tr>
<tr class=b>
<td class=r title='23 = 0x17'>23</td>
<td>DynamicRangeOptimizerLevel</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>CreativeStyle</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Standard
  <br>2 = Vivid
  <br>3 = Portrait
  <br>4 = Landscape</td><td>&nbsp;&nbsp;</td>
  <td>5 = Sunset
  <br>6 = Night View/Portrait
  <br>8 = B&amp;W</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='25 = 0x19'>25</td>
<td>Sharpness</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>Contrast</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='27 = 0x1b'>27</td>
<td>Saturation</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='31 = 0x1f'>31</td>
<td>FlashControl</td>
<td class=c>int16u</td>
<td><span class=s>0 = ADI
  <br>1 = Pre-flash TTL
  <br>2 = Manual</span></td></tr>
<tr class=b>
<td class=r title='37 = 0x25'>37</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='38 = 0x26'>38</td>
<td>HighISONoiseReduction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = Low
  <br>2 = Normal
  <br>3 = High</span></td></tr>
<tr class=b>
<td class=r title='39 = 0x27'>39</td>
<td>ImageStyle</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Standard
  <br>2 = Vivid
  <br>3 = Portrait
  <br>4 = Landscape</td><td>&nbsp;&nbsp;</td>
  <td>5 = Sunset
  <br>7 = Night View/Portrait
  <br>8 = B&amp;W</td></tr></table>
</td></tr>
<tr>
<td class=r title='40 = 0x28'>40</td>
<td>ShutterSpeedSetting</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(used in M, S and Program Shift S modes)</span></span></td></tr>
<tr class=b>
<td class=r title='41 = 0x29'>41</td>
<td>ApertureSetting</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(used in M, A and Program Shift A modes)</span></span></td></tr>
<tr>
<td class=r title='60 = 0x3c'>60</td>
<td>ExposureProgram</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram'>Sony ExposureProgram Values</a></td></tr>
<tr class=b>
<td class=r title='61 = 0x3d'>61</td>
<td>ImageStabilizationSetting</td>
<td class=c>int16u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='62 = 0x3e'>62</td>
<td>FlashAction</td>
<td class=c>int16u</td>
<td><span class=s>0 = Did not fire
  <br>1 = Fired
  <br>2 = External Flash, Did not fire
  <br>3 = External Flash, Fired</span></td></tr>
<tr class=b>
<td class=r title='63 = 0x3f'>63</td>
<td>Rotation</td>
<td class=c>int16u</td>
<td><span class=s>0 = Horizontal (normal)
  <br>1 = Rotate 90 CW
  <br>2 = Rotate 270 CW</span></td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>AELock</td>
<td class=c>int16u</td>
<td><span class=s>1 = Off
  <br>2 = On</span></td></tr>
<tr class=b>
<td class=r title='76 = 0x4c'>76</td>
<td>FlashAction2</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>1 = Fired, Autoflash
  <br>2 = Fired, Fill-flash
  <br>3 = Fired, Rear Sync
  <br>4 = Fired, Wireless
  <br>5 = Did not fire
  <br>6 = Fired, Slow Sync
  <br>17 = Fired, Autoflash, Red-eye reduction
  <br>18 = Fired, Fill-flash, Red-eye reduction
  <br>34 = Fired, Fill-flash, HSS</td></tr></table>
</td></tr>
<tr>
<td class=r title='77 = 0x4d'>77</td>
<td>FocusMode</td>
<td class=c>int16u</td>
<td><span class=s>0 = Manual
  <br>1 = AF-S
  <br>2 = AF-C
  <br>3 = AF-A</span></td></tr>
<tr class=b>
<td class=r title='83 = 0x53'>83</td>
<td>FocusStatus</td>
<td class=c>int16u</td>
<td><span class=s>0x0 = Not confirmed
  <br>0x4 = Not confirmed, Tracking
  <br>Bit 0 = Confirmed
  <br>Bit 1 = Failed
  <br>Bit 2 = Tracking</span></td></tr>
<tr>
<td class=r title='84 = 0x54'>84</td>
<td>SonyImageSize</td>
<td class=c>int16u</td>
<td><span class=s>1 = Large
  <br>2 = Medium
  <br>3 = Small</span></td></tr>
<tr class=b>
<td class=r title='85 = 0x55'>85</td>
<td>AspectRatio</td>
<td class=c>int16u</td>
<td><span class=s>1 = 3:2
  <br>2 = 16:9</span></td></tr>
<tr>
<td class=r title='86 = 0x56'>86</td>
<td>Quality</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = RAW
  <br>2 = CRAW
  <br>16 = Extra Fine
  <br>32 = Fine</td><td>&nbsp;&nbsp;</td>
  <td>34 = RAW + JPEG
  <br>35 = CRAW + JPEG
  <br>48 = Standard</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='88 = 0x58'>88</td>
<td>ExposureLevelIncrements</td>
<td class=c>int16u</td>
<td><span class=s>33 = 1/3 EV
  <br>50 = 1/2 EV</span></td></tr>
<tr>
<td class=r title='126 = 0x7e'>126</td>
<td>DriveMode</td>
<td class=c>int16u</td>
<td><span class=s>[val &amp; 0xff]</span><table class=cols><tr>
  <td>1 = Single Frame
  <br>2 = Continuous High
  <br>4 = Self-timer 10 sec
  <br>5 = Self-timer 2 sec, Mirror Lock-up
  <br>7 = Continuous Bracketing
  <br>10 = Remote Commander
  <br>11 = Continuous Self-timer</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='127 = 0x7f'>127</td>
<td>FlashMode</td>
<td class=c>int16u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>2 = Rear Sync
  <br>3 = Wireless</td><td>&nbsp;&nbsp;</td>
  <td>4 = Fill-flash
  <br>5 = Flash Off
  <br>6 = Slow Sync</td></tr></table>
</td></tr>
<tr>
<td class=r title='131 = 0x83'>131</td>
<td>ColorSpace</td>
<td class=c>int16u</td>
<td><span class=s>5 = Adobe RGB
  <br>6 = sRGB</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CameraSettings3'>Sony CameraSettings3 Tags</a></h2>
<p>Camera settings for models such as the A33, A35, A55, A450, A500, A550,
A560, A580, NEX-3, NEX-5, NEX-C3 and NEX-VG10E.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>ShutterSpeedSetting</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(used only in M and S exposure modes)</span></span></td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>ApertureSetting</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(used only in M and A exposure modes)</span></span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>ISOSetting</td>
<td class=c>int8u</td>
<td><span class=s>0 = Auto
  <br>254 = n/a</span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>ExposureCompensationSet</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>DriveModeSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0x10 = Single Frame
  <br>0x21 = Continuous High
  <br>0x22 = Continuous Low
  <br>0x30 = Speed Priority Continuous
  <br>0x51 = Self-timer 10 sec
  <br>0x52 = Self-timer 2 sec, Mirror Lock-up
  <br>0x71 = Continuous Bracketing 0.3 EV
  <br>0x75 = Continuous Bracketing 0.7 EV
  <br>0x91 = White Balance Bracketing Low
  <br>0x92 = White Balance Bracketing High
  <br>0xc0 = Remote Commander</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram2'>Sony ExposureProgram2 Values</a></td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>FocusModeSetting</td>
<td class=c>int8u</td>
<td><span class=s>17 = AF-S
  <br>18 = AF-C
  <br>19 = AF-A
  <br>32 = Manual
  <br>48 = DMF</span></td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>1 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot</span></td></tr>
<tr>
<td class=r title='9 = 0x9'>9</td>
<td>SonyImageSize</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>21 = Large (3:2)
  <br>22 = Medium (3:2)
  <br>23 = Small (3:2)</td><td>&nbsp;&nbsp;</td>
  <td>25 = Large (16:9)
  <br>26 = Medium (16:9)
  <br>27 = Small (16:9)</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>4 = 3:2
  <br>8 = 16:9</span></td></tr>
<tr>
<td class=r title='11 = 0xb'>11</td>
<td>Quality</td>
<td class=c>int8u</td>
<td><span class=s>2 = RAW
  <br>4 = RAW + JPEG
  <br>6 = Fine
  <br>7 = Standard</span></td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>DynamicRangeOptimizerSetting</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On (Auto)
  <br>17 = On (Manual)</span></td></tr>
<tr>
<td class=r title='13 = 0xd'>13</td>
<td>DynamicRangeOptimizerLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>ColorSpace</td>
<td class=c>int8u</td>
<td><span class=s>1 = sRGB
  <br>2 = Adobe RGB</span></td></tr>
<tr>
<td class=r title='15 = 0xf'>15</td>
<td>CreativeStyleSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>16 = Standard
  <br>32 = Vivid
  <br>64 = Portrait</td><td>&nbsp;&nbsp;</td>
  <td>80 = Landscape
  <br>96 = B&amp;W
  <br>160 = Sunset</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>ContrastSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='17 = 0x11'>17</td>
<td>SaturationSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>SharpnessSetting</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>WhiteBalanceSetting</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#WhiteBalanceSetting'>Sony WhiteBalanceSetting Values</a></td></tr>
<tr class=b>
<td class=r title='23 = 0x17'>23</td>
<td>ColorTemperatureSetting</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>ColorCompensationFilterSet</td>
<td class=c>int8s</td>
<td><span class=s><span class=n>(negative is green, positive is magenta)</span></span></td></tr>
<tr class=b>
<td class=r title='25 = 0x19'>25</td>
<td>CustomWB_RGBLevels</td>
<td class=c>int16uRev[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>1 = Flash Off
  <br>16 = Autoflash
  <br>17 = Fill-flash</td><td>&nbsp;&nbsp;</td>
  <td>18 = Slow Sync
  <br>19 = Rear Sync
  <br>20 = Wireless</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='33 = 0x21'>33</td>
<td>FlashControl</td>
<td class=c>int8u</td>
<td><span class=s>1 = ADI Flash
  <br>2 = Pre-flash TTL</span></td></tr>
<tr>
<td class=r title='35 = 0x23'>35</td>
<td>FlashExposureCompSet</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='36 = 0x24'>36</td>
<td>AFAreaMode</td>
<td class=c>int8u</td>
<td><span class=s>1 = Wide
  <br>2 = Spot
  <br>3 = Local
  <br>4 = Flexible</span></td></tr>
<tr>
<td class=r title='37 = 0x25'>37</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr class=b>
<td class=r title='38 = 0x26'>38</td>
<td>HighISONoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>16 = Low
  <br>17 = High
  <br>19 = Auto</span></td></tr>
<tr>
<td class=r title='39 = 0x27'>39</td>
<td>SmileShutterMode</td>
<td class=c>int8u</td>
<td><span class=s>17 = Slight Smile
  <br>18 = Normal Smile
  <br>19 = Big Smile</span></td></tr>
<tr class=b>
<td class=r title='40 = 0x28'>40</td>
<td>RedEyeReduction</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr>
<td class=r title='45 = 0x2d'>45</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On (Auto)
  <br>17 = On (Manual)</span></td></tr>
<tr class=b>
<td class=r title='46 = 0x2e'>46</td>
<td>HDRLevel</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>33 = 1 EV
  <br>34 = 1.5 EV
  <br>35 = 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>36 = 2.5 EV
  <br>37 = 3 EV
  <br>38 = 3.5 EV</td><td>&nbsp;&nbsp;</td>
  <td>39 = 4 EV
  <br>40 = 5 EV
  <br>41 = 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='47 = 0x2f'>47</td>
<td>ViewingMode</td>
<td class=c>int8u</td>
<td><span class=s>16 = ViewFinder
  <br>33 = Focus Check Live View
  <br>34 = Quick AF Live View</span></td></tr>
<tr class=b>
<td class=r title='48 = 0x30'>48</td>
<td>FaceDetection</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr>
<td class=r title='49 = 0x31'>49</td>
<td>SmileShutter</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>16 = On</span></td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>SweepPanoramaSize</td>
<td class=c>int8u</td>
<td><span class=s>1 = Standard
  <br>2 = Wide</span></td></tr>
<tr>
<td class=r title='51 = 0x33'>51</td>
<td>SweepPanoramaDirection</td>
<td class=c>int8u</td>
<td><span class=s>1 = Right
  <br>2 = Left
  <br>3 = Up
  <br>4 = Down</span></td></tr>
<tr class=b>
<td class=r title='52 = 0x34'>52</td>
<td>DriveMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0x10 = Single Frame
  <br>0x21 = Continuous High
  <br>0x22 = Continuous Low
  <br>0x30 = Speed Priority Continuous
  <br>0x51 = Self-timer 10 sec
  <br>0x52 = Self-timer 2 sec, Mirror Lock-up
  <br>0x71 = Continuous Bracketing 0.3 EV
  <br>0x75 = Continuous Bracketing 0.7 EV
  <br>0x91 = White Balance Bracketing Low
  <br>0x92 = White Balance Bracketing High
  <br>0xc0 = Remote Commander
  <br>0xd1 = Continuous - HDR
  <br>0xd2 = Continuous - Multi Frame NR
  <br>0xd3 = Continuous - Handheld Night Shot
  <br>0xd4 = Continuous - Anti Motion Blur
  <br>0xd5 = Continuous - Sweep Panorama
  <br>0xd6 = Continuous - 3D Sweep Panorama</td></tr></table>
</td></tr>
<tr>
<td class=r title='53 = 0x35'>53</td>
<td>MultiFrameNoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = Off
  <br>16 = On
  <br>255 = None</span></td></tr>
<tr class=b>
<td class=r title='54 = 0x36'>54</td>
<td>LiveViewAFSetting</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = Phase-detect AF
  <br>2 = Contrast AF</span></td></tr>
<tr>
<td class=r title='56 = 0x38'>56</td>
<td>PanoramaSize3D</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = Standard
  <br>2 = Wide
  <br>3 = 16:9</span></td></tr>
<tr class=b>
<td class=r title='131 = 0x83'>131</td>
<td>AFButtonPressed</td>
<td class=c>int8u</td>
<td><span class=s>1 = No
  <br>16 = Yes</span></td></tr>
<tr>
<td class=r title='132 = 0x84'>132</td>
<td>LiveViewMetering</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>16 = 40 Segment
  <br>32 = 1200-zone Evaluative</span></td></tr>
<tr class=b>
<td class=r title='133 = 0x85'>133</td>
<td>ViewingMode2</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>16 = Viewfinder
  <br>33 = Focus Check Live View
  <br>34 = Quick AF Live View</span></td></tr>
<tr>
<td class=r title='134 = 0x86'>134</td>
<td>AELock</td>
<td class=c>int8u</td>
<td><span class=s>1 = On
  <br>2 = Off</span></td></tr>
<tr class=b>
<td class=r title='135 = 0x87'>135</td>
<td>FlashStatusBuilt-in</td>
<td class=c>int8u</td>
<td><span class=s>1 = Off
  <br>2 = On</span></td></tr>
<tr>
<td class=r title='136 = 0x88'>136</td>
<td>FlashStatusExternal</td>
<td class=c>int8u</td>
<td><span class=s>1 = None
  <br>2 = Off
  <br>3 = On</span></td></tr>
<tr class=b>
<td class=r title='139 = 0x8b'>139</td>
<td>LiveViewFocusMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = AF
  <br>16 = Manual</span></td></tr>
<tr>
<td class=r title='153 = 0x99'>153</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>1 = Unknown
  <br>16 = A-mount
  <br>17 = E-mount</span></td></tr>
<tr class=b>
<td class=r title='268 = 0x10c'>268</td>
<td>SequenceNumber</td>
<td class=c>int8u</td>
<td><span class=s>0 = Single
  <br>255 = n/a</span></td></tr>
<tr>
<td class=r title='276 = 0x114'>276</td>
<td>FolderNumber</td>
<td class=c>int32u</td>
<td><span class=s>[val &gt;&gt; 14 &amp; 0x3ff]</span></td></tr>
<tr class=b>
<td class=r title='276 = 0x114'>276.1</td>
<td>ImageNumber</td>
<td class=c>int32u</td>
<td><span class=s>[val &amp; 0x3fff]</span></td></tr>
<tr>
<td class=r title='512 = 0x200'>512</td>
<td>ShotNumberSincePowerUp2</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(same as ShotNumberSincePowerUp for single-shot images, but includes all
shots of the current image in multi-shot modes like HDR, panorama, and
multi-frame noise reduction)</span></span></td></tr>
<tr class=b>
<td class=r title='643 = 0x283'>643</td>
<td>AFButtonPressed</td>
<td class=c>int8u</td>
<td><span class=s>1 = No
  <br>16 = Yes</span></td></tr>
<tr>
<td class=r title='644 = 0x284'>644</td>
<td>LiveViewMetering</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>16 = 40 Segment
  <br>32 = 1200-zone Evaluative</span></td></tr>
<tr class=b>
<td class=r title='645 = 0x285'>645</td>
<td>ViewingMode2</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>16 = Viewfinder
  <br>33 = Focus Check Live View
  <br>34 = Quick AF Live View</span></td></tr>
<tr>
<td class=r title='646 = 0x286'>646</td>
<td>AELock</td>
<td class=c>int8u</td>
<td><span class=s>1 = On
  <br>2 = Off</span></td></tr>
<tr class=b>
<td class=r title='647 = 0x287'>647</td>
<td>FlashStatusBuilt-in</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>1 = Off
  <br>2 = On</span></td></tr>
<tr>
<td class=r title='648 = 0x288'>648</td>
<td>FlashStatusExternal</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>1 = None
  <br>2 = Off
  <br>3 = On</span></td></tr>
<tr class=b>
<td class=r title='651 = 0x28b'>651</td>
<td>LiveViewFocusMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = n/a
  <br>1 = AF
  <br>16 = Manual</span></td></tr>
<tr>
<td class=r title='780 = 0x30c'>780</td>
<td>SequenceNumber</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>0 = Single
  <br>255 = n/a</span></td></tr>
<tr class=b>
<td class=r title='788 = 0x314'>788</td>
<td>ImageNumber</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>[val &amp; 0x3fff]</span></td></tr>
<tr>
<td class=r title='790 = 0x316'>790</td>
<td>FolderNumber</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>[val &amp; 0x3ff]</span></td></tr>
<tr class=b>
<td class=r title='1008 = 0x3f0'>1008</td>
<td>LensE-mountVersion</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1011 = 0x3f3'>1011</td>
<td>LensFirmwareVersion</td>
<td class=c title=' ~ = Writable only with -n'>int16u~</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1015 = 0x3f7'>1015</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr>
<td class=r title='1024 = 0x400'>1024</td>
<td>ImageNumber</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>[val &amp; 0x3fff]</span></td></tr>
<tr class=b>
<td class=r title='1026 = 0x402'>1026</td>
<td>FolderNumber</td>
<td class=c>int16u</td>
<td><span class=s><span class=n>(A450, A500 and A550)</span>
  <br>[val &amp; 0x3ff]</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='LensType2'>Sony LensType2 Values</a></h2>
<p>Lens type numbers for Sony E-mount lenses used by NEX/ILCE models.</p>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>LensType2</th></tr>
<tr><td>0</td><td>= Unknown E-mount lens or other lens</td>
</tr><tr><td>0</td><td>= Sigma 19mm F2.8 [EX] DN</td>
</tr><tr><td>0</td><td>= Sigma 30mm F2.8 [EX] DN</td>
</tr><tr><td>0</td><td>= Sigma 60mm F2.8 DN</td>
</tr><tr><td>0</td><td>= Sony E 18-200mm F3.5-6.3 OSS LE</td>
</tr><tr><td>0</td><td>= Tamron 18-200mm F3.5-6.3 Di III VC</td>
</tr><tr><td>0</td><td>= Tokina FiRIN 20mm F2 FE AF</td>
</tr><tr><td>0</td><td>= Tokina FiRIN 20mm F2 FE MF</td>
</tr><tr><td>0</td><td>= Zeiss Touit 12mm F2.8</td>
</tr><tr><td>0</td><td>= Zeiss Touit 32mm F1.8</td>
</tr><tr><td>0</td><td>= Zeiss Touit 50mm F2.8 Macro</td>
</tr><tr><td>0</td><td>= Zeiss Loxia 50mm F2</td>
</tr><tr><td>0</td><td>= Zeiss Loxia 35mm F2</td>
</tr><tr><td>0</td><td>= Viltrox 85mm F1.8</td>
</tr><tr><td>1</td><td>= Sony LA-EA1 or Sigma MC-11 Adapter</td>
</tr><tr><td>2</td><td>= Sony LA-EA2 Adapter</td>
</tr><tr><td>3</td><td>= Sony LA-EA3 Adapter</td>
</tr><tr><td>6</td><td>= Sony LA-EA4 Adapter</td>
</tr><tr><td>7</td><td>= Sony LA-EA5 Adapter</td>
</tr><tr><td>13</td><td>= Samyang AF 35-150mm F2-2.8</td>
</tr><tr><td>20</td><td>= Samyang AF 35mm F1.4 P FE</td>
</tr><tr><td>21</td><td>= Samyang AF 14-24mm F2.8</td>
</tr><tr><td>44</td><td>= Metabones Canon EF Smart Adapter</td>
</tr><tr><td>78</td><td>= Metabones Canon EF Smart Adapter Mark III or Other Adapter</td>
</tr><tr><td>184</td><td>= Metabones Canon EF Speed Booster Ultra</td>
</tr><tr><td>234</td><td>= Metabones Canon EF Smart Adapter Mark IV</td>
</tr><tr><td>239</td><td>= Metabones Canon EF Speed Booster</td>
</tr><tr><td>24593</td><td>= LA-EA4r MonsterAdapter</td>
</tr><tr><td>32784</td><td>= Sony E 16mm F2.8</td>
</tr><tr><td>32785</td><td>= Sony E 18-55mm F3.5-5.6 OSS</td>
</tr><tr><td>32786</td><td>= Sony E 55-210mm F4.5-6.3 OSS</td>
</tr><tr><td>32787</td><td>= Sony E 18-200mm F3.5-6.3 OSS</td>
</tr><tr><td>32788</td><td>= Sony E 30mm F3.5 Macro</td>
</tr><tr><td>32789</td><td>= Sony E 24mm F1.8 ZA or Samyang AF 50mm F1.4</td>
</tr><tr><td>32789</td><td>= Samyang AF 50mm F1.4</td>
</tr><tr><td>32790</td><td>= Sony E 50mm F1.8 OSS or Samyang AF 14mm F2.8</td>
</tr><tr><td>32790</td><td>= Samyang AF 14mm F2.8</td>
</tr><tr><td>32791</td><td>= Sony E 16-70mm F4 ZA OSS</td>
</tr><tr><td>32792</td><td>= Sony E 10-18mm F4 OSS</td>
</tr><tr><td>32793</td><td>= Sony E PZ 16-50mm F3.5-5.6 OSS</td>
</tr><tr><td>32794</td><td>= Sony FE 35mm F2.8 ZA or Samyang Lens</td>
</tr><tr><td>32794</td><td>= Samyang AF 24mm F2.8</td>
</tr><tr><td>32794</td><td>= Samyang AF 35mm F2.8</td>
</tr><tr><td>32795</td><td>= Sony FE 24-70mm F4 ZA OSS</td>
</tr><tr><td>32796</td><td>= Sony FE 85mm F1.8 or Viltrox PFU RBMH 85mm F1.8</td>
</tr><tr><td>32796</td><td>= Viltrox PFU RBMH 85mm F1.8</td>
</tr><tr><td>32797</td><td>= Sony E 18-200mm F3.5-6.3 OSS LE</td>
</tr><tr><td>32798</td><td>= Sony E 20mm F2.8</td>
</tr><tr><td>32799</td><td>= Sony E 35mm F1.8 OSS</td>
</tr><tr><td>32800</td><td>= Sony E PZ 18-105mm F4 G OSS</td>
</tr><tr><td>32801</td><td>= Sony FE 12-24mm F4 G</td>
</tr><tr><td>32802</td><td>= Sony FE 90mm F2.8 Macro G OSS</td>
</tr><tr><td>32803</td><td>= Sony E 18-50mm F4-5.6</td>
</tr><tr><td>32804</td><td>= Sony FE 24mm F1.4 GM</td>
</tr><tr><td>32805</td><td>= Sony FE 24-105mm F4 G OSS</td>
</tr><tr><td>32807</td><td>= Sony E PZ 18-200mm F3.5-6.3 OSS</td>
</tr><tr><td>32808</td><td>= Sony FE 55mm F1.8 ZA</td>
</tr><tr><td>32810</td><td>= Sony FE 70-200mm F4 G OSS</td>
</tr><tr><td>32811</td><td>= Sony FE 16-35mm F4 ZA OSS</td>
</tr><tr><td>32812</td><td>= Sony FE 50mm F2.8 Macro</td>
</tr><tr><td>32813</td><td>= Sony FE 28-70mm F3.5-5.6 OSS</td>
</tr><tr><td>32814</td><td>= Sony FE 35mm F1.4 ZA</td>
</tr><tr><td>32815</td><td>= Sony FE 24-240mm F3.5-6.3 OSS</td>
</tr><tr><td>32816</td><td>= Sony FE 28mm F2</td>
</tr><tr><td>32817</td><td>= Sony FE PZ 28-135mm F4 G OSS</td>
</tr><tr><td>32819</td><td>= Sony FE 100mm F2.8 STF GM OSS</td>
</tr><tr><td>32820</td><td>= Sony E PZ 18-110mm F4 G OSS</td>
</tr><tr><td>32821</td><td>= Sony FE 24-70mm F2.8 GM</td>
</tr><tr><td>32822</td><td>= Sony FE 50mm F1.4 ZA</td>
</tr><tr><td>32823</td><td>= Sony FE 85mm F1.4 GM or Samyang AF 85mm F1.4</td>
</tr><tr><td>32823</td><td>= Samyang AF 85mm F1.4</td>
</tr><tr><td>32824</td><td>= Sony FE 50mm F1.8</td>
</tr><tr><td>32826</td><td>= Sony FE 21mm F2.8 (SEL28F20 + SEL075UWC)</td>
</tr><tr><td>32827</td><td>= Sony FE 16mm F3.5 Fisheye (SEL28F20 + SEL057FEC)</td>
</tr><tr><td>32828</td><td>= Sony FE 70-300mm F4.5-5.6 G OSS</td>
</tr><tr><td>32829</td><td>= Sony FE 100-400mm F4.5-5.6 GM OSS</td>
</tr><tr><td>32830</td><td>= Sony FE 70-200mm F2.8 GM OSS</td>
</tr><tr><td>32831</td><td>= Sony FE 16-35mm F2.8 GM</td>
</tr><tr><td>32848</td><td>= Sony FE 400mm F2.8 GM OSS</td>
</tr><tr><td>32849</td><td>= Sony E 18-135mm F3.5-5.6 OSS</td>
</tr><tr><td>32850</td><td>= Sony FE 135mm F1.8 GM</td>
</tr><tr><td>32851</td><td>= Sony FE 200-600mm F5.6-6.3 G OSS</td>
</tr><tr><td>32852</td><td>= Sony FE 600mm F4 GM OSS</td>
</tr><tr><td>32853</td><td>= Sony E 16-55mm F2.8 G</td>
</tr><tr><td>32854</td><td>= Sony E 70-350mm F4.5-6.3 G OSS</td>
</tr><tr><td>32855</td><td>= Sony FE C 16-35mm T3.1 G</td>
</tr><tr><td>32858</td><td>= Sony FE 35mm F1.8</td>
</tr><tr><td>32859</td><td>= Sony FE 20mm F1.8 G</td>
</tr><tr><td>32860</td><td>= Sony FE 12-24mm F2.8 GM</td>
</tr><tr><td>32862</td><td>= Sony FE 50mm F1.2 GM</td>
</tr><tr><td>32863</td><td>= Sony FE 14mm F1.8 GM</td>
</tr><tr><td>32864</td><td>= Sony FE 28-60mm F4-5.6</td>
</tr><tr><td>32865</td><td>= Sony FE 35mm F1.4 GM</td>
</tr><tr><td>32866</td><td>= Sony FE 24mm F2.8 G</td>
</tr><tr><td>32867</td><td>= Sony FE 40mm F2.5 G</td>
</tr><tr><td>32868</td><td>= Sony FE 50mm F2.5 G</td>
</tr><tr><td>32871</td><td>= Sony FE PZ 16-35mm F4 G</td>
</tr><tr><td>32873</td><td>= Sony E PZ 10-20mm F4 G</td>
</tr><tr><td>32874</td><td>= Sony FE 70-200mm F2.8 GM OSS II</td>
</tr><tr><td>32875</td><td>= Sony FE 24-70mm F2.8 GM II</td>
</tr><tr><td>32876</td><td>= Sony E 11mm F1.8</td>
</tr><tr><td>32877</td><td>= Sony E 15mm F1.4 G</td>
</tr><tr><td>32878</td><td>= Sony FE 20-70mm F4 G</td>
</tr><tr><td>32879</td><td>= Sony FE 50mm F1.4 GM</td>
</tr><tr><td>32880</td><td>= Sony FE 16mm F1.8 G</td>
</tr><tr><td>32881</td><td>= Sony FE 24-50mm F2.8 G</td>
</tr><tr><td>32882</td><td>= Sony FE 16-25mm F2.8 G</td>
</tr><tr><td>32884</td><td>= Sony FE 70-200mm F4 Macro G OSS II</td>
</tr><tr><td>32885</td><td>= Sony FE 16-35mm F2.8 GM II</td>
</tr><tr><td>32886</td><td>= Sony FE 300mm F2.8 GM OSS</td>
</tr><tr><td>32887</td><td>= Sony E PZ 16-50mm F3.5-5.6 OSS II</td>
</tr><tr><td>32888</td><td>= Sony FE 85mm F1.4 GM II</td>
</tr><tr><td>32889</td><td>= Sony FE 28-70mm F2 GM</td>
</tr><tr><td>32890</td><td>= Sony FE 400-800mm F6.3-8 G OSS</td>
</tr><tr><td>33072</td><td>= Sony FE 70-200mm F2.8 GM OSS + 1.4X Teleconverter</td>
</tr><tr><td>33073</td><td>= Sony FE 70-200mm F2.8 GM OSS + 2X Teleconverter</td>
</tr><tr><td>33076</td><td>= Sony FE 100mm F2.8 STF GM OSS (macro mode)</td>
</tr><tr><td>33077</td><td>= Sony FE 100-400mm F4.5-5.6 GM OSS + 1.4X Teleconverter</td>
</tr><tr><td>33078</td><td>= Sony FE 100-400mm F4.5-5.6 GM OSS + 2X Teleconverter</td>
</tr><tr><td>33079</td><td>= Sony FE 400mm F2.8 GM OSS + 1.4X Teleconverter</td>
</tr><tr><td>33080</td><td>= Sony FE 400mm F2.8 GM OSS + 2X Teleconverter</td>
</tr><tr><td>33081</td><td>= Sony FE 200-600mm F5.6-6.3 G OSS + 1.4X Teleconverter</td>
</tr><tr><td>33082</td><td>= Sony FE 200-600mm F5.6-6.3 G OSS + 2X Teleconverter</td>
</tr><tr><td>33083</td><td>= Sony FE 600mm F4 GM OSS + 1.4X Teleconverter</td>
</tr><tr><td>33084</td><td>= Sony FE 600mm F4 GM OSS + 2X Teleconverter</td>
</tr><tr><td>33085</td><td>= Sony FE 70-200mm F2.8 GM OSS II + 1.4X Teleconverter</td>
</tr><tr><td>33086</td><td>= Sony FE 70-200mm F2.8 GM OSS II + 2X Teleconverter</td>
</tr><tr><td>33087</td><td>= Sony FE 70-200mm F4 Macro G OSS II + 1.4X Teleconverter</td>
</tr><tr><td>33088</td><td>= Sony FE 70-200mm F4 Macro G OSS II + 2X Teleconverter</td>
</tr><tr><td>33089</td><td>= Sony FE 300mm F2.8 GM OSS + 1.4X Teleconverter</td>
</tr><tr><td>33090</td><td>= Sony FE 300mm F2.8 GM OSS + 2X Teleconverter</td>
</tr><tr><td>33091</td><td>= Sony FE 400-800mm F6.3-8 G OSS + 1.4X Teleconverter</td>
</tr><tr><td>33092</td><td>= Sony FE 400-800mm F6.3-8 G OSS + 2X Teleconverter</td>
</tr><tr><td>49201</td><td>= Zeiss Touit 12mm F2.8</td>
</tr><tr><td>49202</td><td>= Zeiss Touit 32mm F1.8</td>
</tr><tr><td>49203</td><td>= Zeiss Touit 50mm F2.8 Macro</td>
</tr><tr><td>49216</td><td>= Zeiss Batis 25mm F2</td>
</tr><tr><td>49217</td><td>= Zeiss Batis 85mm F1.8</td>
</tr><tr><td>49218</td><td>= Zeiss Batis 18mm F2.8</td>
</tr><tr><td>49219</td><td>= Zeiss Batis 135mm F2.8</td>
</tr><tr><td>49220</td><td>= Zeiss Batis 40mm F2 CF</td>
</tr><tr><td>49232</td><td>= Zeiss Loxia 50mm F2</td>
</tr><tr><td>49233</td><td>= Zeiss Loxia 35mm F2</td>
</tr><tr><td>49234</td><td>= Zeiss Loxia 21mm F2.8</td>
</tr><tr><td>49235</td><td>= Zeiss Loxia 85mm F2.4</td>
</tr><tr><td>49236</td><td>= Zeiss Loxia 25mm F2.4</td>
</tr><tr><td>49456</td><td>= Tamron E 18-200mm F3.5-6.3 Di III VC</td>
</tr><tr><td>49457</td><td>= Tamron 28-75mm F2.8 Di III RXD</td>
</tr><tr><td>49458</td><td>= Tamron 17-28mm F2.8 Di III RXD</td>
</tr><tr><td>49459</td><td>= Tamron 35mm F2.8 Di III OSD M1:2</td>
</tr><tr><td>49460</td><td>= Tamron 24mm F2.8 Di III OSD M1:2</td>
</tr><tr><td>49461</td><td>= Tamron 20mm F2.8 Di III OSD M1:2</td>
</tr><tr><td>49462</td><td>= Tamron 70-180mm F2.8 Di III VXD</td>
</tr><tr><td>49463</td><td>= Tamron 28-200mm F2.8-5.6 Di III RXD</td>
</tr><tr><td>49464</td><td>= Tamron 70-300mm F4.5-6.3 Di III RXD</td>
</tr><tr><td>49465</td><td>= Tamron 17-70mm F2.8 Di III-A VC RXD</td>
</tr><tr><td>49466</td><td>= Tamron 150-500mm F5-6.7 Di III VC VXD</td>
</tr><tr><td>49467</td><td>= Tamron 11-20mm F2.8 Di III-A RXD</td>
</tr><tr><td>49468</td><td>= Tamron 18-300mm F3.5-6.3 Di III-A VC VXD</td>
</tr><tr><td>49469</td><td>= Tamron 35-150mm F2-F2.8 Di III VXD</td>
</tr><tr><td>49470</td><td>= Tamron 28-75mm F2.8 Di III VXD G2</td>
</tr><tr><td>49471</td><td>= Tamron 50-400mm F4.5-6.3 Di III VC VXD</td>
</tr><tr><td>49472</td><td>= Tamron 20-40mm F2.8 Di III VXD</td>
</tr><tr><td>49473</td><td>= Tamron 17-50mm F4 Di III VXD or Tokina or Viltrox lens</td>
</tr><tr><td>49473</td><td>= Tokina atx-m 85mm F1.8 FE</td>
</tr><tr><td>49473</td><td>= Viltrox 23mm F1.4 E</td>
</tr><tr><td>49473</td><td>= Viltrox 56mm F1.4 E</td>
</tr><tr><td>49473</td><td>= Viltrox 85mm F1.8 II FE</td>
</tr><tr><td>49474</td><td>= Tamron 70-180mm F2.8 Di III VXD G2 or Viltrox lens</td>
</tr><tr><td>49474</td><td>= Viltrox 13mm F1.4 E</td>
</tr><tr><td>49474</td><td>= Viltrox 16mm F1.8 FE</td>
</tr><tr><td>49474</td><td>= Viltrox 23mm F1.4 E</td>
</tr><tr><td>49474</td><td>= Viltrox 24mm F1.8 FE</td>
</tr><tr><td>49474</td><td>= Viltrox 28mm F1.8 FE</td>
</tr><tr><td>49474</td><td>= Viltrox 33mm F1.4 E</td>
</tr><tr><td>49474</td><td>= Viltrox 35mm F1.8 FE</td>
</tr><tr><td>49474</td><td>= Viltrox 50mm F1.8 FE</td>
</tr><tr><td>49474</td><td>= Viltrox 75mm F1.2 E</td>
</tr><tr><td>49474</td><td>= Viltrox 20mm F2.8 FE</td>
</tr><tr><td>49475</td><td>= Tamron 50-300mm F4.5-6.3 Di III VC VXD</td>
</tr><tr><td>49476</td><td>= Tamron 28-300mm F4-7.1 Di III VC VXD</td>
</tr><tr><td>49477</td><td>= Tamron 90mm F2.8 Di III Macro VXD</td>
</tr><tr><td>49712</td><td>= Tokina FiRIN 20mm F2 FE AF</td>
</tr><tr><td>49713</td><td>= Tokina FiRIN 100mm F2.8 FE MACRO</td>
</tr><tr><td>49714</td><td>= Tokina atx-m 11-18mm F2.8 E</td>
</tr><tr><td>50480</td><td>= Sigma 30mm F1.4 DC DN | C</td>
</tr><tr><td>50481</td><td>= Sigma 50mm F1.4 DG HSM | A</td>
</tr><tr><td>50482</td><td>= Sigma 18-300mm F3.5-6.3 DC MACRO OS HSM | C + MC-11</td>
</tr><tr><td>50483</td><td>= Sigma 18-35mm F1.8 DC HSM | A + MC-11</td>
</tr><tr><td>50484</td><td>= Sigma 24-35mm F2 DG HSM | A + MC-11</td>
</tr><tr><td>50485</td><td>= Sigma 24mm F1.4 DG HSM | A + MC-11</td>
</tr><tr><td>50486</td><td>= Sigma 150-600mm F5-6.3 DG OS HSM | C + MC-11</td>
</tr><tr><td>50487</td><td>= Sigma 20mm F1.4 DG HSM | A + MC-11</td>
</tr><tr><td>50488</td><td>= Sigma 35mm F1.4 DG HSM | A</td>
</tr><tr><td>50489</td><td>= Sigma 150-600mm F5-6.3 DG OS HSM | S + MC-11</td>
</tr><tr><td>50490</td><td>= Sigma 120-300mm F2.8 DG OS HSM | S + MC-11</td>
</tr><tr><td>50492</td><td>= Sigma 24-105mm F4 DG OS HSM | A + MC-11</td>
</tr><tr><td>50493</td><td>= Sigma 17-70mm F2.8-4 DC MACRO OS HSM | C + MC-11</td>
</tr><tr><td>50495</td><td>= Sigma 50-100mm F1.8 DC HSM | A + MC-11</td>
</tr><tr><td>50499</td><td>= Sigma 85mm F1.4 DG HSM | A</td>
</tr><tr><td>50501</td><td>= Sigma 100-400mm F5-6.3 DG OS HSM | C + MC-11</td>
</tr><tr><td>50503</td><td>= Sigma 16mm F1.4 DC DN | C</td>
</tr><tr><td>50507</td><td>= Sigma 105mm F1.4 DG HSM | A</td>
</tr><tr><td>50508</td><td>= Sigma 56mm F1.4 DC DN | C</td>
</tr><tr><td>50512</td><td>= Sigma 70-200mm F2.8 DG OS HSM | S + MC-11</td>
</tr><tr><td>50513</td><td>= Sigma 70mm F2.8 DG MACRO | A</td>
</tr><tr><td>50514</td><td>= Sigma 45mm F2.8 DG DN | C</td>
</tr><tr><td>50515</td><td>= Sigma 35mm F1.2 DG DN | A</td>
</tr><tr><td>50516</td><td>= Sigma 14-24mm F2.8 DG DN | A</td>
</tr><tr><td>50517</td><td>= Sigma 24-70mm F2.8 DG DN | A</td>
</tr><tr><td>50518</td><td>= Sigma 100-400mm F5-6.3 DG DN OS | C</td>
</tr><tr><td>50521</td><td>= Sigma 85mm F1.4 DG DN | A</td>
</tr><tr><td>50522</td><td>= Sigma 105mm F2.8 DG DN MACRO | A</td>
</tr><tr><td>50523</td><td>= Sigma 65mm F2 DG DN | C</td>
</tr><tr><td>50524</td><td>= Sigma 35mm F2 DG DN | C</td>
</tr><tr><td>50525</td><td>= Sigma 24mm F3.5 DG DN | C</td>
</tr><tr><td>50526</td><td>= Sigma 28-70mm F2.8 DG DN | C</td>
</tr><tr><td>50527</td><td>= Sigma 150-600mm F5-6.3 DG DN OS | S</td>
</tr><tr><td>50528</td><td>= Sigma 35mm F1.4 DG DN | A</td>
</tr><tr><td>50529</td><td>= Sigma 90mm F2.8 DG DN | C</td>
</tr><tr><td>50530</td><td>= Sigma 24mm F2 DG DN | C</td>
</tr><tr><td>50531</td><td>= Sigma 18-50mm F2.8 DC DN | C</td>
</tr><tr><td>50532</td><td>= Sigma 20mm F2 DG DN | C</td>
</tr><tr><td>50533</td><td>= Sigma 16-28mm F2.8 DG DN | C</td>
</tr><tr><td>50534</td><td>= Sigma 20mm F1.4 DG DN | A</td>
</tr><tr><td>50535</td><td>= Sigma 24mm F1.4 DG DN | A</td>
</tr><tr><td>50536</td><td>= Sigma 60-600mm F4.5-6.3 DG DN OS | S</td>
</tr><tr><td>50537</td><td>= Sigma 50mm F2 DG DN | C</td>
</tr><tr><td>50538</td><td>= Sigma 17mm F4 DG DN | C</td>
</tr><tr><td>50539</td><td>= Sigma 50mm F1.4 DG DN | A</td>
</tr><tr><td>50540</td><td>= Sigma 14mm F1.4 DG DN | A</td>
</tr><tr><td>50543</td><td>= Sigma 70-200mm F2.8 DG DN OS | S</td>
</tr><tr><td>50544</td><td>= Sigma 23mm F1.4 DC DN | C</td>
</tr><tr><td>50545</td><td>= Sigma 24-70mm F2.8 DG DN II | A</td>
</tr><tr><td>50546</td><td>= Sigma 500mm F5.6 DG DN OS | S</td>
</tr><tr><td>50547</td><td>= Sigma 10-18mm F2.8 DC DN | C</td>
</tr><tr><td>50548</td><td>= Sigma 15mm F1.4 DG DN DIAGONAL FISHEYE | A</td>
</tr><tr><td>50549</td><td>= Sigma 50mm F1.2 DG DN | A</td>
</tr><tr><td>50550</td><td>= Sigma 28-105mm F2.8 DG DN | A</td>
</tr><tr><td>50551</td><td>= Sigma 28-45mm F1.8 DG DN | A</td>
</tr><tr><td>50553</td><td>= Sigma 300-600mm F4 DG OS | S</td>
</tr><tr><td>50992</td><td>= Voigtlander SUPER WIDE-HELIAR 15mm F4.5 III</td>
</tr><tr><td>50993</td><td>= Voigtlander HELIAR-HYPER WIDE 10mm F5.6</td>
</tr><tr><td>50994</td><td>= Voigtlander ULTRA WIDE-HELIAR 12mm F5.6 III</td>
</tr><tr><td>50995</td><td>= Voigtlander MACRO APO-LANTHAR 65mm F2 Aspherical</td>
</tr><tr><td>50996</td><td>= Voigtlander NOKTON 40mm F1.2 Aspherical</td>
</tr><tr><td>50997</td><td>= Voigtlander NOKTON classic 35mm F1.4</td>
</tr><tr><td>50998</td><td>= Voigtlander MACRO APO-LANTHAR 110mm F2.5</td>
</tr><tr><td>50999</td><td>= Voigtlander COLOR-SKOPAR 21mm F3.5 Aspherical</td>
</tr><tr><td>51000</td><td>= Voigtlander NOKTON 50mm F1.2 Aspherical</td>
</tr><tr><td>51001</td><td>= Voigtlander NOKTON 21mm F1.4 Aspherical</td>
</tr><tr><td>51002</td><td>= Voigtlander APO-LANTHAR 50mm F2 Aspherical</td>
</tr><tr><td>51003</td><td>= Voigtlander NOKTON 35mm F1.2 Aspherical SE</td>
</tr><tr><td>51006</td><td>= Voigtlander APO-LANTHAR 35mm F2 Aspherical</td>
</tr><tr><td>51007</td><td>= Voigtlander NOKTON 50mm F1 Aspherical</td>
</tr><tr><td>51008</td><td>= Voigtlander NOKTON 75mm F1.5 Aspherical</td>
</tr><tr><td>51009</td><td>= Voigtlander NOKTON 28mm F1.5 Aspherical</td>
</tr><tr><td>51072</td><td>= ZEISS Otus ML 50mm F1.4</td>
</tr><tr><td>51073</td><td>= ZEISS Otus ML 85mm F1.4</td>
</tr><tr><td>51504</td><td>= Samyang AF 50mm F1.4</td>
</tr><tr><td>51505</td><td>= Samyang AF 14mm F2.8 or Samyang AF 35mm F2.8</td>
</tr><tr><td>51505</td><td>= Samyang AF 35mm F2.8</td>
</tr><tr><td>51507</td><td>= Samyang AF 35mm F1.4</td>
</tr><tr><td>51508</td><td>= Samyang AF 45mm F1.8</td>
</tr><tr><td>51510</td><td>= Samyang AF 18mm F2.8 or Samyang AF 35mm F1.8</td>
</tr><tr><td>51510</td><td>= Samyang AF 35mm F1.8</td>
</tr><tr><td>51512</td><td>= Samyang AF 75mm F1.8</td>
</tr><tr><td>51513</td><td>= Samyang AF 35mm F1.8</td>
</tr><tr><td>51514</td><td>= Samyang AF 24mm F1.8</td>
</tr><tr><td>51515</td><td>= Samyang AF 12mm F2.0</td>
</tr><tr><td>51516</td><td>= Samyang AF 24-70mm F2.8</td>
</tr><tr><td>51517</td><td>= Samyang AF 50mm F1.4 II</td>
</tr><tr><td>51518</td><td>= Samyang AF 135mm F1.8</td>
</tr><tr><td>61569</td><td>= LAOWA FFII 10mm F2.8 C&amp;D Dreamer</td>
</tr><tr><td>61761</td><td>= Viltrox 28mm F4.5 FE</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='CameraSettingsUnknown'>Sony CameraSettingsUnknown Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr><td colspan=4 class=c><i>[no tags known]</i></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ExtraInfo'>Sony ExtraInfo Tags</a></h2>
<p>Extra hardware information for the A850 and A900.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>BatteryTemperature</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>BatteryUnknown?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>BatteryVoltage?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>ImageStabilization2?</td>
<td class=c>int8u</td>
<td><span class=s>191 = On (191)
  <br>207 = On (207)
  <br>210 = On (210)
  <br>213 = On
  <br>246 = Off</span></td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>BatteryLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>ExtraInfoVersion</td>
<td class=c>int8u[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ExtraInfo2'>Sony ExtraInfo2 Tags</a></h2>
<p>Extra hardware information for the A230/290/330/380/390.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>BatteryLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>ImageStabilization</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>64 = On</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ExtraInfo3'>Sony ExtraInfo3 Tags</a></h2>
<p>Extra hardware information for the A33, A35, A55, A450, A500, A550, A560,
A580 and NEX-3/5/C3/VG10.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>BatteryUnknown?</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>BatteryTemperature</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>BatteryLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>BatteryVoltage1</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>BatteryVoltage2</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='17 = 0x11'>17</td>
<td>ImageStabilization</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>64 = On</span></td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>BatteryState
  <br>ExposureProgram
  <br>ModeDialPosition</td>
<td class=c>int8u<br>int8u<br>int8u</td>
<td><span class=s><span class=n>(BatteryState for SLT models)</span>
  <br>1 = Empty
  <br>2 = Low
  <br>3 = Half full
  <br>4 = Almost full
  <br>5 = Full
  <br><span class=n>(ExposureProgram for the A450, A500 and A550)</span></span><table class=cols><tr>
  <td>241 = Landscape
  <br>243 = Aperture-priority AE
  <br>245 = Portrait
  <br>246 = Auto
  <br>247 = Program AE
  <br>249 = Macro
  <br>252 = Sunset
  <br>253 = Sports
  <br>255 = Manual</td></tr></table>
<span class=s><span class=n>(ModeDialPosition for other DSLR models)</span></span><table class=cols><tr>
  <td>248 = No Flash
  <br>249 = Aperture-priority AE
  <br>250 = SCN
  <br>251 = Shutter speed priority AE
  <br>252 = Auto
  <br>253 = Program AE
  <br>254 = Panorama
  <br>255 = Manual</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>MemoryCardConfiguration
  <br>CameraOrientation</td>
<td class=c>int8u<br>int8u</td>
<td><span class=s>244 = MemoryStick in use, SD card present
  <br>245 = MemoryStick in use, SD slot empty
  <br>252 = SD card in use, MemoryStick present
  <br>254 = SD card in use, MemoryStick slot empty
  <br>[val &gt;&gt; 6 &amp; 0x3]
  <br>0 = Horizontal (normal)
  <br>1 = Rotate 90 CW
  <br>2 = Rotate 270 CW
  <br>3 = Rotate 180</span></td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>CameraOrientation</td>
<td class=c>int8u</td>
<td><span class=s>[val &gt;&gt; 4 &amp; 0x3]
  <br>0 = Horizontal (normal)
  <br>1 = Rotate 90 CW
  <br>2 = Rotate 270 CW
  <br>3 = Rotate 180</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Panorama'>Sony Panorama Tags</a></h2>
<p>Tags found in panorama images from various Sony DSC, NEX, SLT and DSLR
cameras.  The width/height values of these tags are not affected by camera
rotation -- the width is always the longer dimension.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>PanoramaFullWidth</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>PanoramaFullHeight</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>PanoramaDirection</td>
<td class=c>int32u</td>
<td><span class=s>0 = Left or Up
  <br>1 = Right or Down</span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>PanoramaCropLeft</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>PanoramaCropTop</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>PanoramaCropRight</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='7 = 0x7'>7</td>
<td>PanoramaCropBottom</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>PanoramaFrameWidth</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='9 = 0x9'>9</td>
<td>PanoramaFrameHeight</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>PanoramaSourceWidth</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='11 = 0xb'>11</td>
<td>PanoramaSourceHeight</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010a'>Sony Tag2010a Tags</a></h2>
<p>Valid for NEX-5N.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1200 = 0x4b0'>1200</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='4392 = 0x1128'>4392</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr>
<td class=r title='4396 = 0x112c'>4396</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='4404 = 0x1134'>4404</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr>
<td class=r title='4408 = 0x1138'>4408</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4414 = 0x113e'>4414</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4416 = 0x1140'>4416</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4420 = 0x1144'>4420</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='4424 = 0x1148'>4424</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4428 = 0x114c'>4428</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4446 = 0x115e'>4446</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4447 = 0x115f'>4447</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4451 = 0x1163'>4451</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr class=b>
<td class=r title='4464 = 0x1170'>4464</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr>
<td class=r title='4468 = 0x1174'>4468</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr class=b>
<td class=r title='4469 = 0x1175'>4469</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='4476 = 0x117c'>4476</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ReleaseMode2'>Sony ReleaseMode2 Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>ReleaseMode2</th><th>Value</th><th>ReleaseMode2</th></tr>
<tr><td class=r>0</td><td>= Normal</td>
<td class='r b'>16</td><td class=b>= Continuous - 3D Image</td>
</tr><tr><td class=r>1</td><td>= Continuous</td>
<td class='r b'>17</td><td class=b>= Continuous - Burst 2</td>
</tr><tr><td class=r>2</td><td>= Continuous - Exposure Bracketing</td>
<td class='r b'>18</td><td class=b>= Normal - iAuto+</td>
</tr><tr><td class=r>3</td><td>= DRO or White Balance Bracketing</td>
<td class='r b'>19</td><td class=b>= Continuous - Speed/Advance Priority</td>
</tr><tr><td class=r>5</td><td>= Continuous - Burst</td>
<td class='r b'>20</td><td class=b>= Continuous - Multi Frame NR</td>
</tr><tr><td class=r>6</td><td>= Single Frame - Capture During Movie</td>
<td class='r b'>23</td><td class=b>= Single-frame - Exposure Bracketing</td>
</tr><tr><td class=r>7</td><td>= Continuous - Sweep Panorama</td>
<td class='r b'>26</td><td class=b>= Continuous Low</td>
</tr><tr><td class=r>8</td><td>= Continuous - Anti-Motion Blur, Hand-held Twilight</td>
<td class='r b'>27</td><td class=b>= Continuous - High Sensitivity</td>
</tr><tr><td class=r>9</td><td>= Continuous - HDR</td>
<td class='r b'>28</td><td class=b>= Smile Shutter</td>
</tr><tr><td class=r>10</td><td>= Continuous - Background defocus</td>
<td class='r b'>29</td><td class=b>= Continuous - Tele-zoom Advance Priority</td>
</tr><tr><td class=r>13</td><td>= Continuous - 3D Sweep Panorama</td>
<td class='r b'>146</td><td class=b>= Single Frame - Movie Capture</td>
</tr><tr><td class=r>15</td><td>= Continuous - High Resolution Sweep Panorama</td>
<td class='r b'>&nbsp;</td><td class=b>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='PictureEffect2'>Sony PictureEffect2 Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>PictureEffect2</th><th>Value</th><th>PictureEffect2</th><th>Value</th><th>PictureEffect2</th></tr>
<tr><td class=r>0</td><td>= Off</td>
<td class='r b'>5</td><td class=b>= Soft High Key</td>
<td class=r>10</td><td>= Rich-tone Monochrome</td>
</tr><tr><td class=r>1</td><td>= Toy Camera</td>
<td class='r b'>6</td><td class=b>= Partial Color</td>
<td class=r>11</td><td>= Miniature</td>
</tr><tr><td class=r>2</td><td>= Pop Color</td>
<td class='r b'>7</td><td class=b>= High Contrast Monochrome</td>
<td class=r>12</td><td>= Water Color</td>
</tr><tr><td class=r>3</td><td>= Posterization</td>
<td class='r b'>8</td><td class=b>= Soft Focus</td>
<td class=r>13</td><td>= Illustration</td>
</tr><tr><td class=r>4</td><td>= Retro Photo</td>
<td class='r b'>9</td><td class=b>= HDR Painting</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='ExposureProgram3'>Sony ExposureProgram3 Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>ExposureProgram3</th><th>Value</th><th>ExposureProgram3</th><th>Value</th><th>ExposureProgram3</th></tr>
<tr><td class=r>0</td><td>= Program AE</td>
<td class='r b'>11</td><td class=b>= Twilight Portrait</td>
<td class=r>27</td><td>= Gourmet</td>
</tr><tr><td class=r>1</td><td>= Aperture-priority AE</td>
<td class='r b'>12</td><td class=b>= Sunset</td>
<td class=r>28</td><td>= Pet</td>
</tr><tr><td class=r>2</td><td>= Shutter speed priority AE</td>
<td class='r b'>14</td><td class=b>= Action (High speed)</td>
<td class=r>29</td><td>= Macro</td>
</tr><tr><td class=r>3</td><td>= Manual</td>
<td class='r b'>16</td><td class=b>= Sports</td>
<td class=r>30</td><td>= Backlight Correction HDR</td>
</tr><tr><td class=r>4</td><td>= Auto</td>
<td class='r b'>17</td><td class=b>= Handheld Night Shot</td>
<td class=r>33</td><td>= Sweep Panorama</td>
</tr><tr><td class=r>5</td><td>= iAuto</td>
<td class='r b'>18</td><td class=b>= Anti Motion Blur</td>
<td class=r>36</td><td>= Background Defocus</td>
</tr><tr><td class=r>6</td><td>= Superior Auto</td>
<td class='r b'>19</td><td class=b>= High Sensitivity</td>
<td class=r>37</td><td>= Soft Skin</td>
</tr><tr><td class=r>7</td><td>= iAuto+</td>
<td class='r b'>21</td><td class=b>= Beach</td>
<td class=r>42</td><td>= 3D Image</td>
</tr><tr><td class=r>8</td><td>= Portrait</td>
<td class='r b'>22</td><td class=b>= Snow</td>
<td class=r>43</td><td>= Cont. Priority AE</td>
</tr><tr><td class=r>9</td><td>= Landscape</td>
<td class='r b'>23</td><td class=b>= Fireworks</td>
<td class=r>45</td><td>= Document</td>
</tr><tr><td class=r>10</td><td>= Twilight</td>
<td class='r b'>26</td><td class=b>= Underwater</td>
<td class=r>46</td><td>= Party</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='MeterInfo'>Sony MeterInfo Tags</a></h2>
<p>Information possibly related to metering.  Extracted only if the Unknown
option is used.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MeterInfo1Row1</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='108 = 0x6c'>108</td>
<td>MeterInfo1Row2</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='216 = 0xd8'>216</td>
<td>MeterInfo1Row3</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='324 = 0x144'>324</td>
<td>MeterInfo1Row4</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='432 = 0x1b0'>432</td>
<td>MeterInfo1Row5</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='540 = 0x21c'>540</td>
<td>MeterInfo1Row6</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='648 = 0x288'>648</td>
<td>MeterInfo1Row7</td>
<td class=c>int32u[27]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='756 = 0x2f4'>756</td>
<td>MeterInfo2Row1</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='888 = 0x378'>888</td>
<td>MeterInfo2Row2</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1020 = 0x3fc'>1020</td>
<td>MeterInfo2Row3</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1152 = 0x480'>1152</td>
<td>MeterInfo2Row4</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1284 = 0x504'>1284</td>
<td>MeterInfo2Row5</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1416 = 0x588'>1416</td>
<td>MeterInfo2Row6</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1548 = 0x60c'>1548</td>
<td>MeterInfo2Row7</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1680 = 0x690'>1680</td>
<td>MeterInfo2Row8</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1812 = 0x714'>1812</td>
<td>MeterInfo2Row9</td>
<td class=c>int32u[33]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010b'>Sony Tag2010b Tags</a></h2>
<p>Valid for SLT-A65/A77, NEX-7/VG20E.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='438 = 0x1b6'>438</td>
<td>SonyDateTime</td>
<td class=c>undef[7]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='804 = 0x324'>804</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='1204 = 0x4b4'>1204</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr>
<td class=r title='4392 = 0x1128'>4392</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4396 = 0x112c'>4396</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='4404 = 0x1134'>4404</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr class=b>
<td class=r title='4408 = 0x1138'>4408</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='4414 = 0x113e'>4414</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4416 = 0x1140'>4416</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4420 = 0x1144'>4420</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4424 = 0x1148'>4424</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='4428 = 0x114c'>4428</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4450 = 0x1162'>4450</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4451 = 0x1163'>4451</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4455 = 0x1167'>4455</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr>
<td class=r title='4468 = 0x1174'>4468</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr class=b>
<td class=r title='4472 = 0x1178'>4472</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr>
<td class=r title='4473 = 0x1179'>4473</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='4480 = 0x1180'>4480</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4632 = 0x1218'>4632</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6691 = 0x1a23'>6691</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010c'>Sony Tag2010c Tags</a></h2>
<p>Valid for SLT-A37/A57 and NEX-F3.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='512 = 0x200'>512</td>
<td>DigitalZoomRatio</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='528 = 0x210'>528</td>
<td>SonyDateTime</td>
<td class=c>undef[7]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='768 = 0x300'>768</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='1168 = 0x490'>1168</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='4356 = 0x1104'>4356</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr>
<td class=r title='4360 = 0x1108'>4360</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='4368 = 0x1110'>4368</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr>
<td class=r title='4372 = 0x1114'>4372</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4378 = 0x111a'>4378</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4380 = 0x111c'>4380</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4384 = 0x1120'>4384</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='4388 = 0x1124'>4388</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4392 = 0x1128'>4392</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4414 = 0x113e'>4414</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4415 = 0x113f'>4415</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4419 = 0x1143'>4419</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr class=b>
<td class=r title='4432 = 0x1150'>4432</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr>
<td class=r title='4436 = 0x1154'>4436</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr class=b>
<td class=r title='4437 = 0x1155'>4437</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='4444 = 0x115c'>4444</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4596 = 0x11f4'>4596</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010d'>Sony Tag2010d Tags</a></h2>
<p>Valid for DSC-HX10V/HX20V/HX200V/TX66/TX200V/TX300V/WX50/WX100/WX150, but
not valid for panorama images.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='510 = 0x1fe'>510</td>
<td>SonyDateTime</td>
<td class=c>undef[7]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='892 = 0x37c'>892</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='1292 = 0x50c'>1292</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr>
<td class=r title='4480 = 0x1180'>4480</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4484 = 0x1184'>4484</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='4492 = 0x118c'>4492</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr class=b>
<td class=r title='4496 = 0x1190'>4496</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='4502 = 0x1196'>4502</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4504 = 0x1198'>4504</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4508 = 0x119c'>4508</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4512 = 0x11a0'>4512</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='4538 = 0x11ba'>4538</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4539 = 0x11bb'>4539</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4543 = 0x11bf'>4543</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr class=b>
<td class=r title='4560 = 0x11d0'>4560</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr>
<td class=r title='4561 = 0x11d1'>4561</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='4568 = 0x11d8'>4568</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4720 = 0x1270'>4720</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010e'>Sony Tag2010e Tags</a></h2>
<p>Valid for SLT-A58/A99, ILCE-3000/3500, NEX-3N/5R/5T/6/VG30E/VG900,
DSC-RX100, DSC-RX1/RX1R. Also valid for DSC-HX300/HX50V/TX30/WX60/WX200/
WX300, but not for panorama images.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='540 = 0x21c'>540</td>
<td>DigitalZoomRatio</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='556 = 0x22c'>556</td>
<td>SonyDateTime</td>
<td class=c>undef[7]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='808 = 0x328'>808</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='1208 = 0x4b8'>1208</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='4444 = 0x115c'>4444</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr>
<td class=r title='4448 = 0x1160'>4448</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='4456 = 0x1168'>4456</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr>
<td class=r title='4460 = 0x116c'>4460</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4466 = 0x1172'>4466</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4468 = 0x1174'>4468</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4472 = 0x1178'>4472</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='4476 = 0x117c'>4476</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4480 = 0x1180'>4480</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4502 = 0x1196'>4502</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4503 = 0x1197'>4503</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4507 = 0x119b'>4507</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr class=b>
<td class=r title='4520 = 0x11a8'>4520</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr>
<td class=r title='4524 = 0x11ac'>4524</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr class=b>
<td class=r title='4525 = 0x11ad'>4525</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='4532 = 0x11b4'>4532</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4692 = 0x1254'>4692</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4696 = 0x1258'>4696</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4728 = 0x1278'>4728</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4730 = 0x127a'>4730</td>
<td>MinFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4732 = 0x127c'>4732</td>
<td>MaxFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4736 = 0x1280'>4736</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6256 = 0x1870'>6256</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6289 = 0x1891'>6289</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='6290 = 0x1892'>6290</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='6291 = 0x1893'>6291</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr class=b>
<td class=r title='6294 = 0x1896'>6294</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr>
<td class=r title='6296 = 0x1898'>6296</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td class=r title='6297 = 0x1899'>6297</td>
<td>DistortionCorrParamsNumber</td>
<td class=c>int8u</td>
<td><span class=s>11 = 11 (APS-C)
  <br>16 = 16 (Full-frame)</span></td></tr>
<tr>
<td class=r title='6444 = 0x192c'>6444</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
<tr class=b>
<td class=r title='6792 = 0x1a88'>6792</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010f'>Sony Tag2010f Tags</a></h2>
<p>Valid for DSC-RX100M2, DSC-QX10/QX100.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='80 = 0x50'>80</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='480 = 0x1e0'>480</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='4116 = 0x1014'>4116</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr>
<td class=r title='4120 = 0x1018'>4120</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='4128 = 0x1020'>4128</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr>
<td class=r title='4132 = 0x1024'>4132</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4138 = 0x102a'>4138</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4140 = 0x102c'>4140</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4144 = 0x1030'>4144</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='4148 = 0x1034'>4148</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4152 = 0x1038'>4152</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4174 = 0x104e'>4174</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='4175 = 0x104f'>4175</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='4179 = 0x1053'>4179</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr class=b>
<td class=r title='4192 = 0x1060'>4192</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr>
<td class=r title='4196 = 0x1064'>4196</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr class=b>
<td class=r title='4197 = 0x1065'>4197</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='4204 = 0x106c'>4204</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4404 = 0x1134'>4404</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4406 = 0x1136'>4406</td>
<td>MinFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4408 = 0x1138'>4408</td>
<td>MaxFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4412 = 0x113c'>4412</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6444 = 0x192c'>6444</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010g'>Sony Tag2010g Tags</a></h2>
<p>Valid for DSC-HX60V/HX350/HX400V/QX30/RX10/RX100M3/WX220/WX350,
ILCE-7/7R/7S/7M2/5000/5100/6000/QX1, ILCA-68/77M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='80 = 0x50'>80</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='524 = 0x20c'>524</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='528 = 0x210'>528</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='536 = 0x218'>536</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr class=b>
<td class=r title='540 = 0x21c'>540</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='546 = 0x222'>546</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='548 = 0x224'>548</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='552 = 0x228'>552</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='556 = 0x22c'>556</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='560 = 0x230'>560</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='582 = 0x246'>582</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='583 = 0x247'>583</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='587 = 0x24b'>587</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr>
<td class=r title='600 = 0x258'>600</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr class=b>
<td class=r title='604 = 0x25c'>604</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr>
<td class=r title='605 = 0x25d'>605</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='612 = 0x264'>612</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='812 = 0x32c'>812</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='814 = 0x32e'>814</td>
<td>MinFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='816 = 0x330'>816</td>
<td>MaxFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='836 = 0x344'>836</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='904 = 0x388'>904</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='6300 = 0x189c'>6300</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6333 = 0x18bd'>6333</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='6334 = 0x18be'>6334</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='6335 = 0x18bf'>6335</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr class=b>
<td class=r title='6338 = 0x18c2'>6338</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr>
<td class=r title='6340 = 0x18c4'>6340</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td class=r title='6341 = 0x18c5'>6341</td>
<td>DistortionCorrParamsNumber</td>
<td class=c>int8u</td>
<td><span class=s>11 = 11 (APS-C)
  <br>16 = 16 (Full-frame)</span></td></tr>
<tr>
<td class=r title='6488 = 0x1958'>6488</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010h'>Sony Tag2010h Tags</a></h2>
<p>Valid for DSC-HX80/HX90V/RX0/RX1RM2/RX10M2/RX10M3/RX100M4/RX100M5/WX500,
ILCE-6300/6500/7RM2/7SM2, ILCA-99M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='80 = 0x50'>80</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='524 = 0x20c'>524</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='528 = 0x210'>528</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='536 = 0x218'>536</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 5 or 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr class=b>
<td class=r title='540 = 0x21c'>540</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='546 = 0x222'>546</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='548 = 0x224'>548</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='552 = 0x228'>552</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='556 = 0x22c'>556</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='560 = 0x230'>560</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='582 = 0x246'>582</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='583 = 0x247'>583</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='587 = 0x24b'>587</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr>
<td class=r title='600 = 0x258'>600</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr class=b>
<td class=r title='604 = 0x25c'>604</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr>
<td class=r title='605 = 0x25d'>605</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='612 = 0x264'>612</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='812 = 0x32c'>812</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='814 = 0x32e'>814</td>
<td>MinFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='816 = 0x330'>816</td>
<td>MaxFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='838 = 0x346'>838</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='904 = 0x388'>904</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='920 = 0x398'>920</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo'>Sony MeterInfo Tags</a></td></tr>
<tr>
<td class=r title='6348 = 0x18cc'>6348</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6381 = 0x18ed'>6381</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr>
<td class=r title='6382 = 0x18ee'>6382</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr class=b>
<td class=r title='6383 = 0x18ef'>6383</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr>
<td class=r title='6386 = 0x18f2'>6386</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr class=b>
<td class=r title='6388 = 0x18f4'>6388</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td class=r title='6389 = 0x18f5'>6389</td>
<td>DistortionCorrParamsNumber</td>
<td class=c>int8u</td>
<td><span class=s>11 = 11 (APS-C)
  <br>16 = 16 (Full-frame)</span></td></tr>
<tr class=b>
<td class=r title='6444 = 0x192c'>6444</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag2010i'>Sony Tag2010i Tags</a></h2>
<p>Valid for ILCE-6100/6400/6600/7C/7M3/7RM3/7RM4/9/9M2, DSC-RX0M2/RX10M4/RX100M6/
RX100M5A/RX100M7/HX99.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>ReleaseMode2</td>
<td class=c>int32u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='78 = 0x4e'>78</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr>
<td class=r title='516 = 0x204'>516</td>
<td>ReleaseMode3</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Normal
  <br>1 = Continuous
  <br>2 = Bracketing
  <br>4 = Continuous - Burst
  <br>5 = Continuous - Speed/Advance Priority
  <br>6 = Normal - Self-timer
  <br>9 = Single Burst Shooting</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='520 = 0x208'>520</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='528 = 0x210'>528</td>
<td>SelfTimer</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Self-timer 5 or 10 s
  <br>2 = Self-timer 2 s</span></td></tr>
<tr class=b>
<td class=r title='529 = 0x211'>529</td>
<td>FlashMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Autoflash
  <br>1 = Fill-flash
  <br>2 = Flash Off</td><td>&nbsp;&nbsp;</td>
  <td>3 = Slow Sync
  <br>4 = Rear Sync
  <br>6 = Wireless</td></tr></table>
</td></tr>
<tr>
<td class=r title='535 = 0x217'>535</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='537 = 0x219'>537</td>
<td>BrightnessValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='539 = 0x21b'>539</td>
<td>DynamicRangeOptimizer</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = Auto
  <br>3 = Lv1
  <br>4 = Lv2</td><td>&nbsp;&nbsp;</td>
  <td>5 = Lv3
  <br>6 = Lv4
  <br>7 = Lv5
  <br>8 = n/a</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='543 = 0x21f'>543</td>
<td>HDRSetting</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Off
  <br>1 = HDR Auto
  <br>3 = HDR 1 EV
  <br>5 = HDR 2 EV</td><td>&nbsp;&nbsp;</td>
  <td>7 = HDR 3 EV
  <br>9 = HDR 4 EV
  <br>11 = HDR 5 EV
  <br>13 = HDR 6 EV</td></tr></table>
</td></tr>
<tr>
<td class=r title='547 = 0x223'>547</td>
<td>ExposureCompensation</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='567 = 0x237'>567</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='568 = 0x238'>568</td>
<td>PictureProfile</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='572 = 0x23c'>572</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr>
<td class=r title='583 = 0x247'>583</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG</span></td></tr>
<tr class=b>
<td class=r title='587 = 0x24b'>587</td>
<td>MeteringMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Multi-segment
  <br>2 = Center-weighted average
  <br>3 = Spot
  <br>4 = Average
  <br>5 = Highlight</span></td></tr>
<tr>
<td class=r title='588 = 0x24c'>588</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='594 = 0x252'>594</td>
<td>WB_RGBLevels</td>
<td class=c>int16u[3]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='778 = 0x30a'>778</td>
<td>FocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='780 = 0x30c'>780</td>
<td>MinFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='782 = 0x30e'>782</td>
<td>MaxFocalLength</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='800 = 0x320'>800</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='877 = 0x36d'>877</td>
<td>MeterInfo?</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#MeterInfo9'>Sony MeterInfo9 Tags</a></td></tr>
<tr class=b>
<td class=r title='6096 = 0x17d0'>6096</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6129 = 0x17f1'>6129</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='6130 = 0x17f2'>6130</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='6131 = 0x17f3'>6131</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr class=b>
<td class=r title='6134 = 0x17f6'>6134</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr>
<td class=r title='6136 = 0x17f8'>6136</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td class=r title='6137 = 0x17f9'>6137</td>
<td>DistortionCorrParamsNumber</td>
<td class=c>int8u</td>
<td><span class=s>11 = 11 (APS-C)
  <br>16 = 16 (Full-frame)</span></td></tr>
<tr>
<td class=r title='6284 = 0x188c'>6284</td>
<td>AspectRatio</td>
<td class=c>int8u</td>
<td><span class=s>0 = 16:9
  <br>1 = 4:3
  <br>2 = 3:2
  <br>3 = 1:1
  <br>5 = Panorama</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MeterInfo9'>Sony MeterInfo9 Tags</a></h2>
<p>Information possibly related to metering.  Extracted only if the Unknown
option is used.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MeterInfo1Row1</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='90 = 0x5a'>90</td>
<td>MeterInfo1Row2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='180 = 0xb4'>180</td>
<td>MeterInfo1Row3</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='270 = 0x10e'>270</td>
<td>MeterInfo1Row4</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='360 = 0x168'>360</td>
<td>MeterInfo1Row5</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='450 = 0x1c2'>450</td>
<td>MeterInfo1Row6</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='540 = 0x21c'>540</td>
<td>MeterInfo1Row7</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='630 = 0x276'>630</td>
<td>MeterInfo2Row1</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='740 = 0x2e4'>740</td>
<td>MeterInfo2Row2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='850 = 0x352'>850</td>
<td>MeterInfo2Row3</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='960 = 0x3c0'>960</td>
<td>MeterInfo2Row4</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1070 = 0x42e'>1070</td>
<td>MeterInfo2Row5</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1180 = 0x49c'>1180</td>
<td>MeterInfo2Row6</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1290 = 0x50a'>1290</td>
<td>MeterInfo2Row7</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1400 = 0x578'>1400</td>
<td>MeterInfo2Row8</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1510 = 0x5e6'>1510</td>
<td>MeterInfo2Row9</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag202a'>Sony Tag202a Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>FocalPlaneAFPointsUsed</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>FocalPlaneAFPointArea</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>FocalPlaneAFPointLocation1</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>FocalPlaneAFPointLocation2</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='14 = 0xe'>14</td>
<td>FocalPlaneAFPointLocation3</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>FocalPlaneAFPointLocation4</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>FocalPlaneAFPointLocation5</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>FocalPlaneAFPointLocation6</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='30 = 0x1e'>30</td>
<td>FocalPlaneAFPointLocation7</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>FocalPlaneAFPointLocation8</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='38 = 0x26'>38</td>
<td>FocalPlaneAFPointLocation9</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>FocalPlaneAFPointLocation10</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='46 = 0x2e'>46</td>
<td>FocalPlaneAFPointLocation11</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>FocalPlaneAFPointLocation12</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='54 = 0x36'>54</td>
<td>FocalPlaneAFPointLocation13</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='58 = 0x3a'>58</td>
<td>FocalPlaneAFPointLocation14</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='62 = 0x3e'>62</td>
<td>FocalPlaneAFPointLocation15</td>
<td class=c>int16u[2]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HiddenInfo'>Sony HiddenInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>HiddenDataOffset</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>HiddenDataLength</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ShotInfo'>Sony ShotInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>FaceInfoOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>SonyDateTime</td>
<td class=c>string[20]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>SonyImageHeight</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='28 = 0x1c'>28</td>
<td>SonyImageWidth</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='48 = 0x30'>48</td>
<td>FacesDetected</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>FaceInfoLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='52 = 0x34'>52</td>
<td>MetaVersion</td>
<td class=c>string[16]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='72 = 0x48'>72</td>
<td>FaceInfo1</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#FaceInfo1'>Sony FaceInfo1 Tags</a></td></tr>
<tr>
<td class=r title='94 = 0x5e'>94</td>
<td>FaceInfo2</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#FaceInfo2'>Sony FaceInfo2 Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FaceInfo1'>Sony FaceInfo1 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>Face1Position</td>
<td class=c>int16u[4]</td>
<td><span class=s><span class=n>(top, left, height and width of detected face.  Coordinates are relative to
the full-sized unrotated image, with increasing Y downwards)</span></span></td></tr>
<tr class=b>
<td class=r title='32 = 0x20'>32</td>
<td>Face2Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>Face3Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='96 = 0x60'>96</td>
<td>Face4Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='128 = 0x80'>128</td>
<td>Face5Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='160 = 0xa0'>160</td>
<td>Face6Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='192 = 0xc0'>192</td>
<td>Face7Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='224 = 0xe0'>224</td>
<td>Face8Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FaceInfo2'>Sony FaceInfo2 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>Face1Position</td>
<td class=c>int16u[4]</td>
<td><span class=s><span class=n>(top, left, height and width of detected face.  Coordinates are relative to
the full-sized unrotated image, with increasing Y downwards)</span></span></td></tr>
<tr class=b>
<td class=r title='37 = 0x25'>37</td>
<td>Face2Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='74 = 0x4a'>74</td>
<td>Face3Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='111 = 0x6f'>111</td>
<td>Face4Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='148 = 0x94'>148</td>
<td>Face5Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='185 = 0xb9'>185</td>
<td>Face6Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='222 = 0xde'>222</td>
<td>Face7Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='259 = 0x103'>259</td>
<td>Face8Position</td>
<td class=c>int16u[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag900b'>Sony Tag900b Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>FacesDetected</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0 = 0
  <br>33 = 5
  <br>57 = 2</td><td>&nbsp;&nbsp;</td>
  <td>77 = 4
  <br>93 = 3
  <br>98 = 1</td><td>&nbsp;&nbsp;</td>
  <td>115 = 8
  <br>168 = 6
  <br>241 = 7</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='189 = 0xbd'>189</td>
<td>FaceDetection</td>
<td class=c>no</td>
<td><span class=s>0 = Off
  <br>98 = On</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9050a'>Sony Tag9050a Tags</a></h2>
<p>Data for tags 0x9050, 0x94xx and 0x2010 is encrypted by a simple
substitution cipher, but the deciphered values are listed below.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SonyMaxAperture</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>SonyMinAperture</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>Shutter</td>
<td class=c>int16u[3]</td>
<td><span class=s>&#39;0 0 0&#39; = Silent / Electronic (0 0 0)</span></td></tr>
<tr class=b>
<td class=r title='49 = 0x31'>49</td>
<td>FlashStatus</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = No Flash present
  <br>2 = Flash Inhibited
  <br>64 = Built-in Flash present
  <br>65 = Built-in Flash Fired
  <br>66 = Built-in Flash Inhibited
  <br>128 = External Flash present
  <br>129 = External Flash Fired</td></tr></table>
</td></tr>
<tr>
<td class=r title='50 = 0x32'>50</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(total number of image exposures made by the camera, modulo 65536 for some
models)</span></span></td></tr>
<tr class=b>
<td class=r title='58 = 0x3a'>58</td>
<td>SonyExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='60 = 0x3c'>60</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='63 = 0x3f'>63</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='76 = 0x4c'>76</td>
<td>ShutterCount2</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='81 = 0x51'>81</td>
<td>SonyDateTime2</td>
<td class=c>undef[6]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='103 = 0x67'>103</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='124 = 0x7c'>124</td>
<td>InternalSerialNumber</td>
<td class=c title=' ~ = Writable only with -n'>int8u[4]~</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='240 = 0xf0'>240</td>
<td>InternalSerialNumber</td>
<td class=c>int8u[5]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='261 = 0x105'>261</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='262 = 0x106'>262</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='263 = 0x107'>263</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr>
<td class=r title='265 = 0x109'>265</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a>
  <br><span class='n s'>(SLT models, and NEX with A-mount lenses)</span></td></tr>
<tr class=b>
<td class=r title='267 = 0x10b'>267</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td class=r title='276 = 0x114'>276</td>
<td>APS-CSizeCapture</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='277 = 0x115'>277</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='278 = 0x116'>278</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='416 = 0x1a0'>416</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='426 = 0x1aa'>426</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='445 = 0x1bd'>445</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9050b'>Sony Tag9050b Tags</a></h2>
<p>Valid from July 2015 for ILCE-6100/6300/6400/6500/6600/7C/7M3/7RM2/7RM3/7RM4/
7SM2/9/9M2, ILCA-99M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SonyMaxAperture</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>SonyMinAperture</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='38 = 0x26'>38</td>
<td>Shutter</td>
<td class=c>int16u[3]</td>
<td><span class=s>&#39;0 0 0&#39; = Silent / Electronic (0 0 0)</span></td></tr>
<tr class=b>
<td class=r title='57 = 0x39'>57</td>
<td>FlashStatus</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = No Flash present
  <br>2 = Flash Inhibited
  <br>64 = Built-in Flash present
  <br>65 = Built-in Flash Fired
  <br>66 = Built-in Flash Inhibited
  <br>128 = External Flash present
  <br>129 = External Flash Fired</td></tr></table>
</td></tr>
<tr>
<td class=r title='58 = 0x3a'>58</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(total number of image exposures made by the camera)</span></span></td></tr>
<tr class=b>
<td class=r title='70 = 0x46'>70</td>
<td>SonyExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='72 = 0x48'>72</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='75 = 0x4b'>75</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='80 = 0x50'>80</td>
<td>ShutterCount2</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='82 = 0x52'>82</td>
<td>ShutterCount2</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='88 = 0x58'>88</td>
<td>ShutterCount2</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='97 = 0x61'>97</td>
<td>SonyTimeMinSec</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='107 = 0x6b'>107</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='109 = 0x6d'>109</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='115 = 0x73'>115</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='136 = 0x88'>136</td>
<td>InternalSerialNumber</td>
<td class=c title=' ~ = Writable only with -n'>int8u[6]~</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='261 = 0x105'>261</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr class=b>
<td class=r title='262 = 0x106'>262</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr>
<td class=r title='263 = 0x107'>263</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr class=b>
<td class=r title='265 = 0x109'>265</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a>
  <br><span class='n s'>(SLT models, and NEX with A-mount lenses)</span></td></tr>
<tr>
<td class=r title='267 = 0x10b'>267</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td class=r title='276 = 0x114'>276</td>
<td>APS-CSizeCapture</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='278 = 0x116'>278</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='415 = 0x19f'>415</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='459 = 0x1cb'>459</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='461 = 0x1cd'>461</td>
<td>ShutterCount3</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='491 = 0x1eb'>491</td>
<td>APS-CSizeCapture</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='493 = 0x1ed'>493</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='494 = 0x1ee'>494</td>
<td>APS-CSizeCapture</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='496 = 0x1f0'>496</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='538 = 0x21a'>538</td>
<td>APS-CSizeCapture</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='540 = 0x21c'>540</td>
<td>LensSpecFeatures
  <br>APS-CSizeCapture</td>
<td class=c>undef[2]<br>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='542 = 0x21e'>542</td>
<td>LensSpecFeatures</td>
<td class=c>undef[2]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9050c'>Sony Tag9050c Tags</a></h2>
<p>Valid from July 2020 for ILCE-1/7SM3, ILME-FX3.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='38 = 0x26'>38</td>
<td>Shutter</td>
<td class=c>int16u[3]</td>
<td><span class=s>&#39;0 0 0&#39; = Silent / Electronic (0 0 0)</span></td></tr>
<tr class=b>
<td class=r title='57 = 0x39'>57</td>
<td>FlashStatus</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = No Flash present
  <br>2 = Flash Inhibited
  <br>64 = Built-in Flash present
  <br>65 = Built-in Flash Fired
  <br>66 = Built-in Flash Inhibited
  <br>128 = External Flash present
  <br>129 = External Flash Fired</td></tr></table>
</td></tr>
<tr>
<td class=r title='58 = 0x3a'>58</td>
<td>ShutterCount</td>
<td class=c title=' ~ = Writable only with -n'>int32u~</td>
<td><span class=s><span class=n>(total number of image exposures made by the camera)</span></span></td></tr>
<tr class=b>
<td class=r title='70 = 0x46'>70</td>
<td>SonyExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='72 = 0x48'>72</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='75 = 0x4b'>75</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='80 = 0x50'>80</td>
<td>ShutterCount2</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='102 = 0x66'>102</td>
<td>SonyExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='104 = 0x68'>104</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='107 = 0x6b'>107</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='136 = 0x88'>136</td>
<td>InternalSerialNumber</td>
<td class=c title=' ~ = Writable only with -n'>int8u[6]~</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='138 = 0x8a'>138</td>
<td>InternalSerialNumber</td>
<td class=c title=' ~ = Writable only with -n'>int8u[6]~</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9050d'>Sony Tag9050d Tags</a></h2>
<p>Valid for ILCE-6700/7CM2/7CR/ZV-E1. Also for ILCE-1M2 when using mechanical
shutter.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>ShutterCount</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(total number of mechanical shutter actuations)</span></span></td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>SonyExposureTime</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='31 = 0x1f'>31</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr>
<td class=r title='56 = 0x38'>56</td>
<td>InternalSerialNumber</td>
<td class=c title=' ~ = Writable only with -n'>int8u[6]~</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9400a'>Sony Tag9400a Tags</a></h2>
<p>Valid for many DSC, NEX and SLT models</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>DigitalZoom</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>ShotNumberSincePowerUp</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>SequenceLength</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Continuous
  <br>1 = 1 shot
  <br>2 = 2 shots
  <br>3 = 3 shots
  <br>4 = 4 shots
  <br>5 = 5 shots
  <br>6 = 6 shots
  <br>10 = 10 shots
  <br>100 = Continuous - iSweep Panorama
  <br>200 = Continuous - Sweep Panorama</td></tr></table>
</td></tr>
<tr>
<td class=r title='40 = 0x28'>40</td>
<td>CameraOrientation</td>
<td class=c>int8u</td>
<td><span class=s>1 = Horizontal (normal)
  <br>3 = Rotate 180
  <br>6 = Rotate 90 CW
  <br>8 = Rotate 270 CW</span></td></tr>
<tr class=b>
<td class=r title='41 = 0x29'>41</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG
  <br>3 = JPEG + MPO</span></td></tr>
<tr>
<td class=r title='68 = 0x44'>68</td>
<td>SonyImageHeight</td>
<td class=c title=' ~ = Writable only with -n'>int16u~</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='82 = 0x52'>82</td>
<td>ModelReleaseYear</td>
<td class=c title=' ~ = Writable only with -n'>int8u~</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9400b'>Sony Tag9400b Tags</a></h2>
<p>Valid for NEX-3N, ILCE-3000/3500, SLT-A58, DSC-WX60, DSC-WX300, DSC-RX100M2,
DSC-HX50V, DSC-QX10/QX100.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>DigitalZoom</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>ShotNumberSincePowerUp</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>SequenceLength</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Continuous
  <br>1 = 1 shot
  <br>2 = 2 shots
  <br>3 = 3 shots
  <br>4 = 4 shots
  <br>5 = 5 shots
  <br>6 = 6 shots
  <br>10 = 10 shots
  <br>100 = Continuous - iSweep Panorama
  <br>200 = Continuous - Sweep Panorama</td></tr></table>
</td></tr>
<tr>
<td class=r title='36 = 0x24'>36</td>
<td>CameraOrientation</td>
<td class=c>int8u</td>
<td><span class=s>1 = Horizontal (normal)
  <br>3 = Rotate 180
  <br>6 = Rotate 90 CW
  <br>8 = Rotate 270 CW</span></td></tr>
<tr class=b>
<td class=r title='37 = 0x25'>37</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG
  <br>3 = JPEG + MPO</span></td></tr>
<tr>
<td class=r title='63 = 0x3f'>63</td>
<td>SonyImageHeight</td>
<td class=c title=' ~ = Writable only with -n'>int16u~</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='70 = 0x46'>70</td>
<td>ModelReleaseYear</td>
<td class=c title=' ~ = Writable only with -n'>int8u~</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9400c'>Sony Tag9400c Tags</a></h2>
<p>Valid for DSC-HX60V/HX80/HX90V/HX99/HX350/HX400V/QX30/RX0/RX1RM2/RX10/
RX10M2/RX10M3/RX10M4/RX100M3/RX100M4/RX100M5/RX100M5A/RX100M6/RX100M7/WX220/
WX350/WX500, ILCE-1/7/7C/7R/7S/7M2/7M3/7RM2/7RM3/7RM4/7SM2/7SM3/9/9M2/5000/
5100/6000/6100/6300/6400/6500/6600/QX1, ILCA-68/77M2/99M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='9 = 0x9'>9</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>ShotNumberSincePowerUp</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(valid only for some models)</span></span></td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>SequenceLength</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Continuous
  <br>1 = 1 shot
  <br>2 = 2 shots
  <br>3 = 3 shots
  <br>4 = 4 shots
  <br>5 = 5 shots
  <br>6 = 6 shots
  <br>7 = 7 shots
  <br>9 = 9 shots
  <br>10 = 10 shots
  <br>12 = 12 shots
  <br>16 = 16 shots
  <br>100 = Continuous - iSweep Panorama
  <br>200 = Continuous - Sweep Panorama</td></tr></table>
</td></tr>
<tr>
<td class=r title='26 = 0x1a'>26</td>
<td>SequenceFileNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(file number in burst sequence)</span></span></td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>SequenceLength</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Continuous
  <br>1 = 1 file
  <br>2 = 2 files
  <br>3 = 3 files</td><td>&nbsp;&nbsp;</td>
  <td>5 = 5 files
  <br>7 = 7 files
  <br>9 = 9 files
  <br>10 = 10 files</td></tr></table>
</td></tr>
<tr>
<td class=r title='41 = 0x29'>41</td>
<td>CameraOrientation</td>
<td class=c>int8u</td>
<td><span class=s>1 = Horizontal (normal)
  <br>3 = Rotate 180
  <br>6 = Rotate 90 CW
  <br>8 = Rotate 270 CW</span></td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>Quality2</td>
<td class=c>int8u</td>
<td><span class=s>0 = JPEG
  <br>1 = RAW
  <br>2 = RAW + JPEG
  <br>3 = JPEG + MPO
  <br>1 = JPEG
  <br>2 = RAW
  <br>3 = RAW + JPEG
  <br>4 = HEIF
  <br>6 = RAW + HEIF</span></td></tr>
<tr>
<td class=r title='83 = 0x53'>83</td>
<td>ModelReleaseYear</td>
<td class=c title=' ~ = Writable only with -n'>int8u~</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='307 = 0x133'>307</td>
<td>ShutterType</td>
<td class=c>int8u</td>
<td><span class=s>7 = Electronic
  <br>23 = Mechanical</span></td></tr>
<tr>
<td class=r title='313 = 0x139'>313</td>
<td>ShutterType</td>
<td class=c>int8u</td>
<td><span class=s>7 = Electronic
  <br>23 = Mechanical</span></td></tr>
<tr class=b>
<td class=r title='319 = 0x13f'>319</td>
<td>ShutterType</td>
<td class=c>int8u</td>
<td><span class=s>7 = Electronic
  <br>23 = Mechanical</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9401'>Sony Tag9401 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='994 = 0x3e2'>994</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1012 = 0x3f4'>1012</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1102 = 0x44e'>1102</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1176 = 0x498'>1176</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1181 = 0x49d'>1181</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1182 = 0x49e'>1182</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1185 = 0x4a1'>1185</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1186 = 0x4a2'>1186</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1210 = 0x4ba'>1210</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1437 = 0x59d'>1437</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1588 = 0x634'>1588</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1590 = 0x636'>1590</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1612 = 0x64c'>1612</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1619 = 0x653'>1619</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1656 = 0x678'>1656</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1720 = 0x6b8'>1720</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr>
<td class=r title='1758 = 0x6de'>1758</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
<tr class=b>
<td class=r title='1767 = 0x6e7'>1767</td>
<td>ISOInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#ISOInfo'>Sony ISOInfo Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ISOInfo'>Sony ISOInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>ISOSetting</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>ISOAutoMin</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>ISOAutoMax</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9402'>Sony Tag9402 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AmbientTemperature</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><span class=s>[val &amp; 0x7f]
  <br>0 = Manual
  <br>2 = AF-S
  <br>3 = AF-C
  <br>4 = AF-A
  <br>6 = DMF</span></td></tr>
<tr>
<td class=r title='23 = 0x17'>23</td>
<td>AFAreaMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Multi
  <br>1 = Center
  <br>2 = Spot
  <br>3 = Flexible Spot
  <br>10 = Selective (for Miniature effect)
  <br>11 = Zone
  <br>12 = Expanded Flexible Spot
  <br>13 = Custom AF Area
  <br>14 = Tracking
  <br>15 = Face Tracking
  <br>20 = Animal Eye Tracking
  <br>21 = Human Eye Tracking
  <br>255 = Manual</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='45 = 0x2d'>45</td>
<td>FocusPosition2</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9403'>Sony Tag9403 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>CameraTemperature</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9404a'>Sony Tag9404a Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='11 = 0xb'>11</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='13 = 0xd'>13</td>
<td>IntelligentAuto</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='25 = 0x19'>25</td>
<td>LensZoomPosition</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9404b'>Sony Tag9404b Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>IntelligentAuto</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='30 = 0x1e'>30</td>
<td>LensZoomPosition</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='32 = 0x20'>32</td>
<td>FocusPosition2</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9404c'>Sony Tag9404c Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='11 = 0xb'>11</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='13 = 0xd'>13</td>
<td>IntelligentAuto</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9405a'>Sony Tag9405a Tags</a></h2>
<p>Valid for SLT, NEX, ILCE-3000/3500 and several DSC models.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1536 = 0x600'>1536</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td class=r title='1537 = 0x601'>1537</td>
<td>DistortionCorrection</td>
<td class=c>int8u</td>
<td><span class=s>0 = None
  <br>1 = Applied</span></td></tr>
<tr>
<td class=r title='1539 = 0x603'>1539</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='1540 = 0x604'>1540</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='1541 = 0x605'>1541</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a>
  <br><span class='n s'>(E-mount lenses)</span></td></tr>
<tr class=b>
<td class=r title='1544 = 0x608'>1544</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a>
  <br><span class='n s'>(A-mount lenses on SLT and NEX)</span></td></tr>
<tr>
<td class=r title='1610 = 0x64a'>1610</td>
<td>VignettingCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1642 = 0x66a'>1642</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='1738 = 0x6ca'>1738</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9405b'>Sony Tag9405b Tags</a></h2>
<p>Valid for DSC-HX60V/HX80/HX90V/HX99/HX350/HX400V/QX30/RX0/RX10/RX10M2/
RX10M3/RX10M4/RX100M3/RX100M4/RX100M5/RX100M5A/RX100M6/RX100M7/WX220/WX350,
ILCE-7/7M2/7M3/7R/7RM2/7RM3/7RM4/7S/7SM2/9/9M2/5000/5100/6000/6100/6300/
6400/6500/6600/QX1, ILCA-68/77M2/99M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>SonyISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>BaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>StopsAboveBaseISO</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>SonyExposureTime2</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>ExposureTime</td>
<td class=c>rational32u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>SonyFNumber</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>SonyMaxApertureValue</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='36 = 0x24'>36</td>
<td>SequenceImageNumber</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='52 = 0x34'>52</td>
<td>ReleaseMode2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='62 = 0x3e'>62</td>
<td>SonyImageWidthMax</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>SonyImageHeightMax</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='66 = 0x42'>66</td>
<td>HighISONoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = Low
  <br>2 = Normal
  <br>3 = High</span></td></tr>
<tr>
<td class=r title='68 = 0x44'>68</td>
<td>LongExposureNoiseReduction</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='70 = 0x46'>70</td>
<td>PictureEffect2</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#PictureEffect2'>Sony PictureEffect2 Values</a></td></tr>
<tr>
<td class=r title='72 = 0x48'>72</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr class=b>
<td class=r title='74 = 0x4a'>74</td>
<td>CreativeStyle</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Standard
  <br>1 = Vivid
  <br>2 = Neutral
  <br>3 = Portrait
  <br>4 = Landscape
  <br>5 = B&amp;W
  <br>6 = Clear
  <br>7 = Deep
  <br>8 = Light
  <br>9 = Sunset
  <br>10 = Night View/Portrait
  <br>11 = Autumn Leaves
  <br>13 = Sepia
  <br>15 = FL
  <br>16 = VV2
  <br>17 = IN
  <br>18 = SH
  <br>255 = Off</td></tr></table>
</td></tr>
<tr>
<td class=r title='82 = 0x52'>82</td>
<td>Sharpness</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='90 = 0x5a'>90</td>
<td>DistortionCorrParamsPresent</td>
<td class=c>int8u</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td class=r title='91 = 0x5b'>91</td>
<td>DistortionCorrection</td>
<td class=c>int8u</td>
<td><span class=s>0 = None
  <br>1 = Applied</span></td></tr>
<tr class=b>
<td class=r title='93 = 0x5d'>93</td>
<td>LensFormat</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr>
<td class=r title='94 = 0x5e'>94</td>
<td>LensMount</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr class=b>
<td class=r title='96 = 0x60'>96</td>
<td>LensType2</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a>
  <br><span class='n s'>(E-mount lenses)</span></td></tr>
<tr>
<td class=r title='98 = 0x62'>98</td>
<td>LensType</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a>
  <br><span class='n s'>(A-mount lenses on SLT and NEX)</span></td></tr>
<tr class=b>
<td class=r title='100 = 0x64'>100</td>
<td>DistortionCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='834 = 0x342'>834</td>
<td>LensZoomPosition</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='842 = 0x34a'>842</td>
<td>VignettingCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='846 = 0x34e'>846</td>
<td>LensZoomPosition</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='848 = 0x350'>848</td>
<td>VignettingCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='858 = 0x35a'>858</td>
<td>LensZoomPosition</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='860 = 0x35c'>860</td>
<td>VignettingCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='872 = 0x368'>872</td>
<td>VignettingCorrParams</td>
<td class=c>int16s[16]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='892 = 0x37c'>892</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='900 = 0x384'>900</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='924 = 0x39c'>924</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='944 = 0x3b0'>944</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='952 = 0x3b8'>952</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>int16s[32]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9406'>Sony Tag9406 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>BatteryTemperature</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>BatteryLevelGrip1</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='7 = 0x7'>7</td>
<td>BatteryLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>BatteryLevelGrip2</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9406b'>Sony Tag9406b Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>BatteryLevel</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>BatteryLevel2</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag940a'>Sony Tag940a Tags</a></h2>
<p>These tags are currently extracted for SLT models only.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AFPointsSelected</td>
<td class=c>int32u</td>
<td><table class=cols><tr>
  <td>0x0 = (none)
  <br>0x7801 = Center Zone
  <br>0x1821c = Right Zone
  <br>0x3ffff = (all LA-EA4)
  <br>0x605c0 = Left Zone
  <br>0x7fffffff = (all)
  <br>0xffffffff = n/a
  <br>Bit 0 = Center
  <br>Bit 1 = Top
  <br>Bit 2 = Upper-right
  <br>Bit 3 = Right
  <br>Bit 4 = Lower-right
  <br>Bit 5 = Bottom</td><td>&nbsp;&nbsp;</td>
  <td>Bit 6 = Lower-left
  <br>Bit 7 = Left
  <br>Bit 8 = Upper-left
  <br>Bit 9 = Far Right
  <br>Bit 10 = Far Left
  <br>Bit 11 = Upper-middle
  <br>Bit 12 = Near Right
  <br>Bit 13 = Lower-middle
  <br>Bit 14 = Near Left
  <br>Bit 15 = Upper Far Right
  <br>Bit 16 = Lower Far Right
  <br>Bit 17 = Lower Far Left
  <br>Bit 18 = Upper Far Left</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag940c'>Sony Tag940c Tags</a></h2>
<p>E-mount cameras only.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>LensMount2</td>
<td class=c>int8u</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount (1)
  <br>4 = E-mount
  <br>5 = A-mount (5)</span></td></tr>
<tr class=b>
<td class=r title='9 = 0x9'>9</td>
<td>LensType3</td>
<td class=c>int16u</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr>
<td class=r title='11 = 0xb'>11</td>
<td>CameraE-mountVersion</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='13 = 0xd'>13</td>
<td>LensE-mountVersion</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>LensFirmwareVersion</td>
<td class=c title=' ~ = Writable only with -n'>int16u~</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AFInfo'>Sony AFInfo Tags</a></h2>
<p>These tags are currently extracted for SLT models only.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>AFType</td>
<td class=c>int8u</td>
<td><span class=s>1 = 15-point
  <br>2 = 19-point
  <br>3 = 79-point</span></td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>AFStatusActiveSensor</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(ILCA models only)</span>
  <br>0 = Manual
  <br>2 = AF-S
  <br>3 = AF-C
  <br>4 = AF-A
  <br>6 = DMF</span></td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models with 15-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper-left
  <br>1 = Left
  <br>2 = Lower-left
  <br>3 = Far Left
  <br>4 = Top (horizontal)
  <br>5 = Near Right
  <br>6 = Center (horizontal)
  <br>7 = Near Left
  <br>8 = Bottom (horizontal)</td><td>&nbsp;&nbsp;</td>
  <td>9 = Top (vertical)
  <br>10 = Center (vertical)
  <br>11 = Bottom (vertical)
  <br>12 = Far Right
  <br>13 = Upper-right
  <br>14 = Right
  <br>15 = Lower-right
  <br>16 = Upper-middle
  <br>17 = Lower-middle</td></tr></table>
<span class=s><span class=n>(models with 19-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper Far Left
  <br>1 = Upper-left (horizontal)
  <br>2 = Far Left (horizontal)
  <br>3 = Left (horizontal)
  <br>4 = Lower Far Left
  <br>5 = Lower-left (horizontal)
  <br>6 = Upper-left (vertical)
  <br>7 = Left (vertical)
  <br>8 = Lower-left (vertical)
  <br>9 = Far Left (vertical)
  <br>10 = Top (horizontal)
  <br>11 = Near Right
  <br>12 = Center (horizontal)
  <br>13 = Near Left
  <br>14 = Bottom (horizontal)</td><td>&nbsp;&nbsp;</td>
  <td>15 = Top (vertical)
  <br>16 = Upper-middle
  <br>17 = Center (vertical)
  <br>18 = Lower-middle
  <br>19 = Bottom (vertical)
  <br>20 = Upper Far Right
  <br>21 = Upper-right (horizontal)
  <br>22 = Far Right (horizontal)
  <br>23 = Right (horizontal)
  <br>24 = Lower Far Right
  <br>25 = Lower-right (horizontal)
  <br>26 = Far Right (vertical)
  <br>27 = Upper-right (vertical)
  <br>28 = Right (vertical)
  <br>29 = Lower-right (vertical)</td></tr></table>
</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AFPointInFocus</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models with 15-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper-left
  <br>1 = Left
  <br>2 = Lower-left
  <br>3 = Far Left
  <br>4 = Top (horizontal)
  <br>5 = Near Right
  <br>6 = Center (horizontal)
  <br>7 = Near Left
  <br>8 = Bottom (horizontal)
  <br>9 = Top (vertical)</td><td>&nbsp;&nbsp;</td>
  <td>10 = Center (vertical)
  <br>11 = Bottom (vertical)
  <br>12 = Far Right
  <br>13 = Upper-right
  <br>14 = Right
  <br>15 = Lower-right
  <br>16 = Upper-middle
  <br>17 = Lower-middle
  <br>255 = (none)</td></tr></table>
<span class=s><span class=n>(models with 19-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper Far Left
  <br>1 = Upper-left (horizontal)
  <br>2 = Far Left (horizontal)
  <br>3 = Left (horizontal)
  <br>4 = Lower Far Left
  <br>5 = Lower-left (horizontal)
  <br>6 = Upper-left (vertical)
  <br>7 = Left (vertical)
  <br>8 = Lower-left (vertical)
  <br>9 = Far Left (vertical)
  <br>10 = Top (horizontal)
  <br>11 = Near Right
  <br>12 = Center (horizontal)
  <br>13 = Near Left
  <br>14 = Bottom (horizontal)
  <br>15 = Top (vertical)</td><td>&nbsp;&nbsp;</td>
  <td>16 = Upper-middle
  <br>17 = Center (vertical)
  <br>18 = Lower-middle
  <br>19 = Bottom (vertical)
  <br>20 = Upper Far Right
  <br>21 = Upper-right (horizontal)
  <br>22 = Far Right (horizontal)
  <br>23 = Right (horizontal)
  <br>24 = Lower Far Right
  <br>25 = Lower-right (horizontal)
  <br>26 = Far Right (vertical)
  <br>27 = Upper-right (vertical)
  <br>28 = Right (vertical)
  <br>29 = Lower-right (vertical)
  <br>255 = (none)</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='9 = 0x9'>9</td>
<td>AFPointAtShutterRelease</td>
<td class=c>int8u</td>
<td><span class=s><span class=n>(models with 15-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper-left
  <br>1 = Left
  <br>2 = Lower-left
  <br>3 = Far Left
  <br>4 = Top (horizontal)
  <br>5 = Near Right
  <br>6 = Center (horizontal)
  <br>7 = Near Left
  <br>8 = Bottom (horizontal)
  <br>9 = Top (vertical)</td><td>&nbsp;&nbsp;</td>
  <td>10 = Center (vertical)
  <br>11 = Bottom (vertical)
  <br>12 = Far Right
  <br>13 = Upper-right
  <br>14 = Right
  <br>15 = Lower-right
  <br>16 = Upper-middle
  <br>17 = Lower-middle
  <br>30 = (out of focus)</td></tr></table>
<span class=s><span class=n>(models with 19-point AF)</span></span><table class=cols><tr>
  <td>0 = Upper Far Left
  <br>1 = Upper-left (horizontal)
  <br>2 = Far Left (horizontal)
  <br>3 = Left (horizontal)
  <br>4 = Lower Far Left
  <br>5 = Lower-left (horizontal)
  <br>6 = Upper-left (vertical)
  <br>7 = Left (vertical)
  <br>8 = Lower-left (vertical)
  <br>9 = Far Left (vertical)
  <br>10 = Top (horizontal)
  <br>11 = Near Right
  <br>12 = Center (horizontal)
  <br>13 = Near Left
  <br>14 = Bottom (horizontal)
  <br>15 = Top (vertical)</td><td>&nbsp;&nbsp;</td>
  <td>16 = Upper-middle
  <br>17 = Center (vertical)
  <br>18 = Lower-middle
  <br>19 = Bottom (vertical)
  <br>20 = Upper Far Right
  <br>21 = Upper-right (horizontal)
  <br>22 = Far Right (horizontal)
  <br>23 = Right (horizontal)
  <br>24 = Lower Far Right
  <br>25 = Lower-right (horizontal)
  <br>26 = Far Right (vertical)
  <br>27 = Upper-right (vertical)
  <br>28 = Right (vertical)
  <br>29 = Lower-right (vertical)
  <br>30 = (out of focus)</td></tr></table>
</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>AFAreaMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Wide
  <br>1 = Spot
  <br>2 = Local
  <br>3 = Zone</span></td></tr>
<tr class=b>
<td class=r title='11 = 0xb'>11</td>
<td>FocusMode</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = Manual
  <br>2 = AF-S
  <br>3 = AF-C</td><td>&nbsp;&nbsp;</td>
  <td>4 = AF-A
  <br>6 = DMF
  <br>7 = AF-D</td></tr></table>
</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>AFPointsUsed</td>
<td class=c>int8u[10]</td>
<td><table class=cols><tr>
  <td>0x0 = (none)
  <br>Bit 0 = A5
  <br>Bit 1 = A6
  <br>Bit 2 = A7
  <br>Bit 3 = B2
  <br>Bit 4 = B3
  <br>Bit 5 = B4
  <br>Bit 6 = B5
  <br>Bit 7 = B6
  <br>Bit 8 = B7
  <br>Bit 9 = B8
  <br>Bit 10 = B9
  <br>Bit 11 = B10
  <br>Bit 12 = C1
  <br>Bit 13 = C2
  <br>Bit 14 = C3
  <br>Bit 15 = C4
  <br>Bit 16 = C5
  <br>Bit 17 = C6
  <br>Bit 18 = C7
  <br>Bit 19 = C8
  <br>Bit 20 = C9
  <br>Bit 21 = C10
  <br>Bit 22 = C11
  <br>Bit 23 = D1
  <br>Bit 24 = D2
  <br>Bit 25 = D3</td><td>&nbsp;&nbsp;</td>
  <td>Bit 26 = D4
  <br>Bit 27 = D5
  <br>Bit 28 = D6
  <br>Bit 29 = D7
  <br>Bit 30 = D8
  <br>Bit 31 = D9
  <br>Bit 32 = D10
  <br>Bit 33 = D11
  <br>Bit 34 = E1
  <br>Bit 35 = E2
  <br>Bit 36 = E3
  <br>Bit 37 = E4
  <br>Bit 38 = E5
  <br>Bit 39 = E6
  <br>Bit 40 = E7
  <br>Bit 41 = E8
  <br>Bit 42 = E9
  <br>Bit 43 = E10
  <br>Bit 44 = E11
  <br>Bit 45 = F1
  <br>Bit 46 = F2
  <br>Bit 47 = F3
  <br>Bit 48 = F4
  <br>Bit 49 = F5
  <br>Bit 50 = F6
  <br>Bit 51 = F7
  <br>Bit 52 = F8</td><td>&nbsp;&nbsp;</td>
  <td>Bit 53 = F9
  <br>Bit 54 = F10
  <br>Bit 55 = F11
  <br>Bit 56 = G1
  <br>Bit 57 = G2
  <br>Bit 58 = G3
  <br>Bit 59 = G4
  <br>Bit 60 = G5
  <br>Bit 61 = G6
  <br>Bit 62 = G7
  <br>Bit 63 = G8
  <br>Bit 64 = G9
  <br>Bit 65 = G10
  <br>Bit 66 = G11
  <br>Bit 67 = H2
  <br>Bit 68 = H3
  <br>Bit 69 = H4
  <br>Bit 70 = H5
  <br>Bit 71 = H6
  <br>Bit 72 = H7
  <br>Bit 73 = H8
  <br>Bit 74 = H9
  <br>Bit 75 = H10
  <br>Bit 76 = I5
  <br>Bit 77 = I6
  <br>Bit 78 = I7</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='17 = 0x11'>17</td>
<td>AFStatus15
  <br>AFStatus19</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='Sony.html#AFStatus15'>Sony AFStatus15 Tags</a>
  <br>--&gt; <a href='Sony.html#AFStatus19'>Sony AFStatus19 Tags</a></td></tr>
<tr>
<td class=r title='55 = 0x37'>55</td>
<td>AFPoint</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = B4
  <br>1 = C4
  <br>2 = D4
  <br>3 = E4
  <br>4 = F4
  <br>5 = G4
  <br>6 = H4
  <br>7 = B3
  <br>8 = C3
  <br>9 = D3
  <br>10 = E3
  <br>11 = F3
  <br>12 = G3
  <br>13 = H3
  <br>14 = B2
  <br>15 = C2
  <br>16 = D2
  <br>17 = E2
  <br>18 = F2
  <br>19 = G2
  <br>20 = H2
  <br>21 = C1
  <br>22 = D1
  <br>23 = E1
  <br>24 = F1
  <br>25 = G1
  <br>26 = A7 Vertical
  <br>27 = A6 Vertical
  <br>28 = A5 Vertical
  <br>29 = C7 Vertical
  <br>30 = C6 Vertical
  <br>31 = C5 Vertical
  <br>32 = E7 Vertical
  <br>33 = E6 Center Vertical
  <br>34 = E5 Vertical
  <br>35 = G7 Vertical
  <br>36 = G6 Vertical
  <br>37 = G5 Vertical
  <br>38 = I7 Vertical
  <br>39 = I6 Vertical
  <br>40 = I5 Vertical
  <br>41 = A7
  <br>42 = B7
  <br>43 = C7
  <br>44 = D7
  <br>45 = E7
  <br>46 = F7
  <br>47 = G7</td><td>&nbsp;&nbsp;</td>
  <td>48 = H7
  <br>49 = I7
  <br>50 = A6
  <br>51 = B6
  <br>52 = C6
  <br>53 = D6
  <br>54 = E6 Center
  <br>55 = F6
  <br>56 = G6
  <br>57 = H6
  <br>58 = I6
  <br>59 = A5
  <br>60 = B5
  <br>61 = C5
  <br>62 = D5
  <br>63 = E5
  <br>64 = F5
  <br>65 = G5
  <br>66 = H5
  <br>67 = I5
  <br>68 = C11
  <br>69 = D11
  <br>70 = E11
  <br>71 = F11
  <br>72 = G11
  <br>73 = B10
  <br>74 = C10
  <br>75 = D10
  <br>76 = E10
  <br>77 = F10
  <br>78 = G10
  <br>79 = H10
  <br>80 = B9
  <br>81 = C9
  <br>82 = D9
  <br>83 = E9
  <br>84 = F9
  <br>85 = G9
  <br>86 = H9
  <br>87 = B8
  <br>88 = C8
  <br>89 = D8
  <br>90 = E8
  <br>91 = F8
  <br>92 = G8
  <br>93 = H8
  <br>94 = E6 Center F2.8
  <br>255 = (none)</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='56 = 0x38'>56</td>
<td>AFPointInFocus</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = B4
  <br>1 = C4
  <br>2 = D4
  <br>3 = E4
  <br>4 = F4
  <br>5 = G4
  <br>6 = H4
  <br>7 = B3
  <br>8 = C3
  <br>9 = D3
  <br>10 = E3
  <br>11 = F3
  <br>12 = G3
  <br>13 = H3
  <br>14 = B2
  <br>15 = C2
  <br>16 = D2
  <br>17 = E2
  <br>18 = F2
  <br>19 = G2
  <br>20 = H2
  <br>21 = C1
  <br>22 = D1
  <br>23 = E1
  <br>24 = F1
  <br>25 = G1
  <br>26 = A7 Vertical
  <br>27 = A6 Vertical
  <br>28 = A5 Vertical
  <br>29 = C7 Vertical
  <br>30 = C6 Vertical
  <br>31 = C5 Vertical
  <br>32 = E7 Vertical
  <br>33 = E6 Center Vertical
  <br>34 = E5 Vertical
  <br>35 = G7 Vertical
  <br>36 = G6 Vertical
  <br>37 = G5 Vertical
  <br>38 = I7 Vertical
  <br>39 = I6 Vertical
  <br>40 = I5 Vertical
  <br>41 = A7
  <br>42 = B7
  <br>43 = C7
  <br>44 = D7
  <br>45 = E7
  <br>46 = F7
  <br>47 = G7</td><td>&nbsp;&nbsp;</td>
  <td>48 = H7
  <br>49 = I7
  <br>50 = A6
  <br>51 = B6
  <br>52 = C6
  <br>53 = D6
  <br>54 = E6 Center
  <br>55 = F6
  <br>56 = G6
  <br>57 = H6
  <br>58 = I6
  <br>59 = A5
  <br>60 = B5
  <br>61 = C5
  <br>62 = D5
  <br>63 = E5
  <br>64 = F5
  <br>65 = G5
  <br>66 = H5
  <br>67 = I5
  <br>68 = C11
  <br>69 = D11
  <br>70 = E11
  <br>71 = F11
  <br>72 = G11
  <br>73 = B10
  <br>74 = C10
  <br>75 = D10
  <br>76 = E10
  <br>77 = F10
  <br>78 = G10
  <br>79 = H10
  <br>80 = B9
  <br>81 = C9
  <br>82 = D9
  <br>83 = E9
  <br>84 = F9
  <br>85 = G9
  <br>86 = H9
  <br>87 = B8
  <br>88 = C8
  <br>89 = D8
  <br>90 = E8
  <br>91 = F8
  <br>92 = G8
  <br>93 = H8
  <br>94 = E6 Center F2.8
  <br>255 = (none)</td></tr></table>
</td></tr>
<tr>
<td class=r title='57 = 0x39'>57</td>
<td>AFPointAtShutterRelease</td>
<td class=c>int8u</td>
<td><table class=cols><tr>
  <td>0 = B4
  <br>1 = C4
  <br>2 = D4
  <br>3 = E4
  <br>4 = F4
  <br>5 = G4
  <br>6 = H4
  <br>7 = B3
  <br>8 = C3
  <br>9 = D3
  <br>10 = E3
  <br>11 = F3
  <br>12 = G3
  <br>13 = H3
  <br>14 = B2
  <br>15 = C2
  <br>16 = D2
  <br>17 = E2
  <br>18 = F2
  <br>19 = G2
  <br>20 = H2
  <br>21 = C1
  <br>22 = D1
  <br>23 = E1
  <br>24 = F1
  <br>25 = G1
  <br>26 = A7 Vertical
  <br>27 = A6 Vertical
  <br>28 = A5 Vertical
  <br>29 = C7 Vertical
  <br>30 = C6 Vertical
  <br>31 = C5 Vertical
  <br>32 = E7 Vertical
  <br>33 = E6 Center Vertical
  <br>34 = E5 Vertical
  <br>35 = G7 Vertical
  <br>36 = G6 Vertical
  <br>37 = G5 Vertical
  <br>38 = I7 Vertical
  <br>39 = I6 Vertical
  <br>40 = I5 Vertical
  <br>41 = A7
  <br>42 = B7
  <br>43 = C7
  <br>44 = D7
  <br>45 = E7
  <br>46 = F7
  <br>47 = G7</td><td>&nbsp;&nbsp;</td>
  <td>48 = H7
  <br>49 = I7
  <br>50 = A6
  <br>51 = B6
  <br>52 = C6
  <br>53 = D6
  <br>54 = E6 Center
  <br>55 = F6
  <br>56 = G6
  <br>57 = H6
  <br>58 = I6
  <br>59 = A5
  <br>60 = B5
  <br>61 = C5
  <br>62 = D5
  <br>63 = E5
  <br>64 = F5
  <br>65 = G5
  <br>66 = H5
  <br>67 = I5
  <br>68 = C11
  <br>69 = D11
  <br>70 = E11
  <br>71 = F11
  <br>72 = G11
  <br>73 = B10
  <br>74 = C10
  <br>75 = D10
  <br>76 = E10
  <br>77 = F10
  <br>78 = G10
  <br>79 = H10
  <br>80 = B9
  <br>81 = C9
  <br>82 = D9
  <br>83 = E9
  <br>84 = F9
  <br>85 = G9
  <br>86 = H9
  <br>87 = B8
  <br>88 = C8
  <br>89 = D8
  <br>90 = E8
  <br>91 = F8
  <br>92 = G8
  <br>93 = H8
  <br>94 = E6 Center F2.8
  <br>95 = (none)</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='58 = 0x3a'>58</td>
<td>AFAreaMode</td>
<td class=c>int8u</td>
<td><span class=s>0 = Wide
  <br>1 = Center
  <br>2 = Flexible Spot
  <br>3 = Zone
  <br>4 = Expanded Flexible Spot</span></td></tr>
<tr>
<td class=r title='59 = 0x3b'>59</td>
<td>AFStatusActiveSensor</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='67 = 0x43'>67</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='80 = 0x50'>80</td>
<td>AFMicroAdj</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='125 = 0x7d'>125</td>
<td>AFStatus79</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#AFStatus79'>Sony AFStatus79 Tags</a></td></tr>
<tr>
<td class=r title='366 = 0x16e'>366</td>
<td>AFPointsUsed</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(SLT models only)</span></span><table class=cols><tr>
  <td>0x0 = (none)
  <br>Bit 0 = Center
  <br>Bit 1 = Top
  <br>Bit 2 = Upper-right
  <br>Bit 3 = Right
  <br>Bit 4 = Lower-right
  <br>Bit 5 = Bottom
  <br>Bit 6 = Lower-left
  <br>Bit 7 = Left
  <br>Bit 8 = Upper-left</td><td>&nbsp;&nbsp;</td>
  <td>Bit 9 = Far Right
  <br>Bit 10 = Far Left
  <br>Bit 11 = Upper-middle
  <br>Bit 12 = Near Right
  <br>Bit 13 = Lower-middle
  <br>Bit 14 = Near Left
  <br>Bit 15 = Upper Far Right
  <br>Bit 16 = Lower Far Right
  <br>Bit 17 = Lower Far Left
  <br>Bit 18 = Upper Far Left</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='381 = 0x17d'>381</td>
<td>AFMicroAdj</td>
<td class=c>int8s</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='382 = 0x17e'>382</td>
<td>ExposureProgram</td>
<td class=c>int8u</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AFStatus19'>Sony AFStatus19 Tags</a></h2>
<p>AF Status information for models with 19-point AF.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>AFStatusUpperFarLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>AFStatusUpper-leftHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AFStatusFarLeftHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AFStatusLeftHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AFStatusLowerFarLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>AFStatusLower-leftHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>AFStatusUpper-leftVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>AFStatusLeftVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>AFStatusLower-leftVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>AFStatusFarLeftVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>AFStatusTopHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>AFStatusNearRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>AFStatusCenterHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>AFStatusNearLeft</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>AFStatusBottomHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>AFStatusTopVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>AFStatusUpper-middle</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>AFStatusCenterVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='36 = 0x24'>36</td>
<td>AFStatusLower-middle</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='38 = 0x26'>38</td>
<td>AFStatusBottomVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='40 = 0x28'>40</td>
<td>AFStatusUpperFarRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>AFStatusUpper-rightHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='44 = 0x2c'>44</td>
<td>AFStatusFarRightHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='46 = 0x2e'>46</td>
<td>AFStatusRightHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='48 = 0x30'>48</td>
<td>AFStatusLowerFarRight</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>AFStatusLower-rightHorizontal</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='52 = 0x34'>52</td>
<td>AFStatusFarRightVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='54 = 0x36'>54</td>
<td>AFStatusUpper-rightVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='56 = 0x38'>56</td>
<td>AFStatusRightVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='58 = 0x3a'>58</td>
<td>AFStatusLower-rightVertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AFStatus79'>Sony AFStatus79 Tags</a></h2>
<p>AF Status information for models with 79-point AF.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>AFStatus_00_B4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>AFStatus_01_C4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AFStatus_02_D4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AFStatus_03_E4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AFStatus_04_F4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>AFStatus_05_G4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>AFStatus_06_H4</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>AFStatus_07_B3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>AFStatus_08_C3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>AFStatus_09_D3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>AFStatus_10_E3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>AFStatus_11_F3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>AFStatus_12_G3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>AFStatus_13_H3</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>AFStatus_14_B2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='30 = 0x1e'>30</td>
<td>AFStatus_15_C2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>AFStatus_16_D2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='34 = 0x22'>34</td>
<td>AFStatus_17_E2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='36 = 0x24'>36</td>
<td>AFStatus_18_F2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='38 = 0x26'>38</td>
<td>AFStatus_19_G2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='40 = 0x28'>40</td>
<td>AFStatus_20_H2</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='42 = 0x2a'>42</td>
<td>AFStatus_21_C1</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='44 = 0x2c'>44</td>
<td>AFStatus_22_D1</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='46 = 0x2e'>46</td>
<td>AFStatus_23_E1</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='48 = 0x30'>48</td>
<td>AFStatus_24_F1</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='50 = 0x32'>50</td>
<td>AFStatus_25_G1</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='52 = 0x34'>52</td>
<td>AFStatus_26_A7_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='54 = 0x36'>54</td>
<td>AFStatus_27_A6_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='56 = 0x38'>56</td>
<td>AFStatus_28_A5_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='58 = 0x3a'>58</td>
<td>AFStatus_29_C7_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='60 = 0x3c'>60</td>
<td>AFStatus_30_C6_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='62 = 0x3e'>62</td>
<td>AFStatus_31_C5_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>AFStatus_32_E7_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='66 = 0x42'>66</td>
<td>AFStatus_33_E6_Center_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='68 = 0x44'>68</td>
<td>AFStatus_34_E5_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='70 = 0x46'>70</td>
<td>AFStatus_35_G7_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='72 = 0x48'>72</td>
<td>AFStatus_36_G6_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='74 = 0x4a'>74</td>
<td>AFStatus_37_G5_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='76 = 0x4c'>76</td>
<td>AFStatus_38_I7_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='78 = 0x4e'>78</td>
<td>AFStatus_39_I6_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='80 = 0x50'>80</td>
<td>AFStatus_40_I5_Vertical</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='82 = 0x52'>82</td>
<td>AFStatus_41_A7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='84 = 0x54'>84</td>
<td>AFStatus_42_B7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='86 = 0x56'>86</td>
<td>AFStatus_43_C7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='88 = 0x58'>88</td>
<td>AFStatus_44_D7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='90 = 0x5a'>90</td>
<td>AFStatus_45_E7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='92 = 0x5c'>92</td>
<td>AFStatus_46_F7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='94 = 0x5e'>94</td>
<td>AFStatus_47_G7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='96 = 0x60'>96</td>
<td>AFStatus_48_H7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='98 = 0x62'>98</td>
<td>AFStatus_49_I7</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='100 = 0x64'>100</td>
<td>AFStatus_50_A6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='102 = 0x66'>102</td>
<td>AFStatus_51_B6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='104 = 0x68'>104</td>
<td>AFStatus_52_C6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='106 = 0x6a'>106</td>
<td>AFStatus_53_D6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='108 = 0x6c'>108</td>
<td>AFStatus_54_E6_Center</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='110 = 0x6e'>110</td>
<td>AFStatus_55_F6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='112 = 0x70'>112</td>
<td>AFStatus_56_G6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='114 = 0x72'>114</td>
<td>AFStatus_57_H6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='116 = 0x74'>116</td>
<td>AFStatus_58_I6</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='118 = 0x76'>118</td>
<td>AFStatus_59_A5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='120 = 0x78'>120</td>
<td>AFStatus_60_B5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='122 = 0x7a'>122</td>
<td>AFStatus_61_C5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='124 = 0x7c'>124</td>
<td>AFStatus_62_D5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='126 = 0x7e'>126</td>
<td>AFStatus_63_E5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='128 = 0x80'>128</td>
<td>AFStatus_64_F5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='130 = 0x82'>130</td>
<td>AFStatus_65_G5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='132 = 0x84'>132</td>
<td>AFStatus_66_H5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='134 = 0x86'>134</td>
<td>AFStatus_67_I5</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='136 = 0x88'>136</td>
<td>AFStatus_68_C11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='138 = 0x8a'>138</td>
<td>AFStatus_69_D11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='140 = 0x8c'>140</td>
<td>AFStatus_70_E11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='142 = 0x8e'>142</td>
<td>AFStatus_71_F11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='144 = 0x90'>144</td>
<td>AFStatus_72_G11</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='146 = 0x92'>146</td>
<td>AFStatus_73_B10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='148 = 0x94'>148</td>
<td>AFStatus_74_C10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='150 = 0x96'>150</td>
<td>AFStatus_75_D10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='152 = 0x98'>152</td>
<td>AFStatus_76_E10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='154 = 0x9a'>154</td>
<td>AFStatus_77_F10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='156 = 0x9c'>156</td>
<td>AFStatus_78_G10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='158 = 0x9e'>158</td>
<td>AFStatus_79_H10</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='160 = 0xa0'>160</td>
<td>AFStatus_80_B9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='162 = 0xa2'>162</td>
<td>AFStatus_81_C9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='164 = 0xa4'>164</td>
<td>AFStatus_82_D9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='166 = 0xa6'>166</td>
<td>AFStatus_83_E9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='168 = 0xa8'>168</td>
<td>AFStatus_84_F9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='170 = 0xaa'>170</td>
<td>AFStatus_85_G9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='172 = 0xac'>172</td>
<td>AFStatus_86_H9</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='174 = 0xae'>174</td>
<td>AFStatus_87_B8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='176 = 0xb0'>176</td>
<td>AFStatus_88_C8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='178 = 0xb2'>178</td>
<td>AFStatus_89_D8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='180 = 0xb4'>180</td>
<td>AFStatus_90_E8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='182 = 0xb6'>182</td>
<td>AFStatus_91_F8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='184 = 0xb8'>184</td>
<td>AFStatus_92_G8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='186 = 0xba'>186</td>
<td>AFStatus_93_H8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
<tr>
<td class=r title='188 = 0xbc'>188</td>
<td>AFStatus_94_E6_Center_F2-8</td>
<td class=c>int16s</td>
<td><table class=cols><tr>
  <td>-32768 = Out of Focus</td><td>&nbsp;&nbsp;</td>
  <td>0 = In Focus</td></tr></table>
</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag940e'>Sony Tag940e Tags</a></h2>
<p>E-mount models.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='6662 = 0x1a06'>6662</td>
<td>TiffMeteringImageWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6663 = 0x1a07'>6663</td>
<td>TiffMeteringImageHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6664 = 0x1a08'>6664</td>
<td>TiffMeteringImage</td>
<td class=c>no</td>
<td><span class=s><span class=n>(13(?)-bit intensity data from 1320 (1200) metering segments, extracted as a
16-bit TIFF image)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tag9416'>Sony Tag9416 Tags</a></h2>
<p>Valid for the ILCE-1/6700/7CM2/7CR/7M4/7RM5/7SM3/9M3, ILME-FX3/FX30, ZV-E1/E10M2.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>Tag9416_0000</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>SonyISO</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>StopsAboveBaseISO</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>SonyExposureTime2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>ExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>SonyFNumber2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>SonyMaxApertureValue</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='29 = 0x1d'>29</td>
<td>SequenceImageNumber</td>
<td class=c>no</td>
<td><span class=s><span class=n>(number of images captured in burst sequence)</span></span></td></tr>
<tr>
<td class=r title='43 = 0x2b'>43</td>
<td>ReleaseMode2</td>
<td class=c>no</td>
<td>--&gt; <a href='Sony.html#ReleaseMode2'>Sony ReleaseMode2 Values</a></td></tr>
<tr class=b>
<td class=r title='53 = 0x35'>53</td>
<td>ExposureProgram</td>
<td class=c>no</td>
<td>--&gt; <a href='Sony.html#ExposureProgram3'>Sony ExposureProgram3 Values</a></td></tr>
<tr>
<td class=r title='55 = 0x37'>55</td>
<td>CreativeStyle</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0 = Standard
  <br>1 = Vivid
  <br>2 = Neutral
  <br>3 = Portrait
  <br>4 = Landscape
  <br>5 = B&amp;W
  <br>6 = Clear
  <br>7 = Deep
  <br>8 = Light
  <br>9 = Sunset
  <br>10 = Night View/Portrait
  <br>11 = Autumn Leaves
  <br>13 = Sepia
  <br>15 = FL
  <br>16 = VV2
  <br>17 = IN
  <br>18 = SH
  <br>255 = Off</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='72 = 0x48'>72</td>
<td>LensMount</td>
<td class=c>no</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount
  <br>3 = A-mount (3)</span></td></tr>
<tr>
<td class=r title='73 = 0x49'>73</td>
<td>LensFormat</td>
<td class=c>no</td>
<td><span class=s>0 = Unknown
  <br>1 = APS-C
  <br>2 = Full-frame</span></td></tr>
<tr class=b>
<td class=r title='74 = 0x4a'>74</td>
<td>LensMount</td>
<td class=c>no</td>
<td><span class=s>0 = Unknown
  <br>1 = A-mount
  <br>2 = E-mount</span></td></tr>
<tr>
<td class=r title='75 = 0x4b'>75</td>
<td>LensType2</td>
<td class=c>no</td>
<td>--&gt; <a href='Sony.html#LensType2'>Sony LensType2 Values</a></td></tr>
<tr class=b>
<td class=r title='77 = 0x4d'>77</td>
<td>LensType</td>
<td class=c>no</td>
<td>--&gt; <a href='Sony.html#LensType'>Sony LensType Values</a></td></tr>
<tr>
<td class=r title='79 = 0x4f'>79</td>
<td>DistortionCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='112 = 0x70'>112</td>
<td>PictureProfile</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0 = Gamma Still - Standard/Neutral (PP2)
  <br>1 = Gamma Still - Portrait
  <br>3 = Gamma Still - Night View/Portrait
  <br>4 = Gamma Still - B&amp;W/Sepia
  <br>5 = Gamma Still - Clear
  <br>6 = Gamma Still - Deep
  <br>7 = Gamma Still - Light
  <br>8 = Gamma Still - Vivid
  <br>9 = Gamma Still - Real
  <br>10 = Gamma Movie (PP1)
  <br>22 = Gamma ITU709 (PP3 or PP4)
  <br>24 = Gamma Cine1 (PP5)
  <br>25 = Gamma Cine2 (PP6)
  <br>26 = Gamma Cine3
  <br>27 = Gamma Cine4
  <br>28 = Gamma S-Log2 (PP7)
  <br>29 = Gamma ITU709 (800%)
  <br>31 = Gamma S-Log3 (PP8 or PP9)
  <br>33 = Gamma HLG2 (PP10)
  <br>34 = Gamma HLG3
  <br>36 = Off
  <br>37 = FL
  <br>38 = VV2
  <br>39 = IN
  <br>40 = SH</td></tr></table>
</td></tr>
<tr>
<td class=r title='113 = 0x71'>113</td>
<td>FocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='115 = 0x73'>115</td>
<td>MinFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='117 = 0x75'>117</td>
<td>MaxFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2191 = 0x88f'>2191</td>
<td>VignettingCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2193 = 0x891'>2193</td>
<td>VignettingCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2205 = 0x89d'>2205</td>
<td>VignettingCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2229 = 0x8b5'>2229</td>
<td>APS-CSizeCapture</td>
<td class=c>no</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='2231 = 0x8b7'>2231</td>
<td>APS-CSizeCapture</td>
<td class=c>no</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td class=r title='2277 = 0x8e5'>2277</td>
<td>APS-CSizeCapture</td>
<td class=c>no</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td class=r title='2324 = 0x914'>2324</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2326 = 0x916'>2326</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2373 = 0x945'>2373</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='PIC'>Sony PIC Tags</a></h2>
<p>The TextInfo data is extracted as a block to preserve the formatting, and
some of the more interesting information is extracted as separate tags.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'BC:'</td>
<td>Barcode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'BarCode:'</td>
<td>Barcode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Capt:'</td>
<td>SensorTemperature</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'FWVer:'</td>
<td>FirmwareVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'IFD'</td>
<td>PIC_IFD</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html'>Sony Tags</a></td></tr>
<tr class=b>
<td>'Temp:'</td>
<td>CameraTemperature</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Temp:Clbt:'</td>
<td>BoardTemperature</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'TextInfo1'</td>
<td>TextInfo1</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'TextInfo2'</td>
<td>TextInfo2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'VR Enable C:'</td>
<td>VibrationReduction</td>
<td class=c>no</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td>'barcode:'</td>
<td>Barcode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Ericsson'>Sony Ericsson Tags</a></h2>
<p>Maker notes found in images from some Sony Ericsson phones.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0201 = 513'>0x0201</td>
<td>PreviewImageStart</td>
<td class=c title=' * = Protected'>int32u*</td>
<td><span class=s><span class=n>(a small 320x200 preview image)</span></span></td></tr>
<tr class=b>
<td title='0x0202 = 514'>0x0202</td>
<td>PreviewImageLength</td>
<td class=c title=' * = Protected'>int32u*</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x2000 = 8192'>0x2000</td>
<td>MakerNoteVersion</td>
<td class=c>undef[4]</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SRF'>Sony SRF Tags</a></h2>
<p>The maker notes in SRF (Sony Raw Format) images contain 7 IFD&#39;s with family
1 group names SRF0 through SRF6.  SRF0 and SRF1 use the tags in this table,
while SRF2 through SRF5 use the tags in the next table, and SRF6 uses
standard EXIF tags.  All information other than SRF0 is encrypted, but
thanks to Dave Coffin the decryption algorithm is known.  SRF images are
written by the Sony DSC-F828 and DSC-V3.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0000 = 0'>0x0000</td>
<td>SRF2Key</td>
<td class=c>no</td>
<td><span class=s><span class=n>(key to decrypt maker notes from the start of SRF2)</span></span></td></tr>
<tr class=b>
<td title='0x0001 = 1'>0x0001</td>
<td>DataKey</td>
<td class=c>no</td>
<td><span class=s><span class=n>(key to decrypt the rest of the file from the end of the maker notes)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SR2Private'>Sony SR2Private Tags</a></h2>
<p>The SR2 format uses the DNGPrivateData tag to reference a private IFD
containing these tags.  SR2 images are written by the Sony DSC-R1, but
this information is also written to ARW images by other models.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x7200 = 29184'>0x7200</td>
<td>SR2SubIFDOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7201 = 29185'>0x7201</td>
<td>SR2SubIFDLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7221 = 29217'>0x7221</td>
<td>SR2SubIFDKey</td>
<td class=c>no</td>
<td><span class=s><span class=n>(key to decrypt SR2SubIFD)</span></span></td></tr>
<tr class=b>
<td title='0x7240 = 29248'>0x7240</td>
<td>IDC_IFD</td>
<td class=c>-</td>
<td>--&gt; <a href='SonyIDC.html'>SonyIDC Tags</a></td></tr>
<tr>
<td title='0x7241 = 29249'>0x7241</td>
<td>IDC2_IFD</td>
<td class=c>-</td>
<td>--&gt; <a href='SonyIDC.html'>SonyIDC Tags</a></td></tr>
<tr class=b>
<td title='0x7250 = 29264'>0x7250</td>
<td>MRWInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='MinoltaRaw.html'>MinoltaRaw Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SRF2'>Sony SRF2 Tags</a></h2>
<p>These tags are found in the SRF2 through SRF5 IFD&#39;s.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0002 = 2'>0x0002</td>
<td>SRF6Offset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0003 = 3'>0x0003</td>
<td>SRFDataOffset?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>RawDataOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0005 = 5'>0x0005</td>
<td>RawDataLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0043 = 67'>0x0043</td>
<td>MaxApertureAtMaxFocal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0044 = 68'>0x0044</td>
<td>MaxApertureAtMinFocal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0045 = 69'>0x0045</td>
<td>MinFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0046 = 70'>0x0046</td>
<td>MaxFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00c0 = 192'>0x00c0</td>
<td>WBRedDaylight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00c1 = 193'>0x00c1</td>
<td>WBGreenDaylight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00c2 = 194'>0x00c2</td>
<td>WBBlueDaylight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00c3 = 195'>0x00c3</td>
<td>WBRedCloudy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00c4 = 196'>0x00c4</td>
<td>WBGreenCloudy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00c5 = 197'>0x00c5</td>
<td>WBBlueCloudy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00c6 = 198'>0x00c6</td>
<td>WBRedFluorescent</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00c7 = 199'>0x00c7</td>
<td>WBGreenFluorescent</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00c8 = 200'>0x00c8</td>
<td>WBBlueFluorescent</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00c9 = 201'>0x00c9</td>
<td>WBRedTungsten</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00ca = 202'>0x00ca</td>
<td>WBGreenTungsten</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00cb = 203'>0x00cb</td>
<td>WBBlueTungsten</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00cc = 204'>0x00cc</td>
<td>WBRedFlash</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00cd = 205'>0x00cd</td>
<td>WBGreenFlash</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00ce = 206'>0x00ce</td>
<td>WBBlueFlash</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00d0 = 208'>0x00d0</td>
<td>WBRedAsShot</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x00d1 = 209'>0x00d1</td>
<td>WBGreenAsShot</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x00d2 = 210'>0x00d2</td>
<td>WBBlueAsShot</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SR2SubIFD'>Sony SR2SubIFD Tags</a></h2>
<p>Tags in the encrypted SR2SubIFD</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x7300 = 29440'>0x7300</td>
<td>BlackLevel</td>
<td class=c title=' ! = Unsafe'>int16u[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7302 = 29442'>0x7302</td>
<td>WB_GRBGLevelsAuto</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7303 = 29443'>0x7303</td>
<td>WB_GRBGLevels</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7310 = 29456'>0x7310</td>
<td>BlackLevel</td>
<td class=c title=' ! = Unsafe'>int16u[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7312 = 29458'>0x7312</td>
<td>WB_RGGBLevelsAuto</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7313 = 29459'>0x7313</td>
<td>WB_RGGBLevels</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7480 = 29824'>0x7480</td>
<td>WB_RGBLevelsDaylight</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7481 = 29825'>0x7481</td>
<td>WB_RGBLevelsCloudy</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7482 = 29826'>0x7482</td>
<td>WB_RGBLevelsTungsten</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7483 = 29827'>0x7483</td>
<td>WB_RGBLevelsFlash</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7484 = 29828'>0x7484</td>
<td>WB_RGBLevels4500K</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7486 = 29830'>0x7486</td>
<td>WB_RGBLevelsFluorescent</td>
<td class=c title=' ! = Unsafe'>int16s[4]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x74a0 = 29856'>0x74a0</td>
<td>MaxApertureAtMaxFocal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x74a1 = 29857'>0x74a1</td>
<td>MaxApertureAtMinFocal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x74a2 = 29858'>0x74a2</td>
<td>MaxFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x74a3 = 29859'>0x74a3</td>
<td>MinFocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x74c0 = 29888'>0x74c0</td>
<td>SR2DataIFD</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#SR2DataIFD'>Sony SR2DataIFD Tags</a></td></tr>
<tr class=b>
<td title='0x7800 = 30720'>0x7800</td>
<td>ColorMatrix</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7820 = 30752'>0x7820</td>
<td>WB_RGBLevelsDaylight</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7821 = 30753'>0x7821</td>
<td>WB_RGBLevelsCloudy</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7822 = 30754'>0x7822</td>
<td>WB_RGBLevelsTungsten</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7823 = 30755'>0x7823</td>
<td>WB_RGBLevelsFlash</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7824 = 30756'>0x7824</td>
<td>WB_RGBLevels4500K</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7825 = 30757'>0x7825</td>
<td>WB_RGBLevelsShade</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7826 = 30758'>0x7826</td>
<td>WB_RGBLevelsFluorescent</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7827 = 30759'>0x7827</td>
<td>WB_RGBLevelsFluorescentP1</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7828 = 30760'>0x7828</td>
<td>WB_RGBLevelsFluorescentP2</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7829 = 30761'>0x7829</td>
<td>WB_RGBLevelsFluorescentM1</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x782a = 30762'>0x782a</td>
<td>WB_RGBLevels8500K</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x782b = 30763'>0x782b</td>
<td>WB_RGBLevels6000K</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x782c = 30764'>0x782c</td>
<td>WB_RGBLevels3200K</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x782d = 30765'>0x782d</td>
<td>WB_RGBLevels2500K</td>
<td class=c title=' ! = Unsafe'>int16s[3]!</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x787f = 30847'>0x787f</td>
<td>WhiteLevel</td>
<td class=c title=' ! = Unsafe'>int16u[3]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x797d = 31101'>0x797d</td>
<td>VignettingCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x7980 = 31104'>0x7980</td>
<td>ChromaticAberrationCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x7982 = 31106'>0x7982</td>
<td>DistortionCorrParams</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SR2DataIFD'>Sony SR2DataIFD Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x7770 = 30576'>0x7770</td>
<td>ColorMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='PMP'>Sony PMP Tags</a></h2>
<p>These tags are written in the proprietary-format header of PMP images from
the DSC-F1.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>JpgFromRawStart</td>
<td class=c>no</td>
<td><span class=s><span class=n>(OK, not really a RAW file, but this mechanism is used to allow extraction of
the JPEG image from a PMP file)</span></span></td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>JpgFromRawLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>SonyImageWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>SonyImageHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='27 = 0x1b'>27</td>
<td>Orientation</td>
<td class=c>no</td>
<td><span class=s>0 = Horizontal (normal)
  <br>1 = Rotate 270 CW
  <br>2 = Rotate 180
  <br>3 = Rotate 90 CW</span></td></tr>
<tr class=b>
<td class=r title='29 = 0x1d'>29</td>
<td>ImageQuality</td>
<td class=c>no</td>
<td><span class=s>8 = Snap Shot
  <br>23 = Standard
  <br>51 = Fine</span></td></tr>
<tr>
<td class=r title='52 = 0x34'>52</td>
<td>Comment</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='76 = 0x4c'>76</td>
<td>DateTimeOriginal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='84 = 0x54'>84</td>
<td>ModifyDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='102 = 0x66'>102</td>
<td>ExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='106 = 0x6a'>106</td>
<td>FNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='108 = 0x6c'>108</td>
<td>ExposureCompensation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='112 = 0x70'>112</td>
<td>FocalLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='118 = 0x76'>118</td>
<td>Flash</td>
<td class=c>no</td>
<td><span class=s>0 = No Flash
  <br>1 = Fired</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='rtmd'>Sony rtmd Tags</a></h2>
<p>These tags are extracted from the &#39;rtmd&#39; timed metadata of MP4 videos from
some models when the <a href="../ExifTool.html#ExtractEmbedded">ExtractEmbedded</a> option is used.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x8000 = 32768'>0x8000</td>
<td>FNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x8109 = 33033'>0x8109</td>
<td>ExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x810a = 33034'>0x810a</td>
<td>MasterGainAdjustment</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x810b = 33035'>0x810b</td>
<td>ISO</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x810c = 33036'>0x810c</td>
<td>ElectricalExtenderMagnification</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x8500 = 34048'>0x8500</td>
<td>GPSVersionID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x8501 = 34049'>0x8501</td>
<td>GPSLatitudeRef</td>
<td class=c>no</td>
<td><span class=s>&#39;N&#39; = North
  <br>&#39;S&#39; = South</span></td></tr>
<tr class=b>
<td title='0x8502 = 34050'>0x8502</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x8503 = 34051'>0x8503</td>
<td>GPSLongitudeRef</td>
<td class=c>no</td>
<td><span class=s>&#39;E&#39; = East
  <br>&#39;W&#39; = West</span></td></tr>
<tr class=b>
<td title='0x8504 = 34052'>0x8504</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x8507 = 34055'>0x8507</td>
<td>GPSTimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x8509 = 34057'>0x8509</td>
<td>GPSStatus</td>
<td class=c>no</td>
<td><span class=s>&#39;A&#39; = Measurement Active
  <br>&#39;V&#39; = Measurement Void</span></td></tr>
<tr>
<td title='0x850a = 34058'>0x850a</td>
<td>GPSMeasureMode</td>
<td class=c>no</td>
<td><span class=s>2 = 2-Dimensional Measurement
  <br>3 = 3-Dimensional Measurement</span></td></tr>
<tr class=b>
<td title='0x8512 = 34066'>0x8512</td>
<td>GPSMapDatum</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x851d = 34077'>0x851d</td>
<td>GPSDateStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0xe303 = 58115'>0xe303</td>
<td>WhiteBalance</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>1 = Incandescent
  <br>2 = Fluorescent
  <br>4 = Daylight</td><td>&nbsp;&nbsp;</td>
  <td>5 = Cloudy
  <br>6 = Custom
  <br>255 = Preset</td></tr></table>
</td></tr>
<tr>
<td title='0xe304 = 58116'>0xe304</td>
<td>DateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0xe43b = 58427'>0xe43b</td>
<td>PitchRollYaw</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0xe44b = 58443'>0xe44b</td>
<td>Accelerometer</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<hr>
(This document generated automatically by Image::ExifTool::BuildTagLookup)
<br><i>Last revised Mar 24, 2025</i>
<p class=lf><a href='index.html'>&lt;-- ExifTool Tag Names</a></p>
</body>
</html>
