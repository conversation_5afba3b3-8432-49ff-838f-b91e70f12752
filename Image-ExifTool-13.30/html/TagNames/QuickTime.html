<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- (this file generated automatically by Image::ExifTool::BuildTagLookup) -->
<head>
<title>QuickTime Tags</title>
<link rel=stylesheet type='text/css' href='style.css' title='Style'>
</head>
<body>
<h2 class=top>QuickTime Tags</h2>
<p>
The QuickTime format is used for many different types of audio, video and
image files (most notably, MOV/MP4 videos and HEIC/CR3 images).  ExifTool
extracts standard meta information and a variety of audio, video and image
parameters, as well as proprietary information written by many camera
models.  Tags with a question mark after their name are not extracted unless
the <a href="../ExifTool.html#Unknown">Unknown</a> option is set.</p>

<p>When writing video files, <PERSON>if<PERSON><PERSON> creates both QuickTime and XMP tags by
default, but the group may be specified to write one or the other
separately.  If no location is specified, newly created QuickTime tags are
added in the <a href="QuickTime.html#ItemList">ItemList</a>
location if possible, otherwise in
<a href="QuickTime.html#UserData">UserData</a>, and
finally in <a href="QuickTime.html#Keys">Keys</a>,
but this order may be changed by setting the PREFERRED level of the
appropriate table in the config file (see
<a href="../config.html#PREF">example.config</a> in the full distribution for an
example).  Note that some tags with the same name but different ID&#39;s may
exist in the same location, but the family 7 group names may be used to
differentiate these.</p>

<p>ExifTool currently writes
<a href="QuickTime.html#ItemList">ItemList</a> and
<a href="QuickTime.html#UserData">UserData</a> only as
top-level metadata, but select Keys tags are may be written to the audio or
video track.  See the
<a href="QuickTime.html#AudioKeys">AudioKeys</a> and
<a href="QuickTime.html#VideoKeys">VideoKeys</a> tags for
more information.</p>

<p>Alternate language tags may be accessed for
<a href="QuickTime.html#ItemList">ItemList</a> and
<a href="QuickTime.html#Keys">Keys</a> tags by adding
a 3-character ISO 639-2 language code and an optional ISO 3166-1 alpha 2
country code to the tag name (eg. &quot;ItemList:Artist-deu&quot; or
&quot;ItemList::Artist-deu-DE&quot;).  Most
<a href="QuickTime.html#UserData">UserData</a> tags support a
language code, but without a country code.  If no language code is specified
when writing, the default language is written and alternate languages for
the tag are deleted.  Use the &quot;und&quot; language code to write the default
language without deleting alternate languages.  Note that when reading,
&quot;eng&quot; is also treated as the default language if there is no country code.</p>

<p>According to the specification, integer-format QuickTime date/time tags
should be stored as UTC.  Unfortunately, digital cameras often store local
time values instead (presumably because they don&#39;t know the time zone).  For
this reason, by default ExifTool does not assume a time zone for these
values.  However, if the API <a href="../ExifTool.html#QuickTimeUTC">QuickTimeUTC</a> option is set, then ExifTool will
assume these values are properly stored as UTC, and will convert them to
local time when extracting.</p>

<p>When writing string-based date/time tags, the system time zone is added if
the PrintConv option is enabled and no time zone is specified.  This is
because Apple software may display crazy values if the time zone is missing
for some tags.</p>

<p>By default ExifTool will remove null padding from some QuickTime containers
in Canon CR3 files when writing, but the
<a href="../ExifTool.html#QuickTimePad">QuickTimePad</a> option may be used to preserve
the original size by padding with nulls if necessary.</p>

<p>See
<a href="https://developer.apple.com/library/archive/documentation/QuickTime/QTFF/">https://developer.apple.com/library/archive/documentation/QuickTime/QTFF/</a>
for the official QuickTime specification.
</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'GPS '</td>
<td>GPSDataList2?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'IDIT'</td>
<td>DateTimeOriginal</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'PICT'</td>
<td>PreviewPICT</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SEAL'</td>
<td>SEAL</td>
<td class=c>-</td>
<td>--&gt; <a href='XMP.html#SEAL'>XMP SEAL Tags</a></td></tr>
<tr>
<td>'_htc'</td>
<td>HTCInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HTCInfo'>QuickTime HTCInfo Tags</a></td></tr>
<tr class=b>
<td>'ardt'</td>
<td>ARDroneFile</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'cust'</td>
<td>CustomInfo</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'frea'</td>
<td>Kodak_frea</td>
<td class=c>-</td>
<td>--&gt; <a href='Kodak.html#frea'>Kodak frea Tags</a></td></tr>
<tr>
<td>'free'</td>
<td>KodakFree
  <br>Pittasoft
  <br>ThumbnailImage
  <br>Free?</td>
<td class=c>-<br>-<br>no<br>no</td>
<td>--&gt; <a href='Kodak.html#Free'>Kodak Free Tags</a>
  <br>--&gt; <a href='QuickTime.html#Pittasoft'>QuickTime Pittasoft Tags</a></td></tr>
<tr class=b>
<td>'ftyp'</td>
<td>FileType</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#FileType'>QuickTime FileType Tags</a></td></tr>
<tr>
<td>'gdat'</td>
<td>GPSData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr class=b>
<td>'gps0'</td>
<td>GPSTrack</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr>
<td>'gsen'</td>
<td>GSensor</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr class=b>
<td>'inst'</td>
<td>Insta360Info</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr>
<td>'junk'</td>
<td>Junk?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'mdat'</td>
<td>MediaData?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'mdat-offset'</td>
<td>MediaDataOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'mdat-size'</td>
<td>MediaDataSize</td>
<td class=c>no</td>
<td><span class=s><span class=n>(not a real tag ID, this tag represents the size of the &#39;mdat&#39; data in bytes
and is used in the AvgBitrate calculation)</span></span></td></tr>
<tr>
<td>'meco'</td>
<td>OtherMeta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#OtherMeta'>QuickTime OtherMeta Tags</a></td></tr>
<tr class=b>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
<tr>
<td>'moof'</td>
<td>MovieFragment</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MovieFragment'>QuickTime MovieFragment Tags</a></td></tr>
<tr class=b>
<td>'moov'</td>
<td>Movie</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Movie'>QuickTime Movie Tags</a></td></tr>
<tr>
<td>'mpvd'</td>
<td>MotionPhotoVideo</td>
<td class=c>undef</td>
<td><span class=s><span class=n>(MP4-format video saved in Samsung motion-photo HEIC images.)</span></span></td></tr>
<tr class=b>
<td>'nbmt'</td>
<td>NextbaseMeta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr>
<td>'pict'</td>
<td>PreviewPICT</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'pnot'</td>
<td>Preview</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Preview'>QuickTime Preview Tags</a></td></tr>
<tr>
<td>'prrt'</td>
<td>ARDroneTelemetry</td>
<td class=c>no</td>
<td><span class=s><span class=n>(telemetry information for each video frame: status1, status2, time, pitch,
roll, yaw, speed, altitude)</span></span></td></tr>
<tr class=b>
<td>'sefd'</td>
<td>SamsungTrailer</td>
<td class=c>-</td>
<td>--&gt; <a href='Samsung.html#Trailer'>Samsung Trailer Tags</a></td></tr>
<tr>
<td>'skip'</td>
<td>CanonSkip
  <br>PreviewImage
  <br>SkipInfo
  <br>LigoGPSInfo
  <br>Skip?</td>
<td class=c>-<br>no<br>-<br>-<br>no</td>
<td>--&gt; <a href='Canon.html#Skip'>Canon Skip Tags</a>
  <br>--&gt; <a href='QuickTime.html#SkipInfo'>QuickTime SkipInfo Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr class=b>
<td>'thm '</td>
<td>ThumbnailImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'thum'</td>
<td>ThumbnailImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'udat'</td>
<td>GPSLog</td>
<td class=c>no</td>
<td><span class=s><span class=n>(parsed to extract GPS separately when ExtractEmbedded is used)</span></span></td></tr>
<tr>
<td>'udta'</td>
<td>KenwoodData
  <br>LigoJSON
  <br>GKUData
  <br>FLIRData</td>
<td class=c>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='FLIR.html#UserData'>FLIR UserData Tags</a></td></tr>
<tr class=b>
<td>'uuid'</td>
<td>XMP
  <br>UUID-PROF
  <br>UUID-Flip
  <br>UUID-Canon2
  <br>SensorData
  <br>SensorData
  <br>JUMBF
  <br>CBOR
  <br>PreviewImage
  <br>ThumbnailImage
  <br>UUID-Unknown?</td>
<td class=c>-<br>-<br>-<br>-<br>-<br>no<br>-<br>-<br>no<br>no<br>no</td>
<td>--&gt; <a href='XMP.html'>XMP Tags</a>
  <br>--&gt; <a href='QuickTime.html#Profile'>QuickTime Profile Tags</a>
  <br>--&gt; <a href='QuickTime.html#Flip'>QuickTime Flip Tags</a>
  <br>--&gt; <a href='Canon.html#uuid2'>Canon uuid2 Tags</a>
  <br>--&gt; <a href='QuickTime.html#Tags360Fly'>QuickTime Tags360Fly Tags</a>
  <br><span class=n>(raw 360Fly sensor data without ExtractEmbedded option)</span>
  <br>--&gt; <a href='Jpeg2000.html'>Jpeg2000 Tags</a>
  <br>--&gt; <a href='CBOR.html'>CBOR Tags</a></td></tr>
<tr>
<td>'wide'</td>
<td>Wide?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Stream'>QuickTime Stream Tags</a></h2>
<p>The tags below are extracted from timed metadata in QuickTime and other
formats of video files when the ExtractEmbedded option is used.  Although
most of these tags are combined into the single table below, ExifTool
currently reads 107 different types of timed GPS metadata from video files.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'Accelerometer'</td>
<td>Accelerometer</td>
<td class=c>no</td>
<td><span class=s><span class=n>(3-axis acceleration, usually in units of g)</span></span></td></tr>
<tr class=b>
<td>'AccelerometerData'</td>
<td>AccelerometerData</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'AngularVelocity'</td>
<td>AngularVelocity</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CTMD'</td>
<td>CTMD</td>
<td class=c>-</td>
<td>--&gt; <a href='Canon.html#CTMD'>Canon CTMD Tags</a></td></tr>
<tr>
<td>'CameraDateTime'</td>
<td>CameraDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CameraModel'</td>
<td>CameraModel</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Car'</td>
<td>Car</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'DateTimeOriginal'</td>
<td>DateTimeOriginal</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'DateTimeStamp'</td>
<td>DateTimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Distance'</td>
<td>Distance</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ExposureCompensation'</td>
<td>ExposureCompensation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'ExposureTime'</td>
<td>ExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'FNumber'</td>
<td>FNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'FrameNumber'</td>
<td>FrameNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSAltitude'</td>
<td>GPSAltitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPSDOP'</td>
<td>GPSDOP</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSDateTime'</td>
<td>GPSDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPSLatitude'</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSLatitude2'</td>
<td>GPSLatitude2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPSLongitude'</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSLongitude2'</td>
<td>GPSLongitude2</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPSSatellites'</td>
<td>GPSSatellites</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSSpeed'</td>
<td>GPSSpeed</td>
<td class=c>no</td>
<td><span class=s><span class=n>(in km/h unless GPSSpeedRef says otherwise)</span></span></td></tr>
<tr class=b>
<td>'GPSSpeedRef'</td>
<td>GPSSpeedRef</td>
<td class=c>no</td>
<td><span class=s>&#39;K&#39; = km/h
  <br>&#39;M&#39; = mph
  <br>&#39;N&#39; = knots</span></td></tr>
<tr>
<td>'GPSTimeStamp'</td>
<td>GPSTimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPSTrack'</td>
<td>GPSTrack</td>
<td class=c>no</td>
<td><span class=s><span class=n>(relative to true north unless GPSTrackRef says otherwise)</span></span></td></tr>
<tr>
<td>'GPSTrackRef'</td>
<td>GPSTrackRef</td>
<td class=c>no</td>
<td><span class=s>&#39;M&#39; = Magnetic North
  <br>&#39;T&#39; = True North</span></td></tr>
<tr class=b>
<td>'GSensor'</td>
<td>GSensor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'INSV'</td>
<td>INSV</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#INSV_MakerNotes'>QuickTime INSV_MakerNotes Tags</a></td></tr>
<tr class=b>
<td>'ISO'</td>
<td>ISO</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'JPEG'</td>
<td>JpgFromRaw</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'KiloCalories'</td>
<td>KiloCalories</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'MagneticVariation'</td>
<td>MagneticVariation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'RVMI'</td>
<td>RVMI_gReV
  <br>RVMI_sReV</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#RVMI_gReV'>QuickTime RVMI_gReV Tags</a>
  <br>--&gt; <a href='QuickTime.html#RVMI_sReV'>QuickTime RVMI_sReV Tags</a></td></tr>
<tr>
<td>'RawGSensor'</td>
<td>RawGSensor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SampleDateTime'</td>
<td>SampleDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SampleDuration'</td>
<td>SampleDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SampleTime'</td>
<td>SampleTime</td>
<td class=c>no</td>
<td><span class=s><span class=n>(sample decoding time)</span></span></td></tr>
<tr>
<td>'Text'</td>
<td>Text</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'TimeCode'</td>
<td>TimeCode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Unknown00'</td>
<td>Unknown00?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Unknown01'</td>
<td>Unknown01?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Unknown02'</td>
<td>Unknown02?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Unknown03'</td>
<td>Unknown03?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'UserLabel'</td>
<td>UserLabel</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'VerticalSpeed'</td>
<td>VerticalSpeed</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'VideoTimeStamp'</td>
<td>VideoTimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'camm'</td>
<td>camm0
  <br>camm1
  <br>camm2
  <br>camm3
  <br>camm4
  <br>camm5
  <br>camm6
  <br>camm7</td>
<td class=c>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#camm0'>QuickTime camm0 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm1'>QuickTime camm1 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm2'>QuickTime camm2 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm3'>QuickTime camm3 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm4'>QuickTime camm4 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm5'>QuickTime camm5 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm6'>QuickTime camm6 Tags</a>
  <br>--&gt; <a href='QuickTime.html#camm7'>QuickTime camm7 Tags</a></td></tr>
<tr>
<td>'dbgi'</td>
<td>DJIDebug?</td>
<td class=c>-</td>
<td>--&gt; <a href='DJI.html#Protobuf'>DJI Protobuf Tags</a>
  <br><span class='n s'>(extracted only if Unknown option is 2 or greater)</span></td></tr>
<tr class=b>
<td>'djmd'</td>
<td>DJIMetadata</td>
<td class=c>-</td>
<td>--&gt; <a href='DJI.html#Protobuf'>DJI Protobuf Tags</a></td></tr>
<tr>
<td>'fdsc'</td>
<td>fdsc</td>
<td class=c>-</td>
<td>--&gt; <a href='GoPro.html#fdsc'>GoPro fdsc Tags</a></td></tr>
<tr class=b>
<td>'gpmd'</td>
<td>gpmd_Kingslim
  <br>gpmd_Rove
  <br>gpmd_FMAS
  <br>gpmd_Wolfbox
  <br>gpmd_GoPro</td>
<td class=c>-<br>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br>--&gt; <a href='GoPro.html#GPMF'>GoPro GPMF Tags</a></td></tr>
<tr>
<td>'marl'</td>
<td>marl</td>
<td class=c>-</td>
<td>--&gt; <a href='GM.html#marl'>GM marl Tags</a></td></tr>
<tr class=b>
<td>'mebx'</td>
<td>mebx</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Keys'>QuickTime Keys Tags</a></td></tr>
<tr>
<td>'mett'</td>
<td>mett</td>
<td class=c>-</td>
<td>--&gt; <a href='Parrot.html#mett'>Parrot mett Tags</a></td></tr>
<tr class=b>
<td>'rtmd'</td>
<td>rtmd</td>
<td class=c>-</td>
<td>--&gt; <a href='Sony.html#rtmd'>Sony rtmd Tags</a></td></tr>
<tr>
<td>'ssmd'</td>
<td>RoveGPS
  <br>Accelerometer
  <br>PreviewImage</td>
<td class=c>-<br>no<br>no</td>
<td>--&gt; <a href='QuickTime.html#RoveGPS'>QuickTime RoveGPS Tags</a></td></tr>
<tr class=b>
<td>'text'</td>
<td>PreviewInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#PreviewInfo'>QuickTime PreviewInfo Tags</a></td></tr>
<tr>
<td>'tx3g'</td>
<td>tx3g</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#tx3g'>QuickTime tx3g Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='INSV_MakerNotes'>QuickTime INSV_MakerNotes Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x000a = 10'>0x000a</td>
<td>SerialNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0012 = 18'>0x0012</td>
<td>Model</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x001a = 26'>0x001a</td>
<td>Firmware</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x002a = 42'>0x002a</td>
<td>Parameters</td>
<td class=c>no</td>
<td><span class=s><span class=n>(number of lenses, 6-axis orientation of each lens, raw resolution)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='RVMI_gReV'>QuickTime RVMI_gReV Tags</a></h2>
<p>GPS information extracted from the RVMI box of MOV videos.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>GPSSpeed</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>GPSTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='RVMI_sReV'>QuickTime RVMI_sReV Tags</a></h2>
<p>G-sensor information extracted from the RVMI box of MOV videos.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>GSensor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm0'>QuickTime camm0 Tags</a></h2>
<p>The camm0 through camm7 tables define tags extracted from the Google Street
View Camera Motion Metadata of MP4 videos.  See
<a href="https://developers.google.com/streetview/publish/camm-spec">https://developers.google.com/streetview/publish/camm-spec</a> for the
specification.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>AngleAxis</td>
<td class=c>no</td>
<td><span class=s><span class=n>(angle axis orientation in radians in local coordinate system)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm1'>QuickTime camm1 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>PixelExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0008 = 8'>0x0008</td>
<td>RollingShutterSkewTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm2'>QuickTime camm2 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>AngularVelocity</td>
<td class=c>no</td>
<td><span class=s><span class=n>(gyro angular velocity about X, Y and Z axes in rad/s)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm3'>QuickTime camm3 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>Acceleration</td>
<td class=c>no</td>
<td><span class=s><span class=n>(acceleration in the X, Y and Z directions in m/s^2)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm4'>QuickTime camm4 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>Position</td>
<td class=c>no</td>
<td><span class=s><span class=n>(X, Y, Z position in local coordinate system)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm5'>QuickTime camm5 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x000c = 12'>0x000c</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0014 = 20'>0x0014</td>
<td>GPSAltitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm6'>QuickTime camm6 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>GPSDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x000c = 12'>0x000c</td>
<td>GPSMeasureMode</td>
<td class=c>no</td>
<td><span class=s>0 = No Measurement
  <br>2 = 2-Dimensional Measurement
  <br>3 = 3-Dimensional Measurement</span></td></tr>
<tr>
<td title='0x0010 = 16'>0x0010</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0018 = 24'>0x0018</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0020 = 32'>0x0020</td>
<td>GPSAltitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0024 = 36'>0x0024</td>
<td>GPSHorizontalAccuracy</td>
<td class=c>no</td>
<td><span class=s><span class=n>(metres)</span></span></td></tr>
<tr>
<td title='0x0028 = 40'>0x0028</td>
<td>GPSVerticalAccuracy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x002c = 44'>0x002c</td>
<td>GPSVelocityEast</td>
<td class=c>no</td>
<td><span class=s><span class=n>(m/s)</span></span></td></tr>
<tr>
<td title='0x0030 = 48'>0x0030</td>
<td>GPSVelocityNorth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0034 = 52'>0x0034</td>
<td>GPSVelocityUp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0038 = 56'>0x0038</td>
<td>GPSSpeedAccuracy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='camm7'>QuickTime camm7 Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>MagneticField</td>
<td class=c>no</td>
<td><span class=s><span class=n>(microtesla)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Keys'>QuickTime Keys Tags</a></h2>
<p>This directory contains a list of key names which are used to decode tags
written by the &quot;mdta&quot; handler.  Also in this table are a few tags found in
timed metadata that are not yet writable by ExifTool.  The prefix of
&quot;com.apple.quicktime.&quot; has been removed from the TagID&#39;s below.  These tags
support alternate languages in the same way as the
<a href="QuickTime.html#ItemList">ItemList</a> tags.  Note
that by default,
<a href="QuickTime.html#ItemList">ItemList</a> and
<a href="QuickTime.html#UserData">UserData</a> tags are
preferred when writing, so to create a tag when a same-named tag exists in
either of these tables, either the &quot;Keys&quot; location must be specified (eg.
<code>-Keys:Author=Phil</code> on the command line), or the PREFERRED level must be
changed via <a href="../config.html#PREF">the config file</a>.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'Encoded_With'</td>
<td>EncodedWith</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'album'</td>
<td>Album</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'apple.photos.variation-identifier'</td>
<td>ApplePhotosVariationIdentifier</td>
<td class=c>int64s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'artist'</td>
<td>Artist</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'artwork'</td>
<td>Artwork</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'author'</td>
<td>Author</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'collection.user'</td>
<td>UserCollection</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'com.android.capture.fps'</td>
<td>AndroidCaptureFPS</td>
<td class=c>float</td>
<td>&nbsp;</td></tr>
<tr>
<td>'com.android.manufacturer'</td>
<td>AndroidMake</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'com.android.model'</td>
<td>AndroidModel</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'com.android.version'</td>
<td>AndroidVersion</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'com.xiaomi.hdr10'</td>
<td>XiaomiHDR10</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'com.xiaomi.preview_video_cover'</td>
<td>XiaomiPreviewVideoCover</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'comment'</td>
<td>Comment</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'compatible_brands'</td>
<td>CompatibleBrands</td>
<td class=c title=' / = Avoid'>yes/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'content.identifier'</td>
<td>ContentIdentifier</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'copyright'</td>
<td>Copyright</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'creation_time'</td>
<td>CreationTime</td>
<td class=c title=' / = Avoid'>yes/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'creationdate'</td>
<td>CreationDate</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'description'</td>
<td>Description</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'detected-face'</td>
<td>FaceInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#FaceInfo'>QuickTime FaceInfo Tags</a></td></tr>
<tr class=b>
<td>'detected-face.bounds'</td>
<td>DetectedFaceBounds</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'detected-face.face-id'</td>
<td>DetectedFaceID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'detected-face.roll-angle'</td>
<td>DetectedFaceRollAngle</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'detected-face.yaw-angle'</td>
<td>DetectedFaceYawAngle</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'direction.facing'</td>
<td>CameraDirection</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'direction.motion'</td>
<td>CameraMotion</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'director'</td>
<td>Director</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'displayname'</td>
<td>DisplayName</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'encoder'</td>
<td>Encoder</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'full-frame-rate-playback-intent'</td>
<td>FullFrameRatePlaybackIntent</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'genre'</td>
<td>Genre</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'information'</td>
<td>Information</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'keywords'</td>
<td>Keywords</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'live-photo-info'</td>
<td>LivePhotoInfo</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'live-photo.auto'</td>
<td>LivePhotoAuto</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td>'live-photo.vitality-score'</td>
<td>LivePhotoVitalityScore</td>
<td class=c>float</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'live-photo.vitality-scoring-version'</td>
<td>LivePhotoVitalityScoringVersion</td>
<td class=c>int64s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'location.ISO6709'</td>
<td>GPSCoordinates</td>
<td class=c>yes</td>
<td><span class=s><span class=n>(Google Photos may ignore this if the coordinates have more than 5 digits
after the decimal)</span></span></td></tr>
<tr class=b>
<td>'location.accuracy.horizontal'</td>
<td>LocationAccuracyHorizontal</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'location.body'</td>
<td>LocationBody</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'location.date'</td>
<td>LocationDate</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'location.name'</td>
<td>LocationName</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'location.note'</td>
<td>LocationNote</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'location.role'</td>
<td>LocationRole</td>
<td class=c>yes</td>
<td><span class=s>0 = Shooting Location
  <br>1 = Real Location
  <br>2 = Fictional Location</span></td></tr>
<tr class=b>
<td>'major_brand'</td>
<td>MajorBrand</td>
<td class=c title=' / = Avoid'>yes/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'make'</td>
<td>Make</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'minor_version'</td>
<td>MinorVersion</td>
<td class=c title=' / = Avoid'>yes/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'model'</td>
<td>Model</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.audio.balance'</td>
<td>Balance</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.audio.bass'</td>
<td>Bass</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.audio.gain'</td>
<td>AudioGain</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.audio.mute'</td>
<td>Mute</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr class=b>
<td>'player.movie.audio.pitchshift'</td>
<td>PitchShift</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.audio.treble'</td>
<td>Treble</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.visual.brightness'</td>
<td>Brightness</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.visual.color'</td>
<td>Color</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.visual.contrast'</td>
<td>Contrast</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.visual.tint'</td>
<td>Tint</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.version'</td>
<td>PlayerVersion</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'producer'</td>
<td>Producer</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'publisher'</td>
<td>Publisher</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'rating.user'</td>
<td>UserRating</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'scene-illuminance'</td>
<td>SceneIlluminance</td>
<td class=c>no</td>
<td><span class=s><span class=n>(milli-lux)</span></span></td></tr>
<tr>
<td>'software'</td>
<td>Software</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'still-image-time'</td>
<td>StillImageTime</td>
<td class=c>no</td>
<td><span class=s><span class=n>(this tag always has a value of -1; the time of the still image is obtained
from the associated SampleTime)</span></span></td></tr>
<tr>
<td>'title'</td>
<td>Title</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'version'</td>
<td>Version</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'video-orientation'</td>
<td>VideoOrientation</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>1 = Horizontal (normal)
  <br>2 = Mirror horizontal
  <br>3 = Rotate 180
  <br>4 = Mirror vertical
  <br>5 = Mirror horizontal and rotate 270 CW
  <br>6 = Rotate 90 CW
  <br>7 = Mirror horizontal and rotate 90 CW
  <br>8 = Rotate 270 CW</td></tr></table>
</td></tr>
<tr class=b>
<td>'xiaomi.exifInfo.videoinfo'</td>
<td>XiaomiExifInfo</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'year'</td>
<td>Year</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AudioKeys'>QuickTime AudioKeys Tags</a></h2>
<p>Keys tags written in the audio track by some Apple devices.  These tags
belong to the ExifTool AudioKeys family 1 gorup.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'player.movie.audio.balance'</td>
<td>Balance</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.audio.bass'</td>
<td>Bass</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'player.movie.audio.gain'</td>
<td>AudioGain</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.audio.mute'</td>
<td>Mute</td>
<td class=c>int8u</td>
<td><span class=s>0 = Off
  <br>1 = On</span></td></tr>
<tr>
<td>'player.movie.audio.pitchshift'</td>
<td>PitchShift</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'player.movie.audio.treble'</td>
<td>Treble</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='VideoKeys'>QuickTime VideoKeys Tags</a></h2>
<p>Keys tags written in the video track.  These tags belong to the ExifTool
VideoKeys family 1 gorup.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'camera.focal_length.35mm_equivalent'</td>
<td>FocalLengthIn35mmFormat</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'camera.framereadouttimeinmicroseconds'</td>
<td>FrameReadoutTime</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'camera.identifier'</td>
<td>CameraIdentifier</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'camera.lens_model'</td>
<td>LensModel</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
<tr>
<td>'com.apple.photos.captureMode'</td>
<td>CaptureMode</td>
<td class=c>yes</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FaceInfo'>QuickTime FaceInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'crec'</td>
<td>FaceRec</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#FaceRec'>QuickTime FaceRec Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FaceRec'>QuickTime FaceRec Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'cits'</td>
<td>FaceItem</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Keys'>QuickTime Keys Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='RoveGPS'>QuickTime RoveGPS Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>GPSSpeed</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>GPSDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='PreviewInfo'>QuickTime PreviewInfo Tags</a></h2>
<p>Preview stored by TomTom Bandit ActionCam.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>PreviewImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='tx3g'>QuickTime tx3g Tags</a></h2>
<p>Tags extracted from the tx3g sbtl timed metadata of Yuneec and Autel drones,
and subtitle text in some other videos.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'Alt'</td>
<td>GPSAltitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'DateTime'</td>
<td>DateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'EV'</td>
<td>ExposureCompensation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'F-NUM'</td>
<td>FNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPSDateTime'</td>
<td>GPSDateTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GimPitch'</td>
<td>GimbalPitch</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GimRoll'</td>
<td>GimbalRoll</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GimYaw'</td>
<td>GimbalYaw</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'HomeLat'</td>
<td>GPSHomeLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'HomeLon'</td>
<td>GPSHomeLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ISO'</td>
<td>ISO</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Lat'</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Lon'</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Pitch'</td>
<td>Pitch</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Roll'</td>
<td>Roll</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SHUTTER'</td>
<td>ExposureTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Text'</td>
<td>Text</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Yaw'</td>
<td>Yaw</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HTCInfo'>QuickTime HTCInfo Tags</a></h2>
<p>Tags written by some HTC camera phones.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'slmt'</td>
<td>Unknown_slmt?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Pittasoft'>QuickTime Pittasoft Tags</a></h2>
<p>Tags found in Pittasoft Blackvue dashcam &quot;free&quot; data.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'3gf '</td>
<td>AccelData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr class=b>
<td>'cprt'</td>
<td>Copyright</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'gps '</td>
<td>GPSLog</td>
<td class=c>no</td>
<td><span class=s><span class=n>(parsed to extract GPS separately when ExtractEmbedded is used)</span></span></td></tr>
<tr class=b>
<td>'ptnm'</td>
<td>OriginalFileName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ptrh'</td>
<td>Ptrh</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Pittasoft'>QuickTime Pittasoft Tags</a></td></tr>
<tr class=b>
<td>'sttm'</td>
<td>StartTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'thum'</td>
<td>PreviewImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FileType'>QuickTime FileType Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MajorBrand</td>
<td class=c>no</td>
<td><span class=s>&#39;3g2a&#39; = 3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-0 V1.0
  <br>&#39;3g2b&#39; = 3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-A V1.0.0
  <br>&#39;3g2c&#39; = 3GPP2 Media (.3G2) compliant with 3GPP2 C.S0050-B v1.0
  <br>&#39;3ge6&#39; = 3GPP (.3GP) Release 6 MBMS Extended Presentations
  <br>&#39;3ge7&#39; = 3GPP (.3GP) Release 7 MBMS Extended Presentations
  <br>&#39;3gg6&#39; = 3GPP Release 6 General Profile
  <br>&#39;3gp1&#39; = 3GPP Media (.3GP) Release 1 (probably non-existent)
  <br>&#39;3gp2&#39; = 3GPP Media (.3GP) Release 2 (probably non-existent)
  <br>&#39;3gp3&#39; = 3GPP Media (.3GP) Release 3 (probably non-existent)
  <br>&#39;3gp4&#39; = 3GPP Media (.3GP) Release 4
  <br>&#39;3gp5&#39; = 3GPP Media (.3GP) Release 5
  <br>&#39;3gp6&#39; = 3GPP Media (.3GP) Release 6 Streaming Servers
  <br>&#39;3gs7&#39; = 3GPP Media (.3GP) Release 7 Streaming Servers
  <br>&#39;CAEP&#39; = Canon Digital Camera
  <br>&#39;CDes&#39; = Convergent Design
  <br>&#39;F4A &#39; = Audio for Adobe Flash Player 9+ (.F4A)
  <br>&#39;F4B &#39; = Audio Book for Adobe Flash Player 9+ (.F4B)
  <br>&#39;F4P &#39; = Protected Video for Adobe Flash Player 9+ (.F4P)
  <br>&#39;F4V &#39; = Video for Adobe Flash Player 9+ (.F4V)
  <br>&#39;JP2 &#39; = JPEG 2000 Image (.JP2) [ISO 15444-1 ?]
  <br>&#39;JP20&#39; = Unknown, from GPAC samples (prob non-existent)
  <br>&#39;KDDI&#39; = 3GPP2 EZmovie for KDDI 3G cellphones
  <br>&#39;M4A &#39; = Apple iTunes AAC-LC (.M4A) Audio
  <br>&#39;M4B &#39; = Apple iTunes AAC-LC (.M4B) Audio Book
  <br>&#39;M4P &#39; = Apple iTunes AAC-LC (.M4P) AES Protected Audio
  <br>&#39;M4V &#39; = Apple iTunes Video (.M4V) Video
  <br>&#39;M4VH&#39; = Apple TV (.M4V)
  <br>&#39;M4VP&#39; = Apple iPhone (.M4V)
  <br>&#39;MPPI&#39; = Photo Player, MAF [ISO/IEC 23000-3]
  <br>&#39;MSNV&#39; = MPEG-4 (.MP4) for SonyPSP
  <br>&#39;NDAS&#39; = MP4 v2 [ISO 14496-14] Nero Digital AAC Audio
  <br>&#39;NDSC&#39; = MPEG-4 (.MP4) Nero Cinema Profile
  <br>&#39;NDSH&#39; = MPEG-4 (.MP4) Nero HDTV Profile
  <br>&#39;NDSM&#39; = MPEG-4 (.MP4) Nero Mobile Profile
  <br>&#39;NDSP&#39; = MPEG-4 (.MP4) Nero Portable Profile
  <br>&#39;NDSS&#39; = MPEG-4 (.MP4) Nero Standard Profile
  <br>&#39;NDXC&#39; = H.264/MPEG-4 AVC (.MP4) Nero Cinema Profile
  <br>&#39;NDXH&#39; = H.264/MPEG-4 AVC (.MP4) Nero HDTV Profile
  <br>&#39;NDXM&#39; = H.264/MPEG-4 AVC (.MP4) Nero Mobile Profile
  <br>&#39;NDXP&#39; = H.264/MPEG-4 AVC (.MP4) Nero Portable Profile
  <br>&#39;NDXS&#39; = H.264/MPEG-4 AVC (.MP4) Nero Standard Profile
  <br>&#39;ROSS&#39; = Ross Video
  <br>&#39;XAVC&#39; = Sony XAVC
  <br>&#39;aax &#39; = Audible Enhanced Audiobook (.AAX)
  <br>&#39;avc1&#39; = MP4 Base w/ AVC ext [ISO 14496-12:2005]
  <br>&#39;avif&#39; = AV1 Image File Format (.AVIF)
  <br>&#39;caqv&#39; = Casio Digital Camera
  <br>&#39;crx &#39; = Canon Raw (.CRX)
  <br>&#39;da0a&#39; = DMB MAF w/ MPEG Layer II aud, MOT slides, DLS, JPG/PNG/MNG images
  <br>&#39;da0b&#39; = DMB MAF, extending DA0A, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;da1a&#39; = DMB MAF audio with ER-BSAC audio, JPG/PNG/MNG images
  <br>&#39;da1b&#39; = DMB MAF, extending da1a, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;da2a&#39; = DMB MAF aud w/ HE-AAC v2 aud, MOT slides, DLS, JPG/PNG/MNG images
  <br>&#39;da2b&#39; = DMB MAF, extending da2a, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;da3a&#39; = DMB MAF aud with HE-AAC aud, JPG/PNG/MNG images
  <br>&#39;da3b&#39; = DMB MAF, extending da3a w/ BIFS, 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;dmb1&#39; = DMB MAF supporting all the components defined in the specification
  <br>&#39;dmpf&#39; = Digital Media Project
  <br>&#39;drc1&#39; = Dirac (wavelet compression), encapsulated in ISO base media (MP4)
  <br>&#39;dv1a&#39; = DMB MAF vid w/ AVC vid, ER-BSAC aud, BIFS, JPG/PNG/MNG images, TS
  <br>&#39;dv1b&#39; = DMB MAF, extending dv1a, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;dv2a&#39; = DMB MAF vid w/ AVC vid, HE-AAC v2 aud, BIFS, JPG/PNG/MNG images, TS
  <br>&#39;dv2b&#39; = DMB MAF, extending dv2a, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;dv3a&#39; = DMB MAF vid w/ AVC vid, HE-AAC aud, BIFS, JPG/PNG/MNG images, TS
  <br>&#39;dv3b&#39; = DMB MAF, extending dv3a, with 3GPP timed text, DID, TVA, REL, IPMP
  <br>&#39;dvr1&#39; = DVB (.DVB) over RTP
  <br>&#39;dvt1&#39; = DVB (.DVB) over MPEG-2 Transport Stream
  <br>&#39;heic&#39; = High Efficiency Image Format HEVC still image (.HEIC)
  <br>&#39;heix&#39; = High Efficiency Image Format still image (.HEIF)
  <br>&#39;hevc&#39; = High Efficiency Image Format HEVC sequence (.HEICS)
  <br>&#39;isc2&#39; = ISMACryp 2.0 Encrypted File
  <br>&#39;iso2&#39; = MP4 Base Media v2 [ISO 14496-12:2005]
  <br>&#39;iso3&#39; = MP4 Base Media v3
  <br>&#39;iso4&#39; = MP4 Base Media v4
  <br>&#39;iso5&#39; = MP4 Base Media v5
  <br>&#39;iso6&#39; = MP4 Base Media v6
  <br>&#39;iso7&#39; = MP4 Base Media v7
  <br>&#39;iso8&#39; = MP4 Base Media v8
  <br>&#39;iso9&#39; = MP4 Base Media v9
  <br>&#39;isom&#39; = MP4 Base Media v1 [IS0 14496-12:2003]
  <br>&#39;jpm &#39; = JPEG 2000 Compound Image (.JPM) [ISO 15444-6]
  <br>&#39;jpx &#39; = JPEG 2000 with extensions (.JPX) [ISO 15444-2]
  <br>&#39;mif1&#39; = High Efficiency Image Format still image (.HEIF)
  <br>&#39;mj2s&#39; = Motion JPEG 2000 [ISO 15444-3] Simple Profile
  <br>&#39;mjp2&#39; = Motion JPEG 2000 [ISO 15444-3] General Profile
  <br>&#39;mmp4&#39; = MPEG-4/3GPP Mobile Profile (.MP4/3GP) (for NTT)
  <br>&#39;mp21&#39; = MPEG-21 [ISO/IEC 21000-9]
  <br>&#39;mp41&#39; = MP4 v1 [ISO 14496-1:ch13]
  <br>&#39;mp42&#39; = MP4 v2 [ISO 14496-14]
  <br>&#39;mp71&#39; = MP4 w/ MPEG-7 Metadata [per ISO 14496-12]
  <br>&#39;mqt &#39; = Sony / Mobile QuickTime (.MQV) US Patent 7,477,830 (Sony Corp)
  <br>&#39;msf1&#39; = High Efficiency Image Format sequence (.HEIFS)
  <br>&#39;odcf&#39; = OMA DCF DRM Format 2.0 (OMA-TS-DRM-DCF-V2_0-20060303-A)
  <br>&#39;opf2&#39; = OMA PDCF DRM Format 2.1 (OMA-TS-DRM-DCF-V2_1-20070724-C)
  <br>&#39;opx2&#39; = OMA PDCF DRM + XBS extensions (OMA-TS-DRM_XBS-V1_0-20070529-C)
  <br>&#39;pana&#39; = Panasonic Digital Camera
  <br>&#39;qt  &#39; = Apple QuickTime (.MOV/QT)
  <br>&#39;sdv &#39; = SD Memory Card Video
  <br>&#39;ssc1&#39; = Samsung stereoscopic, single stream
  <br>&#39;ssc2&#39; = Samsung stereoscopic, dual stream</span></td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>MinorVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>CompatibleBrands</td>
<td class=c title=' + = List'>no+</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='OtherMeta'>QuickTime OtherMeta Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'mere'</td>
<td>MetaRelation</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MetaRelation'>QuickTime MetaRelation Tags</a></td></tr>
<tr class=b>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MetaRelation'>QuickTime MetaRelation Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr><td colspan=4 class=c><i>[no tags known]</i></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Meta'>QuickTime Meta Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'bxml'</td>
<td>BinaryXML?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'dinf'</td>
<td>DataInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#DataInfo'>QuickTime DataInfo Tags</a></td></tr>
<tr>
<td>'free'</td>
<td>Free?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'grpl'</td>
<td>Unknown_grpl</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#grpl'>QuickTime grpl Tags</a></td></tr>
<tr>
<td>'hdlr'</td>
<td>Handler</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Handler'>QuickTime Handler Tags</a></td></tr>
<tr class=b>
<td>'idat'</td>
<td>MetaImageSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'iinf'</td>
<td>ItemInformation</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ItemInfo'>QuickTime ItemInfo Tags</a>
  <br>--&gt; <a href='QuickTime.html#ItemInfo'>QuickTime ItemInfo Tags</a></td></tr>
<tr class=b>
<td>'iloc'</td>
<td>ItemLocation</td>
<td class=c>no</td>
<td><span class=s><span class=n>(parsed, but not extracted as a tag)</span></span></td></tr>
<tr>
<td>'ilst'</td>
<td>ItemList</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ItemList'>QuickTime ItemList Tags</a></td></tr>
<tr class=b>
<td>'ipmc'</td>
<td>IPMPControl?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ipro'</td>
<td>ItemProtection?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'iprp'</td>
<td>ItemProperties</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ItemProp'>QuickTime ItemProp Tags</a></td></tr>
<tr>
<td>'iref'</td>
<td>ItemReference</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ItemRef'>QuickTime ItemRef Tags</a></td></tr>
<tr class=b>
<td>'keys'</td>
<td>AudioKeys
  <br>VideoKeys
  <br>Keys</td>
<td class=c>-<br>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#AudioKeys'>QuickTime AudioKeys Tags</a>
  <br>--&gt; <a href='QuickTime.html#VideoKeys'>QuickTime VideoKeys Tags</a>
  <br>--&gt; <a href='QuickTime.html#Keys'>QuickTime Keys Tags</a></td></tr>
<tr>
<td>'pitm'</td>
<td>PrimaryItemReference</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'uuid'</td>
<td>MetaVersion
  <br>UUID-Unknown?</td>
<td class=c>no<br>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'xml '</td>
<td>XML</td>
<td class=c>-</td>
<td>--&gt; <a href='XMP.html#XML'>XMP XML Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='DataInfo'>QuickTime DataInfo Tags</a></h2>
<p>MP4 data information box.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'dref'</td>
<td>DataRef</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#DataRef'>QuickTime DataRef Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='DataRef'>QuickTime DataRef Tags</a></h2>
<p>MP4 data reference box.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>"url\0"</td>
<td>URL</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'url '</td>
<td>URL</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'urn '</td>
<td>URN</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='grpl'>QuickTime grpl Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr><td colspan=4 class=c><i>[no tags known]</i></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Handler'>QuickTime Handler Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>HandlerClass</td>
<td class=c>no</td>
<td><span class=s>&#39;dhlr&#39; = Data Handler
  <br>&#39;mhlr&#39; = Media Handler</span></td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>HandlerType</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>&#39;alis&#39; = Alias Data
  <br>&#39;camm&#39; = Camera Metadata
  <br>&#39;crsm&#39; = Clock Reference
  <br>&#39;data&#39; = Data
  <br>&#39;hint&#39; = Hint Track
  <br>&#39;ipsm&#39; = IPMP
  <br>&#39;m7sm&#39; = MPEG-7 Stream
  <br>&#39;mdir&#39; = Metadata
  <br>&#39;mdta&#39; = Metadata Tags
  <br>&#39;meta&#39; = NRT Metadata
  <br>&#39;mjsm&#39; = MPEG-J
  <br>&#39;nrtm&#39; = Non-Real Time Metadata
  <br>&#39;ocsm&#39; = Object Content</td><td>&nbsp;&nbsp;</td>
  <td>&#39;odsm&#39; = Object Descriptor
  <br>&#39;pict&#39; = Picture
  <br>&#39;priv&#39; = Private
  <br>&#39;psmd&#39; = Panasonic Static Metadata
  <br>&#39;sbtl&#39; = Subtitle
  <br>&#39;sdsm&#39; = Scene Description
  <br>&#39;soun&#39; = Audio Track
  <br>&#39;subp&#39; = Subpicture
  <br>&#39;text&#39; = Text
  <br>&#39;tmcd&#39; = Time Code
  <br>&#39;url &#39; = URL
  <br>&#39;vide&#39; = Video Track</td></tr></table>
</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>HandlerVendorID</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#VendorID'>QuickTime VendorID Values</a></td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>HandlerDescription</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='VendorID'>QuickTime VendorID Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>VendorID</th><th>Value</th><th>VendorID</th><th>Value</th><th>VendorID</th></tr>
<tr><td>' KD '</td><td>= Kodak</td>
<td class=b>'ZORA'</td><td class=b>= Zoran Corporation</td>
<td>'olym'</td><td>= Olympus</td>
</tr><tr><td>'AR.D'</td><td>= Parrot AR.Drone</td>
<td class=b>'appl'</td><td class=b>= Apple</td>
<td>'pana'</td><td>= Panasonic</td>
</tr><tr><td>'FFMP'</td><td>= FFmpeg</td>
<td class=b>'fe20'</td><td class=b>= Olympus (fe20)</td>
<td>'pent'</td><td>= Pentax</td>
</tr><tr><td>'GIC '</td><td>= General Imaging Co.</td>
<td class=b>'kdak'</td><td class=b>= Kodak</td>
<td>'pr01'</td><td>= Olympus (pr01)</td>
</tr><tr><td>'KMPI'</td><td>= Konica-Minolta</td>
<td class=b>'leic'</td><td class=b>= Leica</td>
<td>'sany'</td><td>= Sanyo</td>
</tr><tr><td>'NIKO'</td><td>= Nikon</td>
<td class=b>'mino'</td><td class=b>= Minolta</td>
<td>&nbsp;</td><td>&nbsp;</td>
</tr><tr><td>'SMI '</td><td>= Sorenson Media Inc.</td>
<td class=b>'niko'</td><td class=b>= Nikon</td>
<td>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='ItemInfo'>QuickTime ItemInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'infe'</td>
<td>ItemInfoEntry</td>
<td class=c>no</td>
<td><span class=s><span class=n>(parsed, but not extracted as a tag)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ItemList'>QuickTime ItemList Tags</a></h2>
<p>This is the preferred location for creating new QuickTime tags.  Tags in
this table support alternate languages which are accessed by adding a
3-character ISO 639-2 language code and an optional ISO 3166-1 alpha 2
country code to the tag name (eg. &quot;ItemList:Title-fra&quot; or
&quot;ItemList::Title-fra-FR&quot;).  When creating a new Meta box to contain the
ItemList directory, by default ExifTool adds an &#39;mdir&#39; (Metadata) Handler
box because Apple software may ignore ItemList tags otherwise, but the API
<a href="../ExifTool.html#QuickTimeHandler">QuickTimeHandler</a> option may be set to 0 to avoid this.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'----'</td>
<td>iTunesInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#iTunesInfo'>QuickTime iTunesInfo Tags</a></td></tr>
<tr class=b>
<td>'@PST'</td>
<td>ParentShortTitle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'@ppi'</td>
<td>ParentProductID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'@pti'</td>
<td>ParentTitle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'@sti'</td>
<td>ShortTitle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'AACR'</td>
<td>Unknown_AACR?</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'CDEK'</td>
<td>Unknown_CDEK?</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CDET'</td>
<td>Unknown_CDET?</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GUID'</td>
<td>GUID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'VERS'</td>
<td>ProductVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'aART'</td>
<td>AlbumArtist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'akID'</td>
<td>AppleStoreAccountType</td>
<td class=c>int8s</td>
<td><span class=s>0 = iTunes
  <br>1 = AOL</span></td></tr>
<tr>
<td>'albm'</td>
<td>Album</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'apID'</td>
<td>AppleStoreAccount</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'atID'</td>
<td>ArtistID</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'auth'</td>
<td>Author</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'catg'</td>
<td>Category</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cmID'</td>
<td>ComposerID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'cnID'</td>
<td>AppleStoreCatalogID</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'covr'</td>
<td>CoverArt</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'cpil'</td>
<td>Compilation</td>
<td class=c>int8s</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td>'cprt'</td>
<td>Copyright</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'desc'</td>
<td>Description</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'disk'</td>
<td>DiskNumber</td>
<td class=c>undef</td>
<td>&nbsp;</td></tr>
<tr>
<td>'dscp'</td>
<td>Description</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'egid'</td>
<td>EpisodeGlobalUniqueID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'geID'</td>
<td>GenreID</td>
<td class=c>int32s</td>
<td>--&gt; <a href='QuickTime.html#GenreID'>QuickTime GenreID Values</a></td></tr>
<tr class=b>
<td>'gnre'</td>
<td>Genre</td>
<td class=c title=' / = Avoid'>undef/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'grup'</td>
<td>Grouping</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'gshh'</td>
<td>GoogleHostHeader</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'gspm'</td>
<td>GooglePingMessage</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'gspu'</td>
<td>GooglePingURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'gssd'</td>
<td>GoogleSourceData</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'gsst'</td>
<td>GoogleStartTime</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'gstd'</td>
<td>GoogleTrackDuration</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'hdvd'</td>
<td>HDVideo</td>
<td class=c>int8s</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr>
<td>'itnu'</td>
<td>iTunesU</td>
<td class=c>int8s</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td>'keyw'</td>
<td>Keyword</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ldes'</td>
<td>LongDescription</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'ownr'</td>
<td>Owner</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pcst'</td>
<td>Podcast</td>
<td class=c>int8s</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td>'perf'</td>
<td>Performer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pgap'</td>
<td>PlayGap</td>
<td class=c>int8s</td>
<td><span class=s>0 = Insert Gap
  <br>1 = No Gap</span></td></tr>
<tr class=b>
<td>'plID'</td>
<td>AlbumID</td>
<td class=c>int32s[2]</td>
<td>&nbsp;</td></tr>
<tr>
<td>'prID'</td>
<td>ProductID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'purd'</td>
<td>PurchaseDate</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'purl'</td>
<td>PodcastURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'rate'</td>
<td>RatingPercent</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'rldt'</td>
<td>ReleaseDate</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'rtng'</td>
<td>Rating</td>
<td class=c>int8s</td>
<td><span class=s>0 = none
  <br>1 = Explicit
  <br>2 = Clean
  <br>4 = Explicit (old)</span></td></tr>
<tr>
<td>'sdes'</td>
<td>StoreDescription</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'sfID'</td>
<td>AppleStoreCountry</td>
<td class=c>int32s</td>
<td>--&gt; <a href='QuickTime.html#AppleStoreCountry'>QuickTime AppleStoreCountry Values</a></td></tr>
<tr>
<td>'shwm'</td>
<td>ShowMovement</td>
<td class=c>int8s</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
<tr class=b>
<td>'snal'</td>
<td>PreviewImage</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'soaa'</td>
<td>SortAlbumArtist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'soal'</td>
<td>SortAlbum</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'soar'</td>
<td>SortArtist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'soco'</td>
<td>SortComposer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'sonm'</td>
<td>SortName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'sosn'</td>
<td>SortShow</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'stik'</td>
<td>MediaType</td>
<td class=c>int8s</td>
<td><table class=cols><tr>
  <td>0 = Movie (old)
  <br>1 = Normal (Music)
  <br>2 = Audiobook
  <br>5 = Whacked Bookmark
  <br>6 = Music Video
  <br>9 = Movie</td><td>&nbsp;&nbsp;</td>
  <td>10 = TV Show
  <br>11 = Booklet
  <br>14 = Ringtone
  <br>21 = Podcast
  <br>23 = iTunes U</td></tr></table>
</td></tr>
<tr class=b>
<td>'titl'</td>
<td>Title</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tmpo'</td>
<td>BeatsPerMinute</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tnal'</td>
<td>ThumbnailImage</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'trkn'</td>
<td>TrackNumber</td>
<td class=c>undef</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tven'</td>
<td>TVEpisodeID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tves'</td>
<td>TVEpisode</td>
<td class=c>int32s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tvnn'</td>
<td>TVNetworkName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tvsh'</td>
<td>TVShow</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tvsn'</td>
<td>TVSeason</td>
<td class=c>int32u</td>
<td>&nbsp;</td></tr>
<tr>
<td>'xid '</td>
<td>ISRC</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'yrrc'</td>
<td>Year</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ART"</td>
<td>Artist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;alb"</td>
<td>Album</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ard"</td>
<td>ArtDirector</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;arg"</td>
<td>Arranger</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;aut"</td>
<td>Author</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;cmt"</td>
<td>Comment</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;com"</td>
<td>Composer</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;con"</td>
<td>Conductor</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;cpy"</td>
<td>Copyright</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;day"</td>
<td>ContentCreateDate</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;des"</td>
<td>Description</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;dir"</td>
<td>Director</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;enc"</td>
<td>EncodedBy</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;gen"</td>
<td>Genre</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;grp"</td>
<td>Grouping</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;lyr"</td>
<td>Lyrics</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;mvc"</td>
<td>MovementCount</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;mvi"</td>
<td>MovementNumber</td>
<td class=c>int16s</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;mvn"</td>
<td>MovementName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;nam"</td>
<td>Title</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;nrt"</td>
<td>Narrator</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ope"</td>
<td>OriginalArtist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;prd"</td>
<td>Producer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;pub"</td>
<td>Publisher</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;sne"</td>
<td>SoundEngineer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;sol"</td>
<td>Soloist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;st3"</td>
<td>Subtitle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;too"</td>
<td>Encoder</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;trk"</td>
<td>Track</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;wrk"</td>
<td>Work</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;wrt"</td>
<td>Composer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;xpd"</td>
<td>ExecutiveProducer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;xyz"</td>
<td>GPSCoordinates</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='GenreID'>QuickTime GenreID Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>GenreID</th></tr>
<tr><td class=r>2</td><td>= Music|Blues</td>
</tr><tr><td class=r>3</td><td>= Music|Comedy</td>
</tr><tr><td class=r>4</td><td>= Music|Children&#39;s Music</td>
</tr><tr><td class=r>5</td><td>= Music|Classical</td>
</tr><tr><td class=r>6</td><td>= Music|Country</td>
</tr><tr><td class=r>7</td><td>= Music|Electronic</td>
</tr><tr><td class=r>8</td><td>= Music|Holiday</td>
</tr><tr><td class=r>9</td><td>= Music|Classical|Opera</td>
</tr><tr><td class=r>10</td><td>= Music|Singer/Songwriter</td>
</tr><tr><td class=r>11</td><td>= Music|Jazz</td>
</tr><tr><td class=r>12</td><td>= Music|Latino</td>
</tr><tr><td class=r>13</td><td>= Music|New Age</td>
</tr><tr><td class=r>14</td><td>= Music|Pop</td>
</tr><tr><td class=r>15</td><td>= Music|R&amp;B/Soul</td>
</tr><tr><td class=r>16</td><td>= Music|Soundtrack</td>
</tr><tr><td class=r>17</td><td>= Music|Dance</td>
</tr><tr><td class=r>18</td><td>= Music|Hip-Hop/Rap</td>
</tr><tr><td class=r>19</td><td>= Music|World</td>
</tr><tr><td class=r>20</td><td>= Music|Alternative</td>
</tr><tr><td class=r>21</td><td>= Music|Rock</td>
</tr><tr><td class=r>22</td><td>= Music|Christian &amp; Gospel</td>
</tr><tr><td class=r>23</td><td>= Music|Vocal</td>
</tr><tr><td class=r>24</td><td>= Music|Reggae</td>
</tr><tr><td class=r>25</td><td>= Music|Easy Listening</td>
</tr><tr><td class=r>26</td><td>= Podcasts</td>
</tr><tr><td class=r>27</td><td>= Music|J-Pop</td>
</tr><tr><td class=r>28</td><td>= Music|Enka</td>
</tr><tr><td class=r>29</td><td>= Music|Anime</td>
</tr><tr><td class=r>30</td><td>= Music|Kayokyoku</td>
</tr><tr><td class=r>31</td><td>= Music Videos</td>
</tr><tr><td class=r>32</td><td>= TV Shows</td>
</tr><tr><td class=r>33</td><td>= Movies</td>
</tr><tr><td class=r>34</td><td>= Music</td>
</tr><tr><td class=r>35</td><td>= iPod Games</td>
</tr><tr><td class=r>36</td><td>= App Store</td>
</tr><tr><td class=r>37</td><td>= Tones</td>
</tr><tr><td class=r>38</td><td>= Books</td>
</tr><tr><td class=r>39</td><td>= Mac App Store</td>
</tr><tr><td class=r>40</td><td>= Textbooks</td>
</tr><tr><td class=r>50</td><td>= Music|Fitness &amp; Workout</td>
</tr><tr><td class=r>51</td><td>= Music|Pop|K-Pop</td>
</tr><tr><td class=r>52</td><td>= Music|Karaoke</td>
</tr><tr><td class=r>53</td><td>= Music|Instrumental</td>
</tr><tr><td class=r>74</td><td>= Audiobooks|News</td>
</tr><tr><td class=r>75</td><td>= Audiobooks|Programs &amp; Performances</td>
</tr><tr><td class=r>500</td><td>= Fitness Music</td>
</tr><tr><td class=r>501</td><td>= Fitness Music|Pop</td>
</tr><tr><td class=r>502</td><td>= Fitness Music|Dance</td>
</tr><tr><td class=r>503</td><td>= Fitness Music|Hip-Hop</td>
</tr><tr><td class=r>504</td><td>= Fitness Music|Rock</td>
</tr><tr><td class=r>505</td><td>= Fitness Music|Alt/Indie</td>
</tr><tr><td class=r>506</td><td>= Fitness Music|Latino</td>
</tr><tr><td class=r>507</td><td>= Fitness Music|Country</td>
</tr><tr><td class=r>508</td><td>= Fitness Music|World</td>
</tr><tr><td class=r>509</td><td>= Fitness Music|New Age</td>
</tr><tr><td class=r>510</td><td>= Fitness Music|Classical</td>
</tr><tr><td class=r>1001</td><td>= Music|Alternative|College Rock</td>
</tr><tr><td class=r>1002</td><td>= Music|Alternative|Goth Rock</td>
</tr><tr><td class=r>1003</td><td>= Music|Alternative|Grunge</td>
</tr><tr><td class=r>1004</td><td>= Music|Alternative|Indie Rock</td>
</tr><tr><td class=r>1005</td><td>= Music|Alternative|New Wave</td>
</tr><tr><td class=r>1006</td><td>= Music|Alternative|Punk</td>
</tr><tr><td class=r>1007</td><td>= Music|Blues|Chicago Blues</td>
</tr><tr><td class=r>1009</td><td>= Music|Blues|Classic Blues</td>
</tr><tr><td class=r>1010</td><td>= Music|Blues|Contemporary Blues</td>
</tr><tr><td class=r>1011</td><td>= Music|Blues|Country Blues</td>
</tr><tr><td class=r>1012</td><td>= Music|Blues|Delta Blues</td>
</tr><tr><td class=r>1013</td><td>= Music|Blues|Electric Blues</td>
</tr><tr><td class=r>1014</td><td>= Music|Children&#39;s Music|Lullabies</td>
</tr><tr><td class=r>1015</td><td>= Music|Children&#39;s Music|Sing-Along</td>
</tr><tr><td class=r>1016</td><td>= Music|Children&#39;s Music|Stories</td>
</tr><tr><td class=r>1017</td><td>= Music|Classical|Avant-Garde</td>
</tr><tr><td class=r>1018</td><td>= Music|Classical|Baroque Era</td>
</tr><tr><td class=r>1019</td><td>= Music|Classical|Chamber Music</td>
</tr><tr><td class=r>1020</td><td>= Music|Classical|Chant</td>
</tr><tr><td class=r>1021</td><td>= Music|Classical|Choral</td>
</tr><tr><td class=r>1022</td><td>= Music|Classical|Classical Crossover</td>
</tr><tr><td class=r>1023</td><td>= Music|Classical|Early Music</td>
</tr><tr><td class=r>1024</td><td>= Music|Classical|Impressionist</td>
</tr><tr><td class=r>1025</td><td>= Music|Classical|Medieval Era</td>
</tr><tr><td class=r>1026</td><td>= Music|Classical|Minimalism</td>
</tr><tr><td class=r>1027</td><td>= Music|Classical|Modern Era</td>
</tr><tr><td class=r>1028</td><td>= Music|Classical|Opera</td>
</tr><tr><td class=r>1029</td><td>= Music|Classical|Orchestral</td>
</tr><tr><td class=r>1030</td><td>= Music|Classical|Renaissance</td>
</tr><tr><td class=r>1031</td><td>= Music|Classical|Romantic Era</td>
</tr><tr><td class=r>1032</td><td>= Music|Classical|Wedding Music</td>
</tr><tr><td class=r>1033</td><td>= Music|Country|Alternative Country</td>
</tr><tr><td class=r>1034</td><td>= Music|Country|Americana</td>
</tr><tr><td class=r>1035</td><td>= Music|Country|Bluegrass</td>
</tr><tr><td class=r>1036</td><td>= Music|Country|Contemporary Bluegrass</td>
</tr><tr><td class=r>1037</td><td>= Music|Country|Contemporary Country</td>
</tr><tr><td class=r>1038</td><td>= Music|Country|Country Gospel</td>
</tr><tr><td class=r>1039</td><td>= Music|Country|Honky Tonk</td>
</tr><tr><td class=r>1040</td><td>= Music|Country|Outlaw Country</td>
</tr><tr><td class=r>1041</td><td>= Music|Country|Traditional Bluegrass</td>
</tr><tr><td class=r>1042</td><td>= Music|Country|Traditional Country</td>
</tr><tr><td class=r>1043</td><td>= Music|Country|Urban Cowboy</td>
</tr><tr><td class=r>1044</td><td>= Music|Dance|Breakbeat</td>
</tr><tr><td class=r>1045</td><td>= Music|Dance|Exercise</td>
</tr><tr><td class=r>1046</td><td>= Music|Dance|Garage</td>
</tr><tr><td class=r>1047</td><td>= Music|Dance|Hardcore</td>
</tr><tr><td class=r>1048</td><td>= Music|Dance|House</td>
</tr><tr><td class=r>1049</td><td>= Music|Dance|Jungle/Drum&#39;n&#39;bass</td>
</tr><tr><td class=r>1050</td><td>= Music|Dance|Techno</td>
</tr><tr><td class=r>1051</td><td>= Music|Dance|Trance</td>
</tr><tr><td class=r>1052</td><td>= Music|Jazz|Big Band</td>
</tr><tr><td class=r>1053</td><td>= Music|Jazz|Bop</td>
</tr><tr><td class=r>1054</td><td>= Music|Easy Listening|Lounge</td>
</tr><tr><td class=r>1055</td><td>= Music|Easy Listening|Swing</td>
</tr><tr><td class=r>1056</td><td>= Music|Electronic|Ambient</td>
</tr><tr><td class=r>1057</td><td>= Music|Electronic|Downtempo</td>
</tr><tr><td class=r>1058</td><td>= Music|Electronic|Electronica</td>
</tr><tr><td class=r>1060</td><td>= Music|Electronic|IDM/Experimental</td>
</tr><tr><td class=r>1061</td><td>= Music|Electronic|Industrial</td>
</tr><tr><td class=r>1062</td><td>= Music|Singer/Songwriter|Alternative Folk</td>
</tr><tr><td class=r>1063</td><td>= Music|Singer/Songwriter|Contemporary Folk</td>
</tr><tr><td class=r>1064</td><td>= Music|Singer/Songwriter|Contemporary Singer/Songwriter</td>
</tr><tr><td class=r>1065</td><td>= Music|Singer/Songwriter|Folk-Rock</td>
</tr><tr><td class=r>1066</td><td>= Music|Singer/Songwriter|New Acoustic</td>
</tr><tr><td class=r>1067</td><td>= Music|Singer/Songwriter|Traditional Folk</td>
</tr><tr><td class=r>1068</td><td>= Music|Hip-Hop/Rap|Alternative Rap</td>
</tr><tr><td class=r>1069</td><td>= Music|Hip-Hop/Rap|Dirty South</td>
</tr><tr><td class=r>1070</td><td>= Music|Hip-Hop/Rap|East Coast Rap</td>
</tr><tr><td class=r>1071</td><td>= Music|Hip-Hop/Rap|Gangsta Rap</td>
</tr><tr><td class=r>1072</td><td>= Music|Hip-Hop/Rap|Hardcore Rap</td>
</tr><tr><td class=r>1073</td><td>= Music|Hip-Hop/Rap|Hip-Hop</td>
</tr><tr><td class=r>1074</td><td>= Music|Hip-Hop/Rap|Latin Rap</td>
</tr><tr><td class=r>1075</td><td>= Music|Hip-Hop/Rap|Old School Rap</td>
</tr><tr><td class=r>1076</td><td>= Music|Hip-Hop/Rap|Rap</td>
</tr><tr><td class=r>1077</td><td>= Music|Hip-Hop/Rap|Underground Rap</td>
</tr><tr><td class=r>1078</td><td>= Music|Hip-Hop/Rap|West Coast Rap</td>
</tr><tr><td class=r>1079</td><td>= Music|Holiday|Chanukah</td>
</tr><tr><td class=r>1080</td><td>= Music|Holiday|Christmas</td>
</tr><tr><td class=r>1081</td><td>= Music|Holiday|Christmas: Children&#39;s</td>
</tr><tr><td class=r>1082</td><td>= Music|Holiday|Christmas: Classic</td>
</tr><tr><td class=r>1083</td><td>= Music|Holiday|Christmas: Classical</td>
</tr><tr><td class=r>1084</td><td>= Music|Holiday|Christmas: Jazz</td>
</tr><tr><td class=r>1085</td><td>= Music|Holiday|Christmas: Modern</td>
</tr><tr><td class=r>1086</td><td>= Music|Holiday|Christmas: Pop</td>
</tr><tr><td class=r>1087</td><td>= Music|Holiday|Christmas: R&amp;B</td>
</tr><tr><td class=r>1088</td><td>= Music|Holiday|Christmas: Religious</td>
</tr><tr><td class=r>1089</td><td>= Music|Holiday|Christmas: Rock</td>
</tr><tr><td class=r>1090</td><td>= Music|Holiday|Easter</td>
</tr><tr><td class=r>1091</td><td>= Music|Holiday|Halloween</td>
</tr><tr><td class=r>1092</td><td>= Music|Holiday|Holiday: Other</td>
</tr><tr><td class=r>1093</td><td>= Music|Holiday|Thanksgiving</td>
</tr><tr><td class=r>1094</td><td>= Music|Christian &amp; Gospel|CCM</td>
</tr><tr><td class=r>1095</td><td>= Music|Christian &amp; Gospel|Christian Metal</td>
</tr><tr><td class=r>1096</td><td>= Music|Christian &amp; Gospel|Christian Pop</td>
</tr><tr><td class=r>1097</td><td>= Music|Christian &amp; Gospel|Christian Rap</td>
</tr><tr><td class=r>1098</td><td>= Music|Christian &amp; Gospel|Christian Rock</td>
</tr><tr><td class=r>1099</td><td>= Music|Christian &amp; Gospel|Classic Christian</td>
</tr><tr><td class=r>1100</td><td>= Music|Christian &amp; Gospel|Contemporary Gospel</td>
</tr><tr><td class=r>1101</td><td>= Music|Christian &amp; Gospel|Gospel</td>
</tr><tr><td class=r>1103</td><td>= Music|Christian &amp; Gospel|Praise &amp; Worship</td>
</tr><tr><td class=r>1104</td><td>= Music|Christian &amp; Gospel|Southern Gospel</td>
</tr><tr><td class=r>1105</td><td>= Music|Christian &amp; Gospel|Traditional Gospel</td>
</tr><tr><td class=r>1106</td><td>= Music|Jazz|Avant-Garde Jazz</td>
</tr><tr><td class=r>1107</td><td>= Music|Jazz|Contemporary Jazz</td>
</tr><tr><td class=r>1108</td><td>= Music|Jazz|Crossover Jazz</td>
</tr><tr><td class=r>1109</td><td>= Music|Jazz|Dixieland</td>
</tr><tr><td class=r>1110</td><td>= Music|Jazz|Fusion</td>
</tr><tr><td class=r>1111</td><td>= Music|Jazz|Latin Jazz</td>
</tr><tr><td class=r>1112</td><td>= Music|Jazz|Mainstream Jazz</td>
</tr><tr><td class=r>1113</td><td>= Music|Jazz|Ragtime</td>
</tr><tr><td class=r>1114</td><td>= Music|Jazz|Smooth Jazz</td>
</tr><tr><td class=r>1115</td><td>= Music|Latino|Latin Jazz</td>
</tr><tr><td class=r>1116</td><td>= Music|Latino|Contemporary Latin</td>
</tr><tr><td class=r>1117</td><td>= Music|Latino|Pop Latino</td>
</tr><tr><td class=r>1118</td><td>= Music|Latino|Raices</td>
</tr><tr><td class=r>1119</td><td>= Music|Latino|Urbano latino</td>
</tr><tr><td class=r>1120</td><td>= Music|Latino|Baladas y Boleros</td>
</tr><tr><td class=r>1121</td><td>= Music|Latino|Rock y Alternativo</td>
</tr><tr><td class=r>1122</td><td>= Music|Brazilian</td>
</tr><tr><td class=r>1123</td><td>= Music|Latino|Musica Mexicana</td>
</tr><tr><td class=r>1124</td><td>= Music|Latino|Musica tropical</td>
</tr><tr><td class=r>1125</td><td>= Music|New Age|Environmental</td>
</tr><tr><td class=r>1126</td><td>= Music|New Age|Healing</td>
</tr><tr><td class=r>1127</td><td>= Music|New Age|Meditation</td>
</tr><tr><td class=r>1128</td><td>= Music|New Age|Nature</td>
</tr><tr><td class=r>1129</td><td>= Music|New Age|Relaxation</td>
</tr><tr><td class=r>1130</td><td>= Music|New Age|Travel</td>
</tr><tr><td class=r>1131</td><td>= Music|Pop|Adult Contemporary</td>
</tr><tr><td class=r>1132</td><td>= Music|Pop|Britpop</td>
</tr><tr><td class=r>1133</td><td>= Music|Pop|Pop/Rock</td>
</tr><tr><td class=r>1134</td><td>= Music|Pop|Soft Rock</td>
</tr><tr><td class=r>1135</td><td>= Music|Pop|Teen Pop</td>
</tr><tr><td class=r>1136</td><td>= Music|R&amp;B/Soul|Contemporary R&amp;B</td>
</tr><tr><td class=r>1137</td><td>= Music|R&amp;B/Soul|Disco</td>
</tr><tr><td class=r>1138</td><td>= Music|R&amp;B/Soul|Doo Wop</td>
</tr><tr><td class=r>1139</td><td>= Music|R&amp;B/Soul|Funk</td>
</tr><tr><td class=r>1140</td><td>= Music|R&amp;B/Soul|Motown</td>
</tr><tr><td class=r>1141</td><td>= Music|R&amp;B/Soul|Neo-Soul</td>
</tr><tr><td class=r>1142</td><td>= Music|R&amp;B/Soul|Quiet Storm</td>
</tr><tr><td class=r>1143</td><td>= Music|R&amp;B/Soul|Soul</td>
</tr><tr><td class=r>1144</td><td>= Music|Rock|Adult Alternative</td>
</tr><tr><td class=r>1145</td><td>= Music|Rock|American Trad Rock</td>
</tr><tr><td class=r>1146</td><td>= Music|Rock|Arena Rock</td>
</tr><tr><td class=r>1147</td><td>= Music|Rock|Blues-Rock</td>
</tr><tr><td class=r>1148</td><td>= Music|Rock|British Invasion</td>
</tr><tr><td class=r>1149</td><td>= Music|Rock|Death Metal/Black Metal</td>
</tr><tr><td class=r>1150</td><td>= Music|Rock|Glam Rock</td>
</tr><tr><td class=r>1151</td><td>= Music|Rock|Hair Metal</td>
</tr><tr><td class=r>1152</td><td>= Music|Rock|Hard Rock</td>
</tr><tr><td class=r>1153</td><td>= Music|Rock|Metal</td>
</tr><tr><td class=r>1154</td><td>= Music|Rock|Jam Bands</td>
</tr><tr><td class=r>1155</td><td>= Music|Rock|Prog-Rock/Art Rock</td>
</tr><tr><td class=r>1156</td><td>= Music|Rock|Psychedelic</td>
</tr><tr><td class=r>1157</td><td>= Music|Rock|Rock &amp; Roll</td>
</tr><tr><td class=r>1158</td><td>= Music|Rock|Rockabilly</td>
</tr><tr><td class=r>1159</td><td>= Music|Rock|Roots Rock</td>
</tr><tr><td class=r>1160</td><td>= Music|Rock|Singer/Songwriter</td>
</tr><tr><td class=r>1161</td><td>= Music|Rock|Southern Rock</td>
</tr><tr><td class=r>1162</td><td>= Music|Rock|Surf</td>
</tr><tr><td class=r>1163</td><td>= Music|Rock|Tex-Mex</td>
</tr><tr><td class=r>1165</td><td>= Music|Soundtrack|Foreign Cinema</td>
</tr><tr><td class=r>1166</td><td>= Music|Soundtrack|Musicals</td>
</tr><tr><td class=r>1167</td><td>= Music|Comedy|Novelty</td>
</tr><tr><td class=r>1168</td><td>= Music|Soundtrack|Original Score</td>
</tr><tr><td class=r>1169</td><td>= Music|Soundtrack|Soundtrack</td>
</tr><tr><td class=r>1171</td><td>= Music|Comedy|Standup Comedy</td>
</tr><tr><td class=r>1172</td><td>= Music|Soundtrack|TV Soundtrack</td>
</tr><tr><td class=r>1173</td><td>= Music|Vocal|Standards</td>
</tr><tr><td class=r>1174</td><td>= Music|Vocal|Traditional Pop</td>
</tr><tr><td class=r>1175</td><td>= Music|Jazz|Vocal Jazz</td>
</tr><tr><td class=r>1176</td><td>= Music|Vocal|Vocal Pop</td>
</tr><tr><td class=r>1177</td><td>= Music|African|Afro-Beat</td>
</tr><tr><td class=r>1178</td><td>= Music|African|Afro-Pop</td>
</tr><tr><td class=r>1179</td><td>= Music|World|Cajun</td>
</tr><tr><td class=r>1180</td><td>= Music|World|Celtic</td>
</tr><tr><td class=r>1181</td><td>= Music|World|Celtic Folk</td>
</tr><tr><td class=r>1182</td><td>= Music|World|Contemporary Celtic</td>
</tr><tr><td class=r>1183</td><td>= Music|Reggae|Modern Dancehall</td>
</tr><tr><td class=r>1184</td><td>= Music|World|Drinking Songs</td>
</tr><tr><td class=r>1185</td><td>= Music|Indian|Indian Pop</td>
</tr><tr><td class=r>1186</td><td>= Music|World|Japanese Pop</td>
</tr><tr><td class=r>1187</td><td>= Music|World|Klezmer</td>
</tr><tr><td class=r>1188</td><td>= Music|World|Polka</td>
</tr><tr><td class=r>1189</td><td>= Music|World|Traditional Celtic</td>
</tr><tr><td class=r>1190</td><td>= Music|World|Worldbeat</td>
</tr><tr><td class=r>1191</td><td>= Music|World|Zydeco</td>
</tr><tr><td class=r>1192</td><td>= Music|Reggae|Roots Reggae</td>
</tr><tr><td class=r>1193</td><td>= Music|Reggae|Dub</td>
</tr><tr><td class=r>1194</td><td>= Music|Reggae|Ska</td>
</tr><tr><td class=r>1195</td><td>= Music|World|Caribbean</td>
</tr><tr><td class=r>1196</td><td>= Music|World|South America</td>
</tr><tr><td class=r>1197</td><td>= Music|Arabic</td>
</tr><tr><td class=r>1198</td><td>= Music|World|North America</td>
</tr><tr><td class=r>1199</td><td>= Music|World|Hawaii</td>
</tr><tr><td class=r>1200</td><td>= Music|World|Australia</td>
</tr><tr><td class=r>1201</td><td>= Music|World|Japan</td>
</tr><tr><td class=r>1202</td><td>= Music|World|France</td>
</tr><tr><td class=r>1203</td><td>= Music|African</td>
</tr><tr><td class=r>1204</td><td>= Music|World|Asia</td>
</tr><tr><td class=r>1205</td><td>= Music|World|Europe</td>
</tr><tr><td class=r>1206</td><td>= Music|World|South Africa</td>
</tr><tr><td class=r>1207</td><td>= Music|Jazz|Hard Bop</td>
</tr><tr><td class=r>1208</td><td>= Music|Jazz|Trad Jazz</td>
</tr><tr><td class=r>1209</td><td>= Music|Jazz|Cool Jazz</td>
</tr><tr><td class=r>1210</td><td>= Music|Blues|Acoustic Blues</td>
</tr><tr><td class=r>1211</td><td>= Music|Classical|High Classical</td>
</tr><tr><td class=r>1220</td><td>= Music|Brazilian|Axe</td>
</tr><tr><td class=r>1221</td><td>= Music|Brazilian|Bossa Nova</td>
</tr><tr><td class=r>1222</td><td>= Music|Brazilian|Choro</td>
</tr><tr><td class=r>1223</td><td>= Music|Brazilian|Forro</td>
</tr><tr><td class=r>1224</td><td>= Music|Brazilian|Frevo</td>
</tr><tr><td class=r>1225</td><td>= Music|Brazilian|MPB</td>
</tr><tr><td class=r>1226</td><td>= Music|Brazilian|Pagode</td>
</tr><tr><td class=r>1227</td><td>= Music|Brazilian|Samba</td>
</tr><tr><td class=r>1228</td><td>= Music|Brazilian|Sertanejo</td>
</tr><tr><td class=r>1229</td><td>= Music|Brazilian|Baile Funk</td>
</tr><tr><td class=r>1230</td><td>= Music|Alternative|Chinese Alt</td>
</tr><tr><td class=r>1231</td><td>= Music|Alternative|Korean Indie</td>
</tr><tr><td class=r>1232</td><td>= Music|Chinese</td>
</tr><tr><td class=r>1233</td><td>= Music|Chinese|Chinese Classical</td>
</tr><tr><td class=r>1234</td><td>= Music|Chinese|Chinese Flute</td>
</tr><tr><td class=r>1235</td><td>= Music|Chinese|Chinese Opera</td>
</tr><tr><td class=r>1236</td><td>= Music|Chinese|Chinese Orchestral</td>
</tr><tr><td class=r>1237</td><td>= Music|Chinese|Chinese Regional Folk</td>
</tr><tr><td class=r>1238</td><td>= Music|Chinese|Chinese Strings</td>
</tr><tr><td class=r>1239</td><td>= Music|Chinese|Taiwanese Folk</td>
</tr><tr><td class=r>1240</td><td>= Music|Chinese|Tibetan Native Music</td>
</tr><tr><td class=r>1241</td><td>= Music|Hip-Hop/Rap|Chinese Hip-Hop</td>
</tr><tr><td class=r>1242</td><td>= Music|Hip-Hop/Rap|Korean Hip-Hop</td>
</tr><tr><td class=r>1243</td><td>= Music|Korean</td>
</tr><tr><td class=r>1244</td><td>= Music|Korean|Korean Classical</td>
</tr><tr><td class=r>1245</td><td>= Music|Korean|Korean Trad Song</td>
</tr><tr><td class=r>1246</td><td>= Music|Korean|Korean Trad Instrumental</td>
</tr><tr><td class=r>1247</td><td>= Music|Korean|Korean Trad Theater</td>
</tr><tr><td class=r>1248</td><td>= Music|Rock|Chinese Rock</td>
</tr><tr><td class=r>1249</td><td>= Music|Rock|Korean Rock</td>
</tr><tr><td class=r>1250</td><td>= Music|Pop|C-Pop</td>
</tr><tr><td class=r>1251</td><td>= Music|Pop|Cantopop/HK-Pop</td>
</tr><tr><td class=r>1252</td><td>= Music|Pop|Korean Folk-Pop</td>
</tr><tr><td class=r>1253</td><td>= Music|Pop|Mandopop</td>
</tr><tr><td class=r>1254</td><td>= Music|Pop|Tai-Pop</td>
</tr><tr><td class=r>1255</td><td>= Music|Pop|Malaysian Pop</td>
</tr><tr><td class=r>1256</td><td>= Music|Pop|Pinoy Pop</td>
</tr><tr><td class=r>1257</td><td>= Music|Pop|Original Pilipino Music</td>
</tr><tr><td class=r>1258</td><td>= Music|Pop|Manilla Sound</td>
</tr><tr><td class=r>1259</td><td>= Music|Pop|Indo Pop</td>
</tr><tr><td class=r>1260</td><td>= Music|Pop|Thai Pop</td>
</tr><tr><td class=r>1261</td><td>= Music|Vocal|Trot</td>
</tr><tr><td class=r>1262</td><td>= Music|Indian</td>
</tr><tr><td class=r>1263</td><td>= Music|Indian|Bollywood</td>
</tr><tr><td class=r>1264</td><td>= Music|Indian|Regional Indian|Tamil</td>
</tr><tr><td class=r>1265</td><td>= Music|Indian|Regional Indian|Telugu</td>
</tr><tr><td class=r>1266</td><td>= Music|Indian|Regional Indian</td>
</tr><tr><td class=r>1267</td><td>= Music|Indian|Devotional &amp; Spiritual</td>
</tr><tr><td class=r>1268</td><td>= Music|Indian|Sufi</td>
</tr><tr><td class=r>1269</td><td>= Music|Indian|Indian Classical</td>
</tr><tr><td class=r>1270</td><td>= Music|Russian|Russian Chanson</td>
</tr><tr><td class=r>1271</td><td>= Music|World|Dini</td>
</tr><tr><td class=r>1272</td><td>= Music|Turkish|Halk</td>
</tr><tr><td class=r>1273</td><td>= Music|Turkish|Sanat</td>
</tr><tr><td class=r>1274</td><td>= Music|World|Dangdut</td>
</tr><tr><td class=r>1275</td><td>= Music|World|Indonesian Religious</td>
</tr><tr><td class=r>1276</td><td>= Music|World|Calypso</td>
</tr><tr><td class=r>1277</td><td>= Music|World|Soca</td>
</tr><tr><td class=r>1278</td><td>= Music|Indian|Ghazals</td>
</tr><tr><td class=r>1279</td><td>= Music|Indian|Indian Folk</td>
</tr><tr><td class=r>1280</td><td>= Music|Turkish|Arabesque</td>
</tr><tr><td class=r>1281</td><td>= Music|African|Afrikaans</td>
</tr><tr><td class=r>1282</td><td>= Music|World|Farsi</td>
</tr><tr><td class=r>1283</td><td>= Music|World|Israeli</td>
</tr><tr><td class=r>1284</td><td>= Music|Arabic|Khaleeji</td>
</tr><tr><td class=r>1285</td><td>= Music|Arabic|North African</td>
</tr><tr><td class=r>1286</td><td>= Music|Arabic|Arabic Pop</td>
</tr><tr><td class=r>1287</td><td>= Music|Arabic|Islamic</td>
</tr><tr><td class=r>1288</td><td>= Music|Soundtrack|Sound Effects</td>
</tr><tr><td class=r>1289</td><td>= Music|Folk</td>
</tr><tr><td class=r>1290</td><td>= Music|Orchestral</td>
</tr><tr><td class=r>1291</td><td>= Music|Marching</td>
</tr><tr><td class=r>1293</td><td>= Music|Pop|Oldies</td>
</tr><tr><td class=r>1294</td><td>= Music|Country|Thai Country</td>
</tr><tr><td class=r>1295</td><td>= Music|World|Flamenco</td>
</tr><tr><td class=r>1296</td><td>= Music|World|Tango</td>
</tr><tr><td class=r>1297</td><td>= Music|World|Fado</td>
</tr><tr><td class=r>1298</td><td>= Music|World|Iberia</td>
</tr><tr><td class=r>1299</td><td>= Music|Russian</td>
</tr><tr><td class=r>1300</td><td>= Music|Turkish</td>
</tr><tr><td class=r>1301</td><td>= Podcasts|Arts</td>
</tr><tr><td class=r>1302</td><td>= Podcasts|Society &amp; Culture|Personal Journals</td>
</tr><tr><td class=r>1303</td><td>= Podcasts|Comedy</td>
</tr><tr><td class=r>1304</td><td>= Podcasts|Education</td>
</tr><tr><td class=r>1305</td><td>= Podcasts|Kids &amp; Family</td>
</tr><tr><td class=r>1306</td><td>= Podcasts|Arts|Food</td>
</tr><tr><td class=r>1307</td><td>= Podcasts|Health</td>
</tr><tr><td class=r>1309</td><td>= Podcasts|TV &amp; Film</td>
</tr><tr><td class=r>1310</td><td>= Podcasts|Music</td>
</tr><tr><td class=r>1311</td><td>= Podcasts|News &amp; Politics</td>
</tr><tr><td class=r>1314</td><td>= Podcasts|Religion &amp; Spirituality</td>
</tr><tr><td class=r>1315</td><td>= Podcasts|Science &amp; Medicine</td>
</tr><tr><td class=r>1316</td><td>= Podcasts|Sports &amp; Recreation</td>
</tr><tr><td class=r>1318</td><td>= Podcasts|Technology</td>
</tr><tr><td class=r>1320</td><td>= Podcasts|Society &amp; Culture|Places &amp; Travel</td>
</tr><tr><td class=r>1321</td><td>= Podcasts|Business</td>
</tr><tr><td class=r>1323</td><td>= Podcasts|Games &amp; Hobbies</td>
</tr><tr><td class=r>1324</td><td>= Podcasts|Society &amp; Culture</td>
</tr><tr><td class=r>1325</td><td>= Podcasts|Government &amp; Organizations</td>
</tr><tr><td class=r>1337</td><td>= Music Videos|Classical|Piano</td>
</tr><tr><td class=r>1401</td><td>= Podcasts|Arts|Literature</td>
</tr><tr><td class=r>1402</td><td>= Podcasts|Arts|Design</td>
</tr><tr><td class=r>1404</td><td>= Podcasts|Games &amp; Hobbies|Video Games</td>
</tr><tr><td class=r>1405</td><td>= Podcasts|Arts|Performing Arts</td>
</tr><tr><td class=r>1406</td><td>= Podcasts|Arts|Visual Arts</td>
</tr><tr><td class=r>1410</td><td>= Podcasts|Business|Careers</td>
</tr><tr><td class=r>1412</td><td>= Podcasts|Business|Investing</td>
</tr><tr><td class=r>1413</td><td>= Podcasts|Business|Management &amp; Marketing</td>
</tr><tr><td class=r>1415</td><td>= Podcasts|Education|K-12</td>
</tr><tr><td class=r>1416</td><td>= Podcasts|Education|Higher Education</td>
</tr><tr><td class=r>1417</td><td>= Podcasts|Health|Fitness &amp; Nutrition</td>
</tr><tr><td class=r>1420</td><td>= Podcasts|Health|Self-Help</td>
</tr><tr><td class=r>1421</td><td>= Podcasts|Health|Sexuality</td>
</tr><tr><td class=r>1438</td><td>= Podcasts|Religion &amp; Spirituality|Buddhism</td>
</tr><tr><td class=r>1439</td><td>= Podcasts|Religion &amp; Spirituality|Christianity</td>
</tr><tr><td class=r>1440</td><td>= Podcasts|Religion &amp; Spirituality|Islam</td>
</tr><tr><td class=r>1441</td><td>= Podcasts|Religion &amp; Spirituality|Judaism</td>
</tr><tr><td class=r>1443</td><td>= Podcasts|Society &amp; Culture|Philosophy</td>
</tr><tr><td class=r>1444</td><td>= Podcasts|Religion &amp; Spirituality|Spirituality</td>
</tr><tr><td class=r>1446</td><td>= Podcasts|Technology|Gadgets</td>
</tr><tr><td class=r>1448</td><td>= Podcasts|Technology|Tech News</td>
</tr><tr><td class=r>1450</td><td>= Podcasts|Technology|Podcasting</td>
</tr><tr><td class=r>1454</td><td>= Podcasts|Games &amp; Hobbies|Automotive</td>
</tr><tr><td class=r>1455</td><td>= Podcasts|Games &amp; Hobbies|Aviation</td>
</tr><tr><td class=r>1456</td><td>= Podcasts|Sports &amp; Recreation|Outdoor</td>
</tr><tr><td class=r>1459</td><td>= Podcasts|Arts|Fashion &amp; Beauty</td>
</tr><tr><td class=r>1460</td><td>= Podcasts|Games &amp; Hobbies|Hobbies</td>
</tr><tr><td class=r>1461</td><td>= Podcasts|Games &amp; Hobbies|Other Games</td>
</tr><tr><td class=r>1462</td><td>= Podcasts|Society &amp; Culture|History</td>
</tr><tr><td class=r>1463</td><td>= Podcasts|Religion &amp; Spirituality|Hinduism</td>
</tr><tr><td class=r>1464</td><td>= Podcasts|Religion &amp; Spirituality|Other</td>
</tr><tr><td class=r>1465</td><td>= Podcasts|Sports &amp; Recreation|Professional</td>
</tr><tr><td class=r>1466</td><td>= Podcasts|Sports &amp; Recreation|College &amp; High School</td>
</tr><tr><td class=r>1467</td><td>= Podcasts|Sports &amp; Recreation|Amateur</td>
</tr><tr><td class=r>1468</td><td>= Podcasts|Education|Educational Technology</td>
</tr><tr><td class=r>1469</td><td>= Podcasts|Education|Language Courses</td>
</tr><tr><td class=r>1470</td><td>= Podcasts|Education|Training</td>
</tr><tr><td class=r>1471</td><td>= Podcasts|Business|Business News</td>
</tr><tr><td class=r>1472</td><td>= Podcasts|Business|Shopping</td>
</tr><tr><td class=r>1473</td><td>= Podcasts|Government &amp; Organizations|National</td>
</tr><tr><td class=r>1474</td><td>= Podcasts|Government &amp; Organizations|Regional</td>
</tr><tr><td class=r>1475</td><td>= Podcasts|Government &amp; Organizations|Local</td>
</tr><tr><td class=r>1476</td><td>= Podcasts|Government &amp; Organizations|Non-Profit</td>
</tr><tr><td class=r>1477</td><td>= Podcasts|Science &amp; Medicine|Natural Sciences</td>
</tr><tr><td class=r>1478</td><td>= Podcasts|Science &amp; Medicine|Medicine</td>
</tr><tr><td class=r>1479</td><td>= Podcasts|Science &amp; Medicine|Social Sciences</td>
</tr><tr><td class=r>1480</td><td>= Podcasts|Technology|Software How-To</td>
</tr><tr><td class=r>1481</td><td>= Podcasts|Health|Alternative Health</td>
</tr><tr><td class=r>1482</td><td>= Podcasts|Arts|Books</td>
</tr><tr><td class=r>1483</td><td>= Podcasts|Fiction</td>
</tr><tr><td class=r>1484</td><td>= Podcasts|Fiction|Drama</td>
</tr><tr><td class=r>1485</td><td>= Podcasts|Fiction|Science Fiction</td>
</tr><tr><td class=r>1486</td><td>= Podcasts|Fiction|Comedy Fiction</td>
</tr><tr><td class=r>1487</td><td>= Podcasts|History</td>
</tr><tr><td class=r>1488</td><td>= Podcasts|True Crime</td>
</tr><tr><td class=r>1489</td><td>= Podcasts|News</td>
</tr><tr><td class=r>1490</td><td>= Podcasts|News|Business News</td>
</tr><tr><td class=r>1491</td><td>= Podcasts|Business|Management</td>
</tr><tr><td class=r>1492</td><td>= Podcasts|Business|Marketing</td>
</tr><tr><td class=r>1493</td><td>= Podcasts|Business|Entrepreneurship</td>
</tr><tr><td class=r>1494</td><td>= Podcasts|Business|Non-Profit</td>
</tr><tr><td class=r>1495</td><td>= Podcasts|Comedy|Improv</td>
</tr><tr><td class=r>1496</td><td>= Podcasts|Comedy|Comedy Interviews</td>
</tr><tr><td class=r>1497</td><td>= Podcasts|Comedy|Stand-Up</td>
</tr><tr><td class=r>1498</td><td>= Podcasts|Education|Language Learning</td>
</tr><tr><td class=r>1499</td><td>= Podcasts|Education|How To</td>
</tr><tr><td class=r>1500</td><td>= Podcasts|Education|Self-Improvement</td>
</tr><tr><td class=r>1501</td><td>= Podcasts|Education|Courses</td>
</tr><tr><td class=r>1502</td><td>= Podcasts|Leisure</td>
</tr><tr><td class=r>1503</td><td>= Podcasts|Leisure|Automotive</td>
</tr><tr><td class=r>1504</td><td>= Podcasts|Leisure|Aviation</td>
</tr><tr><td class=r>1505</td><td>= Podcasts|Leisure|Hobbies</td>
</tr><tr><td class=r>1506</td><td>= Podcasts|Leisure|Crafts</td>
</tr><tr><td class=r>1507</td><td>= Podcasts|Leisure|Games</td>
</tr><tr><td class=r>1508</td><td>= Podcasts|Leisure|Home &amp; Garden</td>
</tr><tr><td class=r>1509</td><td>= Podcasts|Leisure|Video Games</td>
</tr><tr><td class=r>1510</td><td>= Podcasts|Leisure|Animation &amp; Manga</td>
</tr><tr><td class=r>1511</td><td>= Podcasts|Government</td>
</tr><tr><td class=r>1512</td><td>= Podcasts|Health &amp; Fitness</td>
</tr><tr><td class=r>1513</td><td>= Podcasts|Health &amp; Fitness|Alternative Health</td>
</tr><tr><td class=r>1514</td><td>= Podcasts|Health &amp; Fitness|Fitness</td>
</tr><tr><td class=r>1515</td><td>= Podcasts|Health &amp; Fitness|Nutrition</td>
</tr><tr><td class=r>1516</td><td>= Podcasts|Health &amp; Fitness|Sexuality</td>
</tr><tr><td class=r>1517</td><td>= Podcasts|Health &amp; Fitness|Mental Health</td>
</tr><tr><td class=r>1518</td><td>= Podcasts|Health &amp; Fitness|Medicine</td>
</tr><tr><td class=r>1519</td><td>= Podcasts|Kids &amp; Family|Education for Kids</td>
</tr><tr><td class=r>1520</td><td>= Podcasts|Kids &amp; Family|Stories for Kids</td>
</tr><tr><td class=r>1521</td><td>= Podcasts|Kids &amp; Family|Parenting</td>
</tr><tr><td class=r>1522</td><td>= Podcasts|Kids &amp; Family|Pets &amp; Animals</td>
</tr><tr><td class=r>1523</td><td>= Podcasts|Music|Music Commentary</td>
</tr><tr><td class=r>1524</td><td>= Podcasts|Music|Music History</td>
</tr><tr><td class=r>1525</td><td>= Podcasts|Music|Music Interviews</td>
</tr><tr><td class=r>1526</td><td>= Podcasts|News|Daily News</td>
</tr><tr><td class=r>1527</td><td>= Podcasts|News|Politics</td>
</tr><tr><td class=r>1528</td><td>= Podcasts|News|Tech News</td>
</tr><tr><td class=r>1529</td><td>= Podcasts|News|Sports News</td>
</tr><tr><td class=r>1530</td><td>= Podcasts|News|News Commentary</td>
</tr><tr><td class=r>1531</td><td>= Podcasts|News|Entertainment News</td>
</tr><tr><td class=r>1532</td><td>= Podcasts|Religion &amp; Spirituality|Religion</td>
</tr><tr><td class=r>1533</td><td>= Podcasts|Science</td>
</tr><tr><td class=r>1534</td><td>= Podcasts|Science|Natural Sciences</td>
</tr><tr><td class=r>1535</td><td>= Podcasts|Science|Social Sciences</td>
</tr><tr><td class=r>1536</td><td>= Podcasts|Science|Mathematics</td>
</tr><tr><td class=r>1537</td><td>= Podcasts|Science|Nature</td>
</tr><tr><td class=r>1538</td><td>= Podcasts|Science|Astronomy</td>
</tr><tr><td class=r>1539</td><td>= Podcasts|Science|Chemistry</td>
</tr><tr><td class=r>1540</td><td>= Podcasts|Science|Earth Sciences</td>
</tr><tr><td class=r>1541</td><td>= Podcasts|Science|Life Sciences</td>
</tr><tr><td class=r>1542</td><td>= Podcasts|Science|Physics</td>
</tr><tr><td class=r>1543</td><td>= Podcasts|Society &amp; Culture|Documentary</td>
</tr><tr><td class=r>1544</td><td>= Podcasts|Society &amp; Culture|Relationships</td>
</tr><tr><td class=r>1545</td><td>= Podcasts|Sports</td>
</tr><tr><td class=r>1546</td><td>= Podcasts|Sports|Soccer</td>
</tr><tr><td class=r>1547</td><td>= Podcasts|Sports|Football</td>
</tr><tr><td class=r>1548</td><td>= Podcasts|Sports|Basketball</td>
</tr><tr><td class=r>1549</td><td>= Podcasts|Sports|Baseball</td>
</tr><tr><td class=r>1550</td><td>= Podcasts|Sports|Hockey</td>
</tr><tr><td class=r>1551</td><td>= Podcasts|Sports|Running</td>
</tr><tr><td class=r>1552</td><td>= Podcasts|Sports|Rugby</td>
</tr><tr><td class=r>1553</td><td>= Podcasts|Sports|Golf</td>
</tr><tr><td class=r>1554</td><td>= Podcasts|Sports|Cricket</td>
</tr><tr><td class=r>1555</td><td>= Podcasts|Sports|Wrestling</td>
</tr><tr><td class=r>1556</td><td>= Podcasts|Sports|Tennis</td>
</tr><tr><td class=r>1557</td><td>= Podcasts|Sports|Volleyball</td>
</tr><tr><td class=r>1558</td><td>= Podcasts|Sports|Swimming</td>
</tr><tr><td class=r>1559</td><td>= Podcasts|Sports|Wilderness</td>
</tr><tr><td class=r>1560</td><td>= Podcasts|Sports|Fantasy Sports</td>
</tr><tr><td class=r>1561</td><td>= Podcasts|TV &amp; Film|TV Reviews</td>
</tr><tr><td class=r>1562</td><td>= Podcasts|TV &amp; Film|After Shows</td>
</tr><tr><td class=r>1563</td><td>= Podcasts|TV &amp; Film|Film Reviews</td>
</tr><tr><td class=r>1564</td><td>= Podcasts|TV &amp; Film|Film History</td>
</tr><tr><td class=r>1565</td><td>= Podcasts|TV &amp; Film|Film Interviews</td>
</tr><tr><td class=r>1602</td><td>= Music Videos|Blues</td>
</tr><tr><td class=r>1603</td><td>= Music Videos|Comedy</td>
</tr><tr><td class=r>1604</td><td>= Music Videos|Children&#39;s Music</td>
</tr><tr><td class=r>1605</td><td>= Music Videos|Classical</td>
</tr><tr><td class=r>1606</td><td>= Music Videos|Country</td>
</tr><tr><td class=r>1607</td><td>= Music Videos|Electronic</td>
</tr><tr><td class=r>1608</td><td>= Music Videos|Holiday</td>
</tr><tr><td class=r>1609</td><td>= Music Videos|Classical|Opera</td>
</tr><tr><td class=r>1610</td><td>= Music Videos|Singer/Songwriter</td>
</tr><tr><td class=r>1611</td><td>= Music Videos|Jazz</td>
</tr><tr><td class=r>1612</td><td>= Music Videos|Latin</td>
</tr><tr><td class=r>1613</td><td>= Music Videos|New Age</td>
</tr><tr><td class=r>1614</td><td>= Music Videos|Pop</td>
</tr><tr><td class=r>1615</td><td>= Music Videos|R&amp;B/Soul</td>
</tr><tr><td class=r>1616</td><td>= Music Videos|Soundtrack</td>
</tr><tr><td class=r>1617</td><td>= Music Videos|Dance</td>
</tr><tr><td class=r>1618</td><td>= Music Videos|Hip-Hop/Rap</td>
</tr><tr><td class=r>1619</td><td>= Music Videos|World</td>
</tr><tr><td class=r>1620</td><td>= Music Videos|Alternative</td>
</tr><tr><td class=r>1621</td><td>= Music Videos|Rock</td>
</tr><tr><td class=r>1622</td><td>= Music Videos|Christian &amp; Gospel</td>
</tr><tr><td class=r>1623</td><td>= Music Videos|Vocal</td>
</tr><tr><td class=r>1624</td><td>= Music Videos|Reggae</td>
</tr><tr><td class=r>1625</td><td>= Music Videos|Easy Listening</td>
</tr><tr><td class=r>1626</td><td>= Music Videos|Podcasts</td>
</tr><tr><td class=r>1627</td><td>= Music Videos|J-Pop</td>
</tr><tr><td class=r>1628</td><td>= Music Videos|Enka</td>
</tr><tr><td class=r>1629</td><td>= Music Videos|Anime</td>
</tr><tr><td class=r>1630</td><td>= Music Videos|Kayokyoku</td>
</tr><tr><td class=r>1631</td><td>= Music Videos|Disney</td>
</tr><tr><td class=r>1632</td><td>= Music Videos|French Pop</td>
</tr><tr><td class=r>1633</td><td>= Music Videos|German Pop</td>
</tr><tr><td class=r>1634</td><td>= Music Videos|German Folk</td>
</tr><tr><td class=r>1635</td><td>= Music Videos|Alternative|Chinese Alt</td>
</tr><tr><td class=r>1636</td><td>= Music Videos|Alternative|Korean Indie</td>
</tr><tr><td class=r>1637</td><td>= Music Videos|Chinese</td>
</tr><tr><td class=r>1638</td><td>= Music Videos|Chinese|Chinese Classical</td>
</tr><tr><td class=r>1639</td><td>= Music Videos|Chinese|Chinese Flute</td>
</tr><tr><td class=r>1640</td><td>= Music Videos|Chinese|Chinese Opera</td>
</tr><tr><td class=r>1641</td><td>= Music Videos|Chinese|Chinese Orchestral</td>
</tr><tr><td class=r>1642</td><td>= Music Videos|Chinese|Chinese Regional Folk</td>
</tr><tr><td class=r>1643</td><td>= Music Videos|Chinese|Chinese Strings</td>
</tr><tr><td class=r>1644</td><td>= Music Videos|Chinese|Taiwanese Folk</td>
</tr><tr><td class=r>1645</td><td>= Music Videos|Chinese|Tibetan Native Music</td>
</tr><tr><td class=r>1646</td><td>= Music Videos|Hip-Hop/Rap|Chinese Hip-Hop</td>
</tr><tr><td class=r>1647</td><td>= Music Videos|Hip-Hop/Rap|Korean Hip-Hop</td>
</tr><tr><td class=r>1648</td><td>= Music Videos|Korean</td>
</tr><tr><td class=r>1649</td><td>= Music Videos|Korean|Korean Classical</td>
</tr><tr><td class=r>1650</td><td>= Music Videos|Korean|Korean Trad Song</td>
</tr><tr><td class=r>1651</td><td>= Music Videos|Korean|Korean Trad Instrumental</td>
</tr><tr><td class=r>1652</td><td>= Music Videos|Korean|Korean Trad Theater</td>
</tr><tr><td class=r>1653</td><td>= Music Videos|Rock|Chinese Rock</td>
</tr><tr><td class=r>1654</td><td>= Music Videos|Rock|Korean Rock</td>
</tr><tr><td class=r>1655</td><td>= Music Videos|Pop|C-Pop</td>
</tr><tr><td class=r>1656</td><td>= Music Videos|Pop|Cantopop/HK-Pop</td>
</tr><tr><td class=r>1657</td><td>= Music Videos|Pop|Korean Folk-Pop</td>
</tr><tr><td class=r>1658</td><td>= Music Videos|Pop|Mandopop</td>
</tr><tr><td class=r>1659</td><td>= Music Videos|Pop|Tai-Pop</td>
</tr><tr><td class=r>1660</td><td>= Music Videos|Pop|Malaysian Pop</td>
</tr><tr><td class=r>1661</td><td>= Music Videos|Pop|Pinoy Pop</td>
</tr><tr><td class=r>1662</td><td>= Music Videos|Pop|Original Pilipino Music</td>
</tr><tr><td class=r>1663</td><td>= Music Videos|Pop|Manilla Sound</td>
</tr><tr><td class=r>1664</td><td>= Music Videos|Pop|Indo Pop</td>
</tr><tr><td class=r>1665</td><td>= Music Videos|Pop|Thai Pop</td>
</tr><tr><td class=r>1666</td><td>= Music Videos|Vocal|Trot</td>
</tr><tr><td class=r>1671</td><td>= Music Videos|Brazilian</td>
</tr><tr><td class=r>1672</td><td>= Music Videos|Brazilian|Axe</td>
</tr><tr><td class=r>1673</td><td>= Music Videos|Brazilian|Baile Funk</td>
</tr><tr><td class=r>1674</td><td>= Music Videos|Brazilian|Bossa Nova</td>
</tr><tr><td class=r>1675</td><td>= Music Videos|Brazilian|Choro</td>
</tr><tr><td class=r>1676</td><td>= Music Videos|Brazilian|Forro</td>
</tr><tr><td class=r>1677</td><td>= Music Videos|Brazilian|Frevo</td>
</tr><tr><td class=r>1678</td><td>= Music Videos|Brazilian|MPB</td>
</tr><tr><td class=r>1679</td><td>= Music Videos|Brazilian|Pagode</td>
</tr><tr><td class=r>1680</td><td>= Music Videos|Brazilian|Samba</td>
</tr><tr><td class=r>1681</td><td>= Music Videos|Brazilian|Sertanejo</td>
</tr><tr><td class=r>1682</td><td>= Music Videos|Classical|High Classical</td>
</tr><tr><td class=r>1683</td><td>= Music Videos|Fitness &amp; Workout</td>
</tr><tr><td class=r>1684</td><td>= Music Videos|Instrumental</td>
</tr><tr><td class=r>1685</td><td>= Music Videos|Jazz|Big Band</td>
</tr><tr><td class=r>1686</td><td>= Music Videos|Pop|K-Pop</td>
</tr><tr><td class=r>1687</td><td>= Music Videos|Karaoke</td>
</tr><tr><td class=r>1688</td><td>= Music Videos|Rock|Heavy Metal</td>
</tr><tr><td class=r>1689</td><td>= Music Videos|Spoken Word</td>
</tr><tr><td class=r>1690</td><td>= Music Videos|Indian</td>
</tr><tr><td class=r>1691</td><td>= Music Videos|Indian|Bollywood</td>
</tr><tr><td class=r>1692</td><td>= Music Videos|Indian|Regional Indian|Tamil</td>
</tr><tr><td class=r>1693</td><td>= Music Videos|Indian|Regional Indian|Telugu</td>
</tr><tr><td class=r>1694</td><td>= Music Videos|Indian|Regional Indian</td>
</tr><tr><td class=r>1695</td><td>= Music Videos|Indian|Devotional &amp; Spiritual</td>
</tr><tr><td class=r>1696</td><td>= Music Videos|Indian|Sufi</td>
</tr><tr><td class=r>1697</td><td>= Music Videos|Indian|Indian Classical</td>
</tr><tr><td class=r>1698</td><td>= Music Videos|Russian|Russian Chanson</td>
</tr><tr><td class=r>1699</td><td>= Music Videos|World|Dini</td>
</tr><tr><td class=r>1700</td><td>= Music Videos|Turkish|Halk</td>
</tr><tr><td class=r>1701</td><td>= Music Videos|Turkish|Sanat</td>
</tr><tr><td class=r>1702</td><td>= Music Videos|World|Dangdut</td>
</tr><tr><td class=r>1703</td><td>= Music Videos|World|Indonesian Religious</td>
</tr><tr><td class=r>1704</td><td>= Music Videos|Indian|Indian Pop</td>
</tr><tr><td class=r>1705</td><td>= Music Videos|World|Calypso</td>
</tr><tr><td class=r>1706</td><td>= Music Videos|World|Soca</td>
</tr><tr><td class=r>1707</td><td>= Music Videos|Indian|Ghazals</td>
</tr><tr><td class=r>1708</td><td>= Music Videos|Indian|Indian Folk</td>
</tr><tr><td class=r>1709</td><td>= Music Videos|Turkish|Arabesque</td>
</tr><tr><td class=r>1710</td><td>= Music Videos|African|Afrikaans</td>
</tr><tr><td class=r>1711</td><td>= Music Videos|World|Farsi</td>
</tr><tr><td class=r>1712</td><td>= Music Videos|World|Israeli</td>
</tr><tr><td class=r>1713</td><td>= Music Videos|Arabic</td>
</tr><tr><td class=r>1714</td><td>= Music Videos|Arabic|Khaleeji</td>
</tr><tr><td class=r>1715</td><td>= Music Videos|Arabic|North African</td>
</tr><tr><td class=r>1716</td><td>= Music Videos|Arabic|Arabic Pop</td>
</tr><tr><td class=r>1717</td><td>= Music Videos|Arabic|Islamic</td>
</tr><tr><td class=r>1718</td><td>= Music Videos|Soundtrack|Sound Effects</td>
</tr><tr><td class=r>1719</td><td>= Music Videos|Folk</td>
</tr><tr><td class=r>1720</td><td>= Music Videos|Orchestral</td>
</tr><tr><td class=r>1721</td><td>= Music Videos|Marching</td>
</tr><tr><td class=r>1723</td><td>= Music Videos|Pop|Oldies</td>
</tr><tr><td class=r>1724</td><td>= Music Videos|Country|Thai Country</td>
</tr><tr><td class=r>1725</td><td>= Music Videos|World|Flamenco</td>
</tr><tr><td class=r>1726</td><td>= Music Videos|World|Tango</td>
</tr><tr><td class=r>1727</td><td>= Music Videos|World|Fado</td>
</tr><tr><td class=r>1728</td><td>= Music Videos|World|Iberia</td>
</tr><tr><td class=r>1729</td><td>= Music Videos|Russian</td>
</tr><tr><td class=r>1730</td><td>= Music Videos|Turkish</td>
</tr><tr><td class=r>1731</td><td>= Music Videos|Alternative|College Rock</td>
</tr><tr><td class=r>1732</td><td>= Music Videos|Alternative|Goth Rock</td>
</tr><tr><td class=r>1733</td><td>= Music Videos|Alternative|Grunge</td>
</tr><tr><td class=r>1734</td><td>= Music Videos|Alternative|Indie Rock</td>
</tr><tr><td class=r>1735</td><td>= Music Videos|Alternative|New Wave</td>
</tr><tr><td class=r>1736</td><td>= Music Videos|Alternative|Punk</td>
</tr><tr><td class=r>1737</td><td>= Music Videos|Blues|Acoustic Blues</td>
</tr><tr><td class=r>1738</td><td>= Music Videos|Blues|Chicago Blues</td>
</tr><tr><td class=r>1739</td><td>= Music Videos|Blues|Classic Blues</td>
</tr><tr><td class=r>1740</td><td>= Music Videos|Blues|Contemporary Blues</td>
</tr><tr><td class=r>1741</td><td>= Music Videos|Blues|Country Blues</td>
</tr><tr><td class=r>1742</td><td>= Music Videos|Blues|Delta Blues</td>
</tr><tr><td class=r>1743</td><td>= Music Videos|Blues|Electric Blues</td>
</tr><tr><td class=r>1744</td><td>= Music Videos|Children&#39;s Music|Lullabies</td>
</tr><tr><td class=r>1745</td><td>= Music Videos|Children&#39;s Music|Sing-Along</td>
</tr><tr><td class=r>1746</td><td>= Music Videos|Children&#39;s Music|Stories</td>
</tr><tr><td class=r>1747</td><td>= Music Videos|Christian &amp; Gospel|CCM</td>
</tr><tr><td class=r>1748</td><td>= Music Videos|Christian &amp; Gospel|Christian Metal</td>
</tr><tr><td class=r>1749</td><td>= Music Videos|Christian &amp; Gospel|Christian Pop</td>
</tr><tr><td class=r>1750</td><td>= Music Videos|Christian &amp; Gospel|Christian Rap</td>
</tr><tr><td class=r>1751</td><td>= Music Videos|Christian &amp; Gospel|Christian Rock</td>
</tr><tr><td class=r>1752</td><td>= Music Videos|Christian &amp; Gospel|Classic Christian</td>
</tr><tr><td class=r>1753</td><td>= Music Videos|Christian &amp; Gospel|Contemporary Gospel</td>
</tr><tr><td class=r>1754</td><td>= Music Videos|Christian &amp; Gospel|Gospel</td>
</tr><tr><td class=r>1755</td><td>= Music Videos|Christian &amp; Gospel|Praise &amp; Worship</td>
</tr><tr><td class=r>1756</td><td>= Music Videos|Christian &amp; Gospel|Southern Gospel</td>
</tr><tr><td class=r>1757</td><td>= Music Videos|Christian &amp; Gospel|Traditional Gospel</td>
</tr><tr><td class=r>1758</td><td>= Music Videos|Classical|Avant-Garde</td>
</tr><tr><td class=r>1759</td><td>= Music Videos|Classical|Baroque Era</td>
</tr><tr><td class=r>1760</td><td>= Music Videos|Classical|Chamber Music</td>
</tr><tr><td class=r>1761</td><td>= Music Videos|Classical|Chant</td>
</tr><tr><td class=r>1762</td><td>= Music Videos|Classical|Choral</td>
</tr><tr><td class=r>1763</td><td>= Music Videos|Classical|Classical Crossover</td>
</tr><tr><td class=r>1764</td><td>= Music Videos|Classical|Early Music</td>
</tr><tr><td class=r>1765</td><td>= Music Videos|Classical|Impressionist</td>
</tr><tr><td class=r>1766</td><td>= Music Videos|Classical|Medieval Era</td>
</tr><tr><td class=r>1767</td><td>= Music Videos|Classical|Minimalism</td>
</tr><tr><td class=r>1768</td><td>= Music Videos|Classical|Modern Era</td>
</tr><tr><td class=r>1769</td><td>= Music Videos|Classical|Orchestral</td>
</tr><tr><td class=r>1770</td><td>= Music Videos|Classical|Renaissance</td>
</tr><tr><td class=r>1771</td><td>= Music Videos|Classical|Romantic Era</td>
</tr><tr><td class=r>1772</td><td>= Music Videos|Classical|Wedding Music</td>
</tr><tr><td class=r>1773</td><td>= Music Videos|Comedy|Novelty</td>
</tr><tr><td class=r>1774</td><td>= Music Videos|Comedy|Standup Comedy</td>
</tr><tr><td class=r>1775</td><td>= Music Videos|Country|Alternative Country</td>
</tr><tr><td class=r>1776</td><td>= Music Videos|Country|Americana</td>
</tr><tr><td class=r>1777</td><td>= Music Videos|Country|Bluegrass</td>
</tr><tr><td class=r>1778</td><td>= Music Videos|Country|Contemporary Bluegrass</td>
</tr><tr><td class=r>1779</td><td>= Music Videos|Country|Contemporary Country</td>
</tr><tr><td class=r>1780</td><td>= Music Videos|Country|Country Gospel</td>
</tr><tr><td class=r>1781</td><td>= Music Videos|Country|Honky Tonk</td>
</tr><tr><td class=r>1782</td><td>= Music Videos|Country|Outlaw Country</td>
</tr><tr><td class=r>1783</td><td>= Music Videos|Country|Traditional Bluegrass</td>
</tr><tr><td class=r>1784</td><td>= Music Videos|Country|Traditional Country</td>
</tr><tr><td class=r>1785</td><td>= Music Videos|Country|Urban Cowboy</td>
</tr><tr><td class=r>1786</td><td>= Music Videos|Dance|Breakbeat</td>
</tr><tr><td class=r>1787</td><td>= Music Videos|Dance|Exercise</td>
</tr><tr><td class=r>1788</td><td>= Music Videos|Dance|Garage</td>
</tr><tr><td class=r>1789</td><td>= Music Videos|Dance|Hardcore</td>
</tr><tr><td class=r>1790</td><td>= Music Videos|Dance|House</td>
</tr><tr><td class=r>1791</td><td>= Music Videos|Dance|Jungle/Drum&#39;n&#39;bass</td>
</tr><tr><td class=r>1792</td><td>= Music Videos|Dance|Techno</td>
</tr><tr><td class=r>1793</td><td>= Music Videos|Dance|Trance</td>
</tr><tr><td class=r>1794</td><td>= Music Videos|Easy Listening|Lounge</td>
</tr><tr><td class=r>1795</td><td>= Music Videos|Easy Listening|Swing</td>
</tr><tr><td class=r>1796</td><td>= Music Videos|Electronic|Ambient</td>
</tr><tr><td class=r>1797</td><td>= Music Videos|Electronic|Downtempo</td>
</tr><tr><td class=r>1798</td><td>= Music Videos|Electronic|Electronica</td>
</tr><tr><td class=r>1799</td><td>= Music Videos|Electronic|IDM/Experimental</td>
</tr><tr><td class=r>1800</td><td>= Music Videos|Electronic|Industrial</td>
</tr><tr><td class=r>1801</td><td>= Music Videos|Hip-Hop/Rap|Alternative Rap</td>
</tr><tr><td class=r>1802</td><td>= Music Videos|Hip-Hop/Rap|Dirty South</td>
</tr><tr><td class=r>1803</td><td>= Music Videos|Hip-Hop/Rap|East Coast Rap</td>
</tr><tr><td class=r>1804</td><td>= Music Videos|Hip-Hop/Rap|Gangsta Rap</td>
</tr><tr><td class=r>1805</td><td>= Music Videos|Hip-Hop/Rap|Hardcore Rap</td>
</tr><tr><td class=r>1806</td><td>= Music Videos|Hip-Hop/Rap|Hip-Hop</td>
</tr><tr><td class=r>1807</td><td>= Music Videos|Hip-Hop/Rap|Latin Rap</td>
</tr><tr><td class=r>1808</td><td>= Music Videos|Hip-Hop/Rap|Old School Rap</td>
</tr><tr><td class=r>1809</td><td>= Music Videos|Hip-Hop/Rap|Rap</td>
</tr><tr><td class=r>1810</td><td>= Music Videos|Hip-Hop/Rap|Underground Rap</td>
</tr><tr><td class=r>1811</td><td>= Music Videos|Hip-Hop/Rap|West Coast Rap</td>
</tr><tr><td class=r>1812</td><td>= Music Videos|Holiday|Chanukah</td>
</tr><tr><td class=r>1813</td><td>= Music Videos|Holiday|Christmas</td>
</tr><tr><td class=r>1814</td><td>= Music Videos|Holiday|Christmas: Children&#39;s</td>
</tr><tr><td class=r>1815</td><td>= Music Videos|Holiday|Christmas: Classic</td>
</tr><tr><td class=r>1816</td><td>= Music Videos|Holiday|Christmas: Classical</td>
</tr><tr><td class=r>1817</td><td>= Music Videos|Holiday|Christmas: Jazz</td>
</tr><tr><td class=r>1818</td><td>= Music Videos|Holiday|Christmas: Modern</td>
</tr><tr><td class=r>1819</td><td>= Music Videos|Holiday|Christmas: Pop</td>
</tr><tr><td class=r>1820</td><td>= Music Videos|Holiday|Christmas: R&amp;B</td>
</tr><tr><td class=r>1821</td><td>= Music Videos|Holiday|Christmas: Religious</td>
</tr><tr><td class=r>1822</td><td>= Music Videos|Holiday|Christmas: Rock</td>
</tr><tr><td class=r>1823</td><td>= Music Videos|Holiday|Easter</td>
</tr><tr><td class=r>1824</td><td>= Music Videos|Holiday|Halloween</td>
</tr><tr><td class=r>1825</td><td>= Music Videos|Holiday|Thanksgiving</td>
</tr><tr><td class=r>1826</td><td>= Music Videos|Jazz|Avant-Garde Jazz</td>
</tr><tr><td class=r>1828</td><td>= Music Videos|Jazz|Bop</td>
</tr><tr><td class=r>1829</td><td>= Music Videos|Jazz|Contemporary Jazz</td>
</tr><tr><td class=r>1830</td><td>= Music Videos|Jazz|Cool Jazz</td>
</tr><tr><td class=r>1831</td><td>= Music Videos|Jazz|Crossover Jazz</td>
</tr><tr><td class=r>1832</td><td>= Music Videos|Jazz|Dixieland</td>
</tr><tr><td class=r>1833</td><td>= Music Videos|Jazz|Fusion</td>
</tr><tr><td class=r>1834</td><td>= Music Videos|Jazz|Hard Bop</td>
</tr><tr><td class=r>1835</td><td>= Music Videos|Jazz|Latin Jazz</td>
</tr><tr><td class=r>1836</td><td>= Music Videos|Jazz|Mainstream Jazz</td>
</tr><tr><td class=r>1837</td><td>= Music Videos|Jazz|Ragtime</td>
</tr><tr><td class=r>1838</td><td>= Music Videos|Jazz|Smooth Jazz</td>
</tr><tr><td class=r>1839</td><td>= Music Videos|Jazz|Trad Jazz</td>
</tr><tr><td class=r>1840</td><td>= Music Videos|Latin|Alternative &amp; Rock in Spanish</td>
</tr><tr><td class=r>1841</td><td>= Music Videos|Latin|Baladas y Boleros</td>
</tr><tr><td class=r>1842</td><td>= Music Videos|Latin|Contemporary Latin</td>
</tr><tr><td class=r>1843</td><td>= Music Videos|Latin|Latin Jazz</td>
</tr><tr><td class=r>1844</td><td>= Music Videos|Latin|Latin Urban</td>
</tr><tr><td class=r>1845</td><td>= Music Videos|Latin|Pop in Spanish</td>
</tr><tr><td class=r>1846</td><td>= Music Videos|Latin|Raices</td>
</tr><tr><td class=r>1847</td><td>= Music Videos|Latin|Musica Mexicana</td>
</tr><tr><td class=r>1848</td><td>= Music Videos|Latin|Salsa y Tropical</td>
</tr><tr><td class=r>1849</td><td>= Music Videos|New Age|Healing</td>
</tr><tr><td class=r>1850</td><td>= Music Videos|New Age|Meditation</td>
</tr><tr><td class=r>1851</td><td>= Music Videos|New Age|Nature</td>
</tr><tr><td class=r>1852</td><td>= Music Videos|New Age|Relaxation</td>
</tr><tr><td class=r>1853</td><td>= Music Videos|New Age|Travel</td>
</tr><tr><td class=r>1854</td><td>= Music Videos|Pop|Adult Contemporary</td>
</tr><tr><td class=r>1855</td><td>= Music Videos|Pop|Britpop</td>
</tr><tr><td class=r>1856</td><td>= Music Videos|Pop|Pop/Rock</td>
</tr><tr><td class=r>1857</td><td>= Music Videos|Pop|Soft Rock</td>
</tr><tr><td class=r>1858</td><td>= Music Videos|Pop|Teen Pop</td>
</tr><tr><td class=r>1859</td><td>= Music Videos|R&amp;B/Soul|Contemporary R&amp;B</td>
</tr><tr><td class=r>1860</td><td>= Music Videos|R&amp;B/Soul|Disco</td>
</tr><tr><td class=r>1861</td><td>= Music Videos|R&amp;B/Soul|Doo Wop</td>
</tr><tr><td class=r>1862</td><td>= Music Videos|R&amp;B/Soul|Funk</td>
</tr><tr><td class=r>1863</td><td>= Music Videos|R&amp;B/Soul|Motown</td>
</tr><tr><td class=r>1864</td><td>= Music Videos|R&amp;B/Soul|Neo-Soul</td>
</tr><tr><td class=r>1865</td><td>= Music Videos|R&amp;B/Soul|Soul</td>
</tr><tr><td class=r>1866</td><td>= Music Videos|Reggae|Modern Dancehall</td>
</tr><tr><td class=r>1867</td><td>= Music Videos|Reggae|Dub</td>
</tr><tr><td class=r>1868</td><td>= Music Videos|Reggae|Roots Reggae</td>
</tr><tr><td class=r>1869</td><td>= Music Videos|Reggae|Ska</td>
</tr><tr><td class=r>1870</td><td>= Music Videos|Rock|Adult Alternative</td>
</tr><tr><td class=r>1871</td><td>= Music Videos|Rock|American Trad Rock</td>
</tr><tr><td class=r>1872</td><td>= Music Videos|Rock|Arena Rock</td>
</tr><tr><td class=r>1873</td><td>= Music Videos|Rock|Blues-Rock</td>
</tr><tr><td class=r>1874</td><td>= Music Videos|Rock|British Invasion</td>
</tr><tr><td class=r>1875</td><td>= Music Videos|Rock|Death Metal/Black Metal</td>
</tr><tr><td class=r>1876</td><td>= Music Videos|Rock|Glam Rock</td>
</tr><tr><td class=r>1877</td><td>= Music Videos|Rock|Hair Metal</td>
</tr><tr><td class=r>1878</td><td>= Music Videos|Rock|Hard Rock</td>
</tr><tr><td class=r>1879</td><td>= Music Videos|Rock|Jam Bands</td>
</tr><tr><td class=r>1880</td><td>= Music Videos|Rock|Prog-Rock/Art Rock</td>
</tr><tr><td class=r>1881</td><td>= Music Videos|Rock|Psychedelic</td>
</tr><tr><td class=r>1882</td><td>= Music Videos|Rock|Rock &amp; Roll</td>
</tr><tr><td class=r>1883</td><td>= Music Videos|Rock|Rockabilly</td>
</tr><tr><td class=r>1884</td><td>= Music Videos|Rock|Roots Rock</td>
</tr><tr><td class=r>1885</td><td>= Music Videos|Rock|Singer/Songwriter</td>
</tr><tr><td class=r>1886</td><td>= Music Videos|Rock|Southern Rock</td>
</tr><tr><td class=r>1887</td><td>= Music Videos|Rock|Surf</td>
</tr><tr><td class=r>1888</td><td>= Music Videos|Rock|Tex-Mex</td>
</tr><tr><td class=r>1889</td><td>= Music Videos|Singer/Songwriter|Alternative Folk</td>
</tr><tr><td class=r>1890</td><td>= Music Videos|Singer/Songwriter|Contemporary Folk</td>
</tr><tr><td class=r>1891</td><td>= Music Videos|Singer/Songwriter|Contemporary Singer/Songwriter</td>
</tr><tr><td class=r>1892</td><td>= Music Videos|Singer/Songwriter|Folk-Rock</td>
</tr><tr><td class=r>1893</td><td>= Music Videos|Singer/Songwriter|New Acoustic</td>
</tr><tr><td class=r>1894</td><td>= Music Videos|Singer/Songwriter|Traditional Folk</td>
</tr><tr><td class=r>1895</td><td>= Music Videos|Soundtrack|Foreign Cinema</td>
</tr><tr><td class=r>1896</td><td>= Music Videos|Soundtrack|Musicals</td>
</tr><tr><td class=r>1897</td><td>= Music Videos|Soundtrack|Original Score</td>
</tr><tr><td class=r>1898</td><td>= Music Videos|Soundtrack|Soundtrack</td>
</tr><tr><td class=r>1899</td><td>= Music Videos|Soundtrack|TV Soundtrack</td>
</tr><tr><td class=r>1900</td><td>= Music Videos|Vocal|Standards</td>
</tr><tr><td class=r>1901</td><td>= Music Videos|Vocal|Traditional Pop</td>
</tr><tr><td class=r>1902</td><td>= Music Videos|Jazz|Vocal Jazz</td>
</tr><tr><td class=r>1903</td><td>= Music Videos|Vocal|Vocal Pop</td>
</tr><tr><td class=r>1904</td><td>= Music Videos|African</td>
</tr><tr><td class=r>1905</td><td>= Music Videos|African|Afro-Beat</td>
</tr><tr><td class=r>1906</td><td>= Music Videos|African|Afro-Pop</td>
</tr><tr><td class=r>1907</td><td>= Music Videos|World|Asia</td>
</tr><tr><td class=r>1908</td><td>= Music Videos|World|Australia</td>
</tr><tr><td class=r>1909</td><td>= Music Videos|World|Cajun</td>
</tr><tr><td class=r>1910</td><td>= Music Videos|World|Caribbean</td>
</tr><tr><td class=r>1911</td><td>= Music Videos|World|Celtic</td>
</tr><tr><td class=r>1912</td><td>= Music Videos|World|Celtic Folk</td>
</tr><tr><td class=r>1913</td><td>= Music Videos|World|Contemporary Celtic</td>
</tr><tr><td class=r>1914</td><td>= Music Videos|World|Europe</td>
</tr><tr><td class=r>1915</td><td>= Music Videos|World|France</td>
</tr><tr><td class=r>1916</td><td>= Music Videos|World|Hawaii</td>
</tr><tr><td class=r>1917</td><td>= Music Videos|World|Japan</td>
</tr><tr><td class=r>1918</td><td>= Music Videos|World|Klezmer</td>
</tr><tr><td class=r>1919</td><td>= Music Videos|World|North America</td>
</tr><tr><td class=r>1920</td><td>= Music Videos|World|Polka</td>
</tr><tr><td class=r>1921</td><td>= Music Videos|World|South Africa</td>
</tr><tr><td class=r>1922</td><td>= Music Videos|World|South America</td>
</tr><tr><td class=r>1923</td><td>= Music Videos|World|Traditional Celtic</td>
</tr><tr><td class=r>1924</td><td>= Music Videos|World|Worldbeat</td>
</tr><tr><td class=r>1925</td><td>= Music Videos|World|Zydeco</td>
</tr><tr><td class=r>1926</td><td>= Music Videos|Christian &amp; Gospel</td>
</tr><tr><td class=r>1928</td><td>= Music Videos|Classical|Art Song</td>
</tr><tr><td class=r>1929</td><td>= Music Videos|Classical|Brass &amp; Woodwinds</td>
</tr><tr><td class=r>1930</td><td>= Music Videos|Classical|Solo Instrumental</td>
</tr><tr><td class=r>1931</td><td>= Music Videos|Classical|Contemporary Era</td>
</tr><tr><td class=r>1932</td><td>= Music Videos|Classical|Oratorio</td>
</tr><tr><td class=r>1933</td><td>= Music Videos|Classical|Cantata</td>
</tr><tr><td class=r>1934</td><td>= Music Videos|Classical|Electronic</td>
</tr><tr><td class=r>1935</td><td>= Music Videos|Classical|Sacred</td>
</tr><tr><td class=r>1936</td><td>= Music Videos|Classical|Guitar</td>
</tr><tr><td class=r>1938</td><td>= Music Videos|Classical|Violin</td>
</tr><tr><td class=r>1939</td><td>= Music Videos|Classical|Cello</td>
</tr><tr><td class=r>1940</td><td>= Music Videos|Classical|Percussion</td>
</tr><tr><td class=r>1941</td><td>= Music Videos|Electronic|Dubstep</td>
</tr><tr><td class=r>1942</td><td>= Music Videos|Electronic|Bass</td>
</tr><tr><td class=r>1943</td><td>= Music Videos|Hip-Hop/Rap|UK Hip-Hop</td>
</tr><tr><td class=r>1944</td><td>= Music Videos|Reggae|Lovers Rock</td>
</tr><tr><td class=r>1945</td><td>= Music Videos|Alternative|EMO</td>
</tr><tr><td class=r>1946</td><td>= Music Videos|Alternative|Pop Punk</td>
</tr><tr><td class=r>1947</td><td>= Music Videos|Alternative|Indie Pop</td>
</tr><tr><td class=r>1948</td><td>= Music Videos|New Age|Yoga</td>
</tr><tr><td class=r>1949</td><td>= Music Videos|Pop|Tribute</td>
</tr><tr><td class=r>1950</td><td>= Music Videos|Pop|Shows</td>
</tr><tr><td class=r>1951</td><td>= Music Videos|Cuban</td>
</tr><tr><td class=r>1952</td><td>= Music Videos|Cuban|Mambo</td>
</tr><tr><td class=r>1953</td><td>= Music Videos|Cuban|Chachacha</td>
</tr><tr><td class=r>1954</td><td>= Music Videos|Cuban|Guajira</td>
</tr><tr><td class=r>1955</td><td>= Music Videos|Cuban|Son</td>
</tr><tr><td class=r>1956</td><td>= Music Videos|Cuban|Bolero</td>
</tr><tr><td class=r>1957</td><td>= Music Videos|Cuban|Guaracha</td>
</tr><tr><td class=r>1958</td><td>= Music Videos|Cuban|Timba</td>
</tr><tr><td class=r>1959</td><td>= Music Videos|Soundtrack|Video Game</td>
</tr><tr><td class=r>1960</td><td>= Music Videos|Indian|Regional Indian|Punjabi|Punjabi Pop</td>
</tr><tr><td class=r>1961</td><td>= Music Videos|Indian|Regional Indian|Bengali|Rabindra Sangeet</td>
</tr><tr><td class=r>1962</td><td>= Music Videos|Indian|Regional Indian|Malayalam</td>
</tr><tr><td class=r>1963</td><td>= Music Videos|Indian|Regional Indian|Kannada</td>
</tr><tr><td class=r>1964</td><td>= Music Videos|Indian|Regional Indian|Marathi</td>
</tr><tr><td class=r>1965</td><td>= Music Videos|Indian|Regional Indian|Gujarati</td>
</tr><tr><td class=r>1966</td><td>= Music Videos|Indian|Regional Indian|Assamese</td>
</tr><tr><td class=r>1967</td><td>= Music Videos|Indian|Regional Indian|Bhojpuri</td>
</tr><tr><td class=r>1968</td><td>= Music Videos|Indian|Regional Indian|Haryanvi</td>
</tr><tr><td class=r>1969</td><td>= Music Videos|Indian|Regional Indian|Odia</td>
</tr><tr><td class=r>1970</td><td>= Music Videos|Indian|Regional Indian|Rajasthani</td>
</tr><tr><td class=r>1971</td><td>= Music Videos|Indian|Regional Indian|Urdu</td>
</tr><tr><td class=r>1972</td><td>= Music Videos|Indian|Regional Indian|Punjabi</td>
</tr><tr><td class=r>1973</td><td>= Music Videos|Indian|Regional Indian|Bengali</td>
</tr><tr><td class=r>1974</td><td>= Music Videos|Indian|Indian Classical|Carnatic Classical</td>
</tr><tr><td class=r>1975</td><td>= Music Videos|Indian|Indian Classical|Hindustani Classical</td>
</tr><tr><td class=r>1976</td><td>= Music Videos|African|Afro House</td>
</tr><tr><td class=r>1977</td><td>= Music Videos|African|Afro Soul</td>
</tr><tr><td class=r>1978</td><td>= Music Videos|African|Afrobeats</td>
</tr><tr><td class=r>1979</td><td>= Music Videos|African|Benga</td>
</tr><tr><td class=r>1980</td><td>= Music Videos|African|Bongo-Flava</td>
</tr><tr><td class=r>1981</td><td>= Music Videos|African|Coupe-Decale</td>
</tr><tr><td class=r>1982</td><td>= Music Videos|African|Gqom</td>
</tr><tr><td class=r>1983</td><td>= Music Videos|African|Highlife</td>
</tr><tr><td class=r>1984</td><td>= Music Videos|African|Kuduro</td>
</tr><tr><td class=r>1985</td><td>= Music Videos|African|Kizomba</td>
</tr><tr><td class=r>1986</td><td>= Music Videos|African|Kwaito</td>
</tr><tr><td class=r>1987</td><td>= Music Videos|African|Mbalax</td>
</tr><tr><td class=r>1988</td><td>= Music Videos|African|Ndombolo</td>
</tr><tr><td class=r>1989</td><td>= Music Videos|African|Shangaan Electro</td>
</tr><tr><td class=r>1990</td><td>= Music Videos|African|Soukous</td>
</tr><tr><td class=r>1991</td><td>= Music Videos|African|Taarab</td>
</tr><tr><td class=r>1992</td><td>= Music Videos|African|Zouglou</td>
</tr><tr><td class=r>1993</td><td>= Music Videos|Turkish|Ozgun</td>
</tr><tr><td class=r>1994</td><td>= Music Videos|Turkish|Fantezi</td>
</tr><tr><td class=r>1995</td><td>= Music Videos|Turkish|Religious</td>
</tr><tr><td class=r>1996</td><td>= Music Videos|Pop|Turkish Pop</td>
</tr><tr><td class=r>1997</td><td>= Music Videos|Rock|Turkish Rock</td>
</tr><tr><td class=r>1998</td><td>= Music Videos|Alternative|Turkish Alternative</td>
</tr><tr><td class=r>1999</td><td>= Music Videos|Hip-Hop/Rap|Turkish Hip-Hop/Rap</td>
</tr><tr><td class=r>2000</td><td>= Music Videos|African|Maskandi</td>
</tr><tr><td class=r>2001</td><td>= Music Videos|Russian|Russian Romance</td>
</tr><tr><td class=r>2002</td><td>= Music Videos|Russian|Russian Bard</td>
</tr><tr><td class=r>2003</td><td>= Music Videos|Russian|Russian Pop</td>
</tr><tr><td class=r>2004</td><td>= Music Videos|Russian|Russian Rock</td>
</tr><tr><td class=r>2005</td><td>= Music Videos|Russian|Russian Hip-Hop</td>
</tr><tr><td class=r>2006</td><td>= Music Videos|Arabic|Levant</td>
</tr><tr><td class=r>2007</td><td>= Music Videos|Arabic|Levant|Dabke</td>
</tr><tr><td class=r>2008</td><td>= Music Videos|Arabic|Maghreb Rai</td>
</tr><tr><td class=r>2009</td><td>= Music Videos|Arabic|Khaleeji|Khaleeji Jalsat</td>
</tr><tr><td class=r>2010</td><td>= Music Videos|Arabic|Khaleeji|Khaleeji Shailat</td>
</tr><tr><td class=r>2011</td><td>= Music Videos|Tarab</td>
</tr><tr><td class=r>2012</td><td>= Music Videos|Tarab|Iraqi Tarab</td>
</tr><tr><td class=r>2013</td><td>= Music Videos|Tarab|Egyptian Tarab</td>
</tr><tr><td class=r>2014</td><td>= Music Videos|Tarab|Khaleeji Tarab</td>
</tr><tr><td class=r>2015</td><td>= Music Videos|Pop|Levant Pop</td>
</tr><tr><td class=r>2016</td><td>= Music Videos|Pop|Iraqi Pop</td>
</tr><tr><td class=r>2017</td><td>= Music Videos|Pop|Egyptian Pop</td>
</tr><tr><td class=r>2018</td><td>= Music Videos|Pop|Maghreb Pop</td>
</tr><tr><td class=r>2019</td><td>= Music Videos|Pop|Khaleeji Pop</td>
</tr><tr><td class=r>2020</td><td>= Music Videos|Hip-Hop/Rap|Levant Hip-Hop</td>
</tr><tr><td class=r>2021</td><td>= Music Videos|Hip-Hop/Rap|Egyptian Hip-Hop</td>
</tr><tr><td class=r>2022</td><td>= Music Videos|Hip-Hop/Rap|Maghreb Hip-Hop</td>
</tr><tr><td class=r>2023</td><td>= Music Videos|Hip-Hop/Rap|Khaleeji Hip-Hop</td>
</tr><tr><td class=r>2024</td><td>= Music Videos|Alternative|Indie Levant</td>
</tr><tr><td class=r>2025</td><td>= Music Videos|Alternative|Indie Egyptian</td>
</tr><tr><td class=r>2026</td><td>= Music Videos|Alternative|Indie Maghreb</td>
</tr><tr><td class=r>2027</td><td>= Music Videos|Electronic|Levant Electronic</td>
</tr><tr><td class=r>2028</td><td>= Music Videos|Electronic|Electro-Cha&#39;abi</td>
</tr><tr><td class=r>2029</td><td>= Music Videos|Electronic|Maghreb Electronic</td>
</tr><tr><td class=r>2030</td><td>= Music Videos|Folk|Iraqi Folk</td>
</tr><tr><td class=r>2031</td><td>= Music Videos|Folk|Khaleeji Folk</td>
</tr><tr><td class=r>2032</td><td>= Music Videos|Dance|Maghreb Dance</td>
</tr><tr><td class=r>4000</td><td>= TV Shows|Comedy</td>
</tr><tr><td class=r>4001</td><td>= TV Shows|Drama</td>
</tr><tr><td class=r>4002</td><td>= TV Shows|Animation</td>
</tr><tr><td class=r>4003</td><td>= TV Shows|Action &amp; Adventure</td>
</tr><tr><td class=r>4004</td><td>= TV Shows|Classics</td>
</tr><tr><td class=r>4005</td><td>= TV Shows|Kids &amp; Family</td>
</tr><tr><td class=r>4006</td><td>= TV Shows|Nonfiction</td>
</tr><tr><td class=r>4007</td><td>= TV Shows|Reality TV</td>
</tr><tr><td class=r>4008</td><td>= TV Shows|Sci-Fi &amp; Fantasy</td>
</tr><tr><td class=r>4009</td><td>= TV Shows|Sports</td>
</tr><tr><td class=r>4010</td><td>= TV Shows|Teens</td>
</tr><tr><td class=r>4011</td><td>= TV Shows|Latino TV</td>
</tr><tr><td class=r>4401</td><td>= Movies|Action &amp; Adventure</td>
</tr><tr><td class=r>4402</td><td>= Movies|Anime</td>
</tr><tr><td class=r>4403</td><td>= Movies|Classics</td>
</tr><tr><td class=r>4404</td><td>= Movies|Comedy</td>
</tr><tr><td class=r>4405</td><td>= Movies|Documentary</td>
</tr><tr><td class=r>4406</td><td>= Movies|Drama</td>
</tr><tr><td class=r>4407</td><td>= Movies|Foreign</td>
</tr><tr><td class=r>4408</td><td>= Movies|Horror</td>
</tr><tr><td class=r>4409</td><td>= Movies|Independent</td>
</tr><tr><td class=r>4410</td><td>= Movies|Kids &amp; Family</td>
</tr><tr><td class=r>4411</td><td>= Movies|Musicals</td>
</tr><tr><td class=r>4412</td><td>= Movies|Romance</td>
</tr><tr><td class=r>4413</td><td>= Movies|Sci-Fi &amp; Fantasy</td>
</tr><tr><td class=r>4414</td><td>= Movies|Short Films</td>
</tr><tr><td class=r>4415</td><td>= Movies|Special Interest</td>
</tr><tr><td class=r>4416</td><td>= Movies|Thriller</td>
</tr><tr><td class=r>4417</td><td>= Movies|Sports</td>
</tr><tr><td class=r>4418</td><td>= Movies|Western</td>
</tr><tr><td class=r>4419</td><td>= Movies|Urban</td>
</tr><tr><td class=r>4420</td><td>= Movies|Holiday</td>
</tr><tr><td class=r>4421</td><td>= Movies|Made for TV</td>
</tr><tr><td class=r>4422</td><td>= Movies|Concert Films</td>
</tr><tr><td class=r>4423</td><td>= Movies|Music Documentaries</td>
</tr><tr><td class=r>4424</td><td>= Movies|Music Feature Films</td>
</tr><tr><td class=r>4425</td><td>= Movies|Japanese Cinema</td>
</tr><tr><td class=r>4426</td><td>= Movies|Jidaigeki</td>
</tr><tr><td class=r>4427</td><td>= Movies|Tokusatsu</td>
</tr><tr><td class=r>4428</td><td>= Movies|Korean Cinema</td>
</tr><tr><td class=r>4429</td><td>= Movies|Russian</td>
</tr><tr><td class=r>4430</td><td>= Movies|Turkish</td>
</tr><tr><td class=r>4431</td><td>= Movies|Bollywood</td>
</tr><tr><td class=r>4432</td><td>= Movies|Regional Indian</td>
</tr><tr><td class=r>4433</td><td>= Movies|Middle Eastern</td>
</tr><tr><td class=r>4434</td><td>= Movies|African</td>
</tr><tr><td class=r>6000</td><td>= App Store|Business</td>
</tr><tr><td class=r>6001</td><td>= App Store|Weather</td>
</tr><tr><td class=r>6002</td><td>= App Store|Utilities</td>
</tr><tr><td class=r>6003</td><td>= App Store|Travel</td>
</tr><tr><td class=r>6004</td><td>= App Store|Sports</td>
</tr><tr><td class=r>6005</td><td>= App Store|Social Networking</td>
</tr><tr><td class=r>6006</td><td>= App Store|Reference</td>
</tr><tr><td class=r>6007</td><td>= App Store|Productivity</td>
</tr><tr><td class=r>6008</td><td>= App Store|Photo &amp; Video</td>
</tr><tr><td class=r>6009</td><td>= App Store|News</td>
</tr><tr><td class=r>6010</td><td>= App Store|Navigation</td>
</tr><tr><td class=r>6011</td><td>= App Store|Music</td>
</tr><tr><td class=r>6012</td><td>= App Store|Lifestyle</td>
</tr><tr><td class=r>6013</td><td>= App Store|Health &amp; Fitness</td>
</tr><tr><td class=r>6014</td><td>= App Store|Games</td>
</tr><tr><td class=r>6015</td><td>= App Store|Finance</td>
</tr><tr><td class=r>6016</td><td>= App Store|Entertainment</td>
</tr><tr><td class=r>6017</td><td>= App Store|Education</td>
</tr><tr><td class=r>6018</td><td>= App Store|Books</td>
</tr><tr><td class=r>6020</td><td>= App Store|Medical</td>
</tr><tr><td class=r>6021</td><td>= App Store|Magazines &amp; Newspapers</td>
</tr><tr><td class=r>6022</td><td>= App Store|Catalogs</td>
</tr><tr><td class=r>6023</td><td>= App Store|Food &amp; Drink</td>
</tr><tr><td class=r>6024</td><td>= App Store|Shopping</td>
</tr><tr><td class=r>6025</td><td>= App Store|Stickers</td>
</tr><tr><td class=r>6026</td><td>= App Store|Developer Tools</td>
</tr><tr><td class=r>6027</td><td>= App Store|Graphics &amp; Design</td>
</tr><tr><td class=r>7001</td><td>= App Store|Games|Action</td>
</tr><tr><td class=r>7002</td><td>= App Store|Games|Adventure</td>
</tr><tr><td class=r>7003</td><td>= App Store|Games|Casual</td>
</tr><tr><td class=r>7004</td><td>= App Store|Games|Board</td>
</tr><tr><td class=r>7005</td><td>= App Store|Games|Card</td>
</tr><tr><td class=r>7006</td><td>= App Store|Games|Casino</td>
</tr><tr><td class=r>7007</td><td>= App Store|Games|Dice</td>
</tr><tr><td class=r>7008</td><td>= App Store|Games|Educational</td>
</tr><tr><td class=r>7009</td><td>= App Store|Games|Family</td>
</tr><tr><td class=r>7011</td><td>= App Store|Games|Music</td>
</tr><tr><td class=r>7012</td><td>= App Store|Games|Puzzle</td>
</tr><tr><td class=r>7013</td><td>= App Store|Games|Racing</td>
</tr><tr><td class=r>7014</td><td>= App Store|Games|Role Playing</td>
</tr><tr><td class=r>7015</td><td>= App Store|Games|Simulation</td>
</tr><tr><td class=r>7016</td><td>= App Store|Games|Sports</td>
</tr><tr><td class=r>7017</td><td>= App Store|Games|Strategy</td>
</tr><tr><td class=r>7018</td><td>= App Store|Games|Trivia</td>
</tr><tr><td class=r>7019</td><td>= App Store|Games|Word</td>
</tr><tr><td class=r>8001</td><td>= Tones|Ringtones|Alternative</td>
</tr><tr><td class=r>8002</td><td>= Tones|Ringtones|Blues</td>
</tr><tr><td class=r>8003</td><td>= Tones|Ringtones|Children&#39;s Music</td>
</tr><tr><td class=r>8004</td><td>= Tones|Ringtones|Classical</td>
</tr><tr><td class=r>8005</td><td>= Tones|Ringtones|Comedy</td>
</tr><tr><td class=r>8006</td><td>= Tones|Ringtones|Country</td>
</tr><tr><td class=r>8007</td><td>= Tones|Ringtones|Dance</td>
</tr><tr><td class=r>8008</td><td>= Tones|Ringtones|Electronic</td>
</tr><tr><td class=r>8009</td><td>= Tones|Ringtones|Enka</td>
</tr><tr><td class=r>8010</td><td>= Tones|Ringtones|French Pop</td>
</tr><tr><td class=r>8011</td><td>= Tones|Ringtones|German Folk</td>
</tr><tr><td class=r>8012</td><td>= Tones|Ringtones|German Pop</td>
</tr><tr><td class=r>8013</td><td>= Tones|Ringtones|Hip-Hop/Rap</td>
</tr><tr><td class=r>8014</td><td>= Tones|Ringtones|Holiday</td>
</tr><tr><td class=r>8015</td><td>= Tones|Ringtones|Inspirational</td>
</tr><tr><td class=r>8016</td><td>= Tones|Ringtones|J-Pop</td>
</tr><tr><td class=r>8017</td><td>= Tones|Ringtones|Jazz</td>
</tr><tr><td class=r>8018</td><td>= Tones|Ringtones|Kayokyoku</td>
</tr><tr><td class=r>8019</td><td>= Tones|Ringtones|Latin</td>
</tr><tr><td class=r>8020</td><td>= Tones|Ringtones|New Age</td>
</tr><tr><td class=r>8021</td><td>= Tones|Ringtones|Classical|Opera</td>
</tr><tr><td class=r>8022</td><td>= Tones|Ringtones|Pop</td>
</tr><tr><td class=r>8023</td><td>= Tones|Ringtones|R&amp;B/Soul</td>
</tr><tr><td class=r>8024</td><td>= Tones|Ringtones|Reggae</td>
</tr><tr><td class=r>8025</td><td>= Tones|Ringtones|Rock</td>
</tr><tr><td class=r>8026</td><td>= Tones|Ringtones|Singer/Songwriter</td>
</tr><tr><td class=r>8027</td><td>= Tones|Ringtones|Soundtrack</td>
</tr><tr><td class=r>8028</td><td>= Tones|Ringtones|Spoken Word</td>
</tr><tr><td class=r>8029</td><td>= Tones|Ringtones|Vocal</td>
</tr><tr><td class=r>8030</td><td>= Tones|Ringtones|World</td>
</tr><tr><td class=r>8050</td><td>= Tones|Alert Tones|Sound Effects</td>
</tr><tr><td class=r>8051</td><td>= Tones|Alert Tones|Dialogue</td>
</tr><tr><td class=r>8052</td><td>= Tones|Alert Tones|Music</td>
</tr><tr><td class=r>8053</td><td>= Tones|Ringtones</td>
</tr><tr><td class=r>8054</td><td>= Tones|Alert Tones</td>
</tr><tr><td class=r>8055</td><td>= Tones|Ringtones|Alternative|Chinese Alt</td>
</tr><tr><td class=r>8056</td><td>= Tones|Ringtones|Alternative|College Rock</td>
</tr><tr><td class=r>8057</td><td>= Tones|Ringtones|Alternative|Goth Rock</td>
</tr><tr><td class=r>8058</td><td>= Tones|Ringtones|Alternative|Grunge</td>
</tr><tr><td class=r>8059</td><td>= Tones|Ringtones|Alternative|Indie Rock</td>
</tr><tr><td class=r>8060</td><td>= Tones|Ringtones|Alternative|Korean Indie</td>
</tr><tr><td class=r>8061</td><td>= Tones|Ringtones|Alternative|New Wave</td>
</tr><tr><td class=r>8062</td><td>= Tones|Ringtones|Alternative|Punk</td>
</tr><tr><td class=r>8063</td><td>= Tones|Ringtones|Anime</td>
</tr><tr><td class=r>8064</td><td>= Tones|Ringtones|Arabic</td>
</tr><tr><td class=r>8065</td><td>= Tones|Ringtones|Arabic|Arabic Pop</td>
</tr><tr><td class=r>8066</td><td>= Tones|Ringtones|Arabic|Islamic</td>
</tr><tr><td class=r>8067</td><td>= Tones|Ringtones|Arabic|Khaleeji</td>
</tr><tr><td class=r>8068</td><td>= Tones|Ringtones|Arabic|North African</td>
</tr><tr><td class=r>8069</td><td>= Tones|Ringtones|Blues|Acoustic Blues</td>
</tr><tr><td class=r>8070</td><td>= Tones|Ringtones|Blues|Chicago Blues</td>
</tr><tr><td class=r>8071</td><td>= Tones|Ringtones|Blues|Classic Blues</td>
</tr><tr><td class=r>8072</td><td>= Tones|Ringtones|Blues|Contemporary Blues</td>
</tr><tr><td class=r>8073</td><td>= Tones|Ringtones|Blues|Country Blues</td>
</tr><tr><td class=r>8074</td><td>= Tones|Ringtones|Blues|Delta Blues</td>
</tr><tr><td class=r>8075</td><td>= Tones|Ringtones|Blues|Electric Blues</td>
</tr><tr><td class=r>8076</td><td>= Tones|Ringtones|Brazilian</td>
</tr><tr><td class=r>8077</td><td>= Tones|Ringtones|Brazilian|Axe</td>
</tr><tr><td class=r>8078</td><td>= Tones|Ringtones|Brazilian|Baile Funk</td>
</tr><tr><td class=r>8079</td><td>= Tones|Ringtones|Brazilian|Bossa Nova</td>
</tr><tr><td class=r>8080</td><td>= Tones|Ringtones|Brazilian|Choro</td>
</tr><tr><td class=r>8081</td><td>= Tones|Ringtones|Brazilian|Forro</td>
</tr><tr><td class=r>8082</td><td>= Tones|Ringtones|Brazilian|Frevo</td>
</tr><tr><td class=r>8083</td><td>= Tones|Ringtones|Brazilian|MPB</td>
</tr><tr><td class=r>8084</td><td>= Tones|Ringtones|Brazilian|Pagode</td>
</tr><tr><td class=r>8085</td><td>= Tones|Ringtones|Brazilian|Samba</td>
</tr><tr><td class=r>8086</td><td>= Tones|Ringtones|Brazilian|Sertanejo</td>
</tr><tr><td class=r>8087</td><td>= Tones|Ringtones|Children&#39;s Music|Lullabies</td>
</tr><tr><td class=r>8088</td><td>= Tones|Ringtones|Children&#39;s Music|Sing-Along</td>
</tr><tr><td class=r>8089</td><td>= Tones|Ringtones|Children&#39;s Music|Stories</td>
</tr><tr><td class=r>8090</td><td>= Tones|Ringtones|Chinese</td>
</tr><tr><td class=r>8091</td><td>= Tones|Ringtones|Chinese|Chinese Classical</td>
</tr><tr><td class=r>8092</td><td>= Tones|Ringtones|Chinese|Chinese Flute</td>
</tr><tr><td class=r>8093</td><td>= Tones|Ringtones|Chinese|Chinese Opera</td>
</tr><tr><td class=r>8094</td><td>= Tones|Ringtones|Chinese|Chinese Orchestral</td>
</tr><tr><td class=r>8095</td><td>= Tones|Ringtones|Chinese|Chinese Regional Folk</td>
</tr><tr><td class=r>8096</td><td>= Tones|Ringtones|Chinese|Chinese Strings</td>
</tr><tr><td class=r>8097</td><td>= Tones|Ringtones|Chinese|Taiwanese Folk</td>
</tr><tr><td class=r>8098</td><td>= Tones|Ringtones|Chinese|Tibetan Native Music</td>
</tr><tr><td class=r>8099</td><td>= Tones|Ringtones|Christian &amp; Gospel</td>
</tr><tr><td class=r>8100</td><td>= Tones|Ringtones|Christian &amp; Gospel|CCM</td>
</tr><tr><td class=r>8101</td><td>= Tones|Ringtones|Christian &amp; Gospel|Christian Metal</td>
</tr><tr><td class=r>8102</td><td>= Tones|Ringtones|Christian &amp; Gospel|Christian Pop</td>
</tr><tr><td class=r>8103</td><td>= Tones|Ringtones|Christian &amp; Gospel|Christian Rap</td>
</tr><tr><td class=r>8104</td><td>= Tones|Ringtones|Christian &amp; Gospel|Christian Rock</td>
</tr><tr><td class=r>8105</td><td>= Tones|Ringtones|Christian &amp; Gospel|Classic Christian</td>
</tr><tr><td class=r>8106</td><td>= Tones|Ringtones|Christian &amp; Gospel|Contemporary Gospel</td>
</tr><tr><td class=r>8107</td><td>= Tones|Ringtones|Christian &amp; Gospel|Gospel</td>
</tr><tr><td class=r>8108</td><td>= Tones|Ringtones|Christian &amp; Gospel|Praise &amp; Worship</td>
</tr><tr><td class=r>8109</td><td>= Tones|Ringtones|Christian &amp; Gospel|Southern Gospel</td>
</tr><tr><td class=r>8110</td><td>= Tones|Ringtones|Christian &amp; Gospel|Traditional Gospel</td>
</tr><tr><td class=r>8111</td><td>= Tones|Ringtones|Classical|Avant-Garde</td>
</tr><tr><td class=r>8112</td><td>= Tones|Ringtones|Classical|Baroque Era</td>
</tr><tr><td class=r>8113</td><td>= Tones|Ringtones|Classical|Chamber Music</td>
</tr><tr><td class=r>8114</td><td>= Tones|Ringtones|Classical|Chant</td>
</tr><tr><td class=r>8115</td><td>= Tones|Ringtones|Classical|Choral</td>
</tr><tr><td class=r>8116</td><td>= Tones|Ringtones|Classical|Classical Crossover</td>
</tr><tr><td class=r>8117</td><td>= Tones|Ringtones|Classical|Early Music</td>
</tr><tr><td class=r>8118</td><td>= Tones|Ringtones|Classical|High Classical</td>
</tr><tr><td class=r>8119</td><td>= Tones|Ringtones|Classical|Impressionist</td>
</tr><tr><td class=r>8120</td><td>= Tones|Ringtones|Classical|Medieval Era</td>
</tr><tr><td class=r>8121</td><td>= Tones|Ringtones|Classical|Minimalism</td>
</tr><tr><td class=r>8122</td><td>= Tones|Ringtones|Classical|Modern Era</td>
</tr><tr><td class=r>8123</td><td>= Tones|Ringtones|Classical|Orchestral</td>
</tr><tr><td class=r>8124</td><td>= Tones|Ringtones|Classical|Renaissance</td>
</tr><tr><td class=r>8125</td><td>= Tones|Ringtones|Classical|Romantic Era</td>
</tr><tr><td class=r>8126</td><td>= Tones|Ringtones|Classical|Wedding Music</td>
</tr><tr><td class=r>8127</td><td>= Tones|Ringtones|Comedy|Novelty</td>
</tr><tr><td class=r>8128</td><td>= Tones|Ringtones|Comedy|Standup Comedy</td>
</tr><tr><td class=r>8129</td><td>= Tones|Ringtones|Country|Alternative Country</td>
</tr><tr><td class=r>8130</td><td>= Tones|Ringtones|Country|Americana</td>
</tr><tr><td class=r>8131</td><td>= Tones|Ringtones|Country|Bluegrass</td>
</tr><tr><td class=r>8132</td><td>= Tones|Ringtones|Country|Contemporary Bluegrass</td>
</tr><tr><td class=r>8133</td><td>= Tones|Ringtones|Country|Contemporary Country</td>
</tr><tr><td class=r>8134</td><td>= Tones|Ringtones|Country|Country Gospel</td>
</tr><tr><td class=r>8135</td><td>= Tones|Ringtones|Country|Honky Tonk</td>
</tr><tr><td class=r>8136</td><td>= Tones|Ringtones|Country|Outlaw Country</td>
</tr><tr><td class=r>8137</td><td>= Tones|Ringtones|Country|Thai Country</td>
</tr><tr><td class=r>8138</td><td>= Tones|Ringtones|Country|Traditional Bluegrass</td>
</tr><tr><td class=r>8139</td><td>= Tones|Ringtones|Country|Traditional Country</td>
</tr><tr><td class=r>8140</td><td>= Tones|Ringtones|Country|Urban Cowboy</td>
</tr><tr><td class=r>8141</td><td>= Tones|Ringtones|Dance|Breakbeat</td>
</tr><tr><td class=r>8142</td><td>= Tones|Ringtones|Dance|Exercise</td>
</tr><tr><td class=r>8143</td><td>= Tones|Ringtones|Dance|Garage</td>
</tr><tr><td class=r>8144</td><td>= Tones|Ringtones|Dance|Hardcore</td>
</tr><tr><td class=r>8145</td><td>= Tones|Ringtones|Dance|House</td>
</tr><tr><td class=r>8146</td><td>= Tones|Ringtones|Dance|Jungle/Drum&#39;n&#39;bass</td>
</tr><tr><td class=r>8147</td><td>= Tones|Ringtones|Dance|Techno</td>
</tr><tr><td class=r>8148</td><td>= Tones|Ringtones|Dance|Trance</td>
</tr><tr><td class=r>8149</td><td>= Tones|Ringtones|Disney</td>
</tr><tr><td class=r>8150</td><td>= Tones|Ringtones|Easy Listening</td>
</tr><tr><td class=r>8151</td><td>= Tones|Ringtones|Easy Listening|Lounge</td>
</tr><tr><td class=r>8152</td><td>= Tones|Ringtones|Easy Listening|Swing</td>
</tr><tr><td class=r>8153</td><td>= Tones|Ringtones|Electronic|Ambient</td>
</tr><tr><td class=r>8154</td><td>= Tones|Ringtones|Electronic|Downtempo</td>
</tr><tr><td class=r>8155</td><td>= Tones|Ringtones|Electronic|Electronica</td>
</tr><tr><td class=r>8156</td><td>= Tones|Ringtones|Electronic|IDM/Experimental</td>
</tr><tr><td class=r>8157</td><td>= Tones|Ringtones|Electronic|Industrial</td>
</tr><tr><td class=r>8158</td><td>= Tones|Ringtones|Fitness &amp; Workout</td>
</tr><tr><td class=r>8159</td><td>= Tones|Ringtones|Folk</td>
</tr><tr><td class=r>8160</td><td>= Tones|Ringtones|Hip-Hop/Rap|Alternative Rap</td>
</tr><tr><td class=r>8161</td><td>= Tones|Ringtones|Hip-Hop/Rap|Chinese Hip-Hop</td>
</tr><tr><td class=r>8162</td><td>= Tones|Ringtones|Hip-Hop/Rap|Dirty South</td>
</tr><tr><td class=r>8163</td><td>= Tones|Ringtones|Hip-Hop/Rap|East Coast Rap</td>
</tr><tr><td class=r>8164</td><td>= Tones|Ringtones|Hip-Hop/Rap|Gangsta Rap</td>
</tr><tr><td class=r>8165</td><td>= Tones|Ringtones|Hip-Hop/Rap|Hardcore Rap</td>
</tr><tr><td class=r>8166</td><td>= Tones|Ringtones|Hip-Hop/Rap|Hip-Hop</td>
</tr><tr><td class=r>8167</td><td>= Tones|Ringtones|Hip-Hop/Rap|Korean Hip-Hop</td>
</tr><tr><td class=r>8168</td><td>= Tones|Ringtones|Hip-Hop/Rap|Latin Rap</td>
</tr><tr><td class=r>8169</td><td>= Tones|Ringtones|Hip-Hop/Rap|Old School Rap</td>
</tr><tr><td class=r>8170</td><td>= Tones|Ringtones|Hip-Hop/Rap|Rap</td>
</tr><tr><td class=r>8171</td><td>= Tones|Ringtones|Hip-Hop/Rap|Underground Rap</td>
</tr><tr><td class=r>8172</td><td>= Tones|Ringtones|Hip-Hop/Rap|West Coast Rap</td>
</tr><tr><td class=r>8173</td><td>= Tones|Ringtones|Holiday|Chanukah</td>
</tr><tr><td class=r>8174</td><td>= Tones|Ringtones|Holiday|Christmas</td>
</tr><tr><td class=r>8175</td><td>= Tones|Ringtones|Holiday|Christmas: Children&#39;s</td>
</tr><tr><td class=r>8176</td><td>= Tones|Ringtones|Holiday|Christmas: Classic</td>
</tr><tr><td class=r>8177</td><td>= Tones|Ringtones|Holiday|Christmas: Classical</td>
</tr><tr><td class=r>8178</td><td>= Tones|Ringtones|Holiday|Christmas: Jazz</td>
</tr><tr><td class=r>8179</td><td>= Tones|Ringtones|Holiday|Christmas: Modern</td>
</tr><tr><td class=r>8180</td><td>= Tones|Ringtones|Holiday|Christmas: Pop</td>
</tr><tr><td class=r>8181</td><td>= Tones|Ringtones|Holiday|Christmas: R&amp;B</td>
</tr><tr><td class=r>8182</td><td>= Tones|Ringtones|Holiday|Christmas: Religious</td>
</tr><tr><td class=r>8183</td><td>= Tones|Ringtones|Holiday|Christmas: Rock</td>
</tr><tr><td class=r>8184</td><td>= Tones|Ringtones|Holiday|Easter</td>
</tr><tr><td class=r>8185</td><td>= Tones|Ringtones|Holiday|Halloween</td>
</tr><tr><td class=r>8186</td><td>= Tones|Ringtones|Holiday|Thanksgiving</td>
</tr><tr><td class=r>8187</td><td>= Tones|Ringtones|Indian</td>
</tr><tr><td class=r>8188</td><td>= Tones|Ringtones|Indian|Bollywood</td>
</tr><tr><td class=r>8189</td><td>= Tones|Ringtones|Indian|Devotional &amp; Spiritual</td>
</tr><tr><td class=r>8190</td><td>= Tones|Ringtones|Indian|Ghazals</td>
</tr><tr><td class=r>8191</td><td>= Tones|Ringtones|Indian|Indian Classical</td>
</tr><tr><td class=r>8192</td><td>= Tones|Ringtones|Indian|Indian Folk</td>
</tr><tr><td class=r>8193</td><td>= Tones|Ringtones|Indian|Indian Pop</td>
</tr><tr><td class=r>8194</td><td>= Tones|Ringtones|Indian|Regional Indian</td>
</tr><tr><td class=r>8195</td><td>= Tones|Ringtones|Indian|Sufi</td>
</tr><tr><td class=r>8196</td><td>= Tones|Ringtones|Indian|Regional Indian|Tamil</td>
</tr><tr><td class=r>8197</td><td>= Tones|Ringtones|Indian|Regional Indian|Telugu</td>
</tr><tr><td class=r>8198</td><td>= Tones|Ringtones|Instrumental</td>
</tr><tr><td class=r>8199</td><td>= Tones|Ringtones|Jazz|Avant-Garde Jazz</td>
</tr><tr><td class=r>8201</td><td>= Tones|Ringtones|Jazz|Big Band</td>
</tr><tr><td class=r>8202</td><td>= Tones|Ringtones|Jazz|Bop</td>
</tr><tr><td class=r>8203</td><td>= Tones|Ringtones|Jazz|Contemporary Jazz</td>
</tr><tr><td class=r>8204</td><td>= Tones|Ringtones|Jazz|Cool Jazz</td>
</tr><tr><td class=r>8205</td><td>= Tones|Ringtones|Jazz|Crossover Jazz</td>
</tr><tr><td class=r>8206</td><td>= Tones|Ringtones|Jazz|Dixieland</td>
</tr><tr><td class=r>8207</td><td>= Tones|Ringtones|Jazz|Fusion</td>
</tr><tr><td class=r>8208</td><td>= Tones|Ringtones|Jazz|Hard Bop</td>
</tr><tr><td class=r>8209</td><td>= Tones|Ringtones|Jazz|Latin Jazz</td>
</tr><tr><td class=r>8210</td><td>= Tones|Ringtones|Jazz|Mainstream Jazz</td>
</tr><tr><td class=r>8211</td><td>= Tones|Ringtones|Jazz|Ragtime</td>
</tr><tr><td class=r>8212</td><td>= Tones|Ringtones|Jazz|Smooth Jazz</td>
</tr><tr><td class=r>8213</td><td>= Tones|Ringtones|Jazz|Trad Jazz</td>
</tr><tr><td class=r>8214</td><td>= Tones|Ringtones|Pop|K-Pop</td>
</tr><tr><td class=r>8215</td><td>= Tones|Ringtones|Karaoke</td>
</tr><tr><td class=r>8216</td><td>= Tones|Ringtones|Korean</td>
</tr><tr><td class=r>8217</td><td>= Tones|Ringtones|Korean|Korean Classical</td>
</tr><tr><td class=r>8218</td><td>= Tones|Ringtones|Korean|Korean Trad Instrumental</td>
</tr><tr><td class=r>8219</td><td>= Tones|Ringtones|Korean|Korean Trad Song</td>
</tr><tr><td class=r>8220</td><td>= Tones|Ringtones|Korean|Korean Trad Theater</td>
</tr><tr><td class=r>8221</td><td>= Tones|Ringtones|Latin|Alternative &amp; Rock in Spanish</td>
</tr><tr><td class=r>8222</td><td>= Tones|Ringtones|Latin|Baladas y Boleros</td>
</tr><tr><td class=r>8223</td><td>= Tones|Ringtones|Latin|Contemporary Latin</td>
</tr><tr><td class=r>8224</td><td>= Tones|Ringtones|Latin|Latin Jazz</td>
</tr><tr><td class=r>8225</td><td>= Tones|Ringtones|Latin|Latin Urban</td>
</tr><tr><td class=r>8226</td><td>= Tones|Ringtones|Latin|Pop in Spanish</td>
</tr><tr><td class=r>8227</td><td>= Tones|Ringtones|Latin|Raices</td>
</tr><tr><td class=r>8228</td><td>= Tones|Ringtones|Latin|Musica Mexicana</td>
</tr><tr><td class=r>8229</td><td>= Tones|Ringtones|Latin|Salsa y Tropical</td>
</tr><tr><td class=r>8230</td><td>= Tones|Ringtones|Marching Bands</td>
</tr><tr><td class=r>8231</td><td>= Tones|Ringtones|New Age|Healing</td>
</tr><tr><td class=r>8232</td><td>= Tones|Ringtones|New Age|Meditation</td>
</tr><tr><td class=r>8233</td><td>= Tones|Ringtones|New Age|Nature</td>
</tr><tr><td class=r>8234</td><td>= Tones|Ringtones|New Age|Relaxation</td>
</tr><tr><td class=r>8235</td><td>= Tones|Ringtones|New Age|Travel</td>
</tr><tr><td class=r>8236</td><td>= Tones|Ringtones|Orchestral</td>
</tr><tr><td class=r>8237</td><td>= Tones|Ringtones|Pop|Adult Contemporary</td>
</tr><tr><td class=r>8238</td><td>= Tones|Ringtones|Pop|Britpop</td>
</tr><tr><td class=r>8239</td><td>= Tones|Ringtones|Pop|C-Pop</td>
</tr><tr><td class=r>8240</td><td>= Tones|Ringtones|Pop|Cantopop/HK-Pop</td>
</tr><tr><td class=r>8241</td><td>= Tones|Ringtones|Pop|Indo Pop</td>
</tr><tr><td class=r>8242</td><td>= Tones|Ringtones|Pop|Korean Folk-Pop</td>
</tr><tr><td class=r>8243</td><td>= Tones|Ringtones|Pop|Malaysian Pop</td>
</tr><tr><td class=r>8244</td><td>= Tones|Ringtones|Pop|Mandopop</td>
</tr><tr><td class=r>8245</td><td>= Tones|Ringtones|Pop|Manilla Sound</td>
</tr><tr><td class=r>8246</td><td>= Tones|Ringtones|Pop|Oldies</td>
</tr><tr><td class=r>8247</td><td>= Tones|Ringtones|Pop|Original Pilipino Music</td>
</tr><tr><td class=r>8248</td><td>= Tones|Ringtones|Pop|Pinoy Pop</td>
</tr><tr><td class=r>8249</td><td>= Tones|Ringtones|Pop|Pop/Rock</td>
</tr><tr><td class=r>8250</td><td>= Tones|Ringtones|Pop|Soft Rock</td>
</tr><tr><td class=r>8251</td><td>= Tones|Ringtones|Pop|Tai-Pop</td>
</tr><tr><td class=r>8252</td><td>= Tones|Ringtones|Pop|Teen Pop</td>
</tr><tr><td class=r>8253</td><td>= Tones|Ringtones|Pop|Thai Pop</td>
</tr><tr><td class=r>8254</td><td>= Tones|Ringtones|R&amp;B/Soul|Contemporary R&amp;B</td>
</tr><tr><td class=r>8255</td><td>= Tones|Ringtones|R&amp;B/Soul|Disco</td>
</tr><tr><td class=r>8256</td><td>= Tones|Ringtones|R&amp;B/Soul|Doo Wop</td>
</tr><tr><td class=r>8257</td><td>= Tones|Ringtones|R&amp;B/Soul|Funk</td>
</tr><tr><td class=r>8258</td><td>= Tones|Ringtones|R&amp;B/Soul|Motown</td>
</tr><tr><td class=r>8259</td><td>= Tones|Ringtones|R&amp;B/Soul|Neo-Soul</td>
</tr><tr><td class=r>8260</td><td>= Tones|Ringtones|R&amp;B/Soul|Soul</td>
</tr><tr><td class=r>8261</td><td>= Tones|Ringtones|Reggae|Modern Dancehall</td>
</tr><tr><td class=r>8262</td><td>= Tones|Ringtones|Reggae|Dub</td>
</tr><tr><td class=r>8263</td><td>= Tones|Ringtones|Reggae|Roots Reggae</td>
</tr><tr><td class=r>8264</td><td>= Tones|Ringtones|Reggae|Ska</td>
</tr><tr><td class=r>8265</td><td>= Tones|Ringtones|Rock|Adult Alternative</td>
</tr><tr><td class=r>8266</td><td>= Tones|Ringtones|Rock|American Trad Rock</td>
</tr><tr><td class=r>8267</td><td>= Tones|Ringtones|Rock|Arena Rock</td>
</tr><tr><td class=r>8268</td><td>= Tones|Ringtones|Rock|Blues-Rock</td>
</tr><tr><td class=r>8269</td><td>= Tones|Ringtones|Rock|British Invasion</td>
</tr><tr><td class=r>8270</td><td>= Tones|Ringtones|Rock|Chinese Rock</td>
</tr><tr><td class=r>8271</td><td>= Tones|Ringtones|Rock|Death Metal/Black Metal</td>
</tr><tr><td class=r>8272</td><td>= Tones|Ringtones|Rock|Glam Rock</td>
</tr><tr><td class=r>8273</td><td>= Tones|Ringtones|Rock|Hair Metal</td>
</tr><tr><td class=r>8274</td><td>= Tones|Ringtones|Rock|Hard Rock</td>
</tr><tr><td class=r>8275</td><td>= Tones|Ringtones|Rock|Metal</td>
</tr><tr><td class=r>8276</td><td>= Tones|Ringtones|Rock|Jam Bands</td>
</tr><tr><td class=r>8277</td><td>= Tones|Ringtones|Rock|Korean Rock</td>
</tr><tr><td class=r>8278</td><td>= Tones|Ringtones|Rock|Prog-Rock/Art Rock</td>
</tr><tr><td class=r>8279</td><td>= Tones|Ringtones|Rock|Psychedelic</td>
</tr><tr><td class=r>8280</td><td>= Tones|Ringtones|Rock|Rock &amp; Roll</td>
</tr><tr><td class=r>8281</td><td>= Tones|Ringtones|Rock|Rockabilly</td>
</tr><tr><td class=r>8282</td><td>= Tones|Ringtones|Rock|Roots Rock</td>
</tr><tr><td class=r>8283</td><td>= Tones|Ringtones|Rock|Singer/Songwriter</td>
</tr><tr><td class=r>8284</td><td>= Tones|Ringtones|Rock|Southern Rock</td>
</tr><tr><td class=r>8285</td><td>= Tones|Ringtones|Rock|Surf</td>
</tr><tr><td class=r>8286</td><td>= Tones|Ringtones|Rock|Tex-Mex</td>
</tr><tr><td class=r>8287</td><td>= Tones|Ringtones|Singer/Songwriter|Alternative Folk</td>
</tr><tr><td class=r>8288</td><td>= Tones|Ringtones|Singer/Songwriter|Contemporary Folk</td>
</tr><tr><td class=r>8289</td><td>= Tones|Ringtones|Singer/Songwriter|Contemporary Singer/Songwriter</td>
</tr><tr><td class=r>8290</td><td>= Tones|Ringtones|Singer/Songwriter|Folk-Rock</td>
</tr><tr><td class=r>8291</td><td>= Tones|Ringtones|Singer/Songwriter|New Acoustic</td>
</tr><tr><td class=r>8292</td><td>= Tones|Ringtones|Singer/Songwriter|Traditional Folk</td>
</tr><tr><td class=r>8293</td><td>= Tones|Ringtones|Soundtrack|Foreign Cinema</td>
</tr><tr><td class=r>8294</td><td>= Tones|Ringtones|Soundtrack|Musicals</td>
</tr><tr><td class=r>8295</td><td>= Tones|Ringtones|Soundtrack|Original Score</td>
</tr><tr><td class=r>8296</td><td>= Tones|Ringtones|Soundtrack|Sound Effects</td>
</tr><tr><td class=r>8297</td><td>= Tones|Ringtones|Soundtrack|Soundtrack</td>
</tr><tr><td class=r>8298</td><td>= Tones|Ringtones|Soundtrack|TV Soundtrack</td>
</tr><tr><td class=r>8299</td><td>= Tones|Ringtones|Vocal|Standards</td>
</tr><tr><td class=r>8300</td><td>= Tones|Ringtones|Vocal|Traditional Pop</td>
</tr><tr><td class=r>8301</td><td>= Tones|Ringtones|Vocal|Trot</td>
</tr><tr><td class=r>8302</td><td>= Tones|Ringtones|Jazz|Vocal Jazz</td>
</tr><tr><td class=r>8303</td><td>= Tones|Ringtones|Vocal|Vocal Pop</td>
</tr><tr><td class=r>8304</td><td>= Tones|Ringtones|African</td>
</tr><tr><td class=r>8305</td><td>= Tones|Ringtones|African|Afrikaans</td>
</tr><tr><td class=r>8306</td><td>= Tones|Ringtones|African|Afro-Beat</td>
</tr><tr><td class=r>8307</td><td>= Tones|Ringtones|African|Afro-Pop</td>
</tr><tr><td class=r>8308</td><td>= Tones|Ringtones|Turkish|Arabesque</td>
</tr><tr><td class=r>8309</td><td>= Tones|Ringtones|World|Asia</td>
</tr><tr><td class=r>8310</td><td>= Tones|Ringtones|World|Australia</td>
</tr><tr><td class=r>8311</td><td>= Tones|Ringtones|World|Cajun</td>
</tr><tr><td class=r>8312</td><td>= Tones|Ringtones|World|Calypso</td>
</tr><tr><td class=r>8313</td><td>= Tones|Ringtones|World|Caribbean</td>
</tr><tr><td class=r>8314</td><td>= Tones|Ringtones|World|Celtic</td>
</tr><tr><td class=r>8315</td><td>= Tones|Ringtones|World|Celtic Folk</td>
</tr><tr><td class=r>8316</td><td>= Tones|Ringtones|World|Contemporary Celtic</td>
</tr><tr><td class=r>8317</td><td>= Tones|Ringtones|World|Dangdut</td>
</tr><tr><td class=r>8318</td><td>= Tones|Ringtones|World|Dini</td>
</tr><tr><td class=r>8319</td><td>= Tones|Ringtones|World|Europe</td>
</tr><tr><td class=r>8320</td><td>= Tones|Ringtones|World|Fado</td>
</tr><tr><td class=r>8321</td><td>= Tones|Ringtones|World|Farsi</td>
</tr><tr><td class=r>8322</td><td>= Tones|Ringtones|World|Flamenco</td>
</tr><tr><td class=r>8323</td><td>= Tones|Ringtones|World|France</td>
</tr><tr><td class=r>8324</td><td>= Tones|Ringtones|Turkish|Halk</td>
</tr><tr><td class=r>8325</td><td>= Tones|Ringtones|World|Hawaii</td>
</tr><tr><td class=r>8326</td><td>= Tones|Ringtones|World|Iberia</td>
</tr><tr><td class=r>8327</td><td>= Tones|Ringtones|World|Indonesian Religious</td>
</tr><tr><td class=r>8328</td><td>= Tones|Ringtones|World|Israeli</td>
</tr><tr><td class=r>8329</td><td>= Tones|Ringtones|World|Japan</td>
</tr><tr><td class=r>8330</td><td>= Tones|Ringtones|World|Klezmer</td>
</tr><tr><td class=r>8331</td><td>= Tones|Ringtones|World|North America</td>
</tr><tr><td class=r>8332</td><td>= Tones|Ringtones|World|Polka</td>
</tr><tr><td class=r>8333</td><td>= Tones|Ringtones|Russian</td>
</tr><tr><td class=r>8334</td><td>= Tones|Ringtones|Russian|Russian Chanson</td>
</tr><tr><td class=r>8335</td><td>= Tones|Ringtones|Turkish|Sanat</td>
</tr><tr><td class=r>8336</td><td>= Tones|Ringtones|World|Soca</td>
</tr><tr><td class=r>8337</td><td>= Tones|Ringtones|World|South Africa</td>
</tr><tr><td class=r>8338</td><td>= Tones|Ringtones|World|South America</td>
</tr><tr><td class=r>8339</td><td>= Tones|Ringtones|World|Tango</td>
</tr><tr><td class=r>8340</td><td>= Tones|Ringtones|World|Traditional Celtic</td>
</tr><tr><td class=r>8341</td><td>= Tones|Ringtones|Turkish</td>
</tr><tr><td class=r>8342</td><td>= Tones|Ringtones|World|Worldbeat</td>
</tr><tr><td class=r>8343</td><td>= Tones|Ringtones|World|Zydeco</td>
</tr><tr><td class=r>8345</td><td>= Tones|Ringtones|Classical|Art Song</td>
</tr><tr><td class=r>8346</td><td>= Tones|Ringtones|Classical|Brass &amp; Woodwinds</td>
</tr><tr><td class=r>8347</td><td>= Tones|Ringtones|Classical|Solo Instrumental</td>
</tr><tr><td class=r>8348</td><td>= Tones|Ringtones|Classical|Contemporary Era</td>
</tr><tr><td class=r>8349</td><td>= Tones|Ringtones|Classical|Oratorio</td>
</tr><tr><td class=r>8350</td><td>= Tones|Ringtones|Classical|Cantata</td>
</tr><tr><td class=r>8351</td><td>= Tones|Ringtones|Classical|Electronic</td>
</tr><tr><td class=r>8352</td><td>= Tones|Ringtones|Classical|Sacred</td>
</tr><tr><td class=r>8353</td><td>= Tones|Ringtones|Classical|Guitar</td>
</tr><tr><td class=r>8354</td><td>= Tones|Ringtones|Classical|Piano</td>
</tr><tr><td class=r>8355</td><td>= Tones|Ringtones|Classical|Violin</td>
</tr><tr><td class=r>8356</td><td>= Tones|Ringtones|Classical|Cello</td>
</tr><tr><td class=r>8357</td><td>= Tones|Ringtones|Classical|Percussion</td>
</tr><tr><td class=r>8358</td><td>= Tones|Ringtones|Electronic|Dubstep</td>
</tr><tr><td class=r>8359</td><td>= Tones|Ringtones|Electronic|Bass</td>
</tr><tr><td class=r>8360</td><td>= Tones|Ringtones|Hip-Hop/Rap|UK Hip Hop</td>
</tr><tr><td class=r>8361</td><td>= Tones|Ringtones|Reggae|Lovers Rock</td>
</tr><tr><td class=r>8362</td><td>= Tones|Ringtones|Alternative|EMO</td>
</tr><tr><td class=r>8363</td><td>= Tones|Ringtones|Alternative|Pop Punk</td>
</tr><tr><td class=r>8364</td><td>= Tones|Ringtones|Alternative|Indie Pop</td>
</tr><tr><td class=r>8365</td><td>= Tones|Ringtones|New Age|Yoga</td>
</tr><tr><td class=r>8366</td><td>= Tones|Ringtones|Pop|Tribute</td>
</tr><tr><td class=r>8367</td><td>= Tones|Ringtones|Pop|Shows</td>
</tr><tr><td class=r>8368</td><td>= Tones|Ringtones|Cuban</td>
</tr><tr><td class=r>8369</td><td>= Tones|Ringtones|Cuban|Mambo</td>
</tr><tr><td class=r>8370</td><td>= Tones|Ringtones|Cuban|Chachacha</td>
</tr><tr><td class=r>8371</td><td>= Tones|Ringtones|Cuban|Guajira</td>
</tr><tr><td class=r>8372</td><td>= Tones|Ringtones|Cuban|Son</td>
</tr><tr><td class=r>8373</td><td>= Tones|Ringtones|Cuban|Bolero</td>
</tr><tr><td class=r>8374</td><td>= Tones|Ringtones|Cuban|Guaracha</td>
</tr><tr><td class=r>8375</td><td>= Tones|Ringtones|Cuban|Timba</td>
</tr><tr><td class=r>8376</td><td>= Tones|Ringtones|Soundtrack|Video Game</td>
</tr><tr><td class=r>8377</td><td>= Tones|Ringtones|Indian|Regional Indian|Punjabi|Punjabi Pop</td>
</tr><tr><td class=r>8378</td><td>= Tones|Ringtones|Indian|Regional Indian|Bengali|Rabindra Sangeet</td>
</tr><tr><td class=r>8379</td><td>= Tones|Ringtones|Indian|Regional Indian|Malayalam</td>
</tr><tr><td class=r>8380</td><td>= Tones|Ringtones|Indian|Regional Indian|Kannada</td>
</tr><tr><td class=r>8381</td><td>= Tones|Ringtones|Indian|Regional Indian|Marathi</td>
</tr><tr><td class=r>8382</td><td>= Tones|Ringtones|Indian|Regional Indian|Gujarati</td>
</tr><tr><td class=r>8383</td><td>= Tones|Ringtones|Indian|Regional Indian|Assamese</td>
</tr><tr><td class=r>8384</td><td>= Tones|Ringtones|Indian|Regional Indian|Bhojpuri</td>
</tr><tr><td class=r>8385</td><td>= Tones|Ringtones|Indian|Regional Indian|Haryanvi</td>
</tr><tr><td class=r>8386</td><td>= Tones|Ringtones|Indian|Regional Indian|Odia</td>
</tr><tr><td class=r>8387</td><td>= Tones|Ringtones|Indian|Regional Indian|Rajasthani</td>
</tr><tr><td class=r>8388</td><td>= Tones|Ringtones|Indian|Regional Indian|Urdu</td>
</tr><tr><td class=r>8389</td><td>= Tones|Ringtones|Indian|Regional Indian|Punjabi</td>
</tr><tr><td class=r>8390</td><td>= Tones|Ringtones|Indian|Regional Indian|Bengali</td>
</tr><tr><td class=r>8391</td><td>= Tones|Ringtones|Indian|Indian Classical|Carnatic Classical</td>
</tr><tr><td class=r>8392</td><td>= Tones|Ringtones|Indian|Indian Classical|Hindustani Classical</td>
</tr><tr><td class=r>8393</td><td>= Tones|Ringtones|African|Afro House</td>
</tr><tr><td class=r>8394</td><td>= Tones|Ringtones|African|Afro Soul</td>
</tr><tr><td class=r>8395</td><td>= Tones|Ringtones|African|Afrobeats</td>
</tr><tr><td class=r>8396</td><td>= Tones|Ringtones|African|Benga</td>
</tr><tr><td class=r>8397</td><td>= Tones|Ringtones|African|Bongo-Flava</td>
</tr><tr><td class=r>8398</td><td>= Tones|Ringtones|African|Coupe-Decale</td>
</tr><tr><td class=r>8399</td><td>= Tones|Ringtones|African|Gqom</td>
</tr><tr><td class=r>8400</td><td>= Tones|Ringtones|African|Highlife</td>
</tr><tr><td class=r>8401</td><td>= Tones|Ringtones|African|Kuduro</td>
</tr><tr><td class=r>8402</td><td>= Tones|Ringtones|African|Kizomba</td>
</tr><tr><td class=r>8403</td><td>= Tones|Ringtones|African|Kwaito</td>
</tr><tr><td class=r>8404</td><td>= Tones|Ringtones|African|Mbalax</td>
</tr><tr><td class=r>8405</td><td>= Tones|Ringtones|African|Ndombolo</td>
</tr><tr><td class=r>8406</td><td>= Tones|Ringtones|African|Shangaan Electro</td>
</tr><tr><td class=r>8407</td><td>= Tones|Ringtones|African|Soukous</td>
</tr><tr><td class=r>8408</td><td>= Tones|Ringtones|African|Taarab</td>
</tr><tr><td class=r>8409</td><td>= Tones|Ringtones|African|Zouglou</td>
</tr><tr><td class=r>8410</td><td>= Tones|Ringtones|Turkish|Ozgun</td>
</tr><tr><td class=r>8411</td><td>= Tones|Ringtones|Turkish|Fantezi</td>
</tr><tr><td class=r>8412</td><td>= Tones|Ringtones|Turkish|Religious</td>
</tr><tr><td class=r>8413</td><td>= Tones|Ringtones|Pop|Turkish Pop</td>
</tr><tr><td class=r>8414</td><td>= Tones|Ringtones|Rock|Turkish Rock</td>
</tr><tr><td class=r>8415</td><td>= Tones|Ringtones|Alternative|Turkish Alternative</td>
</tr><tr><td class=r>8416</td><td>= Tones|Ringtones|Hip-Hop/Rap|Turkish Hip-Hop/Rap</td>
</tr><tr><td class=r>8417</td><td>= Tones|Ringtones|African|Maskandi</td>
</tr><tr><td class=r>8418</td><td>= Tones|Ringtones|Russian|Russian Romance</td>
</tr><tr><td class=r>8419</td><td>= Tones|Ringtones|Russian|Russian Bard</td>
</tr><tr><td class=r>8420</td><td>= Tones|Ringtones|Russian|Russian Pop</td>
</tr><tr><td class=r>8421</td><td>= Tones|Ringtones|Russian|Russian Rock</td>
</tr><tr><td class=r>8422</td><td>= Tones|Ringtones|Russian|Russian Hip-Hop</td>
</tr><tr><td class=r>8423</td><td>= Tones|Ringtones|Arabic|Levant</td>
</tr><tr><td class=r>8424</td><td>= Tones|Ringtones|Arabic|Levant|Dabke</td>
</tr><tr><td class=r>8425</td><td>= Tones|Ringtones|Arabic|Maghreb Rai</td>
</tr><tr><td class=r>8426</td><td>= Tones|Ringtones|Arabic|Khaleeji|Khaleeji Jalsat</td>
</tr><tr><td class=r>8427</td><td>= Tones|Ringtones|Arabic|Khaleeji|Khaleeji Shailat</td>
</tr><tr><td class=r>8428</td><td>= Tones|Ringtones|Tarab</td>
</tr><tr><td class=r>8429</td><td>= Tones|Ringtones|Tarab|Iraqi Tarab</td>
</tr><tr><td class=r>8430</td><td>= Tones|Ringtones|Tarab|Egyptian Tarab</td>
</tr><tr><td class=r>8431</td><td>= Tones|Ringtones|Tarab|Khaleeji Tarab</td>
</tr><tr><td class=r>8432</td><td>= Tones|Ringtones|Pop|Levant Pop</td>
</tr><tr><td class=r>8433</td><td>= Tones|Ringtones|Pop|Iraqi Pop</td>
</tr><tr><td class=r>8434</td><td>= Tones|Ringtones|Pop|Egyptian Pop</td>
</tr><tr><td class=r>8435</td><td>= Tones|Ringtones|Pop|Maghreb Pop</td>
</tr><tr><td class=r>8436</td><td>= Tones|Ringtones|Pop|Khaleeji Pop</td>
</tr><tr><td class=r>8437</td><td>= Tones|Ringtones|Hip-Hop/Rap|Levant Hip-Hop</td>
</tr><tr><td class=r>8438</td><td>= Tones|Ringtones|Hip-Hop/Rap|Egyptian Hip-Hop</td>
</tr><tr><td class=r>8439</td><td>= Tones|Ringtones|Hip-Hop/Rap|Maghreb Hip-Hop</td>
</tr><tr><td class=r>8440</td><td>= Tones|Ringtones|Hip-Hop/Rap|Khaleeji Hip-Hop</td>
</tr><tr><td class=r>8441</td><td>= Tones|Ringtones|Alternative|Indie Levant</td>
</tr><tr><td class=r>8442</td><td>= Tones|Ringtones|Alternative|Indie Egyptian</td>
</tr><tr><td class=r>8443</td><td>= Tones|Ringtones|Alternative|Indie Maghreb</td>
</tr><tr><td class=r>8444</td><td>= Tones|Ringtones|Electronic|Levant Electronic</td>
</tr><tr><td class=r>8445</td><td>= Tones|Ringtones|Electronic|Electro-Cha&#39;abi</td>
</tr><tr><td class=r>8446</td><td>= Tones|Ringtones|Electronic|Maghreb Electronic</td>
</tr><tr><td class=r>8447</td><td>= Tones|Ringtones|Folk|Iraqi Folk</td>
</tr><tr><td class=r>8448</td><td>= Tones|Ringtones|Folk|Khaleeji Folk</td>
</tr><tr><td class=r>8449</td><td>= Tones|Ringtones|Dance|Maghreb Dance</td>
</tr><tr><td class=r>9002</td><td>= Books|Nonfiction</td>
</tr><tr><td class=r>9003</td><td>= Books|Romance</td>
</tr><tr><td class=r>9004</td><td>= Books|Travel &amp; Adventure</td>
</tr><tr><td class=r>9007</td><td>= Books|Arts &amp; Entertainment</td>
</tr><tr><td class=r>9008</td><td>= Books|Biographies &amp; Memoirs</td>
</tr><tr><td class=r>9009</td><td>= Books|Business &amp; Personal Finance</td>
</tr><tr><td class=r>9010</td><td>= Books|Children &amp; Teens</td>
</tr><tr><td class=r>9012</td><td>= Books|Humor</td>
</tr><tr><td class=r>9015</td><td>= Books|History</td>
</tr><tr><td class=r>9018</td><td>= Books|Religion &amp; Spirituality</td>
</tr><tr><td class=r>9019</td><td>= Books|Science &amp; Nature</td>
</tr><tr><td class=r>9020</td><td>= Books|Sci-Fi &amp; Fantasy</td>
</tr><tr><td class=r>9024</td><td>= Books|Lifestyle &amp; Home</td>
</tr><tr><td class=r>9025</td><td>= Books|Self-Development</td>
</tr><tr><td class=r>9026</td><td>= Books|Comics &amp; Graphic Novels</td>
</tr><tr><td class=r>9027</td><td>= Books|Computers &amp; Internet</td>
</tr><tr><td class=r>9028</td><td>= Books|Cookbooks, Food &amp; Wine</td>
</tr><tr><td class=r>9029</td><td>= Books|Professional &amp; Technical</td>
</tr><tr><td class=r>9030</td><td>= Books|Parenting</td>
</tr><tr><td class=r>9031</td><td>= Books|Fiction &amp; Literature</td>
</tr><tr><td class=r>9032</td><td>= Books|Mysteries &amp; Thrillers</td>
</tr><tr><td class=r>9033</td><td>= Books|Reference</td>
</tr><tr><td class=r>9034</td><td>= Books|Politics &amp; Current Events</td>
</tr><tr><td class=r>9035</td><td>= Books|Sports &amp; Outdoors</td>
</tr><tr><td class=r>10001</td><td>= Books|Lifestyle &amp; Home|Antiques &amp; Collectibles</td>
</tr><tr><td class=r>10002</td><td>= Books|Arts &amp; Entertainment|Art &amp; Architecture</td>
</tr><tr><td class=r>10003</td><td>= Books|Religion &amp; Spirituality|Bibles</td>
</tr><tr><td class=r>10004</td><td>= Books|Self-Development|Spirituality</td>
</tr><tr><td class=r>10005</td><td>= Books|Business &amp; Personal Finance|Industries &amp; Professions</td>
</tr><tr><td class=r>10006</td><td>= Books|Business &amp; Personal Finance|Marketing &amp; Sales</td>
</tr><tr><td class=r>10007</td><td>= Books|Business &amp; Personal Finance|Small Business &amp; Entrepreneurship</td>
</tr><tr><td class=r>10008</td><td>= Books|Business &amp; Personal Finance|Personal Finance</td>
</tr><tr><td class=r>10009</td><td>= Books|Business &amp; Personal Finance|Reference</td>
</tr><tr><td class=r>10010</td><td>= Books|Business &amp; Personal Finance|Careers</td>
</tr><tr><td class=r>10011</td><td>= Books|Business &amp; Personal Finance|Economics</td>
</tr><tr><td class=r>10012</td><td>= Books|Business &amp; Personal Finance|Investing</td>
</tr><tr><td class=r>10013</td><td>= Books|Business &amp; Personal Finance|Finance</td>
</tr><tr><td class=r>10014</td><td>= Books|Business &amp; Personal Finance|Management &amp; Leadership</td>
</tr><tr><td class=r>10015</td><td>= Books|Comics &amp; Graphic Novels|Graphic Novels</td>
</tr><tr><td class=r>10016</td><td>= Books|Comics &amp; Graphic Novels|Manga</td>
</tr><tr><td class=r>10017</td><td>= Books|Computers &amp; Internet|Computers</td>
</tr><tr><td class=r>10018</td><td>= Books|Computers &amp; Internet|Databases</td>
</tr><tr><td class=r>10019</td><td>= Books|Computers &amp; Internet|Digital Media</td>
</tr><tr><td class=r>10020</td><td>= Books|Computers &amp; Internet|Internet</td>
</tr><tr><td class=r>10021</td><td>= Books|Computers &amp; Internet|Network</td>
</tr><tr><td class=r>10022</td><td>= Books|Computers &amp; Internet|Operating Systems</td>
</tr><tr><td class=r>10023</td><td>= Books|Computers &amp; Internet|Programming</td>
</tr><tr><td class=r>10024</td><td>= Books|Computers &amp; Internet|Software</td>
</tr><tr><td class=r>10025</td><td>= Books|Computers &amp; Internet|System Administration</td>
</tr><tr><td class=r>10026</td><td>= Books|Cookbooks, Food &amp; Wine|Beverages</td>
</tr><tr><td class=r>10027</td><td>= Books|Cookbooks, Food &amp; Wine|Courses &amp; Dishes</td>
</tr><tr><td class=r>10028</td><td>= Books|Cookbooks, Food &amp; Wine|Special Diet</td>
</tr><tr><td class=r>10029</td><td>= Books|Cookbooks, Food &amp; Wine|Special Occasions</td>
</tr><tr><td class=r>10030</td><td>= Books|Cookbooks, Food &amp; Wine|Methods</td>
</tr><tr><td class=r>10031</td><td>= Books|Cookbooks, Food &amp; Wine|Reference</td>
</tr><tr><td class=r>10032</td><td>= Books|Cookbooks, Food &amp; Wine|Regional &amp; Ethnic</td>
</tr><tr><td class=r>10033</td><td>= Books|Cookbooks, Food &amp; Wine|Specific Ingredients</td>
</tr><tr><td class=r>10034</td><td>= Books|Lifestyle &amp; Home|Crafts &amp; Hobbies</td>
</tr><tr><td class=r>10035</td><td>= Books|Professional &amp; Technical|Design</td>
</tr><tr><td class=r>10036</td><td>= Books|Arts &amp; Entertainment|Theater</td>
</tr><tr><td class=r>10037</td><td>= Books|Professional &amp; Technical|Education</td>
</tr><tr><td class=r>10038</td><td>= Books|Nonfiction|Family &amp; Relationships</td>
</tr><tr><td class=r>10039</td><td>= Books|Fiction &amp; Literature|Action &amp; Adventure</td>
</tr><tr><td class=r>10040</td><td>= Books|Fiction &amp; Literature|African American</td>
</tr><tr><td class=r>10041</td><td>= Books|Fiction &amp; Literature|Religious</td>
</tr><tr><td class=r>10042</td><td>= Books|Fiction &amp; Literature|Classics</td>
</tr><tr><td class=r>10043</td><td>= Books|Fiction &amp; Literature|Erotica</td>
</tr><tr><td class=r>10044</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy</td>
</tr><tr><td class=r>10045</td><td>= Books|Fiction &amp; Literature|Gay</td>
</tr><tr><td class=r>10046</td><td>= Books|Fiction &amp; Literature|Ghost</td>
</tr><tr><td class=r>10047</td><td>= Books|Fiction &amp; Literature|Historical</td>
</tr><tr><td class=r>10048</td><td>= Books|Fiction &amp; Literature|Horror</td>
</tr><tr><td class=r>10049</td><td>= Books|Fiction &amp; Literature|Literary</td>
</tr><tr><td class=r>10050</td><td>= Books|Mysteries &amp; Thrillers|Hard-Boiled</td>
</tr><tr><td class=r>10051</td><td>= Books|Mysteries &amp; Thrillers|Historical</td>
</tr><tr><td class=r>10052</td><td>= Books|Mysteries &amp; Thrillers|Police Procedural</td>
</tr><tr><td class=r>10053</td><td>= Books|Mysteries &amp; Thrillers|Short Stories</td>
</tr><tr><td class=r>10054</td><td>= Books|Mysteries &amp; Thrillers|British Detectives</td>
</tr><tr><td class=r>10055</td><td>= Books|Mysteries &amp; Thrillers|Women Sleuths</td>
</tr><tr><td class=r>10056</td><td>= Books|Romance|Erotic Romance</td>
</tr><tr><td class=r>10057</td><td>= Books|Romance|Contemporary</td>
</tr><tr><td class=r>10058</td><td>= Books|Romance|Paranormal</td>
</tr><tr><td class=r>10059</td><td>= Books|Romance|Historical</td>
</tr><tr><td class=r>10060</td><td>= Books|Romance|Short Stories</td>
</tr><tr><td class=r>10061</td><td>= Books|Romance|Suspense</td>
</tr><tr><td class=r>10062</td><td>= Books|Romance|Western</td>
</tr><tr><td class=r>10063</td><td>= Books|Sci-Fi &amp; Fantasy|Science Fiction</td>
</tr><tr><td class=r>10064</td><td>= Books|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature</td>
</tr><tr><td class=r>10065</td><td>= Books|Fiction &amp; Literature|Short Stories</td>
</tr><tr><td class=r>10066</td><td>= Books|Reference|Foreign Languages</td>
</tr><tr><td class=r>10067</td><td>= Books|Arts &amp; Entertainment|Games</td>
</tr><tr><td class=r>10068</td><td>= Books|Lifestyle &amp; Home|Gardening</td>
</tr><tr><td class=r>10069</td><td>= Books|Self-Development|Health &amp; Fitness</td>
</tr><tr><td class=r>10070</td><td>= Books|History|Africa</td>
</tr><tr><td class=r>10071</td><td>= Books|History|Americas</td>
</tr><tr><td class=r>10072</td><td>= Books|History|Ancient</td>
</tr><tr><td class=r>10073</td><td>= Books|History|Asia</td>
</tr><tr><td class=r>10074</td><td>= Books|History|Australia &amp; Oceania</td>
</tr><tr><td class=r>10075</td><td>= Books|History|Europe</td>
</tr><tr><td class=r>10076</td><td>= Books|History|Latin America</td>
</tr><tr><td class=r>10077</td><td>= Books|History|Middle East</td>
</tr><tr><td class=r>10078</td><td>= Books|History|Military</td>
</tr><tr><td class=r>10079</td><td>= Books|History|United States</td>
</tr><tr><td class=r>10080</td><td>= Books|History|World</td>
</tr><tr><td class=r>10081</td><td>= Books|Children &amp; Teens|Children&#39;s Fiction</td>
</tr><tr><td class=r>10082</td><td>= Books|Children &amp; Teens|Children&#39;s Nonfiction</td>
</tr><tr><td class=r>10083</td><td>= Books|Professional &amp; Technical|Law</td>
</tr><tr><td class=r>10084</td><td>= Books|Fiction &amp; Literature|Literary Criticism</td>
</tr><tr><td class=r>10085</td><td>= Books|Science &amp; Nature|Mathematics</td>
</tr><tr><td class=r>10086</td><td>= Books|Professional &amp; Technical|Medical</td>
</tr><tr><td class=r>10087</td><td>= Books|Arts &amp; Entertainment|Music</td>
</tr><tr><td class=r>10088</td><td>= Books|Science &amp; Nature|Nature</td>
</tr><tr><td class=r>10089</td><td>= Books|Arts &amp; Entertainment|Performing Arts</td>
</tr><tr><td class=r>10090</td><td>= Books|Lifestyle &amp; Home|Pets</td>
</tr><tr><td class=r>10091</td><td>= Books|Nonfiction|Philosophy</td>
</tr><tr><td class=r>10092</td><td>= Books|Arts &amp; Entertainment|Photography</td>
</tr><tr><td class=r>10093</td><td>= Books|Fiction &amp; Literature|Poetry</td>
</tr><tr><td class=r>10094</td><td>= Books|Self-Development|Psychology</td>
</tr><tr><td class=r>10095</td><td>= Books|Reference|Almanacs &amp; Yearbooks</td>
</tr><tr><td class=r>10096</td><td>= Books|Reference|Atlases &amp; Maps</td>
</tr><tr><td class=r>10097</td><td>= Books|Reference|Catalogs &amp; Directories</td>
</tr><tr><td class=r>10098</td><td>= Books|Reference|Consumer Guides</td>
</tr><tr><td class=r>10099</td><td>= Books|Reference|Dictionaries &amp; Thesauruses</td>
</tr><tr><td class=r>10100</td><td>= Books|Reference|Encyclopedias</td>
</tr><tr><td class=r>10101</td><td>= Books|Reference|Etiquette</td>
</tr><tr><td class=r>10102</td><td>= Books|Reference|Quotations</td>
</tr><tr><td class=r>10103</td><td>= Books|Reference|Words &amp; Language</td>
</tr><tr><td class=r>10104</td><td>= Books|Reference|Writing</td>
</tr><tr><td class=r>10105</td><td>= Books|Religion &amp; Spirituality|Bible Studies</td>
</tr><tr><td class=r>10106</td><td>= Books|Religion &amp; Spirituality|Buddhism</td>
</tr><tr><td class=r>10107</td><td>= Books|Religion &amp; Spirituality|Christianity</td>
</tr><tr><td class=r>10108</td><td>= Books|Religion &amp; Spirituality|Hinduism</td>
</tr><tr><td class=r>10109</td><td>= Books|Religion &amp; Spirituality|Islam</td>
</tr><tr><td class=r>10110</td><td>= Books|Religion &amp; Spirituality|Judaism</td>
</tr><tr><td class=r>10111</td><td>= Books|Science &amp; Nature|Astronomy</td>
</tr><tr><td class=r>10112</td><td>= Books|Science &amp; Nature|Chemistry</td>
</tr><tr><td class=r>10113</td><td>= Books|Science &amp; Nature|Earth Sciences</td>
</tr><tr><td class=r>10114</td><td>= Books|Science &amp; Nature|Essays</td>
</tr><tr><td class=r>10115</td><td>= Books|Science &amp; Nature|History</td>
</tr><tr><td class=r>10116</td><td>= Books|Science &amp; Nature|Life Sciences</td>
</tr><tr><td class=r>10117</td><td>= Books|Science &amp; Nature|Physics</td>
</tr><tr><td class=r>10118</td><td>= Books|Science &amp; Nature|Reference</td>
</tr><tr><td class=r>10119</td><td>= Books|Self-Development|Self-Improvement</td>
</tr><tr><td class=r>10120</td><td>= Books|Nonfiction|Social Science</td>
</tr><tr><td class=r>10121</td><td>= Books|Sports &amp; Outdoors|Baseball</td>
</tr><tr><td class=r>10122</td><td>= Books|Sports &amp; Outdoors|Basketball</td>
</tr><tr><td class=r>10123</td><td>= Books|Sports &amp; Outdoors|Coaching</td>
</tr><tr><td class=r>10124</td><td>= Books|Sports &amp; Outdoors|Extreme Sports</td>
</tr><tr><td class=r>10125</td><td>= Books|Sports &amp; Outdoors|Football</td>
</tr><tr><td class=r>10126</td><td>= Books|Sports &amp; Outdoors|Golf</td>
</tr><tr><td class=r>10127</td><td>= Books|Sports &amp; Outdoors|Hockey</td>
</tr><tr><td class=r>10128</td><td>= Books|Sports &amp; Outdoors|Mountaineering</td>
</tr><tr><td class=r>10129</td><td>= Books|Sports &amp; Outdoors|Outdoors</td>
</tr><tr><td class=r>10130</td><td>= Books|Sports &amp; Outdoors|Racket Sports</td>
</tr><tr><td class=r>10131</td><td>= Books|Sports &amp; Outdoors|Reference</td>
</tr><tr><td class=r>10132</td><td>= Books|Sports &amp; Outdoors|Soccer</td>
</tr><tr><td class=r>10133</td><td>= Books|Sports &amp; Outdoors|Training</td>
</tr><tr><td class=r>10134</td><td>= Books|Sports &amp; Outdoors|Water Sports</td>
</tr><tr><td class=r>10135</td><td>= Books|Sports &amp; Outdoors|Winter Sports</td>
</tr><tr><td class=r>10136</td><td>= Books|Reference|Study Aids</td>
</tr><tr><td class=r>10137</td><td>= Books|Professional &amp; Technical|Engineering</td>
</tr><tr><td class=r>10138</td><td>= Books|Nonfiction|Transportation</td>
</tr><tr><td class=r>10139</td><td>= Books|Travel &amp; Adventure|Africa</td>
</tr><tr><td class=r>10140</td><td>= Books|Travel &amp; Adventure|Asia</td>
</tr><tr><td class=r>10141</td><td>= Books|Travel &amp; Adventure|Specialty Travel</td>
</tr><tr><td class=r>10142</td><td>= Books|Travel &amp; Adventure|Canada</td>
</tr><tr><td class=r>10143</td><td>= Books|Travel &amp; Adventure|Caribbean</td>
</tr><tr><td class=r>10144</td><td>= Books|Travel &amp; Adventure|Latin America</td>
</tr><tr><td class=r>10145</td><td>= Books|Travel &amp; Adventure|Essays &amp; Memoirs</td>
</tr><tr><td class=r>10146</td><td>= Books|Travel &amp; Adventure|Europe</td>
</tr><tr><td class=r>10147</td><td>= Books|Travel &amp; Adventure|Middle East</td>
</tr><tr><td class=r>10148</td><td>= Books|Travel &amp; Adventure|United States</td>
</tr><tr><td class=r>10149</td><td>= Books|Nonfiction|True Crime</td>
</tr><tr><td class=r>11001</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Contemporary</td>
</tr><tr><td class=r>11002</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Epic</td>
</tr><tr><td class=r>11003</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Historical</td>
</tr><tr><td class=r>11004</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Paranormal</td>
</tr><tr><td class=r>11005</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Short Stories</td>
</tr><tr><td class=r>11006</td><td>= Books|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|Adventure</td>
</tr><tr><td class=r>11007</td><td>= Books|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|High Tech</td>
</tr><tr><td class=r>11008</td><td>= Books|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|Short Stories</td>
</tr><tr><td class=r>11009</td><td>= Books|Professional &amp; Technical|Education|Language Arts &amp; Disciplines</td>
</tr><tr><td class=r>11010</td><td>= Books|Communications &amp; Media</td>
</tr><tr><td class=r>11011</td><td>= Books|Communications &amp; Media|Broadcasting</td>
</tr><tr><td class=r>11012</td><td>= Books|Communications &amp; Media|Digital Media</td>
</tr><tr><td class=r>11013</td><td>= Books|Communications &amp; Media|Journalism</td>
</tr><tr><td class=r>11014</td><td>= Books|Communications &amp; Media|Photojournalism</td>
</tr><tr><td class=r>11015</td><td>= Books|Communications &amp; Media|Print</td>
</tr><tr><td class=r>11016</td><td>= Books|Communications &amp; Media|Speech</td>
</tr><tr><td class=r>11017</td><td>= Books|Communications &amp; Media|Writing</td>
</tr><tr><td class=r>11018</td><td>= Books|Arts &amp; Entertainment|Art &amp; Architecture|Urban Planning</td>
</tr><tr><td class=r>11019</td><td>= Books|Arts &amp; Entertainment|Dance</td>
</tr><tr><td class=r>11020</td><td>= Books|Arts &amp; Entertainment|Fashion</td>
</tr><tr><td class=r>11021</td><td>= Books|Arts &amp; Entertainment|Film</td>
</tr><tr><td class=r>11022</td><td>= Books|Arts &amp; Entertainment|Interior Design</td>
</tr><tr><td class=r>11023</td><td>= Books|Arts &amp; Entertainment|Media Arts</td>
</tr><tr><td class=r>11024</td><td>= Books|Arts &amp; Entertainment|Radio</td>
</tr><tr><td class=r>11025</td><td>= Books|Arts &amp; Entertainment|TV</td>
</tr><tr><td class=r>11026</td><td>= Books|Arts &amp; Entertainment|Visual Arts</td>
</tr><tr><td class=r>11027</td><td>= Books|Biographies &amp; Memoirs|Arts &amp; Entertainment</td>
</tr><tr><td class=r>11028</td><td>= Books|Biographies &amp; Memoirs|Business</td>
</tr><tr><td class=r>11029</td><td>= Books|Biographies &amp; Memoirs|Culinary</td>
</tr><tr><td class=r>11030</td><td>= Books|Biographies &amp; Memoirs|Gay &amp; Lesbian</td>
</tr><tr><td class=r>11031</td><td>= Books|Biographies &amp; Memoirs|Historical</td>
</tr><tr><td class=r>11032</td><td>= Books|Biographies &amp; Memoirs|Literary</td>
</tr><tr><td class=r>11033</td><td>= Books|Biographies &amp; Memoirs|Media &amp; Journalism</td>
</tr><tr><td class=r>11034</td><td>= Books|Biographies &amp; Memoirs|Military</td>
</tr><tr><td class=r>11035</td><td>= Books|Biographies &amp; Memoirs|Politics</td>
</tr><tr><td class=r>11036</td><td>= Books|Biographies &amp; Memoirs|Religious</td>
</tr><tr><td class=r>11037</td><td>= Books|Biographies &amp; Memoirs|Science &amp; Technology</td>
</tr><tr><td class=r>11038</td><td>= Books|Biographies &amp; Memoirs|Sports</td>
</tr><tr><td class=r>11039</td><td>= Books|Biographies &amp; Memoirs|Women</td>
</tr><tr><td class=r>11040</td><td>= Books|Romance|New Adult</td>
</tr><tr><td class=r>11042</td><td>= Books|Romance|Romantic Comedy</td>
</tr><tr><td class=r>11043</td><td>= Books|Romance|Gay &amp; Lesbian</td>
</tr><tr><td class=r>11044</td><td>= Books|Fiction &amp; Literature|Essays</td>
</tr><tr><td class=r>11045</td><td>= Books|Fiction &amp; Literature|Anthologies</td>
</tr><tr><td class=r>11046</td><td>= Books|Fiction &amp; Literature|Comparative Literature</td>
</tr><tr><td class=r>11047</td><td>= Books|Fiction &amp; Literature|Drama</td>
</tr><tr><td class=r>11049</td><td>= Books|Fiction &amp; Literature|Fairy Tales, Myths &amp; Fables</td>
</tr><tr><td class=r>11050</td><td>= Books|Fiction &amp; Literature|Family</td>
</tr><tr><td class=r>11051</td><td>= Books|Comics &amp; Graphic Novels|Manga|School Drama</td>
</tr><tr><td class=r>11052</td><td>= Books|Comics &amp; Graphic Novels|Manga|Human Drama</td>
</tr><tr><td class=r>11053</td><td>= Books|Comics &amp; Graphic Novels|Manga|Family Drama</td>
</tr><tr><td class=r>11054</td><td>= Books|Sports &amp; Outdoors|Boxing</td>
</tr><tr><td class=r>11055</td><td>= Books|Sports &amp; Outdoors|Cricket</td>
</tr><tr><td class=r>11056</td><td>= Books|Sports &amp; Outdoors|Cycling</td>
</tr><tr><td class=r>11057</td><td>= Books|Sports &amp; Outdoors|Equestrian</td>
</tr><tr><td class=r>11058</td><td>= Books|Sports &amp; Outdoors|Martial Arts &amp; Self Defense</td>
</tr><tr><td class=r>11059</td><td>= Books|Sports &amp; Outdoors|Motor Sports</td>
</tr><tr><td class=r>11060</td><td>= Books|Sports &amp; Outdoors|Rugby</td>
</tr><tr><td class=r>11061</td><td>= Books|Sports &amp; Outdoors|Running</td>
</tr><tr><td class=r>11062</td><td>= Books|Self-Development|Diet &amp; Nutrition</td>
</tr><tr><td class=r>11063</td><td>= Books|Science &amp; Nature|Agriculture</td>
</tr><tr><td class=r>11064</td><td>= Books|Science &amp; Nature|Atmosphere</td>
</tr><tr><td class=r>11065</td><td>= Books|Science &amp; Nature|Biology</td>
</tr><tr><td class=r>11066</td><td>= Books|Science &amp; Nature|Ecology</td>
</tr><tr><td class=r>11067</td><td>= Books|Science &amp; Nature|Environment</td>
</tr><tr><td class=r>11068</td><td>= Books|Science &amp; Nature|Geography</td>
</tr><tr><td class=r>11069</td><td>= Books|Science &amp; Nature|Geology</td>
</tr><tr><td class=r>11070</td><td>= Books|Nonfiction|Social Science|Anthropology</td>
</tr><tr><td class=r>11071</td><td>= Books|Nonfiction|Social Science|Archaeology</td>
</tr><tr><td class=r>11072</td><td>= Books|Nonfiction|Social Science|Civics</td>
</tr><tr><td class=r>11073</td><td>= Books|Nonfiction|Social Science|Government</td>
</tr><tr><td class=r>11074</td><td>= Books|Nonfiction|Social Science|Social Studies</td>
</tr><tr><td class=r>11075</td><td>= Books|Nonfiction|Social Science|Social Welfare</td>
</tr><tr><td class=r>11076</td><td>= Books|Nonfiction|Social Science|Society</td>
</tr><tr><td class=r>11077</td><td>= Books|Nonfiction|Philosophy|Aesthetics</td>
</tr><tr><td class=r>11078</td><td>= Books|Nonfiction|Philosophy|Epistemology</td>
</tr><tr><td class=r>11079</td><td>= Books|Nonfiction|Philosophy|Ethics</td>
</tr><tr><td class=r>11080</td><td>= Books|Nonfiction|Philosophy|Language</td>
</tr><tr><td class=r>11081</td><td>= Books|Nonfiction|Philosophy|Logic</td>
</tr><tr><td class=r>11082</td><td>= Books|Nonfiction|Philosophy|Metaphysics</td>
</tr><tr><td class=r>11083</td><td>= Books|Nonfiction|Philosophy|Political</td>
</tr><tr><td class=r>11084</td><td>= Books|Nonfiction|Philosophy|Religion</td>
</tr><tr><td class=r>11085</td><td>= Books|Reference|Manuals</td>
</tr><tr><td class=r>11086</td><td>= Books|Kids</td>
</tr><tr><td class=r>11087</td><td>= Books|Kids|Animals</td>
</tr><tr><td class=r>11088</td><td>= Books|Kids|Basic Concepts</td>
</tr><tr><td class=r>11089</td><td>= Books|Kids|Basic Concepts|Alphabet</td>
</tr><tr><td class=r>11090</td><td>= Books|Kids|Basic Concepts|Body</td>
</tr><tr><td class=r>11091</td><td>= Books|Kids|Basic Concepts|Colors</td>
</tr><tr><td class=r>11092</td><td>= Books|Kids|Basic Concepts|Counting &amp; Numbers</td>
</tr><tr><td class=r>11093</td><td>= Books|Kids|Basic Concepts|Date &amp; Time</td>
</tr><tr><td class=r>11094</td><td>= Books|Kids|Basic Concepts|General</td>
</tr><tr><td class=r>11095</td><td>= Books|Kids|Basic Concepts|Money</td>
</tr><tr><td class=r>11096</td><td>= Books|Kids|Basic Concepts|Opposites</td>
</tr><tr><td class=r>11097</td><td>= Books|Kids|Basic Concepts|Seasons</td>
</tr><tr><td class=r>11098</td><td>= Books|Kids|Basic Concepts|Senses &amp; Sensation</td>
</tr><tr><td class=r>11099</td><td>= Books|Kids|Basic Concepts|Size &amp; Shape</td>
</tr><tr><td class=r>11100</td><td>= Books|Kids|Basic Concepts|Sounds</td>
</tr><tr><td class=r>11101</td><td>= Books|Kids|Basic Concepts|Words</td>
</tr><tr><td class=r>11102</td><td>= Books|Kids|Biography</td>
</tr><tr><td class=r>11103</td><td>= Books|Kids|Careers &amp; Occupations</td>
</tr><tr><td class=r>11104</td><td>= Books|Kids|Computers &amp; Technology</td>
</tr><tr><td class=r>11105</td><td>= Books|Kids|Cooking &amp; Food</td>
</tr><tr><td class=r>11106</td><td>= Books|Kids|Arts &amp; Entertainment</td>
</tr><tr><td class=r>11107</td><td>= Books|Kids|Arts &amp; Entertainment|Art</td>
</tr><tr><td class=r>11108</td><td>= Books|Kids|Arts &amp; Entertainment|Crafts</td>
</tr><tr><td class=r>11109</td><td>= Books|Kids|Arts &amp; Entertainment|Music</td>
</tr><tr><td class=r>11110</td><td>= Books|Kids|Arts &amp; Entertainment|Performing Arts</td>
</tr><tr><td class=r>11111</td><td>= Books|Kids|Family</td>
</tr><tr><td class=r>11112</td><td>= Books|Kids|Fiction</td>
</tr><tr><td class=r>11113</td><td>= Books|Kids|Fiction|Action &amp; Adventure</td>
</tr><tr><td class=r>11114</td><td>= Books|Kids|Fiction|Animals</td>
</tr><tr><td class=r>11115</td><td>= Books|Kids|Fiction|Classics</td>
</tr><tr><td class=r>11116</td><td>= Books|Kids|Fiction|Comics &amp; Graphic Novels</td>
</tr><tr><td class=r>11117</td><td>= Books|Kids|Fiction|Culture, Places &amp; People</td>
</tr><tr><td class=r>11118</td><td>= Books|Kids|Fiction|Family &amp; Relationships</td>
</tr><tr><td class=r>11119</td><td>= Books|Kids|Fiction|Fantasy</td>
</tr><tr><td class=r>11120</td><td>= Books|Kids|Fiction|Fairy Tales, Myths &amp; Fables</td>
</tr><tr><td class=r>11121</td><td>= Books|Kids|Fiction|Favorite Characters</td>
</tr><tr><td class=r>11122</td><td>= Books|Kids|Fiction|Historical</td>
</tr><tr><td class=r>11123</td><td>= Books|Kids|Fiction|Holidays &amp; Celebrations</td>
</tr><tr><td class=r>11124</td><td>= Books|Kids|Fiction|Monsters &amp; Ghosts</td>
</tr><tr><td class=r>11125</td><td>= Books|Kids|Fiction|Mysteries</td>
</tr><tr><td class=r>11126</td><td>= Books|Kids|Fiction|Nature</td>
</tr><tr><td class=r>11127</td><td>= Books|Kids|Fiction|Religion</td>
</tr><tr><td class=r>11128</td><td>= Books|Kids|Fiction|Sci-Fi</td>
</tr><tr><td class=r>11129</td><td>= Books|Kids|Fiction|Social Issues</td>
</tr><tr><td class=r>11130</td><td>= Books|Kids|Fiction|Sports &amp; Recreation</td>
</tr><tr><td class=r>11131</td><td>= Books|Kids|Fiction|Transportation</td>
</tr><tr><td class=r>11132</td><td>= Books|Kids|Games &amp; Activities</td>
</tr><tr><td class=r>11133</td><td>= Books|Kids|General Nonfiction</td>
</tr><tr><td class=r>11134</td><td>= Books|Kids|Health</td>
</tr><tr><td class=r>11135</td><td>= Books|Kids|History</td>
</tr><tr><td class=r>11136</td><td>= Books|Kids|Holidays &amp; Celebrations</td>
</tr><tr><td class=r>11137</td><td>= Books|Kids|Holidays &amp; Celebrations|Birthdays</td>
</tr><tr><td class=r>11138</td><td>= Books|Kids|Holidays &amp; Celebrations|Christmas &amp; Advent</td>
</tr><tr><td class=r>11139</td><td>= Books|Kids|Holidays &amp; Celebrations|Easter &amp; Lent</td>
</tr><tr><td class=r>11140</td><td>= Books|Kids|Holidays &amp; Celebrations|General</td>
</tr><tr><td class=r>11141</td><td>= Books|Kids|Holidays &amp; Celebrations|Halloween</td>
</tr><tr><td class=r>11142</td><td>= Books|Kids|Holidays &amp; Celebrations|Hanukkah</td>
</tr><tr><td class=r>11143</td><td>= Books|Kids|Holidays &amp; Celebrations|Other</td>
</tr><tr><td class=r>11144</td><td>= Books|Kids|Holidays &amp; Celebrations|Passover</td>
</tr><tr><td class=r>11145</td><td>= Books|Kids|Holidays &amp; Celebrations|Patriotic Holidays</td>
</tr><tr><td class=r>11146</td><td>= Books|Kids|Holidays &amp; Celebrations|Ramadan</td>
</tr><tr><td class=r>11147</td><td>= Books|Kids|Holidays &amp; Celebrations|Thanksgiving</td>
</tr><tr><td class=r>11148</td><td>= Books|Kids|Holidays &amp; Celebrations|Valentine&#39;s Day</td>
</tr><tr><td class=r>11149</td><td>= Books|Kids|Humor</td>
</tr><tr><td class=r>11150</td><td>= Books|Kids|Humor|Jokes &amp; Riddles</td>
</tr><tr><td class=r>11151</td><td>= Books|Kids|Poetry</td>
</tr><tr><td class=r>11152</td><td>= Books|Kids|Learning to Read</td>
</tr><tr><td class=r>11153</td><td>= Books|Kids|Learning to Read|Chapter Books</td>
</tr><tr><td class=r>11154</td><td>= Books|Kids|Learning to Read|Early Readers</td>
</tr><tr><td class=r>11155</td><td>= Books|Kids|Learning to Read|Intermediate Readers</td>
</tr><tr><td class=r>11156</td><td>= Books|Kids|Nursery Rhymes</td>
</tr><tr><td class=r>11157</td><td>= Books|Kids|Government</td>
</tr><tr><td class=r>11158</td><td>= Books|Kids|Reference</td>
</tr><tr><td class=r>11159</td><td>= Books|Kids|Religion</td>
</tr><tr><td class=r>11160</td><td>= Books|Kids|Science &amp; Nature</td>
</tr><tr><td class=r>11161</td><td>= Books|Kids|Social Issues</td>
</tr><tr><td class=r>11162</td><td>= Books|Kids|Social Studies</td>
</tr><tr><td class=r>11163</td><td>= Books|Kids|Sports &amp; Recreation</td>
</tr><tr><td class=r>11164</td><td>= Books|Kids|Transportation</td>
</tr><tr><td class=r>11165</td><td>= Books|Young Adult</td>
</tr><tr><td class=r>11166</td><td>= Books|Young Adult|Animals</td>
</tr><tr><td class=r>11167</td><td>= Books|Young Adult|Biography</td>
</tr><tr><td class=r>11168</td><td>= Books|Young Adult|Careers &amp; Occupations</td>
</tr><tr><td class=r>11169</td><td>= Books|Young Adult|Computers &amp; Technology</td>
</tr><tr><td class=r>11170</td><td>= Books|Young Adult|Cooking &amp; Food</td>
</tr><tr><td class=r>11171</td><td>= Books|Young Adult|Arts &amp; Entertainment</td>
</tr><tr><td class=r>11172</td><td>= Books|Young Adult|Arts &amp; Entertainment|Art</td>
</tr><tr><td class=r>11173</td><td>= Books|Young Adult|Arts &amp; Entertainment|Crafts</td>
</tr><tr><td class=r>11174</td><td>= Books|Young Adult|Arts &amp; Entertainment|Music</td>
</tr><tr><td class=r>11175</td><td>= Books|Young Adult|Arts &amp; Entertainment|Performing Arts</td>
</tr><tr><td class=r>11176</td><td>= Books|Young Adult|Family</td>
</tr><tr><td class=r>11177</td><td>= Books|Young Adult|Fiction</td>
</tr><tr><td class=r>11178</td><td>= Books|Young Adult|Fiction|Action &amp; Adventure</td>
</tr><tr><td class=r>11179</td><td>= Books|Young Adult|Fiction|Animals</td>
</tr><tr><td class=r>11180</td><td>= Books|Young Adult|Fiction|Classics</td>
</tr><tr><td class=r>11181</td><td>= Books|Young Adult|Fiction|Comics &amp; Graphic Novels</td>
</tr><tr><td class=r>11182</td><td>= Books|Young Adult|Fiction|Culture, Places &amp; People</td>
</tr><tr><td class=r>11183</td><td>= Books|Young Adult|Fiction|Dystopian</td>
</tr><tr><td class=r>11184</td><td>= Books|Young Adult|Fiction|Family &amp; Relationships</td>
</tr><tr><td class=r>11185</td><td>= Books|Young Adult|Fiction|Fantasy</td>
</tr><tr><td class=r>11186</td><td>= Books|Young Adult|Fiction|Fairy Tales, Myths &amp; Fables</td>
</tr><tr><td class=r>11187</td><td>= Books|Young Adult|Fiction|Favorite Characters</td>
</tr><tr><td class=r>11188</td><td>= Books|Young Adult|Fiction|Historical</td>
</tr><tr><td class=r>11189</td><td>= Books|Young Adult|Fiction|Holidays &amp; Celebrations</td>
</tr><tr><td class=r>11190</td><td>= Books|Young Adult|Fiction|Horror, Monsters &amp; Ghosts</td>
</tr><tr><td class=r>11191</td><td>= Books|Young Adult|Fiction|Crime &amp; Mystery</td>
</tr><tr><td class=r>11192</td><td>= Books|Young Adult|Fiction|Nature</td>
</tr><tr><td class=r>11193</td><td>= Books|Young Adult|Fiction|Religion</td>
</tr><tr><td class=r>11194</td><td>= Books|Young Adult|Fiction|Romance</td>
</tr><tr><td class=r>11195</td><td>= Books|Young Adult|Fiction|Sci-Fi</td>
</tr><tr><td class=r>11196</td><td>= Books|Young Adult|Fiction|Coming of Age</td>
</tr><tr><td class=r>11197</td><td>= Books|Young Adult|Fiction|Sports &amp; Recreation</td>
</tr><tr><td class=r>11198</td><td>= Books|Young Adult|Fiction|Transportation</td>
</tr><tr><td class=r>11199</td><td>= Books|Young Adult|Games &amp; Activities</td>
</tr><tr><td class=r>11200</td><td>= Books|Young Adult|General Nonfiction</td>
</tr><tr><td class=r>11201</td><td>= Books|Young Adult|Health</td>
</tr><tr><td class=r>11202</td><td>= Books|Young Adult|History</td>
</tr><tr><td class=r>11203</td><td>= Books|Young Adult|Holidays &amp; Celebrations</td>
</tr><tr><td class=r>11204</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Birthdays</td>
</tr><tr><td class=r>11205</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Christmas &amp; Advent</td>
</tr><tr><td class=r>11206</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Easter &amp; Lent</td>
</tr><tr><td class=r>11207</td><td>= Books|Young Adult|Holidays &amp; Celebrations|General</td>
</tr><tr><td class=r>11208</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Halloween</td>
</tr><tr><td class=r>11209</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Hanukkah</td>
</tr><tr><td class=r>11210</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Other</td>
</tr><tr><td class=r>11211</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Passover</td>
</tr><tr><td class=r>11212</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Patriotic Holidays</td>
</tr><tr><td class=r>11213</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Ramadan</td>
</tr><tr><td class=r>11214</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Thanksgiving</td>
</tr><tr><td class=r>11215</td><td>= Books|Young Adult|Holidays &amp; Celebrations|Valentine&#39;s Day</td>
</tr><tr><td class=r>11216</td><td>= Books|Young Adult|Humor</td>
</tr><tr><td class=r>11217</td><td>= Books|Young Adult|Humor|Jokes &amp; Riddles</td>
</tr><tr><td class=r>11218</td><td>= Books|Young Adult|Poetry</td>
</tr><tr><td class=r>11219</td><td>= Books|Young Adult|Politics &amp; Government</td>
</tr><tr><td class=r>11220</td><td>= Books|Young Adult|Reference</td>
</tr><tr><td class=r>11221</td><td>= Books|Young Adult|Religion</td>
</tr><tr><td class=r>11222</td><td>= Books|Young Adult|Science &amp; Nature</td>
</tr><tr><td class=r>11223</td><td>= Books|Young Adult|Coming of Age</td>
</tr><tr><td class=r>11224</td><td>= Books|Young Adult|Social Studies</td>
</tr><tr><td class=r>11225</td><td>= Books|Young Adult|Sports &amp; Recreation</td>
</tr><tr><td class=r>11226</td><td>= Books|Young Adult|Transportation</td>
</tr><tr><td class=r>11227</td><td>= Books|Communications &amp; Media</td>
</tr><tr><td class=r>11228</td><td>= Books|Military &amp; Warfare</td>
</tr><tr><td class=r>11229</td><td>= Books|Romance|Inspirational</td>
</tr><tr><td class=r>11231</td><td>= Books|Romance|Holiday</td>
</tr><tr><td class=r>11232</td><td>= Books|Romance|Wholesome</td>
</tr><tr><td class=r>11233</td><td>= Books|Romance|Military</td>
</tr><tr><td class=r>11234</td><td>= Books|Arts &amp; Entertainment|Art History</td>
</tr><tr><td class=r>11236</td><td>= Books|Arts &amp; Entertainment|Design</td>
</tr><tr><td class=r>11243</td><td>= Books|Business &amp; Personal Finance|Accounting</td>
</tr><tr><td class=r>11244</td><td>= Books|Business &amp; Personal Finance|Hospitality</td>
</tr><tr><td class=r>11245</td><td>= Books|Business &amp; Personal Finance|Real Estate</td>
</tr><tr><td class=r>11246</td><td>= Books|Humor|Jokes &amp; Riddles</td>
</tr><tr><td class=r>11247</td><td>= Books|Religion &amp; Spirituality|Comparative Religion</td>
</tr><tr><td class=r>11255</td><td>= Books|Cookbooks, Food &amp; Wine|Culinary Arts</td>
</tr><tr><td class=r>11259</td><td>= Books|Mysteries &amp; Thrillers|Cozy</td>
</tr><tr><td class=r>11260</td><td>= Books|Politics &amp; Current Events|Current Events</td>
</tr><tr><td class=r>11261</td><td>= Books|Politics &amp; Current Events|Foreign Policy &amp; International Relations</td>
</tr><tr><td class=r>11262</td><td>= Books|Politics &amp; Current Events|Local Government</td>
</tr><tr><td class=r>11263</td><td>= Books|Politics &amp; Current Events|National Government</td>
</tr><tr><td class=r>11264</td><td>= Books|Politics &amp; Current Events|Political Science</td>
</tr><tr><td class=r>11265</td><td>= Books|Politics &amp; Current Events|Public Administration</td>
</tr><tr><td class=r>11266</td><td>= Books|Politics &amp; Current Events|World Affairs</td>
</tr><tr><td class=r>11273</td><td>= Books|Nonfiction|Family &amp; Relationships|Family &amp; Childcare</td>
</tr><tr><td class=r>11274</td><td>= Books|Nonfiction|Family &amp; Relationships|Love &amp; Romance</td>
</tr><tr><td class=r>11275</td><td>= Books|Sci-Fi &amp; Fantasy|Fantasy|Urban</td>
</tr><tr><td class=r>11276</td><td>= Books|Reference|Foreign Languages|Arabic</td>
</tr><tr><td class=r>11277</td><td>= Books|Reference|Foreign Languages|Bilingual Editions</td>
</tr><tr><td class=r>11278</td><td>= Books|Reference|Foreign Languages|African Languages</td>
</tr><tr><td class=r>11279</td><td>= Books|Reference|Foreign Languages|Ancient Languages</td>
</tr><tr><td class=r>11280</td><td>= Books|Reference|Foreign Languages|Chinese</td>
</tr><tr><td class=r>11281</td><td>= Books|Reference|Foreign Languages|English</td>
</tr><tr><td class=r>11282</td><td>= Books|Reference|Foreign Languages|French</td>
</tr><tr><td class=r>11283</td><td>= Books|Reference|Foreign Languages|German</td>
</tr><tr><td class=r>11284</td><td>= Books|Reference|Foreign Languages|Hebrew</td>
</tr><tr><td class=r>11285</td><td>= Books|Reference|Foreign Languages|Hindi</td>
</tr><tr><td class=r>11286</td><td>= Books|Reference|Foreign Languages|Italian</td>
</tr><tr><td class=r>11287</td><td>= Books|Reference|Foreign Languages|Japanese</td>
</tr><tr><td class=r>11288</td><td>= Books|Reference|Foreign Languages|Korean</td>
</tr><tr><td class=r>11289</td><td>= Books|Reference|Foreign Languages|Linguistics</td>
</tr><tr><td class=r>11290</td><td>= Books|Reference|Foreign Languages|Other Languages</td>
</tr><tr><td class=r>11291</td><td>= Books|Reference|Foreign Languages|Portuguese</td>
</tr><tr><td class=r>11292</td><td>= Books|Reference|Foreign Languages|Russian</td>
</tr><tr><td class=r>11293</td><td>= Books|Reference|Foreign Languages|Spanish</td>
</tr><tr><td class=r>11294</td><td>= Books|Reference|Foreign Languages|Speech Pathology</td>
</tr><tr><td class=r>11295</td><td>= Books|Science &amp; Nature|Mathematics|Advanced Mathematics</td>
</tr><tr><td class=r>11296</td><td>= Books|Science &amp; Nature|Mathematics|Algebra</td>
</tr><tr><td class=r>11297</td><td>= Books|Science &amp; Nature|Mathematics|Arithmetic</td>
</tr><tr><td class=r>11298</td><td>= Books|Science &amp; Nature|Mathematics|Calculus</td>
</tr><tr><td class=r>11299</td><td>= Books|Science &amp; Nature|Mathematics|Geometry</td>
</tr><tr><td class=r>11300</td><td>= Books|Science &amp; Nature|Mathematics|Statistics</td>
</tr><tr><td class=r>11301</td><td>= Books|Professional &amp; Technical|Medical|Veterinary</td>
</tr><tr><td class=r>11302</td><td>= Books|Professional &amp; Technical|Medical|Neuroscience</td>
</tr><tr><td class=r>11303</td><td>= Books|Professional &amp; Technical|Medical|Immunology</td>
</tr><tr><td class=r>11304</td><td>= Books|Professional &amp; Technical|Medical|Nursing</td>
</tr><tr><td class=r>11305</td><td>= Books|Professional &amp; Technical|Medical|Pharmacology &amp; Toxicology</td>
</tr><tr><td class=r>11306</td><td>= Books|Professional &amp; Technical|Medical|Anatomy &amp; Physiology</td>
</tr><tr><td class=r>11307</td><td>= Books|Professional &amp; Technical|Medical|Dentistry</td>
</tr><tr><td class=r>11308</td><td>= Books|Professional &amp; Technical|Medical|Emergency Medicine</td>
</tr><tr><td class=r>11309</td><td>= Books|Professional &amp; Technical|Medical|Genetics</td>
</tr><tr><td class=r>11310</td><td>= Books|Professional &amp; Technical|Medical|Psychiatry</td>
</tr><tr><td class=r>11311</td><td>= Books|Professional &amp; Technical|Medical|Radiology</td>
</tr><tr><td class=r>11312</td><td>= Books|Professional &amp; Technical|Medical|Alternative Medicine</td>
</tr><tr><td class=r>11317</td><td>= Books|Nonfiction|Philosophy|Political Philosophy</td>
</tr><tr><td class=r>11319</td><td>= Books|Nonfiction|Philosophy|Philosophy of Language</td>
</tr><tr><td class=r>11320</td><td>= Books|Nonfiction|Philosophy|Philosophy of Religion</td>
</tr><tr><td class=r>11327</td><td>= Books|Nonfiction|Social Science|Sociology</td>
</tr><tr><td class=r>11329</td><td>= Books|Professional &amp; Technical|Engineering|Aeronautics</td>
</tr><tr><td class=r>11330</td><td>= Books|Professional &amp; Technical|Engineering|Chemical &amp; Petroleum Engineering</td>
</tr><tr><td class=r>11331</td><td>= Books|Professional &amp; Technical|Engineering|Civil Engineering</td>
</tr><tr><td class=r>11332</td><td>= Books|Professional &amp; Technical|Engineering|Computer Science</td>
</tr><tr><td class=r>11333</td><td>= Books|Professional &amp; Technical|Engineering|Electrical Engineering</td>
</tr><tr><td class=r>11334</td><td>= Books|Professional &amp; Technical|Engineering|Environmental Engineering</td>
</tr><tr><td class=r>11335</td><td>= Books|Professional &amp; Technical|Engineering|Mechanical Engineering</td>
</tr><tr><td class=r>11336</td><td>= Books|Professional &amp; Technical|Engineering|Power Resources</td>
</tr><tr><td class=r>11337</td><td>= Books|Comics &amp; Graphic Novels|Manga|Boys</td>
</tr><tr><td class=r>11338</td><td>= Books|Comics &amp; Graphic Novels|Manga|Men</td>
</tr><tr><td class=r>11339</td><td>= Books|Comics &amp; Graphic Novels|Manga|Girls</td>
</tr><tr><td class=r>11340</td><td>= Books|Comics &amp; Graphic Novels|Manga|Women</td>
</tr><tr><td class=r>11341</td><td>= Books|Comics &amp; Graphic Novels|Manga|Other</td>
</tr><tr><td class=r>11342</td><td>= Books|Comics &amp; Graphic Novels|Manga|Yaoi</td>
</tr><tr><td class=r>11343</td><td>= Books|Comics &amp; Graphic Novels|Manga|Comic Essays</td>
</tr><tr><td class=r>12001</td><td>= Mac App Store|Business</td>
</tr><tr><td class=r>12002</td><td>= Mac App Store|Developer Tools</td>
</tr><tr><td class=r>12003</td><td>= Mac App Store|Education</td>
</tr><tr><td class=r>12004</td><td>= Mac App Store|Entertainment</td>
</tr><tr><td class=r>12005</td><td>= Mac App Store|Finance</td>
</tr><tr><td class=r>12006</td><td>= Mac App Store|Games</td>
</tr><tr><td class=r>12007</td><td>= Mac App Store|Health &amp; Fitness</td>
</tr><tr><td class=r>12008</td><td>= Mac App Store|Lifestyle</td>
</tr><tr><td class=r>12010</td><td>= Mac App Store|Medical</td>
</tr><tr><td class=r>12011</td><td>= Mac App Store|Music</td>
</tr><tr><td class=r>12012</td><td>= Mac App Store|News</td>
</tr><tr><td class=r>12013</td><td>= Mac App Store|Photography</td>
</tr><tr><td class=r>12014</td><td>= Mac App Store|Productivity</td>
</tr><tr><td class=r>12015</td><td>= Mac App Store|Reference</td>
</tr><tr><td class=r>12016</td><td>= Mac App Store|Social Networking</td>
</tr><tr><td class=r>12017</td><td>= Mac App Store|Sports</td>
</tr><tr><td class=r>12018</td><td>= Mac App Store|Travel</td>
</tr><tr><td class=r>12019</td><td>= Mac App Store|Utilities</td>
</tr><tr><td class=r>12020</td><td>= Mac App Store|Video</td>
</tr><tr><td class=r>12021</td><td>= Mac App Store|Weather</td>
</tr><tr><td class=r>12022</td><td>= Mac App Store|Graphics &amp; Design</td>
</tr><tr><td class=r>12201</td><td>= Mac App Store|Games|Action</td>
</tr><tr><td class=r>12202</td><td>= Mac App Store|Games|Adventure</td>
</tr><tr><td class=r>12203</td><td>= Mac App Store|Games|Casual</td>
</tr><tr><td class=r>12204</td><td>= Mac App Store|Games|Board</td>
</tr><tr><td class=r>12205</td><td>= Mac App Store|Games|Card</td>
</tr><tr><td class=r>12206</td><td>= Mac App Store|Games|Casino</td>
</tr><tr><td class=r>12207</td><td>= Mac App Store|Games|Dice</td>
</tr><tr><td class=r>12208</td><td>= Mac App Store|Games|Educational</td>
</tr><tr><td class=r>12209</td><td>= Mac App Store|Games|Family</td>
</tr><tr><td class=r>12210</td><td>= Mac App Store|Games|Kids</td>
</tr><tr><td class=r>12211</td><td>= Mac App Store|Games|Music</td>
</tr><tr><td class=r>12212</td><td>= Mac App Store|Games|Puzzle</td>
</tr><tr><td class=r>12213</td><td>= Mac App Store|Games|Racing</td>
</tr><tr><td class=r>12214</td><td>= Mac App Store|Games|Role Playing</td>
</tr><tr><td class=r>12215</td><td>= Mac App Store|Games|Simulation</td>
</tr><tr><td class=r>12216</td><td>= Mac App Store|Games|Sports</td>
</tr><tr><td class=r>12217</td><td>= Mac App Store|Games|Strategy</td>
</tr><tr><td class=r>12218</td><td>= Mac App Store|Games|Trivia</td>
</tr><tr><td class=r>12219</td><td>= Mac App Store|Games|Word</td>
</tr><tr><td class=r>13001</td><td>= App Store|Magazines &amp; Newspapers|News &amp; Politics</td>
</tr><tr><td class=r>13002</td><td>= App Store|Magazines &amp; Newspapers|Fashion &amp; Style</td>
</tr><tr><td class=r>13003</td><td>= App Store|Magazines &amp; Newspapers|Home &amp; Garden</td>
</tr><tr><td class=r>13004</td><td>= App Store|Magazines &amp; Newspapers|Outdoors &amp; Nature</td>
</tr><tr><td class=r>13005</td><td>= App Store|Magazines &amp; Newspapers|Sports &amp; Leisure</td>
</tr><tr><td class=r>13006</td><td>= App Store|Magazines &amp; Newspapers|Automotive</td>
</tr><tr><td class=r>13007</td><td>= App Store|Magazines &amp; Newspapers|Arts &amp; Photography</td>
</tr><tr><td class=r>13008</td><td>= App Store|Magazines &amp; Newspapers|Brides &amp; Weddings</td>
</tr><tr><td class=r>13009</td><td>= App Store|Magazines &amp; Newspapers|Business &amp; Investing</td>
</tr><tr><td class=r>13010</td><td>= App Store|Magazines &amp; Newspapers|Children&#39;s Magazines</td>
</tr><tr><td class=r>13011</td><td>= App Store|Magazines &amp; Newspapers|Computers &amp; Internet</td>
</tr><tr><td class=r>13012</td><td>= App Store|Magazines &amp; Newspapers|Cooking, Food &amp; Drink</td>
</tr><tr><td class=r>13013</td><td>= App Store|Magazines &amp; Newspapers|Crafts &amp; Hobbies</td>
</tr><tr><td class=r>13014</td><td>= App Store|Magazines &amp; Newspapers|Electronics &amp; Audio</td>
</tr><tr><td class=r>13015</td><td>= App Store|Magazines &amp; Newspapers|Entertainment</td>
</tr><tr><td class=r>13017</td><td>= App Store|Magazines &amp; Newspapers|Health, Mind &amp; Body</td>
</tr><tr><td class=r>13018</td><td>= App Store|Magazines &amp; Newspapers|History</td>
</tr><tr><td class=r>13019</td><td>= App Store|Magazines &amp; Newspapers|Literary Magazines &amp; Journals</td>
</tr><tr><td class=r>13020</td><td>= App Store|Magazines &amp; Newspapers|Men&#39;s Interest</td>
</tr><tr><td class=r>13021</td><td>= App Store|Magazines &amp; Newspapers|Movies &amp; Music</td>
</tr><tr><td class=r>13023</td><td>= App Store|Magazines &amp; Newspapers|Parenting &amp; Family</td>
</tr><tr><td class=r>13024</td><td>= App Store|Magazines &amp; Newspapers|Pets</td>
</tr><tr><td class=r>13025</td><td>= App Store|Magazines &amp; Newspapers|Professional &amp; Trade</td>
</tr><tr><td class=r>13026</td><td>= App Store|Magazines &amp; Newspapers|Regional News</td>
</tr><tr><td class=r>13027</td><td>= App Store|Magazines &amp; Newspapers|Science</td>
</tr><tr><td class=r>13028</td><td>= App Store|Magazines &amp; Newspapers|Teens</td>
</tr><tr><td class=r>13029</td><td>= App Store|Magazines &amp; Newspapers|Travel &amp; Regional</td>
</tr><tr><td class=r>13030</td><td>= App Store|Magazines &amp; Newspapers|Women&#39;s Interest</td>
</tr><tr><td class=r>15000</td><td>= Textbooks|Arts &amp; Entertainment</td>
</tr><tr><td class=r>15001</td><td>= Textbooks|Arts &amp; Entertainment|Art &amp; Architecture</td>
</tr><tr><td class=r>15002</td><td>= Textbooks|Arts &amp; Entertainment|Art &amp; Architecture|Urban Planning</td>
</tr><tr><td class=r>15003</td><td>= Textbooks|Arts &amp; Entertainment|Art History</td>
</tr><tr><td class=r>15004</td><td>= Textbooks|Arts &amp; Entertainment|Dance</td>
</tr><tr><td class=r>15005</td><td>= Textbooks|Arts &amp; Entertainment|Design</td>
</tr><tr><td class=r>15006</td><td>= Textbooks|Arts &amp; Entertainment|Fashion</td>
</tr><tr><td class=r>15007</td><td>= Textbooks|Arts &amp; Entertainment|Film</td>
</tr><tr><td class=r>15008</td><td>= Textbooks|Arts &amp; Entertainment|Games</td>
</tr><tr><td class=r>15009</td><td>= Textbooks|Arts &amp; Entertainment|Interior Design</td>
</tr><tr><td class=r>15010</td><td>= Textbooks|Arts &amp; Entertainment|Media Arts</td>
</tr><tr><td class=r>15011</td><td>= Textbooks|Arts &amp; Entertainment|Music</td>
</tr><tr><td class=r>15012</td><td>= Textbooks|Arts &amp; Entertainment|Performing Arts</td>
</tr><tr><td class=r>15013</td><td>= Textbooks|Arts &amp; Entertainment|Photography</td>
</tr><tr><td class=r>15014</td><td>= Textbooks|Arts &amp; Entertainment|Theater</td>
</tr><tr><td class=r>15015</td><td>= Textbooks|Arts &amp; Entertainment|TV</td>
</tr><tr><td class=r>15016</td><td>= Textbooks|Arts &amp; Entertainment|Visual Arts</td>
</tr><tr><td class=r>15017</td><td>= Textbooks|Biographies &amp; Memoirs</td>
</tr><tr><td class=r>15018</td><td>= Textbooks|Business &amp; Personal Finance</td>
</tr><tr><td class=r>15019</td><td>= Textbooks|Business &amp; Personal Finance|Accounting</td>
</tr><tr><td class=r>15020</td><td>= Textbooks|Business &amp; Personal Finance|Careers</td>
</tr><tr><td class=r>15021</td><td>= Textbooks|Business &amp; Personal Finance|Economics</td>
</tr><tr><td class=r>15022</td><td>= Textbooks|Business &amp; Personal Finance|Finance</td>
</tr><tr><td class=r>15023</td><td>= Textbooks|Business &amp; Personal Finance|Hospitality</td>
</tr><tr><td class=r>15024</td><td>= Textbooks|Business &amp; Personal Finance|Industries &amp; Professions</td>
</tr><tr><td class=r>15025</td><td>= Textbooks|Business &amp; Personal Finance|Investing</td>
</tr><tr><td class=r>15026</td><td>= Textbooks|Business &amp; Personal Finance|Management &amp; Leadership</td>
</tr><tr><td class=r>15027</td><td>= Textbooks|Business &amp; Personal Finance|Marketing &amp; Sales</td>
</tr><tr><td class=r>15028</td><td>= Textbooks|Business &amp; Personal Finance|Personal Finance</td>
</tr><tr><td class=r>15029</td><td>= Textbooks|Business &amp; Personal Finance|Real Estate</td>
</tr><tr><td class=r>15030</td><td>= Textbooks|Business &amp; Personal Finance|Reference</td>
</tr><tr><td class=r>15031</td><td>= Textbooks|Business &amp; Personal Finance|Small Business &amp; Entrepreneurship</td>
</tr><tr><td class=r>15032</td><td>= Textbooks|Children &amp; Teens</td>
</tr><tr><td class=r>15033</td><td>= Textbooks|Children &amp; Teens|Fiction</td>
</tr><tr><td class=r>15034</td><td>= Textbooks|Children &amp; Teens|Nonfiction</td>
</tr><tr><td class=r>15035</td><td>= Textbooks|Comics &amp; Graphic Novels</td>
</tr><tr><td class=r>15036</td><td>= Textbooks|Comics &amp; Graphic Novels|Graphic Novels</td>
</tr><tr><td class=r>15037</td><td>= Textbooks|Comics &amp; Graphic Novels|Manga</td>
</tr><tr><td class=r>15038</td><td>= Textbooks|Communications &amp; Media</td>
</tr><tr><td class=r>15039</td><td>= Textbooks|Communications &amp; Media|Broadcasting</td>
</tr><tr><td class=r>15040</td><td>= Textbooks|Communications &amp; Media|Digital Media</td>
</tr><tr><td class=r>15041</td><td>= Textbooks|Communications &amp; Media|Journalism</td>
</tr><tr><td class=r>15042</td><td>= Textbooks|Communications &amp; Media|Photojournalism</td>
</tr><tr><td class=r>15043</td><td>= Textbooks|Communications &amp; Media|Print</td>
</tr><tr><td class=r>15044</td><td>= Textbooks|Communications &amp; Media|Speech</td>
</tr><tr><td class=r>15045</td><td>= Textbooks|Communications &amp; Media|Writing</td>
</tr><tr><td class=r>15046</td><td>= Textbooks|Computers &amp; Internet</td>
</tr><tr><td class=r>15047</td><td>= Textbooks|Computers &amp; Internet|Computers</td>
</tr><tr><td class=r>15048</td><td>= Textbooks|Computers &amp; Internet|Databases</td>
</tr><tr><td class=r>15049</td><td>= Textbooks|Computers &amp; Internet|Digital Media</td>
</tr><tr><td class=r>15050</td><td>= Textbooks|Computers &amp; Internet|Internet</td>
</tr><tr><td class=r>15051</td><td>= Textbooks|Computers &amp; Internet|Network</td>
</tr><tr><td class=r>15052</td><td>= Textbooks|Computers &amp; Internet|Operating Systems</td>
</tr><tr><td class=r>15053</td><td>= Textbooks|Computers &amp; Internet|Programming</td>
</tr><tr><td class=r>15054</td><td>= Textbooks|Computers &amp; Internet|Software</td>
</tr><tr><td class=r>15055</td><td>= Textbooks|Computers &amp; Internet|System Administration</td>
</tr><tr><td class=r>15056</td><td>= Textbooks|Cookbooks, Food &amp; Wine</td>
</tr><tr><td class=r>15057</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Beverages</td>
</tr><tr><td class=r>15058</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Courses &amp; Dishes</td>
</tr><tr><td class=r>15059</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Culinary Arts</td>
</tr><tr><td class=r>15060</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Methods</td>
</tr><tr><td class=r>15061</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Reference</td>
</tr><tr><td class=r>15062</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Regional &amp; Ethnic</td>
</tr><tr><td class=r>15063</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Special Diet</td>
</tr><tr><td class=r>15064</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Special Occasions</td>
</tr><tr><td class=r>15065</td><td>= Textbooks|Cookbooks, Food &amp; Wine|Specific Ingredients</td>
</tr><tr><td class=r>15066</td><td>= Textbooks|Engineering</td>
</tr><tr><td class=r>15067</td><td>= Textbooks|Engineering|Aeronautics</td>
</tr><tr><td class=r>15068</td><td>= Textbooks|Engineering|Chemical &amp; Petroleum Engineering</td>
</tr><tr><td class=r>15069</td><td>= Textbooks|Engineering|Civil Engineering</td>
</tr><tr><td class=r>15070</td><td>= Textbooks|Engineering|Computer Science</td>
</tr><tr><td class=r>15071</td><td>= Textbooks|Engineering|Electrical Engineering</td>
</tr><tr><td class=r>15072</td><td>= Textbooks|Engineering|Environmental Engineering</td>
</tr><tr><td class=r>15073</td><td>= Textbooks|Engineering|Mechanical Engineering</td>
</tr><tr><td class=r>15074</td><td>= Textbooks|Engineering|Power Resources</td>
</tr><tr><td class=r>15075</td><td>= Textbooks|Fiction &amp; Literature</td>
</tr><tr><td class=r>15076</td><td>= Textbooks|Fiction &amp; Literature|Latino</td>
</tr><tr><td class=r>15077</td><td>= Textbooks|Fiction &amp; Literature|Action &amp; Adventure</td>
</tr><tr><td class=r>15078</td><td>= Textbooks|Fiction &amp; Literature|African American</td>
</tr><tr><td class=r>15079</td><td>= Textbooks|Fiction &amp; Literature|Anthologies</td>
</tr><tr><td class=r>15080</td><td>= Textbooks|Fiction &amp; Literature|Classics</td>
</tr><tr><td class=r>15081</td><td>= Textbooks|Fiction &amp; Literature|Comparative Literature</td>
</tr><tr><td class=r>15082</td><td>= Textbooks|Fiction &amp; Literature|Erotica</td>
</tr><tr><td class=r>15083</td><td>= Textbooks|Fiction &amp; Literature|Gay</td>
</tr><tr><td class=r>15084</td><td>= Textbooks|Fiction &amp; Literature|Ghost</td>
</tr><tr><td class=r>15085</td><td>= Textbooks|Fiction &amp; Literature|Historical</td>
</tr><tr><td class=r>15086</td><td>= Textbooks|Fiction &amp; Literature|Horror</td>
</tr><tr><td class=r>15087</td><td>= Textbooks|Fiction &amp; Literature|Literary</td>
</tr><tr><td class=r>15088</td><td>= Textbooks|Fiction &amp; Literature|Literary Criticism</td>
</tr><tr><td class=r>15089</td><td>= Textbooks|Fiction &amp; Literature|Poetry</td>
</tr><tr><td class=r>15090</td><td>= Textbooks|Fiction &amp; Literature|Religious</td>
</tr><tr><td class=r>15091</td><td>= Textbooks|Fiction &amp; Literature|Short Stories</td>
</tr><tr><td class=r>15092</td><td>= Textbooks|Health, Mind &amp; Body</td>
</tr><tr><td class=r>15093</td><td>= Textbooks|Health, Mind &amp; Body|Fitness</td>
</tr><tr><td class=r>15094</td><td>= Textbooks|Health, Mind &amp; Body|Self-Improvement</td>
</tr><tr><td class=r>15095</td><td>= Textbooks|History</td>
</tr><tr><td class=r>15096</td><td>= Textbooks|History|Africa</td>
</tr><tr><td class=r>15097</td><td>= Textbooks|History|Americas</td>
</tr><tr><td class=r>15098</td><td>= Textbooks|History|Americas|Canada</td>
</tr><tr><td class=r>15099</td><td>= Textbooks|History|Americas|Latin America</td>
</tr><tr><td class=r>15100</td><td>= Textbooks|History|Americas|United States</td>
</tr><tr><td class=r>15101</td><td>= Textbooks|History|Ancient</td>
</tr><tr><td class=r>15102</td><td>= Textbooks|History|Asia</td>
</tr><tr><td class=r>15103</td><td>= Textbooks|History|Australia &amp; Oceania</td>
</tr><tr><td class=r>15104</td><td>= Textbooks|History|Europe</td>
</tr><tr><td class=r>15105</td><td>= Textbooks|History|Middle East</td>
</tr><tr><td class=r>15106</td><td>= Textbooks|History|Military</td>
</tr><tr><td class=r>15107</td><td>= Textbooks|History|World</td>
</tr><tr><td class=r>15108</td><td>= Textbooks|Humor</td>
</tr><tr><td class=r>15109</td><td>= Textbooks|Language Studies</td>
</tr><tr><td class=r>15110</td><td>= Textbooks|Language Studies|African Languages</td>
</tr><tr><td class=r>15111</td><td>= Textbooks|Language Studies|Ancient Languages</td>
</tr><tr><td class=r>15112</td><td>= Textbooks|Language Studies|Arabic</td>
</tr><tr><td class=r>15113</td><td>= Textbooks|Language Studies|Bilingual Editions</td>
</tr><tr><td class=r>15114</td><td>= Textbooks|Language Studies|Chinese</td>
</tr><tr><td class=r>15115</td><td>= Textbooks|Language Studies|English</td>
</tr><tr><td class=r>15116</td><td>= Textbooks|Language Studies|French</td>
</tr><tr><td class=r>15117</td><td>= Textbooks|Language Studies|German</td>
</tr><tr><td class=r>15118</td><td>= Textbooks|Language Studies|Hebrew</td>
</tr><tr><td class=r>15119</td><td>= Textbooks|Language Studies|Hindi</td>
</tr><tr><td class=r>15120</td><td>= Textbooks|Language Studies|Indigenous Languages</td>
</tr><tr><td class=r>15121</td><td>= Textbooks|Language Studies|Italian</td>
</tr><tr><td class=r>15122</td><td>= Textbooks|Language Studies|Japanese</td>
</tr><tr><td class=r>15123</td><td>= Textbooks|Language Studies|Korean</td>
</tr><tr><td class=r>15124</td><td>= Textbooks|Language Studies|Linguistics</td>
</tr><tr><td class=r>15125</td><td>= Textbooks|Language Studies|Other Language</td>
</tr><tr><td class=r>15126</td><td>= Textbooks|Language Studies|Portuguese</td>
</tr><tr><td class=r>15127</td><td>= Textbooks|Language Studies|Russian</td>
</tr><tr><td class=r>15128</td><td>= Textbooks|Language Studies|Spanish</td>
</tr><tr><td class=r>15129</td><td>= Textbooks|Language Studies|Speech Pathology</td>
</tr><tr><td class=r>15130</td><td>= Textbooks|Lifestyle &amp; Home</td>
</tr><tr><td class=r>15131</td><td>= Textbooks|Lifestyle &amp; Home|Antiques &amp; Collectibles</td>
</tr><tr><td class=r>15132</td><td>= Textbooks|Lifestyle &amp; Home|Crafts &amp; Hobbies</td>
</tr><tr><td class=r>15133</td><td>= Textbooks|Lifestyle &amp; Home|Gardening</td>
</tr><tr><td class=r>15134</td><td>= Textbooks|Lifestyle &amp; Home|Pets</td>
</tr><tr><td class=r>15135</td><td>= Textbooks|Mathematics</td>
</tr><tr><td class=r>15136</td><td>= Textbooks|Mathematics|Advanced Mathematics</td>
</tr><tr><td class=r>15137</td><td>= Textbooks|Mathematics|Algebra</td>
</tr><tr><td class=r>15138</td><td>= Textbooks|Mathematics|Arithmetic</td>
</tr><tr><td class=r>15139</td><td>= Textbooks|Mathematics|Calculus</td>
</tr><tr><td class=r>15140</td><td>= Textbooks|Mathematics|Geometry</td>
</tr><tr><td class=r>15141</td><td>= Textbooks|Mathematics|Statistics</td>
</tr><tr><td class=r>15142</td><td>= Textbooks|Medicine</td>
</tr><tr><td class=r>15143</td><td>= Textbooks|Medicine|Anatomy &amp; Physiology</td>
</tr><tr><td class=r>15144</td><td>= Textbooks|Medicine|Dentistry</td>
</tr><tr><td class=r>15145</td><td>= Textbooks|Medicine|Emergency Medicine</td>
</tr><tr><td class=r>15146</td><td>= Textbooks|Medicine|Genetics</td>
</tr><tr><td class=r>15147</td><td>= Textbooks|Medicine|Immunology</td>
</tr><tr><td class=r>15148</td><td>= Textbooks|Medicine|Neuroscience</td>
</tr><tr><td class=r>15149</td><td>= Textbooks|Medicine|Nursing</td>
</tr><tr><td class=r>15150</td><td>= Textbooks|Medicine|Pharmacology &amp; Toxicology</td>
</tr><tr><td class=r>15151</td><td>= Textbooks|Medicine|Psychiatry</td>
</tr><tr><td class=r>15152</td><td>= Textbooks|Medicine|Psychology</td>
</tr><tr><td class=r>15153</td><td>= Textbooks|Medicine|Radiology</td>
</tr><tr><td class=r>15154</td><td>= Textbooks|Medicine|Veterinary</td>
</tr><tr><td class=r>15155</td><td>= Textbooks|Mysteries &amp; Thrillers</td>
</tr><tr><td class=r>15156</td><td>= Textbooks|Mysteries &amp; Thrillers|British Detectives</td>
</tr><tr><td class=r>15157</td><td>= Textbooks|Mysteries &amp; Thrillers|Hard-Boiled</td>
</tr><tr><td class=r>15158</td><td>= Textbooks|Mysteries &amp; Thrillers|Historical</td>
</tr><tr><td class=r>15159</td><td>= Textbooks|Mysteries &amp; Thrillers|Police Procedural</td>
</tr><tr><td class=r>15160</td><td>= Textbooks|Mysteries &amp; Thrillers|Short Stories</td>
</tr><tr><td class=r>15161</td><td>= Textbooks|Mysteries &amp; Thrillers|Women Sleuths</td>
</tr><tr><td class=r>15162</td><td>= Textbooks|Nonfiction</td>
</tr><tr><td class=r>15163</td><td>= Textbooks|Nonfiction|Family &amp; Relationships</td>
</tr><tr><td class=r>15164</td><td>= Textbooks|Nonfiction|Transportation</td>
</tr><tr><td class=r>15165</td><td>= Textbooks|Nonfiction|True Crime</td>
</tr><tr><td class=r>15166</td><td>= Textbooks|Parenting</td>
</tr><tr><td class=r>15167</td><td>= Textbooks|Philosophy</td>
</tr><tr><td class=r>15168</td><td>= Textbooks|Philosophy|Aesthetics</td>
</tr><tr><td class=r>15169</td><td>= Textbooks|Philosophy|Epistemology</td>
</tr><tr><td class=r>15170</td><td>= Textbooks|Philosophy|Ethics</td>
</tr><tr><td class=r>15171</td><td>= Textbooks|Philosophy|Philosophy of Language</td>
</tr><tr><td class=r>15172</td><td>= Textbooks|Philosophy|Logic</td>
</tr><tr><td class=r>15173</td><td>= Textbooks|Philosophy|Metaphysics</td>
</tr><tr><td class=r>15174</td><td>= Textbooks|Philosophy|Political Philosophy</td>
</tr><tr><td class=r>15175</td><td>= Textbooks|Philosophy|Philosophy of Religion</td>
</tr><tr><td class=r>15176</td><td>= Textbooks|Politics &amp; Current Events</td>
</tr><tr><td class=r>15177</td><td>= Textbooks|Politics &amp; Current Events|Current Events</td>
</tr><tr><td class=r>15178</td><td>= Textbooks|Politics &amp; Current Events|Foreign Policy &amp; International Relations</td>
</tr><tr><td class=r>15179</td><td>= Textbooks|Politics &amp; Current Events|Local Governments</td>
</tr><tr><td class=r>15180</td><td>= Textbooks|Politics &amp; Current Events|National Governments</td>
</tr><tr><td class=r>15181</td><td>= Textbooks|Politics &amp; Current Events|Political Science</td>
</tr><tr><td class=r>15182</td><td>= Textbooks|Politics &amp; Current Events|Public Administration</td>
</tr><tr><td class=r>15183</td><td>= Textbooks|Politics &amp; Current Events|World Affairs</td>
</tr><tr><td class=r>15184</td><td>= Textbooks|Professional &amp; Technical</td>
</tr><tr><td class=r>15185</td><td>= Textbooks|Professional &amp; Technical|Design</td>
</tr><tr><td class=r>15186</td><td>= Textbooks|Professional &amp; Technical|Language Arts &amp; Disciplines</td>
</tr><tr><td class=r>15187</td><td>= Textbooks|Professional &amp; Technical|Engineering</td>
</tr><tr><td class=r>15188</td><td>= Textbooks|Professional &amp; Technical|Law</td>
</tr><tr><td class=r>15189</td><td>= Textbooks|Professional &amp; Technical|Medical</td>
</tr><tr><td class=r>15190</td><td>= Textbooks|Reference</td>
</tr><tr><td class=r>15191</td><td>= Textbooks|Reference|Almanacs &amp; Yearbooks</td>
</tr><tr><td class=r>15192</td><td>= Textbooks|Reference|Atlases &amp; Maps</td>
</tr><tr><td class=r>15193</td><td>= Textbooks|Reference|Catalogs &amp; Directories</td>
</tr><tr><td class=r>15194</td><td>= Textbooks|Reference|Consumer Guides</td>
</tr><tr><td class=r>15195</td><td>= Textbooks|Reference|Dictionaries &amp; Thesauruses</td>
</tr><tr><td class=r>15196</td><td>= Textbooks|Reference|Encyclopedias</td>
</tr><tr><td class=r>15197</td><td>= Textbooks|Reference|Etiquette</td>
</tr><tr><td class=r>15198</td><td>= Textbooks|Reference|Quotations</td>
</tr><tr><td class=r>15199</td><td>= Textbooks|Reference|Study Aids</td>
</tr><tr><td class=r>15200</td><td>= Textbooks|Reference|Words &amp; Language</td>
</tr><tr><td class=r>15201</td><td>= Textbooks|Reference|Writing</td>
</tr><tr><td class=r>15202</td><td>= Textbooks|Religion &amp; Spirituality</td>
</tr><tr><td class=r>15203</td><td>= Textbooks|Religion &amp; Spirituality|Bible Studies</td>
</tr><tr><td class=r>15204</td><td>= Textbooks|Religion &amp; Spirituality|Bibles</td>
</tr><tr><td class=r>15205</td><td>= Textbooks|Religion &amp; Spirituality|Buddhism</td>
</tr><tr><td class=r>15206</td><td>= Textbooks|Religion &amp; Spirituality|Christianity</td>
</tr><tr><td class=r>15207</td><td>= Textbooks|Religion &amp; Spirituality|Comparative Religion</td>
</tr><tr><td class=r>15208</td><td>= Textbooks|Religion &amp; Spirituality|Hinduism</td>
</tr><tr><td class=r>15209</td><td>= Textbooks|Religion &amp; Spirituality|Islam</td>
</tr><tr><td class=r>15210</td><td>= Textbooks|Religion &amp; Spirituality|Judaism</td>
</tr><tr><td class=r>15211</td><td>= Textbooks|Religion &amp; Spirituality|Spirituality</td>
</tr><tr><td class=r>15212</td><td>= Textbooks|Romance</td>
</tr><tr><td class=r>15213</td><td>= Textbooks|Romance|Contemporary</td>
</tr><tr><td class=r>15214</td><td>= Textbooks|Romance|Erotic Romance</td>
</tr><tr><td class=r>15215</td><td>= Textbooks|Romance|Paranormal</td>
</tr><tr><td class=r>15216</td><td>= Textbooks|Romance|Historical</td>
</tr><tr><td class=r>15217</td><td>= Textbooks|Romance|Short Stories</td>
</tr><tr><td class=r>15218</td><td>= Textbooks|Romance|Suspense</td>
</tr><tr><td class=r>15219</td><td>= Textbooks|Romance|Western</td>
</tr><tr><td class=r>15220</td><td>= Textbooks|Sci-Fi &amp; Fantasy</td>
</tr><tr><td class=r>15221</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy</td>
</tr><tr><td class=r>15222</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy|Contemporary</td>
</tr><tr><td class=r>15223</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy|Epic</td>
</tr><tr><td class=r>15224</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy|Historical</td>
</tr><tr><td class=r>15225</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy|Paranormal</td>
</tr><tr><td class=r>15226</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Fantasy|Short Stories</td>
</tr><tr><td class=r>15227</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Science Fiction</td>
</tr><tr><td class=r>15228</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature</td>
</tr><tr><td class=r>15229</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|Adventure</td>
</tr><tr><td class=r>15230</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|High Tech</td>
</tr><tr><td class=r>15231</td><td>= Textbooks|Sci-Fi &amp; Fantasy|Science Fiction &amp; Literature|Short Stories</td>
</tr><tr><td class=r>15232</td><td>= Textbooks|Science &amp; Nature</td>
</tr><tr><td class=r>15233</td><td>= Textbooks|Science &amp; Nature|Agriculture</td>
</tr><tr><td class=r>15234</td><td>= Textbooks|Science &amp; Nature|Astronomy</td>
</tr><tr><td class=r>15235</td><td>= Textbooks|Science &amp; Nature|Atmosphere</td>
</tr><tr><td class=r>15236</td><td>= Textbooks|Science &amp; Nature|Biology</td>
</tr><tr><td class=r>15237</td><td>= Textbooks|Science &amp; Nature|Chemistry</td>
</tr><tr><td class=r>15238</td><td>= Textbooks|Science &amp; Nature|Earth Sciences</td>
</tr><tr><td class=r>15239</td><td>= Textbooks|Science &amp; Nature|Ecology</td>
</tr><tr><td class=r>15240</td><td>= Textbooks|Science &amp; Nature|Environment</td>
</tr><tr><td class=r>15241</td><td>= Textbooks|Science &amp; Nature|Essays</td>
</tr><tr><td class=r>15242</td><td>= Textbooks|Science &amp; Nature|Geography</td>
</tr><tr><td class=r>15243</td><td>= Textbooks|Science &amp; Nature|Geology</td>
</tr><tr><td class=r>15244</td><td>= Textbooks|Science &amp; Nature|History</td>
</tr><tr><td class=r>15245</td><td>= Textbooks|Science &amp; Nature|Life Sciences</td>
</tr><tr><td class=r>15246</td><td>= Textbooks|Science &amp; Nature|Nature</td>
</tr><tr><td class=r>15247</td><td>= Textbooks|Science &amp; Nature|Physics</td>
</tr><tr><td class=r>15248</td><td>= Textbooks|Science &amp; Nature|Reference</td>
</tr><tr><td class=r>15249</td><td>= Textbooks|Social Science</td>
</tr><tr><td class=r>15250</td><td>= Textbooks|Social Science|Anthropology</td>
</tr><tr><td class=r>15251</td><td>= Textbooks|Social Science|Archaeology</td>
</tr><tr><td class=r>15252</td><td>= Textbooks|Social Science|Civics</td>
</tr><tr><td class=r>15253</td><td>= Textbooks|Social Science|Government</td>
</tr><tr><td class=r>15254</td><td>= Textbooks|Social Science|Social Studies</td>
</tr><tr><td class=r>15255</td><td>= Textbooks|Social Science|Social Welfare</td>
</tr><tr><td class=r>15256</td><td>= Textbooks|Social Science|Society</td>
</tr><tr><td class=r>15257</td><td>= Textbooks|Social Science|Society|African Studies</td>
</tr><tr><td class=r>15258</td><td>= Textbooks|Social Science|Society|American Studies</td>
</tr><tr><td class=r>15259</td><td>= Textbooks|Social Science|Society|Asia Pacific Studies</td>
</tr><tr><td class=r>15260</td><td>= Textbooks|Social Science|Society|Cross-Cultural Studies</td>
</tr><tr><td class=r>15261</td><td>= Textbooks|Social Science|Society|European Studies</td>
</tr><tr><td class=r>15262</td><td>= Textbooks|Social Science|Society|Immigration &amp; Emigration</td>
</tr><tr><td class=r>15263</td><td>= Textbooks|Social Science|Society|Indigenous Studies</td>
</tr><tr><td class=r>15264</td><td>= Textbooks|Social Science|Society|Latin &amp; Caribbean Studies</td>
</tr><tr><td class=r>15265</td><td>= Textbooks|Social Science|Society|Middle Eastern Studies</td>
</tr><tr><td class=r>15266</td><td>= Textbooks|Social Science|Society|Race &amp; Ethnicity Studies</td>
</tr><tr><td class=r>15267</td><td>= Textbooks|Social Science|Society|Sexuality Studies</td>
</tr><tr><td class=r>15268</td><td>= Textbooks|Social Science|Society|Women&#39;s Studies</td>
</tr><tr><td class=r>15269</td><td>= Textbooks|Social Science|Sociology</td>
</tr><tr><td class=r>15270</td><td>= Textbooks|Sports &amp; Outdoors</td>
</tr><tr><td class=r>15271</td><td>= Textbooks|Sports &amp; Outdoors|Baseball</td>
</tr><tr><td class=r>15272</td><td>= Textbooks|Sports &amp; Outdoors|Basketball</td>
</tr><tr><td class=r>15273</td><td>= Textbooks|Sports &amp; Outdoors|Coaching</td>
</tr><tr><td class=r>15274</td><td>= Textbooks|Sports &amp; Outdoors|Equestrian</td>
</tr><tr><td class=r>15275</td><td>= Textbooks|Sports &amp; Outdoors|Extreme Sports</td>
</tr><tr><td class=r>15276</td><td>= Textbooks|Sports &amp; Outdoors|Football</td>
</tr><tr><td class=r>15277</td><td>= Textbooks|Sports &amp; Outdoors|Golf</td>
</tr><tr><td class=r>15278</td><td>= Textbooks|Sports &amp; Outdoors|Hockey</td>
</tr><tr><td class=r>15279</td><td>= Textbooks|Sports &amp; Outdoors|Motor Sports</td>
</tr><tr><td class=r>15280</td><td>= Textbooks|Sports &amp; Outdoors|Mountaineering</td>
</tr><tr><td class=r>15281</td><td>= Textbooks|Sports &amp; Outdoors|Outdoors</td>
</tr><tr><td class=r>15282</td><td>= Textbooks|Sports &amp; Outdoors|Racket Sports</td>
</tr><tr><td class=r>15283</td><td>= Textbooks|Sports &amp; Outdoors|Reference</td>
</tr><tr><td class=r>15284</td><td>= Textbooks|Sports &amp; Outdoors|Soccer</td>
</tr><tr><td class=r>15285</td><td>= Textbooks|Sports &amp; Outdoors|Training</td>
</tr><tr><td class=r>15286</td><td>= Textbooks|Sports &amp; Outdoors|Water Sports</td>
</tr><tr><td class=r>15287</td><td>= Textbooks|Sports &amp; Outdoors|Winter Sports</td>
</tr><tr><td class=r>15288</td><td>= Textbooks|Teaching &amp; Learning</td>
</tr><tr><td class=r>15289</td><td>= Textbooks|Teaching &amp; Learning|Adult Education</td>
</tr><tr><td class=r>15290</td><td>= Textbooks|Teaching &amp; Learning|Curriculum &amp; Teaching</td>
</tr><tr><td class=r>15291</td><td>= Textbooks|Teaching &amp; Learning|Educational Leadership</td>
</tr><tr><td class=r>15292</td><td>= Textbooks|Teaching &amp; Learning|Educational Technology</td>
</tr><tr><td class=r>15293</td><td>= Textbooks|Teaching &amp; Learning|Family &amp; Childcare</td>
</tr><tr><td class=r>15294</td><td>= Textbooks|Teaching &amp; Learning|Information &amp; Library Science</td>
</tr><tr><td class=r>15295</td><td>= Textbooks|Teaching &amp; Learning|Learning Resources</td>
</tr><tr><td class=r>15296</td><td>= Textbooks|Teaching &amp; Learning|Psychology &amp; Research</td>
</tr><tr><td class=r>15297</td><td>= Textbooks|Teaching &amp; Learning|Special Education</td>
</tr><tr><td class=r>15298</td><td>= Textbooks|Travel &amp; Adventure</td>
</tr><tr><td class=r>15299</td><td>= Textbooks|Travel &amp; Adventure|Africa</td>
</tr><tr><td class=r>15300</td><td>= Textbooks|Travel &amp; Adventure|Americas</td>
</tr><tr><td class=r>15301</td><td>= Textbooks|Travel &amp; Adventure|Americas|Canada</td>
</tr><tr><td class=r>15302</td><td>= Textbooks|Travel &amp; Adventure|Americas|Latin America</td>
</tr><tr><td class=r>15303</td><td>= Textbooks|Travel &amp; Adventure|Americas|United States</td>
</tr><tr><td class=r>15304</td><td>= Textbooks|Travel &amp; Adventure|Asia</td>
</tr><tr><td class=r>15305</td><td>= Textbooks|Travel &amp; Adventure|Caribbean</td>
</tr><tr><td class=r>15306</td><td>= Textbooks|Travel &amp; Adventure|Essays &amp; Memoirs</td>
</tr><tr><td class=r>15307</td><td>= Textbooks|Travel &amp; Adventure|Europe</td>
</tr><tr><td class=r>15308</td><td>= Textbooks|Travel &amp; Adventure|Middle East</td>
</tr><tr><td class=r>15309</td><td>= Textbooks|Travel &amp; Adventure|Oceania</td>
</tr><tr><td class=r>15310</td><td>= Textbooks|Travel &amp; Adventure|Specialty Travel</td>
</tr><tr><td class=r>15311</td><td>= Textbooks|Comics &amp; Graphic Novels|Comics</td>
</tr><tr><td class=r>15312</td><td>= Textbooks|Reference|Manuals</td>
</tr><tr><td class=r>16001</td><td>= App Store|Stickers|Emoji &amp; Expressions</td>
</tr><tr><td class=r>16003</td><td>= App Store|Stickers|Animals &amp; Nature</td>
</tr><tr><td class=r>16005</td><td>= App Store|Stickers|Art</td>
</tr><tr><td class=r>16006</td><td>= App Store|Stickers|Celebrations</td>
</tr><tr><td class=r>16007</td><td>= App Store|Stickers|Celebrities</td>
</tr><tr><td class=r>16008</td><td>= App Store|Stickers|Comics &amp; Cartoons</td>
</tr><tr><td class=r>16009</td><td>= App Store|Stickers|Eating &amp; Drinking</td>
</tr><tr><td class=r>16010</td><td>= App Store|Stickers|Gaming</td>
</tr><tr><td class=r>16014</td><td>= App Store|Stickers|Movies &amp; TV</td>
</tr><tr><td class=r>16015</td><td>= App Store|Stickers|Music</td>
</tr><tr><td class=r>16017</td><td>= App Store|Stickers|People</td>
</tr><tr><td class=r>16019</td><td>= App Store|Stickers|Places &amp; Objects</td>
</tr><tr><td class=r>16021</td><td>= App Store|Stickers|Sports &amp; Activities</td>
</tr><tr><td class=r>16025</td><td>= App Store|Stickers|Kids &amp; Family</td>
</tr><tr><td class=r>16026</td><td>= App Store|Stickers|Fashion</td>
</tr><tr><td class=r>100000</td><td>= Music|Christian &amp; Gospel</td>
</tr><tr><td class=r>100001</td><td>= Music|Classical|Art Song</td>
</tr><tr><td class=r>100002</td><td>= Music|Classical|Brass &amp; Woodwinds</td>
</tr><tr><td class=r>100003</td><td>= Music|Classical|Solo Instrumental</td>
</tr><tr><td class=r>100004</td><td>= Music|Classical|Contemporary Era</td>
</tr><tr><td class=r>100005</td><td>= Music|Classical|Oratorio</td>
</tr><tr><td class=r>100006</td><td>= Music|Classical|Cantata</td>
</tr><tr><td class=r>100007</td><td>= Music|Classical|Electronic</td>
</tr><tr><td class=r>100008</td><td>= Music|Classical|Sacred</td>
</tr><tr><td class=r>100009</td><td>= Music|Classical|Guitar</td>
</tr><tr><td class=r>100010</td><td>= Music|Classical|Piano</td>
</tr><tr><td class=r>100011</td><td>= Music|Classical|Violin</td>
</tr><tr><td class=r>100012</td><td>= Music|Classical|Cello</td>
</tr><tr><td class=r>100013</td><td>= Music|Classical|Percussion</td>
</tr><tr><td class=r>100014</td><td>= Music|Electronic|Dubstep</td>
</tr><tr><td class=r>100015</td><td>= Music|Electronic|Bass</td>
</tr><tr><td class=r>100016</td><td>= Music|Hip-Hop/Rap|UK Hip-Hop</td>
</tr><tr><td class=r>100017</td><td>= Music|Reggae|Lovers Rock</td>
</tr><tr><td class=r>100018</td><td>= Music|Alternative|EMO</td>
</tr><tr><td class=r>100019</td><td>= Music|Alternative|Pop Punk</td>
</tr><tr><td class=r>100020</td><td>= Music|Alternative|Indie Pop</td>
</tr><tr><td class=r>100021</td><td>= Music|New Age|Yoga</td>
</tr><tr><td class=r>100022</td><td>= Music|Pop|Tribute</td>
</tr><tr><td class=r>100023</td><td>= Music|Pop|Shows</td>
</tr><tr><td class=r>100024</td><td>= Music|Cuban</td>
</tr><tr><td class=r>100025</td><td>= Music|Cuban|Mambo</td>
</tr><tr><td class=r>100026</td><td>= Music|Cuban|Chachacha</td>
</tr><tr><td class=r>100027</td><td>= Music|Cuban|Guajira</td>
</tr><tr><td class=r>100028</td><td>= Music|Cuban|Son</td>
</tr><tr><td class=r>100029</td><td>= Music|Cuban|Bolero</td>
</tr><tr><td class=r>100030</td><td>= Music|Cuban|Guaracha</td>
</tr><tr><td class=r>100031</td><td>= Music|Cuban|Timba</td>
</tr><tr><td class=r>100032</td><td>= Music|Soundtrack|Video Game</td>
</tr><tr><td class=r>100033</td><td>= Music|Indian|Regional Indian|Punjabi|Punjabi Pop</td>
</tr><tr><td class=r>100034</td><td>= Music|Indian|Regional Indian|Bengali|Rabindra Sangeet</td>
</tr><tr><td class=r>100035</td><td>= Music|Indian|Regional Indian|Malayalam</td>
</tr><tr><td class=r>100036</td><td>= Music|Indian|Regional Indian|Kannada</td>
</tr><tr><td class=r>100037</td><td>= Music|Indian|Regional Indian|Marathi</td>
</tr><tr><td class=r>100038</td><td>= Music|Indian|Regional Indian|Gujarati</td>
</tr><tr><td class=r>100039</td><td>= Music|Indian|Regional Indian|Assamese</td>
</tr><tr><td class=r>100040</td><td>= Music|Indian|Regional Indian|Bhojpuri</td>
</tr><tr><td class=r>100041</td><td>= Music|Indian|Regional Indian|Haryanvi</td>
</tr><tr><td class=r>100042</td><td>= Music|Indian|Regional Indian|Odia</td>
</tr><tr><td class=r>100043</td><td>= Music|Indian|Regional Indian|Rajasthani</td>
</tr><tr><td class=r>100044</td><td>= Music|Indian|Regional Indian|Urdu</td>
</tr><tr><td class=r>100045</td><td>= Music|Indian|Regional Indian|Punjabi</td>
</tr><tr><td class=r>100046</td><td>= Music|Indian|Regional Indian|Bengali</td>
</tr><tr><td class=r>100047</td><td>= Music|Indian|Indian Classical|Carnatic Classical</td>
</tr><tr><td class=r>100048</td><td>= Music|Indian|Indian Classical|Hindustani Classical</td>
</tr><tr><td class=r>100049</td><td>= Music|African|Afro House</td>
</tr><tr><td class=r>100050</td><td>= Music|African|Afro Soul</td>
</tr><tr><td class=r>100051</td><td>= Music|African|Afrobeats</td>
</tr><tr><td class=r>100052</td><td>= Music|African|Benga</td>
</tr><tr><td class=r>100053</td><td>= Music|African|Bongo-Flava</td>
</tr><tr><td class=r>100054</td><td>= Music|African|Coupe-Decale</td>
</tr><tr><td class=r>100055</td><td>= Music|African|Gqom</td>
</tr><tr><td class=r>100056</td><td>= Music|African|Highlife</td>
</tr><tr><td class=r>100057</td><td>= Music|African|Kuduro</td>
</tr><tr><td class=r>100058</td><td>= Music|African|Kizomba</td>
</tr><tr><td class=r>100059</td><td>= Music|African|Kwaito</td>
</tr><tr><td class=r>100060</td><td>= Music|African|Mbalax</td>
</tr><tr><td class=r>100061</td><td>= Music|African|Ndombolo</td>
</tr><tr><td class=r>100062</td><td>= Music|African|Shangaan Electro</td>
</tr><tr><td class=r>100063</td><td>= Music|African|Soukous</td>
</tr><tr><td class=r>100064</td><td>= Music|African|Taarab</td>
</tr><tr><td class=r>100065</td><td>= Music|African|Zouglou</td>
</tr><tr><td class=r>100066</td><td>= Music|Turkish|Ozgun</td>
</tr><tr><td class=r>100067</td><td>= Music|Turkish|Fantezi</td>
</tr><tr><td class=r>100068</td><td>= Music|Turkish|Religious</td>
</tr><tr><td class=r>100069</td><td>= Music|Pop|Turkish Pop</td>
</tr><tr><td class=r>100070</td><td>= Music|Rock|Turkish Rock</td>
</tr><tr><td class=r>100071</td><td>= Music|Alternative|Turkish Alternative</td>
</tr><tr><td class=r>100072</td><td>= Music|Hip-Hop/Rap|Turkish Hip-Hop/Rap</td>
</tr><tr><td class=r>100073</td><td>= Music|African|Maskandi</td>
</tr><tr><td class=r>100074</td><td>= Music|Russian|Russian Romance</td>
</tr><tr><td class=r>100075</td><td>= Music|Russian|Russian Bard</td>
</tr><tr><td class=r>100076</td><td>= Music|Russian|Russian Pop</td>
</tr><tr><td class=r>100077</td><td>= Music|Russian|Russian Rock</td>
</tr><tr><td class=r>100078</td><td>= Music|Russian|Russian Hip-Hop</td>
</tr><tr><td class=r>100079</td><td>= Music|Arabic|Levant</td>
</tr><tr><td class=r>100080</td><td>= Music|Arabic|Levant|Dabke</td>
</tr><tr><td class=r>100081</td><td>= Music|Arabic|Maghreb Rai</td>
</tr><tr><td class=r>100082</td><td>= Music|Arabic|Khaleeji|Khaleeji Jalsat</td>
</tr><tr><td class=r>100083</td><td>= Music|Arabic|Khaleeji|Khaleeji Shailat</td>
</tr><tr><td class=r>100084</td><td>= Music|Tarab</td>
</tr><tr><td class=r>100085</td><td>= Music|Tarab|Iraqi Tarab</td>
</tr><tr><td class=r>100086</td><td>= Music|Tarab|Egyptian Tarab</td>
</tr><tr><td class=r>100087</td><td>= Music|Tarab|Khaleeji Tarab</td>
</tr><tr><td class=r>100088</td><td>= Music|Pop|Levant Pop</td>
</tr><tr><td class=r>100089</td><td>= Music|Pop|Iraqi Pop</td>
</tr><tr><td class=r>100090</td><td>= Music|Pop|Egyptian Pop</td>
</tr><tr><td class=r>100091</td><td>= Music|Pop|Maghreb Pop</td>
</tr><tr><td class=r>100092</td><td>= Music|Pop|Khaleeji Pop</td>
</tr><tr><td class=r>100093</td><td>= Music|Hip-Hop/Rap|Levant Hip-Hop</td>
</tr><tr><td class=r>100094</td><td>= Music|Hip-Hop/Rap|Egyptian Hip-Hop</td>
</tr><tr><td class=r>100095</td><td>= Music|Hip-Hop/Rap|Maghreb Hip-Hop</td>
</tr><tr><td class=r>100096</td><td>= Music|Hip-Hop/Rap|Khaleeji Hip-Hop</td>
</tr><tr><td class=r>100097</td><td>= Music|Alternative|Indie Levant</td>
</tr><tr><td class=r>100098</td><td>= Music|Alternative|Indie Egyptian</td>
</tr><tr><td class=r>100099</td><td>= Music|Alternative|Indie Maghreb</td>
</tr><tr><td class=r>100100</td><td>= Music|Electronic|Levant Electronic</td>
</tr><tr><td class=r>100101</td><td>= Music|Electronic|Electro-Cha&#39;abi</td>
</tr><tr><td class=r>100102</td><td>= Music|Electronic|Maghreb Electronic</td>
</tr><tr><td class=r>100103</td><td>= Music|Folk|Iraqi Folk</td>
</tr><tr><td class=r>100104</td><td>= Music|Folk|Khaleeji Folk</td>
</tr><tr><td class=r>100105</td><td>= Music|Dance|Maghreb Dance</td>
</tr><tr><td class=r>40000000</td><td>= iTunes U</td>
</tr><tr><td class=r>40000001</td><td>= iTunes U|Business &amp; Economics</td>
</tr><tr><td class=r>40000002</td><td>= iTunes U|Business &amp; Economics|Economics</td>
</tr><tr><td class=r>40000003</td><td>= iTunes U|Business &amp; Economics|Finance</td>
</tr><tr><td class=r>40000004</td><td>= iTunes U|Business &amp; Economics|Hospitality</td>
</tr><tr><td class=r>40000005</td><td>= iTunes U|Business &amp; Economics|Management</td>
</tr><tr><td class=r>40000006</td><td>= iTunes U|Business &amp; Economics|Marketing</td>
</tr><tr><td class=r>40000007</td><td>= iTunes U|Business &amp; Economics|Personal Finance</td>
</tr><tr><td class=r>40000008</td><td>= iTunes U|Business &amp; Economics|Real Estate</td>
</tr><tr><td class=r>40000009</td><td>= iTunes U|Engineering</td>
</tr><tr><td class=r>40000010</td><td>= iTunes U|Engineering|Chemical &amp; Petroleum Engineering</td>
</tr><tr><td class=r>40000011</td><td>= iTunes U|Engineering|Civil Engineering</td>
</tr><tr><td class=r>40000012</td><td>= iTunes U|Engineering|Computer Science</td>
</tr><tr><td class=r>40000013</td><td>= iTunes U|Engineering|Electrical Engineering</td>
</tr><tr><td class=r>40000014</td><td>= iTunes U|Engineering|Environmental Engineering</td>
</tr><tr><td class=r>40000015</td><td>= iTunes U|Engineering|Mechanical Engineering</td>
</tr><tr><td class=r>40000016</td><td>= iTunes U|Music, Art, &amp; Design</td>
</tr><tr><td class=r>40000017</td><td>= iTunes U|Music, Art, &amp; Design|Architecture</td>
</tr><tr><td class=r>40000019</td><td>= iTunes U|Music, Art, &amp; Design|Art History</td>
</tr><tr><td class=r>40000020</td><td>= iTunes U|Music, Art, &amp; Design|Dance</td>
</tr><tr><td class=r>40000021</td><td>= iTunes U|Music, Art, &amp; Design|Film</td>
</tr><tr><td class=r>40000022</td><td>= iTunes U|Music, Art, &amp; Design|Design</td>
</tr><tr><td class=r>40000023</td><td>= iTunes U|Music, Art, &amp; Design|Interior Design</td>
</tr><tr><td class=r>40000024</td><td>= iTunes U|Music, Art, &amp; Design|Music</td>
</tr><tr><td class=r>40000025</td><td>= iTunes U|Music, Art, &amp; Design|Theater</td>
</tr><tr><td class=r>40000026</td><td>= iTunes U|Health &amp; Medicine</td>
</tr><tr><td class=r>40000027</td><td>= iTunes U|Health &amp; Medicine|Anatomy &amp; Physiology</td>
</tr><tr><td class=r>40000028</td><td>= iTunes U|Health &amp; Medicine|Behavioral Science</td>
</tr><tr><td class=r>40000029</td><td>= iTunes U|Health &amp; Medicine|Dentistry</td>
</tr><tr><td class=r>40000030</td><td>= iTunes U|Health &amp; Medicine|Diet &amp; Nutrition</td>
</tr><tr><td class=r>40000031</td><td>= iTunes U|Health &amp; Medicine|Emergency Medicine</td>
</tr><tr><td class=r>40000032</td><td>= iTunes U|Health &amp; Medicine|Genetics</td>
</tr><tr><td class=r>40000033</td><td>= iTunes U|Health &amp; Medicine|Gerontology</td>
</tr><tr><td class=r>40000034</td><td>= iTunes U|Health &amp; Medicine|Health &amp; Exercise Science</td>
</tr><tr><td class=r>40000035</td><td>= iTunes U|Health &amp; Medicine|Immunology</td>
</tr><tr><td class=r>40000036</td><td>= iTunes U|Health &amp; Medicine|Neuroscience</td>
</tr><tr><td class=r>40000037</td><td>= iTunes U|Health &amp; Medicine|Pharmacology &amp; Toxicology</td>
</tr><tr><td class=r>40000038</td><td>= iTunes U|Health &amp; Medicine|Psychiatry</td>
</tr><tr><td class=r>40000039</td><td>= iTunes U|Health &amp; Medicine|Global Health</td>
</tr><tr><td class=r>40000040</td><td>= iTunes U|Health &amp; Medicine|Radiology</td>
</tr><tr><td class=r>40000041</td><td>= iTunes U|History</td>
</tr><tr><td class=r>40000042</td><td>= iTunes U|History|Ancient History</td>
</tr><tr><td class=r>40000043</td><td>= iTunes U|History|Medieval History</td>
</tr><tr><td class=r>40000044</td><td>= iTunes U|History|Military History</td>
</tr><tr><td class=r>40000045</td><td>= iTunes U|History|Modern History</td>
</tr><tr><td class=r>40000046</td><td>= iTunes U|History|African History</td>
</tr><tr><td class=r>40000047</td><td>= iTunes U|History|Asia-Pacific History</td>
</tr><tr><td class=r>40000048</td><td>= iTunes U|History|European History</td>
</tr><tr><td class=r>40000049</td><td>= iTunes U|History|Middle Eastern History</td>
</tr><tr><td class=r>40000050</td><td>= iTunes U|History|North American History</td>
</tr><tr><td class=r>40000051</td><td>= iTunes U|History|South American History</td>
</tr><tr><td class=r>40000053</td><td>= iTunes U|Communications &amp; Journalism</td>
</tr><tr><td class=r>40000054</td><td>= iTunes U|Philosophy</td>
</tr><tr><td class=r>40000055</td><td>= iTunes U|Religion &amp; Spirituality</td>
</tr><tr><td class=r>40000056</td><td>= iTunes U|Languages</td>
</tr><tr><td class=r>40000057</td><td>= iTunes U|Languages|African Languages</td>
</tr><tr><td class=r>40000058</td><td>= iTunes U|Languages|Ancient Languages</td>
</tr><tr><td class=r>40000061</td><td>= iTunes U|Languages|English</td>
</tr><tr><td class=r>40000063</td><td>= iTunes U|Languages|French</td>
</tr><tr><td class=r>40000064</td><td>= iTunes U|Languages|German</td>
</tr><tr><td class=r>40000065</td><td>= iTunes U|Languages|Italian</td>
</tr><tr><td class=r>40000066</td><td>= iTunes U|Languages|Linguistics</td>
</tr><tr><td class=r>40000068</td><td>= iTunes U|Languages|Spanish</td>
</tr><tr><td class=r>40000069</td><td>= iTunes U|Languages|Speech Pathology</td>
</tr><tr><td class=r>40000070</td><td>= iTunes U|Writing &amp; Literature</td>
</tr><tr><td class=r>40000071</td><td>= iTunes U|Writing &amp; Literature|Anthologies</td>
</tr><tr><td class=r>40000072</td><td>= iTunes U|Writing &amp; Literature|Biography</td>
</tr><tr><td class=r>40000073</td><td>= iTunes U|Writing &amp; Literature|Classics</td>
</tr><tr><td class=r>40000074</td><td>= iTunes U|Writing &amp; Literature|Literary Criticism</td>
</tr><tr><td class=r>40000075</td><td>= iTunes U|Writing &amp; Literature|Fiction</td>
</tr><tr><td class=r>40000076</td><td>= iTunes U|Writing &amp; Literature|Poetry</td>
</tr><tr><td class=r>40000077</td><td>= iTunes U|Mathematics</td>
</tr><tr><td class=r>40000078</td><td>= iTunes U|Mathematics|Advanced Mathematics</td>
</tr><tr><td class=r>40000079</td><td>= iTunes U|Mathematics|Algebra</td>
</tr><tr><td class=r>40000080</td><td>= iTunes U|Mathematics|Arithmetic</td>
</tr><tr><td class=r>40000081</td><td>= iTunes U|Mathematics|Calculus</td>
</tr><tr><td class=r>40000082</td><td>= iTunes U|Mathematics|Geometry</td>
</tr><tr><td class=r>40000083</td><td>= iTunes U|Mathematics|Statistics</td>
</tr><tr><td class=r>40000084</td><td>= iTunes U|Science</td>
</tr><tr><td class=r>40000085</td><td>= iTunes U|Science|Agricultural</td>
</tr><tr><td class=r>40000086</td><td>= iTunes U|Science|Astronomy</td>
</tr><tr><td class=r>40000087</td><td>= iTunes U|Science|Atmosphere</td>
</tr><tr><td class=r>40000088</td><td>= iTunes U|Science|Biology</td>
</tr><tr><td class=r>40000089</td><td>= iTunes U|Science|Chemistry</td>
</tr><tr><td class=r>40000090</td><td>= iTunes U|Science|Ecology</td>
</tr><tr><td class=r>40000091</td><td>= iTunes U|Science|Geography</td>
</tr><tr><td class=r>40000092</td><td>= iTunes U|Science|Geology</td>
</tr><tr><td class=r>40000093</td><td>= iTunes U|Science|Physics</td>
</tr><tr><td class=r>40000094</td><td>= iTunes U|Social Science</td>
</tr><tr><td class=r>40000095</td><td>= iTunes U|Law &amp; Politics|Law</td>
</tr><tr><td class=r>40000096</td><td>= iTunes U|Law &amp; Politics|Political Science</td>
</tr><tr><td class=r>40000097</td><td>= iTunes U|Law &amp; Politics|Public Administration</td>
</tr><tr><td class=r>40000098</td><td>= iTunes U|Social Science|Psychology</td>
</tr><tr><td class=r>40000099</td><td>= iTunes U|Social Science|Social Welfare</td>
</tr><tr><td class=r>40000100</td><td>= iTunes U|Social Science|Sociology</td>
</tr><tr><td class=r>40000101</td><td>= iTunes U|Society</td>
</tr><tr><td class=r>40000103</td><td>= iTunes U|Society|Asia Pacific Studies</td>
</tr><tr><td class=r>40000104</td><td>= iTunes U|Society|European Studies</td>
</tr><tr><td class=r>40000105</td><td>= iTunes U|Society|Indigenous Studies</td>
</tr><tr><td class=r>40000106</td><td>= iTunes U|Society|Latin &amp; Caribbean Studies</td>
</tr><tr><td class=r>40000107</td><td>= iTunes U|Society|Middle Eastern Studies</td>
</tr><tr><td class=r>40000108</td><td>= iTunes U|Society|Women&#39;s Studies</td>
</tr><tr><td class=r>40000109</td><td>= iTunes U|Teaching &amp; Learning</td>
</tr><tr><td class=r>40000110</td><td>= iTunes U|Teaching &amp; Learning|Curriculum &amp; Teaching</td>
</tr><tr><td class=r>40000111</td><td>= iTunes U|Teaching &amp; Learning|Educational Leadership</td>
</tr><tr><td class=r>40000112</td><td>= iTunes U|Teaching &amp; Learning|Family &amp; Childcare</td>
</tr><tr><td class=r>40000113</td><td>= iTunes U|Teaching &amp; Learning|Learning Resources</td>
</tr><tr><td class=r>40000114</td><td>= iTunes U|Teaching &amp; Learning|Psychology &amp; Research</td>
</tr><tr><td class=r>40000115</td><td>= iTunes U|Teaching &amp; Learning|Special Education</td>
</tr><tr><td class=r>40000116</td><td>= iTunes U|Music, Art, &amp; Design|Culinary Arts</td>
</tr><tr><td class=r>40000117</td><td>= iTunes U|Music, Art, &amp; Design|Fashion</td>
</tr><tr><td class=r>40000118</td><td>= iTunes U|Music, Art, &amp; Design|Media Arts</td>
</tr><tr><td class=r>40000119</td><td>= iTunes U|Music, Art, &amp; Design|Photography</td>
</tr><tr><td class=r>40000120</td><td>= iTunes U|Music, Art, &amp; Design|Visual Art</td>
</tr><tr><td class=r>40000121</td><td>= iTunes U|Business &amp; Economics|Entrepreneurship</td>
</tr><tr><td class=r>40000122</td><td>= iTunes U|Communications &amp; Journalism|Broadcasting</td>
</tr><tr><td class=r>40000123</td><td>= iTunes U|Communications &amp; Journalism|Digital Media</td>
</tr><tr><td class=r>40000124</td><td>= iTunes U|Communications &amp; Journalism|Journalism</td>
</tr><tr><td class=r>40000125</td><td>= iTunes U|Communications &amp; Journalism|Photojournalism</td>
</tr><tr><td class=r>40000126</td><td>= iTunes U|Communications &amp; Journalism|Print</td>
</tr><tr><td class=r>40000127</td><td>= iTunes U|Communications &amp; Journalism|Speech</td>
</tr><tr><td class=r>40000128</td><td>= iTunes U|Communications &amp; Journalism|Writing</td>
</tr><tr><td class=r>40000129</td><td>= iTunes U|Health &amp; Medicine|Nursing</td>
</tr><tr><td class=r>40000130</td><td>= iTunes U|Languages|Arabic</td>
</tr><tr><td class=r>40000131</td><td>= iTunes U|Languages|Chinese</td>
</tr><tr><td class=r>40000132</td><td>= iTunes U|Languages|Hebrew</td>
</tr><tr><td class=r>40000133</td><td>= iTunes U|Languages|Hindi</td>
</tr><tr><td class=r>40000134</td><td>= iTunes U|Languages|Indigenous Languages</td>
</tr><tr><td class=r>40000135</td><td>= iTunes U|Languages|Japanese</td>
</tr><tr><td class=r>40000136</td><td>= iTunes U|Languages|Korean</td>
</tr><tr><td class=r>40000137</td><td>= iTunes U|Languages|Other Languages</td>
</tr><tr><td class=r>40000138</td><td>= iTunes U|Languages|Portuguese</td>
</tr><tr><td class=r>40000139</td><td>= iTunes U|Languages|Russian</td>
</tr><tr><td class=r>40000140</td><td>= iTunes U|Law &amp; Politics</td>
</tr><tr><td class=r>40000141</td><td>= iTunes U|Law &amp; Politics|Foreign Policy &amp; International Relations</td>
</tr><tr><td class=r>40000142</td><td>= iTunes U|Law &amp; Politics|Local Governments</td>
</tr><tr><td class=r>40000143</td><td>= iTunes U|Law &amp; Politics|National Governments</td>
</tr><tr><td class=r>40000144</td><td>= iTunes U|Law &amp; Politics|World Affairs</td>
</tr><tr><td class=r>40000145</td><td>= iTunes U|Writing &amp; Literature|Comparative Literature</td>
</tr><tr><td class=r>40000146</td><td>= iTunes U|Philosophy|Aesthetics</td>
</tr><tr><td class=r>40000147</td><td>= iTunes U|Philosophy|Epistemology</td>
</tr><tr><td class=r>40000148</td><td>= iTunes U|Philosophy|Ethics</td>
</tr><tr><td class=r>40000149</td><td>= iTunes U|Philosophy|Metaphysics</td>
</tr><tr><td class=r>40000150</td><td>= iTunes U|Philosophy|Political Philosophy</td>
</tr><tr><td class=r>40000151</td><td>= iTunes U|Philosophy|Logic</td>
</tr><tr><td class=r>40000152</td><td>= iTunes U|Philosophy|Philosophy of Language</td>
</tr><tr><td class=r>40000153</td><td>= iTunes U|Philosophy|Philosophy of Religion</td>
</tr><tr><td class=r>40000154</td><td>= iTunes U|Social Science|Archaeology</td>
</tr><tr><td class=r>40000155</td><td>= iTunes U|Social Science|Anthropology</td>
</tr><tr><td class=r>40000156</td><td>= iTunes U|Religion &amp; Spirituality|Buddhism</td>
</tr><tr><td class=r>40000157</td><td>= iTunes U|Religion &amp; Spirituality|Christianity</td>
</tr><tr><td class=r>40000158</td><td>= iTunes U|Religion &amp; Spirituality|Comparative Religion</td>
</tr><tr><td class=r>40000159</td><td>= iTunes U|Religion &amp; Spirituality|Hinduism</td>
</tr><tr><td class=r>40000160</td><td>= iTunes U|Religion &amp; Spirituality|Islam</td>
</tr><tr><td class=r>40000161</td><td>= iTunes U|Religion &amp; Spirituality|Judaism</td>
</tr><tr><td class=r>40000162</td><td>= iTunes U|Religion &amp; Spirituality|Other Religions</td>
</tr><tr><td class=r>40000163</td><td>= iTunes U|Religion &amp; Spirituality|Spirituality</td>
</tr><tr><td class=r>40000164</td><td>= iTunes U|Science|Environment</td>
</tr><tr><td class=r>40000165</td><td>= iTunes U|Society|African Studies</td>
</tr><tr><td class=r>40000166</td><td>= iTunes U|Society|American Studies</td>
</tr><tr><td class=r>40000167</td><td>= iTunes U|Society|Cross-cultural Studies</td>
</tr><tr><td class=r>40000168</td><td>= iTunes U|Society|Immigration &amp; Emigration</td>
</tr><tr><td class=r>40000169</td><td>= iTunes U|Society|Race &amp; Ethnicity Studies</td>
</tr><tr><td class=r>40000170</td><td>= iTunes U|Society|Sexuality Studies</td>
</tr><tr><td class=r>40000171</td><td>= iTunes U|Teaching &amp; Learning|Educational Technology</td>
</tr><tr><td class=r>40000172</td><td>= iTunes U|Teaching &amp; Learning|Information/Library Science</td>
</tr><tr><td class=r>40000173</td><td>= iTunes U|Languages|Dutch</td>
</tr><tr><td class=r>40000174</td><td>= iTunes U|Languages|Luxembourgish</td>
</tr><tr><td class=r>40000175</td><td>= iTunes U|Languages|Swedish</td>
</tr><tr><td class=r>40000176</td><td>= iTunes U|Languages|Norwegian</td>
</tr><tr><td class=r>40000177</td><td>= iTunes U|Languages|Finnish</td>
</tr><tr><td class=r>40000178</td><td>= iTunes U|Languages|Danish</td>
</tr><tr><td class=r>40000179</td><td>= iTunes U|Languages|Polish</td>
</tr><tr><td class=r>40000180</td><td>= iTunes U|Languages|Turkish</td>
</tr><tr><td class=r>40000181</td><td>= iTunes U|Languages|Flemish</td>
</tr><tr><td class=r>50000024</td><td>= Audiobooks</td>
</tr><tr><td class=r>50000040</td><td>= Audiobooks|Fiction</td>
</tr><tr><td class=r>50000041</td><td>= Audiobooks|Arts &amp; Entertainment</td>
</tr><tr><td class=r>50000042</td><td>= Audiobooks|Biographies &amp; Memoirs</td>
</tr><tr><td class=r>50000043</td><td>= Audiobooks|Business &amp; Personal Finance</td>
</tr><tr><td class=r>50000044</td><td>= Audiobooks|Kids &amp; Young Adults</td>
</tr><tr><td class=r>50000045</td><td>= Audiobooks|Classics</td>
</tr><tr><td class=r>50000046</td><td>= Audiobooks|Comedy</td>
</tr><tr><td class=r>50000047</td><td>= Audiobooks|Drama &amp; Poetry</td>
</tr><tr><td class=r>50000048</td><td>= Audiobooks|Speakers &amp; Storytellers</td>
</tr><tr><td class=r>50000049</td><td>= Audiobooks|History</td>
</tr><tr><td class=r>50000050</td><td>= Audiobooks|Languages</td>
</tr><tr><td class=r>50000051</td><td>= Audiobooks|Mysteries &amp; Thrillers</td>
</tr><tr><td class=r>50000052</td><td>= Audiobooks|Nonfiction</td>
</tr><tr><td class=r>50000053</td><td>= Audiobooks|Religion &amp; Spirituality</td>
</tr><tr><td class=r>50000054</td><td>= Audiobooks|Science &amp; Nature</td>
</tr><tr><td class=r>50000055</td><td>= Audiobooks|Sci Fi &amp; Fantasy</td>
</tr><tr><td class=r>50000056</td><td>= Audiobooks|Self-Development</td>
</tr><tr><td class=r>50000057</td><td>= Audiobooks|Sports &amp; Outdoors</td>
</tr><tr><td class=r>50000058</td><td>= Audiobooks|Technology</td>
</tr><tr><td class=r>50000059</td><td>= Audiobooks|Travel &amp; Adventure</td>
</tr><tr><td class=r>50000061</td><td>= Music|Spoken Word</td>
</tr><tr><td class=r>50000063</td><td>= Music|Disney</td>
</tr><tr><td class=r>50000064</td><td>= Music|French Pop</td>
</tr><tr><td class=r>50000066</td><td>= Music|German Pop</td>
</tr><tr><td class=r>50000068</td><td>= Music|German Folk</td>
</tr><tr><td class=r>50000069</td><td>= Audiobooks|Romance</td>
</tr><tr><td class=r>50000070</td><td>= Audiobooks|Audiobooks Latino</td>
</tr><tr><td class=r>50000071</td><td>= Books|Comics &amp; Graphic Novels|Manga|Action</td>
</tr><tr><td class=r>50000072</td><td>= Books|Comics &amp; Graphic Novels|Manga|Comedy</td>
</tr><tr><td class=r>50000073</td><td>= Books|Comics &amp; Graphic Novels|Manga|Erotica</td>
</tr><tr><td class=r>50000074</td><td>= Books|Comics &amp; Graphic Novels|Manga|Fantasy</td>
</tr><tr><td class=r>50000075</td><td>= Books|Comics &amp; Graphic Novels|Manga|Four Cell Manga</td>
</tr><tr><td class=r>50000076</td><td>= Books|Comics &amp; Graphic Novels|Manga|Gay &amp; Lesbian</td>
</tr><tr><td class=r>50000077</td><td>= Books|Comics &amp; Graphic Novels|Manga|Hard-Boiled</td>
</tr><tr><td class=r>50000078</td><td>= Books|Comics &amp; Graphic Novels|Manga|Heroes</td>
</tr><tr><td class=r>50000079</td><td>= Books|Comics &amp; Graphic Novels|Manga|Historical Fiction</td>
</tr><tr><td class=r>50000080</td><td>= Books|Comics &amp; Graphic Novels|Manga|Mecha</td>
</tr><tr><td class=r>50000081</td><td>= Books|Comics &amp; Graphic Novels|Manga|Mystery</td>
</tr><tr><td class=r>50000082</td><td>= Books|Comics &amp; Graphic Novels|Manga|Nonfiction</td>
</tr><tr><td class=r>50000083</td><td>= Books|Comics &amp; Graphic Novels|Manga|Religious</td>
</tr><tr><td class=r>50000084</td><td>= Books|Comics &amp; Graphic Novels|Manga|Romance</td>
</tr><tr><td class=r>50000085</td><td>= Books|Comics &amp; Graphic Novels|Manga|Romantic Comedy</td>
</tr><tr><td class=r>50000086</td><td>= Books|Comics &amp; Graphic Novels|Manga|Science Fiction</td>
</tr><tr><td class=r>50000087</td><td>= Books|Comics &amp; Graphic Novels|Manga|Sports</td>
</tr><tr><td class=r>50000088</td><td>= Books|Fiction &amp; Literature|Light Novels</td>
</tr><tr><td class=r>50000089</td><td>= Books|Comics &amp; Graphic Novels|Manga|Horror</td>
</tr><tr><td class=r>50000090</td><td>= Books|Comics &amp; Graphic Novels|Comics</td>
</tr><tr><td class=r>50000091</td><td>= Books|Romance|Multicultural</td>
</tr><tr><td class=r>50000092</td><td>= Audiobooks|Erotica</td>
</tr><tr><td class=r>50000093</td><td>= Audiobooks|Light Novels</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='AppleStoreCountry'>QuickTime AppleStoreCountry Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>AppleStoreCountry</th><th>Value</th><th>AppleStoreCountry</th></tr>
<tr><td class=r>143441</td><td>= United States</td>
<td class='r b'>143523</td><td class=b>= Moldova</td>
</tr><tr><td class=r>143442</td><td>= France</td>
<td class='r b'>143524</td><td class=b>= Armenia</td>
</tr><tr><td class=r>143443</td><td>= Germany</td>
<td class='r b'>143525</td><td class=b>= Botswana</td>
</tr><tr><td class=r>143444</td><td>= United Kingdom</td>
<td class='r b'>143526</td><td class=b>= Bulgaria</td>
</tr><tr><td class=r>143445</td><td>= Austria</td>
<td class='r b'>143528</td><td class=b>= Jordan</td>
</tr><tr><td class=r>143446</td><td>= Belgium</td>
<td class='r b'>143529</td><td class=b>= Kenya</td>
</tr><tr><td class=r>143447</td><td>= Finland</td>
<td class='r b'>143530</td><td class=b>= Macedonia</td>
</tr><tr><td class=r>143448</td><td>= Greece</td>
<td class='r b'>143531</td><td class=b>= Madagascar</td>
</tr><tr><td class=r>143449</td><td>= Ireland</td>
<td class='r b'>143532</td><td class=b>= Mali</td>
</tr><tr><td class=r>143450</td><td>= Italy</td>
<td class='r b'>143533</td><td class=b>= Mauritius</td>
</tr><tr><td class=r>143451</td><td>= Luxembourg</td>
<td class='r b'>143534</td><td class=b>= Niger</td>
</tr><tr><td class=r>143452</td><td>= Netherlands</td>
<td class='r b'>143535</td><td class=b>= Senegal</td>
</tr><tr><td class=r>143453</td><td>= Portugal</td>
<td class='r b'>143536</td><td class=b>= Tunisia</td>
</tr><tr><td class=r>143454</td><td>= Spain</td>
<td class='r b'>143537</td><td class=b>= Uganda</td>
</tr><tr><td class=r>143455</td><td>= Canada</td>
<td class='r b'>143538</td><td class=b>= Anguilla</td>
</tr><tr><td class=r>143456</td><td>= Sweden</td>
<td class='r b'>143539</td><td class=b>= Bahamas</td>
</tr><tr><td class=r>143457</td><td>= Norway</td>
<td class='r b'>143540</td><td class=b>= Antigua and Barbuda</td>
</tr><tr><td class=r>143458</td><td>= Denmark</td>
<td class='r b'>143541</td><td class=b>= Barbados</td>
</tr><tr><td class=r>143459</td><td>= Switzerland</td>
<td class='r b'>143542</td><td class=b>= Bermuda</td>
</tr><tr><td class=r>143460</td><td>= Australia</td>
<td class='r b'>143543</td><td class=b>= British Virgin Islands</td>
</tr><tr><td class=r>143461</td><td>= New Zealand</td>
<td class='r b'>143544</td><td class=b>= Cayman Islands</td>
</tr><tr><td class=r>143462</td><td>= Japan</td>
<td class='r b'>143545</td><td class=b>= Dominica</td>
</tr><tr><td class=r>143463</td><td>= Hong Kong</td>
<td class='r b'>143546</td><td class=b>= Grenada</td>
</tr><tr><td class=r>143464</td><td>= Singapore</td>
<td class='r b'>143547</td><td class=b>= Montserrat</td>
</tr><tr><td class=r>143465</td><td>= China</td>
<td class='r b'>143548</td><td class=b>= St. Kitts and Nevis</td>
</tr><tr><td class=r>143466</td><td>= Republic of Korea</td>
<td class='r b'>143549</td><td class=b>= St. Lucia</td>
</tr><tr><td class=r>143467</td><td>= India</td>
<td class='r b'>143550</td><td class=b>= St. Vincent and The Grenadines</td>
</tr><tr><td class=r>143468</td><td>= Mexico</td>
<td class='r b'>143551</td><td class=b>= Trinidad and Tobago</td>
</tr><tr><td class=r>143469</td><td>= Russia</td>
<td class='r b'>143552</td><td class=b>= Turks and Caicos</td>
</tr><tr><td class=r>143470</td><td>= Taiwan</td>
<td class='r b'>143553</td><td class=b>= Guyana</td>
</tr><tr><td class=r>143471</td><td>= Vietnam</td>
<td class='r b'>143554</td><td class=b>= Suriname</td>
</tr><tr><td class=r>143472</td><td>= South Africa</td>
<td class='r b'>143555</td><td class=b>= Belize</td>
</tr><tr><td class=r>143473</td><td>= Malaysia</td>
<td class='r b'>143556</td><td class=b>= Bolivia</td>
</tr><tr><td class=r>143474</td><td>= Philippines</td>
<td class='r b'>143557</td><td class=b>= Cyprus</td>
</tr><tr><td class=r>143475</td><td>= Thailand</td>
<td class='r b'>143558</td><td class=b>= Iceland</td>
</tr><tr><td class=r>143476</td><td>= Indonesia</td>
<td class='r b'>143559</td><td class=b>= Bahrain</td>
</tr><tr><td class=r>143477</td><td>= Pakistan</td>
<td class='r b'>143560</td><td class=b>= Brunei Darussalam</td>
</tr><tr><td class=r>143478</td><td>= Poland</td>
<td class='r b'>143561</td><td class=b>= Nigeria</td>
</tr><tr><td class=r>143479</td><td>= Saudi Arabia</td>
<td class='r b'>143562</td><td class=b>= Oman</td>
</tr><tr><td class=r>143480</td><td>= Turkey</td>
<td class='r b'>143563</td><td class=b>= Algeria</td>
</tr><tr><td class=r>143481</td><td>= United Arab Emirates</td>
<td class='r b'>143564</td><td class=b>= Angola</td>
</tr><tr><td class=r>143482</td><td>= Hungary</td>
<td class='r b'>143565</td><td class=b>= Belarus</td>
</tr><tr><td class=r>143483</td><td>= Chile</td>
<td class='r b'>143566</td><td class=b>= Uzbekistan</td>
</tr><tr><td class=r>143484</td><td>= Nepal</td>
<td class='r b'>143568</td><td class=b>= Azerbaijan</td>
</tr><tr><td class=r>143485</td><td>= Panama</td>
<td class='r b'>143571</td><td class=b>= Yemen</td>
</tr><tr><td class=r>143486</td><td>= Sri Lanka</td>
<td class='r b'>143572</td><td class=b>= Tanzania</td>
</tr><tr><td class=r>143487</td><td>= Romania</td>
<td class='r b'>143573</td><td class=b>= Ghana</td>
</tr><tr><td class=r>143489</td><td>= Czech Republic</td>
<td class='r b'>143575</td><td class=b>= Albania</td>
</tr><tr><td class=r>143491</td><td>= Israel</td>
<td class='r b'>143576</td><td class=b>= Benin</td>
</tr><tr><td class=r>143492</td><td>= Ukraine</td>
<td class='r b'>143577</td><td class=b>= Bhutan</td>
</tr><tr><td class=r>143493</td><td>= Kuwait</td>
<td class='r b'>143578</td><td class=b>= Burkina Faso</td>
</tr><tr><td class=r>143494</td><td>= Croatia</td>
<td class='r b'>143579</td><td class=b>= Cambodia</td>
</tr><tr><td class=r>143495</td><td>= Costa Rica</td>
<td class='r b'>143580</td><td class=b>= Cape Verde</td>
</tr><tr><td class=r>143496</td><td>= Slovakia</td>
<td class='r b'>143581</td><td class=b>= Chad</td>
</tr><tr><td class=r>143497</td><td>= Lebanon</td>
<td class='r b'>143582</td><td class=b>= Republic of the Congo</td>
</tr><tr><td class=r>143498</td><td>= Qatar</td>
<td class='r b'>143583</td><td class=b>= Fiji</td>
</tr><tr><td class=r>143499</td><td>= Slovenia</td>
<td class='r b'>143584</td><td class=b>= Gambia</td>
</tr><tr><td class=r>143501</td><td>= Colombia</td>
<td class='r b'>143585</td><td class=b>= Guinea-Bissau</td>
</tr><tr><td class=r>143502</td><td>= Venezuela</td>
<td class='r b'>143586</td><td class=b>= Kyrgyzstan</td>
</tr><tr><td class=r>143503</td><td>= Brazil</td>
<td class='r b'>143587</td><td class=b>= Lao People&#39;s Democratic Republic</td>
</tr><tr><td class=r>143504</td><td>= Guatemala</td>
<td class='r b'>143588</td><td class=b>= Liberia</td>
</tr><tr><td class=r>143505</td><td>= Argentina</td>
<td class='r b'>143589</td><td class=b>= Malawi</td>
</tr><tr><td class=r>143506</td><td>= El Salvador</td>
<td class='r b'>143590</td><td class=b>= Mauritania</td>
</tr><tr><td class=r>143507</td><td>= Peru</td>
<td class='r b'>143591</td><td class=b>= Federated States of Micronesia</td>
</tr><tr><td class=r>143508</td><td>= Dominican Republic</td>
<td class='r b'>143592</td><td class=b>= Mongolia</td>
</tr><tr><td class=r>143509</td><td>= Ecuador</td>
<td class='r b'>143593</td><td class=b>= Mozambique</td>
</tr><tr><td class=r>143510</td><td>= Honduras</td>
<td class='r b'>143594</td><td class=b>= Namibia</td>
</tr><tr><td class=r>143511</td><td>= Jamaica</td>
<td class='r b'>143595</td><td class=b>= Palau</td>
</tr><tr><td class=r>143512</td><td>= Nicaragua</td>
<td class='r b'>143597</td><td class=b>= Papua New Guinea</td>
</tr><tr><td class=r>143513</td><td>= Paraguay</td>
<td class='r b'>143598</td><td class=b>= Sao Tome and Principe</td>
</tr><tr><td class=r>143514</td><td>= Uruguay</td>
<td class='r b'>143599</td><td class=b>= Seychelles</td>
</tr><tr><td class=r>143515</td><td>= Macau</td>
<td class='r b'>143600</td><td class=b>= Sierra Leone</td>
</tr><tr><td class=r>143516</td><td>= Egypt</td>
<td class='r b'>143601</td><td class=b>= Solomon Islands</td>
</tr><tr><td class=r>143517</td><td>= Kazakhstan</td>
<td class='r b'>143602</td><td class=b>= Swaziland</td>
</tr><tr><td class=r>143518</td><td>= Estonia</td>
<td class='r b'>143603</td><td class=b>= Tajikistan</td>
</tr><tr><td class=r>143519</td><td>= Latvia</td>
<td class='r b'>143604</td><td class=b>= Turkmenistan</td>
</tr><tr><td class=r>143520</td><td>= Lithuania</td>
<td class='r b'>143605</td><td class=b>= Zimbabwe</td>
</tr><tr><td class=r>143521</td><td>= Malta</td>
<td class='r b'>&nbsp;</td><td class=b>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='iTunesInfo'>QuickTime iTunesInfo Tags</a></h2>
<p>ExifTool will extract any iTunesInfo tags that exist, even if they are not
defined in this table.  These tags belong to the family 1 &quot;iTunes&quot; group,
and are not currently writable.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'ARTISTS'</td>
<td>Artists</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Actors'</td>
<td>Actors</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'BARCODE'</td>
<td>Barcode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CATALOGNUMBER'</td>
<td>CatalogNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'COSTUME_DESIGNER'</td>
<td>CostumeDesigner</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'DIRECTOR'</td>
<td>Director</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'DIRECTOR_OF_PHOTOGRAPHY'</td>
<td>DirectorOfPhotography</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'DISCNUMBER'</td>
<td>DiscNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Dynamic Range (DR)'</td>
<td>DynamicRange</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Dynamic Range (R128)'</td>
<td>DynamicRangeR128</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'EDITED_BY'</td>
<td>EditedBy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Encoding Params'</td>
<td>EncodingParams</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#EncodingParams'>QuickTime EncodingParams Tags</a></td></tr>
<tr>
<td>'IMDB_ID'</td>
<td>IMDB_ID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'LABEL'</td>
<td>Label</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'MEDIA'</td>
<td>Media</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'MOOD'</td>
<td>Mood</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'PRODUCER'</td>
<td>Producer</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'PRODUCTION_DESIGNER'</td>
<td>ProductionDesigner</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Peak Level (R128)'</td>
<td>PeakLevelR128</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Peak Level (Sample)'</td>
<td>PeakLevelSample</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'RATING'</td>
<td>Rating</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SCREENPLAY_BY'</td>
<td>ScreenplayBy</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SCRIPT'</td>
<td>Script</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'TIPL'</td>
<td>TIPL</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'TMDB_ID'</td>
<td>TMDB_ID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'TRACKNUMBER'</td>
<td>TrackNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Volume Level (R128)'</td>
<td>VolumeLevelR128</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Volume Level (ReplayGain)'</td>
<td>ReplayVolumeLevel</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'iTunEXTC'</td>
<td>ContentRating</td>
<td class=c>no</td>
<td><span class=s><span class=n>(standard | rating | score | reasons)</span></span></td></tr>
<tr class=b>
<td>'iTunMOVI'</td>
<td>iTunMOVI</td>
<td class=c>-</td>
<td>--&gt; <a href='PLIST.html'>PLIST Tags</a></td></tr>
<tr>
<td>'iTunNORM'</td>
<td>VolumeNormalization</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'iTunSMPB'</td>
<td>iTunSMPB</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'iTunes_CDDB_1'</td>
<td>CDDB1Info</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'iTunes_CDDB_TrackNumber'</td>
<td>CDDBTrackNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'initialkey'</td>
<td>InitialKey</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'originaldate'</td>
<td>OriginalDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'originalyear'</td>
<td>OriginalYear</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'popularimeter'</td>
<td>Popularimeter</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'replaygain_track_gain'</td>
<td>ReplayTrackGain</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'replaygain_track_peak'</td>
<td>ReplayTrackPeak</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tool'</td>
<td>iTunTool</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'~length'</td>
<td>Length</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='EncodingParams'>QuickTime EncodingParams Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'abrt'</td>
<td>AudioAvailableBitRateRange</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'acbf'</td>
<td>AudioBitRateControlMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'acef'</td>
<td>AudioExtendFrequencies</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'brat'</td>
<td>AudioCurrentTargetBitRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'cdcv'</td>
<td>AudioComponentVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cmnc'</td>
<td>AudioAvailableNumberChannels</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'init'</td>
<td>AudioIsInitialized</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'lmrc'</td>
<td>AudioDoesSampleRateConversion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'mdel'</td>
<td>AudioMinimumDelayMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'mnip'</td>
<td>AudioMinimumNumberInputPackets</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'mnop'</td>
<td>AudioMinimumNumberOutputPackets</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'oppr'</td>
<td>AudioOutputPrecedence</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pad0'</td>
<td>AudioZeroFramesPadded</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'pakb'</td>
<td>AudioMaximumPacketByteSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pakd'</td>
<td>AudioRequiresPacketDescription</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'pakf'</td>
<td>AudioPacketFrameSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'prmm'</td>
<td>AudioCodecPrimeMethod</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'srcq'</td>
<td>AudioQualitySetting</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tbuf'</td>
<td>AudioInputBufferSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'ubuf'</td>
<td>AudioUsedInputBufferSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ursr'</td>
<td>AudioUseRecommendedSampleRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'vbrq'</td>
<td>AudioVBRQuality</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'vers'</td>
<td>AudioEncodingParamsVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'vpk?'</td>
<td>AudioHasVariablePacketByteSizes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ItemProp'>QuickTime ItemProp Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'ipco'</td>
<td>ItemPropertyContainer</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ItemPropCont'>QuickTime ItemPropCont Tags</a></td></tr>
<tr class=b>
<td>'ipma'</td>
<td>ItemPropertyAssociation</td>
<td class=c>no</td>
<td><span class=s><span class=n>(parsed, but not extracted as a tag)</span></span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ItemPropCont'>QuickTime ItemPropCont Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'auxC'</td>
<td>AuxiliaryImageType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'av1C'</td>
<td>AV1Configuration</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#AV1Config'>QuickTime AV1Config Tags</a></td></tr>
<tr>
<td>'clap'</td>
<td>CleanAperture</td>
<td class=c>no</td>
<td><span class=s><span class=n>(4 numbers: width, height, left and top)</span></span></td></tr>
<tr class=b>
<td>'clli'</td>
<td>ContentLightLevel</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ContentLightLevel'>QuickTime ContentLightLevel Tags</a></td></tr>
<tr>
<td>'colr'</td>
<td>ICC_Profile
  <br>ColorRepresentation</td>
<td class=c>-<br>-</td>
<td>--&gt; <a href='ICC_Profile.html'>ICC_Profile Tags</a>
  <br>--&gt; <a href='QuickTime.html#ColorRep'>QuickTime ColorRep Tags</a></td></tr>
<tr class=b>
<td>'hvcC'</td>
<td>HEVCConfiguration</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HEVCConfig'>QuickTime HEVCConfig Tags</a></td></tr>
<tr>
<td>'irot'</td>
<td>Rotation</td>
<td class=c title=' ! = Unsafe'>int8u!</td>
<td><span class=s>0 = Horizontal (Normal)
  <br>1 = Rotate 270 CW
  <br>2 = Rotate 180
  <br>3 = Rotate 90 CW</span></td></tr>
<tr class=b>
<td>'ispe'</td>
<td>ImageSpatialExtent</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pasp'</td>
<td>PixelAspectRatio</td>
<td class=c title=' ! = Unsafe'>int32u[2]!</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'pixi'</td>
<td>ImagePixelDepth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'rloc'</td>
<td>RelativeLocation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AV1Config'>QuickTime AV1Config Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>AV1ConfigurationVersion</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x7f]</span></td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>SeqProfile?</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 5 &amp; 0x7]</span></td></tr>
<tr>
<td class=r title='1 = 0x1'>1.1</td>
<td>SeqLevelIdx0?</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x1f]</span></td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SeqTier0?</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 7 &amp; 0x1]</span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2.1</td>
<td>HighBitDepth?</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 6 &amp; 0x1]</span></td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2.2</td>
<td>TwelveBit?</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 5 &amp; 0x1]</span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2.3</td>
<td>ChromaFormat</td>
<td class=c>no</td>
<td><span class=s><span class=n>(bits: 0x04 = Monochrome, 0x02 = SubSamplingX, 0x01 = SubSamplingY)</span>
  <br>[val &gt;&gt; 2 &amp; 0x7]
  <br>0 = YUV 4:4:4
  <br>2 = YUV 4:2:2
  <br>3 = YUV 4:2:0
  <br>7 = Monochrome 4:0:0</span></td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2.4</td>
<td>ChromaSamplePosition</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x3]
  <br>0 = Unknown
  <br>1 = Vertical
  <br>2 = Colocated
  <br>3 = (reserved)</span></td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>InitialDelaySamples?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ContentLightLevel'>QuickTime ContentLightLevel Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MaxContentLightLevel</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>MaxPicAverageLightLevel</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ColorRep'>QuickTime ColorRep Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>ColorProfiles</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>ColorPrimaries</td>
<td class=c>no</td>
<td><span class=s>1 = BT.709
  <br>2 = Unspecified
  <br>4 = BT.470 System M (historical)
  <br>5 = BT.470 System B, G (historical)
  <br>6 = BT.601
  <br>7 = SMPTE 240
  <br>8 = Generic film (color filters using illuminant C)
  <br>9 = BT.2020, BT.2100
  <br>10 = SMPTE 428 (CIE 1931 XYZ)
  <br>11 = SMPTE RP 431-2
  <br>12 = SMPTE EG 432-1
  <br>22 = EBU Tech. 3213-E</span></td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>TransferCharacteristics</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0 = For future use (0)
  <br>1 = BT.709
  <br>2 = Unspecified
  <br>3 = For future use (3)
  <br>4 = BT.470 System M (historical)
  <br>5 = BT.470 System B, G (historical)
  <br>6 = BT.601
  <br>7 = SMPTE 240 M
  <br>8 = Linear
  <br>9 = Logarithmic (100 : 1 range)
  <br>10 = Logarithmic (100 * Sqrt(10) : 1 range)
  <br>11 = IEC 61966-2-4
  <br>12 = BT.1361
  <br>13 = sRGB or sYCC
  <br>14 = BT.2020 10-bit systems
  <br>15 = BT.2020 12-bit systems
  <br>16 = SMPTE ST 2084, ITU BT.2100 PQ
  <br>17 = SMPTE ST 428
  <br>18 = BT.2100 HLG, ARIB STD-B67</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>MatrixCoefficients</td>
<td class=c>no</td>
<td><span class=s>0 = Identity matrix
  <br>1 = BT.709
  <br>2 = Unspecified
  <br>3 = For future use (3)
  <br>4 = US FCC 73.628
  <br>5 = BT.470 System B, G (historical)
  <br>6 = BT.601
  <br>7 = SMPTE 240 M
  <br>8 = YCgCo
  <br>9 = BT.2020 non-constant luminance, BT.2100 YCbCr
  <br>10 = BT.2020 constant luminance
  <br>11 = SMPTE ST 2085 YDzDx
  <br>12 = Chromaticity-derived non-constant luminance
  <br>13 = Chromaticity-derived constant luminance
  <br>14 = BT.2100 ICtCp</span></td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>VideoFullRangeFlag</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 7 &amp; 0x1]
  <br>0 = Limited
  <br>1 = Full</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HEVCConfig'>QuickTime HEVCConfig Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>HEVCConfigurationVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>GeneralProfileSpace</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 6 &amp; 0x3]
  <br>0 = Conforming</span></td></tr>
<tr>
<td class=r title='1 = 0x1'>1.1</td>
<td>GeneralTierFlag</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 5 &amp; 0x1]
  <br>0 = Main Tier
  <br>1 = High Tier</span></td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1.2</td>
<td>GeneralProfileIDC</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x1f]
  <br>0 = No Profile
  <br>1 = Main
  <br>2 = Main 10
  <br>3 = Main Still Picture
  <br>4 = Format Range Extensions
  <br>5 = High Throughput
  <br>6 = Multiview Main
  <br>7 = Scalable Main
  <br>8 = 3D Main
  <br>9 = Screen Content Coding Extensions
  <br>10 = Scalable Format Range Extensions
  <br>11 = High Throughput Screen Content Coding Extensions</span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>GenProfileCompatibilityFlags</td>
<td class=c>no</td>
<td><span class=s>Bit 20 = High Throughput Screen Content Coding Extensions
  <br>Bit 21 = Scalable Format Range Extensions
  <br>Bit 22 = Screen Content Coding Extensions
  <br>Bit 23 = 3D Main
  <br>Bit 24 = Scalable Main
  <br>Bit 25 = Multiview Main
  <br>Bit 26 = High Throughput
  <br>Bit 27 = Format Range Extensions
  <br>Bit 28 = Main Still Picture
  <br>Bit 29 = Main 10
  <br>Bit 30 = Main
  <br>Bit 31 = No Profile</span></td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>ConstraintIndicatorFlags</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>GeneralLevelIDC</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='13 = 0xd'>13</td>
<td>MinSpatialSegmentationIDC</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0xfff]</span></td></tr>
<tr>
<td class=r title='15 = 0xf'>15</td>
<td>ParallelismType</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x3]</span></td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>ChromaFormat</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x3]
  <br>0 = Monochrome
  <br>1 = 4:2:0
  <br>2 = 4:2:2
  <br>3 = 4:4:4</span></td></tr>
<tr>
<td class=r title='17 = 0x11'>17</td>
<td>BitDepthLuma</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x7]</span></td></tr>
<tr class=b>
<td class=r title='18 = 0x12'>18</td>
<td>BitDepthChroma</td>
<td class=c>no</td>
<td><span class=s>[val &amp; 0x7]</span></td></tr>
<tr>
<td class=r title='19 = 0x13'>19</td>
<td>AverageFrameRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>ConstantFrameRate</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 6 &amp; 0x3]
  <br>0 = Unknown
  <br>1 = Constant Frame Rate
  <br>2 = Each Temporal Layer is Constant Frame Rate</span></td></tr>
<tr>
<td class=r title='21 = 0x15'>21.1</td>
<td>NumTemporalLayers</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 3 &amp; 0x7]</span></td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21.2</td>
<td>TemporalIDNested</td>
<td class=c>no</td>
<td><span class=s>[val &gt;&gt; 2 &amp; 0x1]
  <br>0 = No
  <br>1 = Yes</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ItemRef'>QuickTime ItemRef Tags</a></h2>
<p>The Item reference entries listed in the table below contain information about
the associations between items in the file.  This information is used by
ExifTool, but these entries are not extracted as tags.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'auxl'</td>
<td>AuxiliaryImageRef</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cdsc'</td>
<td>ContentDescribes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'dimg'</td>
<td>DerivedImageRef</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'thmb'</td>
<td>ThumbnailRef</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MovieFragment'>QuickTime MovieFragment Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
<tr class=b>
<td>'mfhd'</td>
<td>MovieFragmentHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MovieFragHdr'>QuickTime MovieFragHdr Tags</a></td></tr>
<tr>
<td>'traf'</td>
<td>TrackFragment</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TrackFragment'>QuickTime TrackFragment Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MovieFragHdr'>QuickTime MovieFragHdr Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>MovieFragmentSequence</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TrackFragment'>QuickTime TrackFragment Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Movie'>QuickTime Movie Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'cmov'</td>
<td>CompressedMovie</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#CMovie'>QuickTime CMovie Tags</a></td></tr>
<tr class=b>
<td>'gps '</td>
<td>GPSDataList?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'htka'</td>
<td>HTCTrack</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Track'>QuickTime Track Tags</a></td></tr>
<tr class=b>
<td>'iods'</td>
<td>InitialObjectDescriptor?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'meco'</td>
<td>OtherMeta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#OtherMeta'>QuickTime OtherMeta Tags</a></td></tr>
<tr class=b>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
<tr>
<td>'mvhd'</td>
<td>MovieHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MovieHeader'>QuickTime MovieHeader Tags</a></td></tr>
<tr class=b>
<td>'trak'</td>
<td>Track</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Track'>QuickTime Track Tags</a></td></tr>
<tr>
<td>'udta'</td>
<td>UserData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#UserData'>QuickTime UserData Tags</a></td></tr>
<tr class=b>
<td>'uuid'</td>
<td>UUID-USMT
  <br>UUID-Canon
  <br>GarminGPS
  <br>GarminGPS
  <br>UUID-Unknown?</td>
<td class=c>-<br>-<br>-<br>no<br>no</td>
<td>--&gt; <a href='QuickTime.html#UserMedia'>QuickTime UserMedia Tags</a>
  <br>--&gt; <a href='Canon.html#uuid'>Canon uuid Tags</a>
  <br>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a>
  <br><span class=n>(Garmin GPS sensor data)</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CMovie'>QuickTime CMovie Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'dcom'</td>
<td>Compression</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Track'>QuickTime Track Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'mdia'</td>
<td>Media</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Media'>QuickTime Media Tags</a></td></tr>
<tr class=b>
<td>'meco'</td>
<td>OtherMeta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#OtherMeta'>QuickTime OtherMeta Tags</a></td></tr>
<tr>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
<tr class=b>
<td>'tapt'</td>
<td>TrackAperture</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TrackAperture'>QuickTime TrackAperture Tags</a></td></tr>
<tr>
<td>'tkhd'</td>
<td>TrackHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TrackHeader'>QuickTime TrackHeader Tags</a></td></tr>
<tr class=b>
<td>'tref'</td>
<td>TrackRef</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TrackRef'>QuickTime TrackRef Tags</a></td></tr>
<tr>
<td>'udta'</td>
<td>UserData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#UserData'>QuickTime UserData Tags</a></td></tr>
<tr class=b>
<td>'uuid'</td>
<td>UUID-USMT
  <br>SphericalVideoXML
  <br>UUID-Unknown?</td>
<td class=c>-<br>-<br>no</td>
<td>--&gt; <a href='QuickTime.html#UserMedia'>QuickTime UserMedia Tags</a>
  <br>--&gt; <a href='XMP.html'>XMP Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Media'>QuickTime Media Tags</a></h2>
<p>MP4 media box.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'elng'</td>
<td>ExtendedLanguageTag</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'hdlr'</td>
<td>Handler</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Handler'>QuickTime Handler Tags</a></td></tr>
<tr>
<td>'mdhd'</td>
<td>MediaHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MediaHeader'>QuickTime MediaHeader Tags</a></td></tr>
<tr class=b>
<td>'minf'</td>
<td>MediaInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MediaInfo'>QuickTime MediaInfo Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MediaHeader'>QuickTime MediaHeader Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MediaHeaderVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>MediaCreateDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>MediaModifyDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>MediaTimeScale</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>MediaDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>MediaLanguageCode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MediaInfo'>QuickTime MediaInfo Tags</a></h2>
<p>MP4 media info box.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'dinf'</td>
<td>DataInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#DataInfo'>QuickTime DataInfo Tags</a></td></tr>
<tr class=b>
<td>'gmhd'</td>
<td>GenMediaHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#GenMediaHeader'>QuickTime GenMediaHeader Tags</a></td></tr>
<tr>
<td>'hdlr'</td>
<td>Handler</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Handler'>QuickTime Handler Tags</a></td></tr>
<tr class=b>
<td>'hmhd'</td>
<td>HintHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HintHeader'>QuickTime HintHeader Tags</a></td></tr>
<tr>
<td>'nmhd'</td>
<td>NullMediaHeader?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'smhd'</td>
<td>AudioHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#AudioHeader'>QuickTime AudioHeader Tags</a></td></tr>
<tr>
<td>'stbl'</td>
<td>SampleTable</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#SampleTable'>QuickTime SampleTable Tags</a></td></tr>
<tr class=b>
<td>'vmhd'</td>
<td>VideoHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#VideoHeader'>QuickTime VideoHeader Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='GenMediaHeader'>QuickTime GenMediaHeader Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'gmin'</td>
<td>GenMediaInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#GenMediaInfo'>QuickTime GenMediaInfo Tags</a></td></tr>
<tr class=b>
<td>'text'</td>
<td>Text?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tmcd'</td>
<td>TimeCode</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TimeCode'>QuickTime TimeCode Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='GenMediaInfo'>QuickTime GenMediaInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>GenMediaVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>GenFlags</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>GenGraphicsMode</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#GraphicsMode'>QuickTime GraphicsMode Values</a></td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>GenOpColor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>GenBalance</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='GraphicsMode'>QuickTime GraphicsMode Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>GraphicsMode</th><th>Value</th><th>GraphicsMode</th><th>Value</th><th>GraphicsMode</th></tr>
<tr><td class=r>0x0</td><td>= srcCopy</td>
<td class='r b'>0xb</td><td class=b>= patBic</td>
<td class=r>0x26</td><td>= subOver</td>
</tr><tr><td class=r>0x1</td><td>= srcOr</td>
<td class='r b'>0xc</td><td class=b>= notPatCopy</td>
<td class=r>0x27</td><td>= addMin</td>
</tr><tr><td class=r>0x2</td><td>= srcXor</td>
<td class='r b'>0xd</td><td class=b>= notPatOr</td>
<td class=r>0x31</td><td>= grayishTextOr</td>
</tr><tr><td class=r>0x3</td><td>= srcBic</td>
<td class='r b'>0xe</td><td class=b>= notPatXor</td>
<td class=r>0x32</td><td>= hilite</td>
</tr><tr><td class=r>0x4</td><td>= notSrcCopy</td>
<td class='r b'>0xf</td><td class=b>= notPatBic</td>
<td class=r>0x40</td><td>= ditherCopy</td>
</tr><tr><td class=r>0x5</td><td>= notSrcOr</td>
<td class='r b'>0x20</td><td class=b>= blend</td>
<td class=r>0x100</td><td>= Alpha</td>
</tr><tr><td class=r>0x6</td><td>= notSrcXor</td>
<td class='r b'>0x21</td><td class=b>= addPin</td>
<td class=r>0x101</td><td>= White Alpha</td>
</tr><tr><td class=r>0x7</td><td>= notSrcBic</td>
<td class='r b'>0x22</td><td class=b>= addOver</td>
<td class=r>0x102</td><td>= Pre-multiplied Black Alpha</td>
</tr><tr><td class=r>0x8</td><td>= patCopy</td>
<td class='r b'>0x23</td><td class=b>= subPin</td>
<td class=r>0x110</td><td>= Component Alpha</td>
</tr><tr><td class=r>0x9</td><td>= patOr</td>
<td class='r b'>0x24</td><td class=b>= transparent</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr><tr><td class=r>0xa</td><td>= patXor</td>
<td class='r b'>0x25</td><td class=b>= addMax</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='TimeCode'>QuickTime TimeCode Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'tcmi'</td>
<td>TCMediaInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TCMediaInfo'>QuickTime TCMediaInfo Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TCMediaInfo'>QuickTime TCMediaInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>TextFont</td>
<td class=c>no</td>
<td><span class=s>0 = System</span></td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>TextFace</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0x0 = Plain
  <br>Bit 0 = Bold
  <br>Bit 1 = Italic
  <br>Bit 2 = Underline</td><td>&nbsp;&nbsp;</td>
  <td>Bit 3 = Outline
  <br>Bit 4 = Shadow
  <br>Bit 5 = Condense
  <br>Bit 6 = Extend</td></tr></table>
</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>TextSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>TextColor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>BackgroundColor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='24 = 0x18'>24</td>
<td>FontName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HintHeader'>QuickTime HintHeader Tags</a></h2>
<p>MP4 hint media header.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>MaxPDUSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>AvgPDUSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>MaxBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AvgBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AudioHeader'>QuickTime AudioHeader Tags</a></h2>
<p>MP4 audio media header.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>Balance</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SampleTable'>QuickTime SampleTable Tags</a></h2>
<p>MP4 sample table box.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'co64'</td>
<td>ChunkOffset64?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cslg'</td>
<td>CompositionToDecodeTimelineMapping?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ctts'</td>
<td>CompositionTimeToSample?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'padb'</td>
<td>SamplePaddingBits?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'sbgp'</td>
<td>SampleToGroup?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'sdtp'</td>
<td>IdependentAndDisposableSamples?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'sgpd'</td>
<td>SampleGroupDescription?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'stco'</td>
<td>ChunkOffset?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'stdp'</td>
<td>SampleDegradationPriority?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'stps'</td>
<td>PartialSyncSamples</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'stsc'</td>
<td>SampleToChunk?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'stsd'</td>
<td>AudioSampleDesc
  <br>VisualSampleDesc
  <br>HintSampleDesc
  <br>MetaSampleDesc
  <br>OtherSampleDesc</td>
<td class=c>-<br>-<br>-<br>-<br>-</td>
<td>--&gt; <a href='QuickTime.html#AudioSampleDesc'>QuickTime AudioSampleDesc Tags</a>
  <br>--&gt; <a href='QuickTime.html#VisualSampleDesc'>QuickTime VisualSampleDesc Tags</a>
  <br>--&gt; <a href='QuickTime.html#HintSampleDesc'>QuickTime HintSampleDesc Tags</a>
  <br>--&gt; <a href='QuickTime.html#MetaSampleDesc'>QuickTime MetaSampleDesc Tags</a>
  <br>--&gt; <a href='QuickTime.html#OtherSampleDesc'>QuickTime OtherSampleDesc Tags</a></td></tr>
<tr>
<td>'stsh'</td>
<td>ShadowSyncSampleTable?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'stss'</td>
<td>SyncSampleTable?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'stsz'</td>
<td>SampleSizes?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'stts'</td>
<td>VideoFrameRate
  <br>TimeToSampleTable?</td>
<td class=c>no<br>no</td>
<td><span class=s><span class=n>(average rate calculated from time-to-sample table for video media)</span></span></td></tr>
<tr>
<td>'stz2'</td>
<td>CompactSampleSizes?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'subs'</td>
<td>Sub-sampleInformation?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AudioSampleDesc'>QuickTime AudioSampleDesc Tags</a></h2>
<p>MP4 audio sample description.  This hybrid atom contains both data and child
atoms.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>ID/Index</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AudioFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>AudioVendorID</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#VendorID'>QuickTime VendorID Values</a></td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>AudioChannels</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>AudioBitsPerSample</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='32 = 0x20'>32</td>
<td>AudioSampleRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SA3D'</td>
<td>SpatialAudio</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#SpatialAudio'>QuickTime SpatialAudio Tags</a></td></tr>
<tr>
<td>'btrt'</td>
<td>BitrateInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Bitrate'>QuickTime Bitrate Tags</a></td></tr>
<tr class=b>
<td>'chan'</td>
<td>AudioChannelLayout</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ChannelLayout'>QuickTime ChannelLayout Tags</a></td></tr>
<tr>
<td>'damr'</td>
<td>DecodeConfig</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#DecodeConfig'>QuickTime DecodeConfig Tags</a></td></tr>
<tr class=b>
<td>'pinf'</td>
<td>PurchaseInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ProtectionInfo'>QuickTime ProtectionInfo Tags</a></td></tr>
<tr>
<td>'sinf'</td>
<td>ProtectionInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ProtectionInfo'>QuickTime ProtectionInfo Tags</a></td></tr>
<tr class=b>
<td>'wave'</td>
<td>Wave</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Wave'>QuickTime Wave Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SpatialAudio'>QuickTime SpatialAudio Tags</a></h2>
<p>Spatial Audio tags.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>SpatialAudioVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>AmbisonicType</td>
<td class=c>no</td>
<td><span class=s>0 = Periphonic</span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>AmbisonicOrder</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AmbisonicChannelOrdering</td>
<td class=c>no</td>
<td><span class=s>0 = ACN</span></td></tr>
<tr>
<td class=r title='7 = 0x7'>7</td>
<td>AmbisonicNormalization</td>
<td class=c>no</td>
<td><span class=s>0 = SN3D</span></td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>AmbisonicChannels</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='12 = 0xc'>12</td>
<td>AmbisonicChannelMap</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Bitrate'>QuickTime Bitrate Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>BufferSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>MaxBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>AverageBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ChannelLayout'>QuickTime ChannelLayout Tags</a></h2>
<p>Audio channel layout.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>LayoutFlags</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>0 = UseDescriptions
  <br>1 = UseBitmap
  <br>100 = Mono
  <br>101 = Stereo
  <br>102 = StereoHeadphones
  <br>103 = MatrixStereo
  <br>104 = MidSide
  <br>105 = XY
  <br>106 = Binaural
  <br>107 = Ambisonic_B_Format
  <br>108 = Quadraphonic
  <br>109 = Pentagonal
  <br>110 = Hexagonal
  <br>111 = Octagonal
  <br>112 = Cube
  <br>113 = MPEG_3_0_A
  <br>114 = MPEG_3_0_B
  <br>115 = MPEG_4_0_A
  <br>116 = MPEG_4_0_B
  <br>117 = MPEG_5_0_A
  <br>118 = MPEG_5_0_B
  <br>119 = MPEG_5_0_C
  <br>120 = MPEG_5_0_D
  <br>121 = MPEG_5_1_A
  <br>122 = MPEG_5_1_B
  <br>123 = MPEG_5_1_C
  <br>124 = MPEG_5_1_D
  <br>125 = MPEG_6_1_A
  <br>126 = MPEG_7_1_A
  <br>127 = MPEG_7_1_B
  <br>128 = MPEG_7_1_C
  <br>129 = Emagic_Default_7_1
  <br>130 = SMPTE_DTV
  <br>131 = ITU_2_1
  <br>132 = ITU_2_2
  <br>133 = DVD_4
  <br>134 = DVD_5
  <br>135 = DVD_6
  <br>136 = DVD_10
  <br>137 = DVD_11
  <br>138 = DVD_18
  <br>139 = AudioUnit_6_0
  <br>140 = AudioUnit_7_0
  <br>141 = AAC_6_0</td><td>&nbsp;&nbsp;</td>
  <td>142 = AAC_6_1
  <br>143 = AAC_7_0
  <br>144 = AAC_Octagonal
  <br>145 = TMH_10_2_std
  <br>146 = TMH_10_2_full
  <br>147 = DiscreteInOrder
  <br>148 = AudioUnit_7_0_Front
  <br>149 = AC3_1_0_1
  <br>150 = AC3_3_0
  <br>151 = AC3_3_1
  <br>152 = AC3_3_0_1
  <br>153 = AC3_2_1_1
  <br>154 = AC3_3_1_1
  <br>155 = EAC_6_0_A
  <br>156 = EAC_7_0_A
  <br>157 = EAC3_6_1_A
  <br>158 = EAC3_6_1_B
  <br>159 = EAC3_6_1_C
  <br>160 = EAC3_7_1_A
  <br>161 = EAC3_7_1_B
  <br>162 = EAC3_7_1_C
  <br>163 = EAC3_7_1_D
  <br>164 = EAC3_7_1_E
  <br>165 = EAC3_7_1_F
  <br>166 = EAC3_7_1_G
  <br>167 = EAC3_7_1_H
  <br>168 = DTS_3_1
  <br>169 = DTS_4_1
  <br>170 = DTS_6_0_A
  <br>171 = DTS_6_0_B
  <br>172 = DTS_6_0_C
  <br>173 = DTS_6_1_A
  <br>174 = DTS_6_1_B
  <br>175 = DTS_6_1_C
  <br>176 = DTS_7_0
  <br>177 = DTS_7_1
  <br>178 = DTS_8_0_A
  <br>179 = DTS_8_0_B
  <br>180 = DTS_8_1_A
  <br>181 = DTS_8_1_B
  <br>182 = DTS_6_1_D
  <br>183 = AAC_7_1_B
  <br>65535 = Unknown</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>AudioChannels</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AudioChannelTypes</td>
<td class=c>no</td>
<td><table class=cols><tr>
  <td>Bit 0 = Left
  <br>Bit 1 = Right
  <br>Bit 2 = Center
  <br>Bit 3 = LFEScreen
  <br>Bit 4 = LeftSurround
  <br>Bit 5 = RightSurround
  <br>Bit 6 = LeftCenter
  <br>Bit 7 = RightCenter
  <br>Bit 8 = CenterSurround
  <br>Bit 9 = LeftSurroundDirect
  <br>Bit 10 = RightSurroundDirect
  <br>Bit 11 = TopCenterSurround
  <br>Bit 12 = VerticalHeightLeft
  <br>Bit 13 = VerticalHeightCenter
  <br>Bit 14 = VerticalHeightRight
  <br>Bit 15 = TopBackLeft
  <br>Bit 16 = TopBackCenter
  <br>Bit 17 = TopBackRight</td></tr></table>
</td></tr>
<tr class=b>
<td class=r title='12 = 0xc'>12</td>
<td>NumChannelDescriptions</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>Channel1Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>Channel1Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>Channel1Coordinates</td>
<td class=c>no</td>
<td><span class=s><span class=n>(3 numbers:  for rectangular coordinates left/right, back/front, down/up; for
spherical coordinates left/right degrees, down/up degrees, distance)</span></span></td></tr>
<tr class=b>
<td class=r title='36 = 0x24'>36</td>
<td>Channel2Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr>
<td class=r title='40 = 0x28'>40</td>
<td>Channel2Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr class=b>
<td class=r title='44 = 0x2c'>44</td>
<td>Channel2Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='56 = 0x38'>56</td>
<td>Channel3Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr class=b>
<td class=r title='60 = 0x3c'>60</td>
<td>Channel3Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr>
<td class=r title='64 = 0x40'>64</td>
<td>Channel3Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='76 = 0x4c'>76</td>
<td>Channel4Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr>
<td class=r title='80 = 0x50'>80</td>
<td>Channel4Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr class=b>
<td class=r title='84 = 0x54'>84</td>
<td>Channel4Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='96 = 0x60'>96</td>
<td>Channel5Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr class=b>
<td class=r title='100 = 0x64'>100</td>
<td>Channel5Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr>
<td class=r title='104 = 0x68'>104</td>
<td>Channel5Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='116 = 0x74'>116</td>
<td>Channel6Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr>
<td class=r title='120 = 0x78'>120</td>
<td>Channel6Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr class=b>
<td class=r title='124 = 0x7c'>124</td>
<td>Channel6Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='136 = 0x88'>136</td>
<td>Channel7Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr class=b>
<td class=r title='140 = 0x8c'>140</td>
<td>Channel7Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr>
<td class=r title='144 = 0x90'>144</td>
<td>Channel7Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='156 = 0x9c'>156</td>
<td>Channel8Label</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#ChannelLabel'>QuickTime ChannelLabel Values</a></td></tr>
<tr>
<td class=r title='160 = 0xa0'>160</td>
<td>Channel8Flags</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Rectangular
  <br>Bit 1 = Spherical
  <br>Bit 2 = Meters</span></td></tr>
<tr class=b>
<td class=r title='164 = 0xa4'>164</td>
<td>Channel8Coordinates</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ChannelLabel'>QuickTime ChannelLabel Values</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class='inner sep' cellspacing=1>
<tr class=h><th>Value</th><th>ChannelLabel</th><th>Value</th><th>ChannelLabel</th><th>Value</th><th>ChannelLabel</th></tr>
<tr><td class=r>0</td><td>= Unused</td>
<td class='r b'>36</td><td class=b>= RightWide</td>
<td class=r>305</td><td>= ForeignLanguage</td>
</tr><tr><td class=r>1</td><td>= Left</td>
<td class='r b'>37</td><td class=b>= LFE2</td>
<td class=r>400</td><td>= Discrete</td>
</tr><tr><td class=r>2</td><td>= Right</td>
<td class='r b'>38</td><td class=b>= LeftTotal</td>
<td class=r>65536</td><td>= Discrete_0</td>
</tr><tr><td class=r>3</td><td>= Center</td>
<td class='r b'>39</td><td class=b>= RightTotal</td>
<td class=r>65537</td><td>= Discrete_1</td>
</tr><tr><td class=r>4</td><td>= LFEScreen</td>
<td class='r b'>40</td><td class=b>= HearingImpaired</td>
<td class=r>65538</td><td>= Discrete_2</td>
</tr><tr><td class=r>5</td><td>= LeftSurround</td>
<td class='r b'>41</td><td class=b>= Narration</td>
<td class=r>65539</td><td>= Discrete_3</td>
</tr><tr><td class=r>6</td><td>= RightSurround</td>
<td class='r b'>42</td><td class=b>= Mono</td>
<td class=r>65540</td><td>= Discrete_4</td>
</tr><tr><td class=r>7</td><td>= LeftCenter</td>
<td class='r b'>43</td><td class=b>= DialogCentricMix</td>
<td class=r>65541</td><td>= Discrete_5</td>
</tr><tr><td class=r>8</td><td>= RightCenter</td>
<td class='r b'>44</td><td class=b>= CenterSurroundDirect</td>
<td class=r>65542</td><td>= Discrete_6</td>
</tr><tr><td class=r>9</td><td>= CenterSurround</td>
<td class='r b'>45</td><td class=b>= Haptic</td>
<td class=r>65543</td><td>= Discrete_7</td>
</tr><tr><td class=r>10</td><td>= LeftSurroundDirect</td>
<td class='r b'>100</td><td class=b>= UseCoordinates</td>
<td class=r>65544</td><td>= Discrete_8</td>
</tr><tr><td class=r>11</td><td>= RightSurroundDirect</td>
<td class='r b'>200</td><td class=b>= Ambisonic_W</td>
<td class=r>65545</td><td>= Discrete_9</td>
</tr><tr><td class=r>12</td><td>= TopCenterSurround</td>
<td class='r b'>201</td><td class=b>= Ambisonic_X</td>
<td class=r>65546</td><td>= Discrete_10</td>
</tr><tr><td class=r>13</td><td>= VerticalHeightLeft</td>
<td class='r b'>202</td><td class=b>= Ambisonic_Y</td>
<td class=r>65547</td><td>= Discrete_11</td>
</tr><tr><td class=r>14</td><td>= VerticalHeightCenter</td>
<td class='r b'>203</td><td class=b>= Ambisonic_Z</td>
<td class=r>65548</td><td>= Discrete_12</td>
</tr><tr><td class=r>15</td><td>= VerticalHeightRight</td>
<td class='r b'>204</td><td class=b>= MS_Mid</td>
<td class=r>65549</td><td>= Discrete_13</td>
</tr><tr><td class=r>16</td><td>= TopBackLeft</td>
<td class='r b'>205</td><td class=b>= MS_Side</td>
<td class=r>65550</td><td>= Discrete_14</td>
</tr><tr><td class=r>17</td><td>= TopBackCenter</td>
<td class='r b'>206</td><td class=b>= XY_X</td>
<td class=r>65551</td><td>= Discrete_15</td>
</tr><tr><td class=r>18</td><td>= TopBackRight</td>
<td class='r b'>207</td><td class=b>= XY_Y</td>
<td class=r>131071</td><td>= Discrete_65535</td>
</tr><tr><td class=r>33</td><td>= RearSurroundLeft</td>
<td class='r b'>301</td><td class=b>= HeadphonesLeft</td>
<td class=r>4294967295</td><td>= Unknown</td>
</tr><tr><td class=r>34</td><td>= RearSurroundRight</td>
<td class='r b'>302</td><td class=b>= HeadphonesRight</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr><tr><td class=r>35</td><td>= LeftWide</td>
<td class='r b'>304</td><td class=b>= ClickTrack</td>
<td class=r>&nbsp;</td><td>&nbsp;</td>
</tr></table></td></tr></table></blockquote>

<h2><a name='DecodeConfig'>QuickTime DecodeConfig Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>EncoderVendor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>EncoderVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ProtectionInfo'>QuickTime ProtectionInfo Tags</a></h2>
<p>Child atoms found in &quot;sinf&quot; and/or &quot;pinf&quot; atoms.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'enda'</td>
<td>Endianness</td>
<td class=c>no</td>
<td><span class=s>0 = Big-endian (Motorola, MM)
  <br>1 = Little-endian (Intel, II)</span></td></tr>
<tr class=b>
<td>'frma'</td>
<td>OriginalFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'schi'</td>
<td>SchemeInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#SchemeInfo'>QuickTime SchemeInfo Tags</a></td></tr>
<tr class=b>
<td>'schm'</td>
<td>SchemeType</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#SchemeType'>QuickTime SchemeType Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SchemeInfo'>QuickTime SchemeInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'cert'</td>
<td>Certificate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'iviv'</td>
<td>InitializationVector</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'key '</td>
<td>KeyID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'name'</td>
<td>UserName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'righ'</td>
<td>Rights</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Rights'>QuickTime Rights Tags</a></td></tr>
<tr class=b>
<td>'user'</td>
<td>UserID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Rights'>QuickTime Rights Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'aver'</td>
<td>VersionRestrictions</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'medi'</td>
<td>MediaFlags</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'mode'</td>
<td>ModeFlags</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'plat'</td>
<td>Platform</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'song'</td>
<td>ItemID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tool'</td>
<td>ItemTool</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tran'</td>
<td>TransactionID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'veID'</td>
<td>ItemVendorID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SchemeType'>QuickTime SchemeType Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>SchemeType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>SchemeVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>SchemeURL</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Wave'>QuickTime Wave Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'enda'</td>
<td>Endianness</td>
<td class=c>no</td>
<td><span class=s>0 = Big-endian (Motorola, MM)
  <br>1 = Little-endian (Intel, II)</span></td></tr>
<tr class=b>
<td>'frma'</td>
<td>PurchaseFileFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='VisualSampleDesc'>QuickTime VisualSampleDesc Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>ID/Index</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>CompressorID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>VendorID</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#VendorID'>QuickTime VendorID Values</a></td></tr>
<tr>
<td class=r title='16 = 0x10'>16</td>
<td>SourceImageWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='17 = 0x11'>17</td>
<td>SourceImageHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>XResolution</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>YResolution</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='25 = 0x19'>25</td>
<td>CompressorName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='41 = 0x29'>41</td>
<td>BitDepth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'CDI1'</td>
<td>CDI1</td>
<td class=c>-</td>
<td>--&gt; <a href='Canon.html#CDI1'>Canon CDI1 Tags</a></td></tr>
<tr class=b>
<td>'CMP1'</td>
<td>CMP1</td>
<td class=c>-</td>
<td>--&gt; <a href='Canon.html#CMP1'>Canon CMP1 Tags</a></td></tr>
<tr>
<td>'JPEG'</td>
<td>JPEGInfo?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'avcC'</td>
<td>AVCConfiguration?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'btrt'</td>
<td>BitrateInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Bitrate'>QuickTime Bitrate Tags</a></td></tr>
<tr class=b>
<td>'clap'</td>
<td>CleanAperture</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#CleanAperture'>QuickTime CleanAperture Tags</a></td></tr>
<tr>
<td>'colr'</td>
<td>ColorRepresentation</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#ColorRep'>QuickTime ColorRep Tags</a></td></tr>
<tr class=b>
<td>'fiel'</td>
<td>VideoFieldOrder</td>
<td class=c>no</td>
<td><span class=s>[Value 0]
  <br>1 = Progressive
  <br>2 = 2:1 Interlaced</span></td></tr>
<tr>
<td>'gama'</td>
<td>Gamma</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'pasp'</td>
<td>PixelAspectRatio</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'st3d'</td>
<td>Stereoscopic3D</td>
<td class=c>no</td>
<td><span class=s>0 = Monoscopic
  <br>1 = Stereoscopic Top-Bottom
  <br>2 = Stereoscopic Left-Right
  <br>3 = Stereoscopic Stereo-Custom
  <br>4 = Stereoscopic Right-Left</span></td></tr>
<tr class=b>
<td>'sv3d'</td>
<td>SphericalVideo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#sv3d'>QuickTime sv3d Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='CleanAperture'>QuickTime CleanAperture Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index8</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>CleanApertureWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>CleanApertureHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>CleanApertureOffsetX</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>CleanApertureOffsetY</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='sv3d'>QuickTime sv3d Tags</a></h2>
<p>Tags defined by the Spherical Video V2 specification.  See
<a href="https://github.com/google/spatial-media/blob/master/docs/spherical-video-v2-rfc.md">https://github.com/google/spatial-media/blob/master/docs/spherical-video-v2-rfc.md</a>
for the specification.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'proj'</td>
<td>Projection</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#proj'>QuickTime proj Tags</a></td></tr>
<tr class=b>
<td>'svhd'</td>
<td>MetadataSource</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='proj'>QuickTime proj Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'cbmp'</td>
<td>CubemapProj</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#cbmp'>QuickTime cbmp Tags</a></td></tr>
<tr class=b>
<td>'equi'</td>
<td>EquirectangularProj</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#equi'>QuickTime equi Tags</a></td></tr>
<tr>
<td>'prhd'</td>
<td>ProjectionHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#prhd'>QuickTime prhd Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='cbmp'>QuickTime cbmp Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>Layout</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>Padding</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='equi'>QuickTime equi Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>ProjectionBoundsTop</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>ProjectionBoundsBottom</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>ProjectionBoundsLeft</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='4 = 0x4'>4</td>
<td>ProjectionBoundsRight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='prhd'>QuickTime prhd Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>PoseYawDegrees</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>PosePitchDegrees</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>PoseRollDegrees</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HintSampleDesc'>QuickTime HintSampleDesc Tags</a></h2>
<p>MP4 hint sample description.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>ID/Index</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>HintFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='16 = 0x10'>16</td>
<td>HintTrackVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>MaxPacketSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'snro'</td>
<td>SequenceNumberRandomOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tims'</td>
<td>RTPTimeScale</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tsro'</td>
<td>TimestampRandomOffset</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MetaSampleDesc'>QuickTime MetaSampleDesc Tags</a></h2>
<p>MP4 metadata sample description.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>MetaFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0008 = 8'>0x0008</td>
<td>MetaType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'btrt'</td>
<td>BitrateInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Bitrate'>QuickTime Bitrate Tags</a></td></tr>
<tr class=b>
<td>'keys'</td>
<td>Keys</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Keys'>QuickTime Keys Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='OtherSampleDesc'>QuickTime OtherSampleDesc Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>OtherFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0018 = 24'>0x0018</td>
<td>PlaybackFrameRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ftab'</td>
<td>FontTable</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'mrld'</td>
<td>MarlinDictionary</td>
<td class=c>-</td>
<td>--&gt; <a href='GM.html#mrld'>GM mrld Tags</a></td></tr>
<tr>
<td>'mrlh'</td>
<td>MarlinHeader</td>
<td class=c>-</td>
<td>--&gt; <a href='GM.html#mrlh'>GM mrlh Tags</a></td></tr>
<tr class=b>
<td>'mrlv'</td>
<td>MarlinValues</td>
<td class=c>-</td>
<td>--&gt; <a href='GM.html#mrlv'>GM mrlv Tags</a></td></tr>
<tr>
<td>'name'</td>
<td>OtherName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='VideoHeader'>QuickTime VideoHeader Tags</a></h2>
<p>MP4 video media header.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>GraphicsMode</td>
<td class=c>no</td>
<td>--&gt; <a href='QuickTime.html#GraphicsMode'>QuickTime GraphicsMode Values</a></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>OpColor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TrackAperture'>QuickTime TrackAperture Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'clef'</td>
<td>CleanApertureDimensions</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'enof'</td>
<td>EncodedPixelsDimensions</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'prof'</td>
<td>ProductionApertureDimensions</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TrackHeader'>QuickTime TrackHeader Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>TrackHeaderVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>TrackCreateDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>TrackModifyDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>TrackID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='5 = 0x5'>5</td>
<td>TrackDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='8 = 0x8'>8</td>
<td>TrackLayer</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='9 = 0x9'>9</td>
<td>TrackVolume</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='10 = 0xa'>10</td>
<td>MatrixStructure</td>
<td class=c title=' ! = Unsafe'>fixed32s[9]!</td>
<td><span class=s><span class=n>(writable for the video track via the Composite Rotation tag)</span></span></td></tr>
<tr>
<td class=r title='19 = 0x13'>19</td>
<td>ImageWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='20 = 0x14'>20</td>
<td>ImageHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TrackRef'>QuickTime TrackRef Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'cdsc'</td>
<td>ContentDescribes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'chap'</td>
<td>ChapterListTrackID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'clcp'</td>
<td>ClosedCaptionTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'fall'</td>
<td>AlternateFormatTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'folw'</td>
<td>SubtitleTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'forc'</td>
<td>ForcedSubtitleTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'mpod'</td>
<td>ElementaryStreamTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'scpt'</td>
<td>TranscriptTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ssrc'</td>
<td>Non-primarySourceTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'sync'</td>
<td>SyncronizedTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tmcd'</td>
<td>TimecodeTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='UserData'>QuickTime UserData Tags</a></h2>
<p>Tag ID&#39;s beginning with the copyright symbol (hex 0xa9) are multi-language
text.  Alternate language tags are accessed by adding a dash followed by a
3-character ISO 639-2 language code to the tag name.  ExifTool will extract
any multi-language user data tags found, even if they aren&#39;t in this table.
Note when creating new tags,
<a href="QuickTime.html#ItemList">ItemList</a> tags are
preferred over these, so to create the tag when a same-named ItemList tag
exists, either &quot;UserData&quot; must be specified (eg. <code>-UserData:Artist=Monet</code>
on the command line), or the PREFERRED level must be changed via
<a href="../config.html#PREF">the config file</a>.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'@day'</td>
<td>ContentCreateDate</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(some stupid Ricoh programmer used the &#39;@&#39; symbol instead of the copyright
symbol in these tag ID&#39;s for the Ricoh Theta Z1 and maybe other models)</span></span></td></tr>
<tr class=b>
<td>'@mak'</td>
<td>Make</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'@mod'</td>
<td>Model</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'@sec'</td>
<td>SamsungSec</td>
<td class=c>-</td>
<td>--&gt; <a href='Samsung.html#sec'>Samsung sec Tags</a></td></tr>
<tr>
<td>'@swr'</td>
<td>SoftwareVersion</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'@xyz'</td>
<td>GPSCoordinates</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'AllF'</td>
<td>PlayAllFrames</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CAME'</td>
<td>SerialNumberHash</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'CNCV'</td>
<td>CompressorVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CNFV'</td>
<td>FirmwareVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'CNMN'</td>
<td>Model</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'CNOP'</td>
<td>CanonCNOP</td>
<td class=c>-</td>
<td>--&gt; <a href='Canon.html#CNOP'>Canon CNOP Tags</a></td></tr>
<tr>
<td>'CNTH'</td>
<td>CanonCNTH</td>
<td class=c>-</td>
<td>--&gt; <a href='Canon.html#CNTH'>Canon CNTH Tags</a></td></tr>
<tr class=b>
<td>'DcMD'</td>
<td>KodakDcMD</td>
<td class=c>-</td>
<td>--&gt; <a href='Kodak.html#DcMD'>Kodak DcMD Tags</a></td></tr>
<tr>
<td>'FFMV'</td>
<td>FujiFilmFFMV</td>
<td class=c>-</td>
<td>--&gt; <a href='FujiFilm.html#FFMV'>FujiFilm FFMV Tags</a></td></tr>
<tr class=b>
<td>'FIRM'</td>
<td>FirmwareVersion</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>"FOV\0"</td>
<td>FieldOfView</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'GPMF'</td>
<td>GoProGPMF</td>
<td class=c>-</td>
<td>--&gt; <a href='GoPro.html#GPMF'>GoPro GPMF Tags</a></td></tr>
<tr>
<td>'GoPr'</td>
<td>GoProType</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'INFO'</td>
<td>SamsungINFO</td>
<td class=c>-</td>
<td>--&gt; <a href='Samsung.html#INFO'>Samsung INFO Tags</a></td></tr>
<tr>
<td>'LEIC'</td>
<td>LeicaLEIC</td>
<td class=c>-</td>
<td>--&gt; <a href='Panasonic.html#PANA'>Panasonic PANA Tags</a></td></tr>
<tr class=b>
<td>'LENS'</td>
<td>LensSerialNumber</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'LOOP'</td>
<td>LoopStyle</td>
<td class=c>int32u</td>
<td><span class=s>1 = Normal
  <br>2 = Palindromic</span></td></tr>
<tr class=b>
<td>'Lvlm'</td>
<td>LevelMeter?</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'MMA0'</td>
<td>MinoltaMMA0</td>
<td class=c>-</td>
<td>--&gt; <a href='Minolta.html#MMA'>Minolta MMA Tags</a></td></tr>
<tr class=b>
<td>'MMA1'</td>
<td>MinoltaMMA1</td>
<td class=c>-</td>
<td>--&gt; <a href='Minolta.html#MMA'>Minolta MMA Tags</a></td></tr>
<tr>
<td>'MUID'</td>
<td>MediaUID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'MVTG'</td>
<td>FujiFilmMVTG</td>
<td class=c>-</td>
<td>--&gt; <a href='EXIF.html'>EXIF Tags</a></td></tr>
<tr>
<td>'NCDT'</td>
<td>NikonNCDT</td>
<td class=c>-</td>
<td>--&gt; <a href='Nikon.html#NCDT'>Nikon NCDT Tags</a></td></tr>
<tr class=b>
<td>'PANA'</td>
<td>PanasonicPANA</td>
<td class=c>-</td>
<td>--&gt; <a href='Panasonic.html#PANA'>Panasonic PANA Tags</a></td></tr>
<tr>
<td>'PENT'</td>
<td>PentaxPENT</td>
<td class=c>-</td>
<td>--&gt; <a href='Pentax.html#PENT'>Pentax PENT Tags</a></td></tr>
<tr class=b>
<td>'PXMN'</td>
<td>MakerNotePentax5b
  <br>MakerNotePentax5c
  <br>MakerNotePentaxUnknown</td>
<td class=c>-<br>-<br>string</td>
<td>--&gt; <a href='Pentax.html'>Pentax Tags</a>
  <br>--&gt; <a href='Pentax.html'>Pentax Tags</a></td></tr>
<tr>
<td>'PXTH'</td>
<td>PentaxPreview</td>
<td class=c>-</td>
<td>--&gt; <a href='Pentax.html#PXTH'>Pentax PXTH Tags</a></td></tr>
<tr class=b>
<td>'QVMI'</td>
<td>CasioQVMI</td>
<td class=c>-</td>
<td>--&gt; <a href='EXIF.html'>EXIF Tags</a></td></tr>
<tr>
<td>'RDTA'</td>
<td>RicohRDTA</td>
<td class=c>-</td>
<td>--&gt; <a href='Ricoh.html#RDTA'>Ricoh RDTA Tags</a></td></tr>
<tr class=b>
<td>'RDTB'</td>
<td>RicohRDTB</td>
<td class=c>-</td>
<td>--&gt; <a href='Ricoh.html#RDTB'>Ricoh RDTB Tags</a></td></tr>
<tr>
<td>'RDTC'</td>
<td>RicohRDTC</td>
<td class=c>-</td>
<td>--&gt; <a href='Ricoh.html#RDTC'>Ricoh RDTC Tags</a></td></tr>
<tr class=b>
<td>'RDTG'</td>
<td>RicohRDTG</td>
<td class=c>-</td>
<td>--&gt; <a href='Ricoh.html#RDTG'>Ricoh RDTG Tags</a></td></tr>
<tr>
<td>'RDTL'</td>
<td>RicohRDTL</td>
<td class=c>-</td>
<td>--&gt; <a href='Ricoh.html#RDTL'>Ricoh RDTL Tags</a></td></tr>
<tr class=b>
<td>'RICO'</td>
<td>RicohInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='JPEG.html'>JPEG Tags</a></td></tr>
<tr>
<td>'RMKN'</td>
<td>RicohRMKN</td>
<td class=c>-</td>
<td>--&gt; <a href='EXIF.html'>EXIF Tags</a></td></tr>
<tr class=b>
<td>'RTHU'</td>
<td>PreviewImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SDLN'</td>
<td>PlayMode</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SIGM'</td>
<td>PreviewImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SNum'</td>
<td>SerialNumber</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SelO'</td>
<td>PlaySelection</td>
<td class=c>int8u</td>
<td>&nbsp;</td></tr>
<tr>
<td>'TAGS'</td>
<td>FujiFilmTags
  <br>KodakTags
  <br>KonicaMinoltaTags
  <br>MinoltaTags
  <br>NikonTags
  <br>OlympusTags1
  <br>OlympusTags2
  <br>OlympusTags3
  <br>OlympusTags4
  <br>PentaxTags
  <br>SamsungTags
  <br>SanyoMOV
  <br>SanyoMP4
  <br>UnknownTags?</td>
<td class=c>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>-<br>string</td>
<td>--&gt; <a href='FujiFilm.html#MOV'>FujiFilm MOV Tags</a>
  <br>--&gt; <a href='Kodak.html#MOV'>Kodak MOV Tags</a>
  <br>--&gt; <a href='Minolta.html#MOV1'>Minolta MOV1 Tags</a>
  <br>--&gt; <a href='Minolta.html#MOV2'>Minolta MOV2 Tags</a>
  <br>--&gt; <a href='Nikon.html#MOV'>Nikon MOV Tags</a>
  <br>--&gt; <a href='Olympus.html#MOV1'>Olympus MOV1 Tags</a>
  <br>--&gt; <a href='Olympus.html#MOV2'>Olympus MOV2 Tags</a>
  <br>--&gt; <a href='Olympus.html#MP4'>Olympus MP4 Tags</a>
  <br>--&gt; <a href='Olympus.html#MOV3'>Olympus MOV3 Tags</a>
  <br>--&gt; <a href='Pentax.html#MOV'>Pentax MOV Tags</a>
  <br>--&gt; <a href='Samsung.html#MP4'>Samsung MP4 Tags</a>
  <br>--&gt; <a href='Sanyo.html#MOV'>Sanyo MOV Tags</a>
  <br>--&gt; <a href='Sanyo.html#MP4'>Sanyo MP4 Tags</a></td></tr>
<tr class=b>
<td>'TTMD'</td>
<td>TomTomMetaData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#TomTom'>QuickTime TomTom Tags</a></td></tr>
<tr>
<td>'WLOC'</td>
<td>WindowLocation</td>
<td class=c>int16u</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'XMP_'</td>
<td>XMP</td>
<td class=c>-</td>
<td>--&gt; <a href='XMP.html'>XMP Tags</a></td></tr>
<tr>
<td>'Xtra'</td>
<td>MicrosoftXtra</td>
<td class=c>-</td>
<td>--&gt; <a href='Microsoft.html#Xtra'>Microsoft Xtra Tags</a></td></tr>
<tr class=b>
<td>'_cx_'</td>
<td>CX?</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'_cy_'</td>
<td>CY?</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'_yaw'</td>
<td>Yaw</td>
<td class=c title=' / = Avoid'>rational64s/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'albm'</td>
<td>Album</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'albr'</td>
<td>AlbumArtist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'angl'</td>
<td>CameraAngle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'apmd'</td>
<td>ApertureMode</td>
<td class=c>undef</td>
<td>&nbsp;</td></tr>
<tr>
<td>'auth'</td>
<td>Author</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'btec'</td>
<td>GlamourSettings</td>
<td class=c>-</td>
<td>--&gt; <a href='DJI.html#Glamour'>DJI Glamour Tags</a></td></tr>
<tr>
<td>'ccid'</td>
<td>ContentID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cdis'</td>
<td>ContentDistributorID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'chpl'</td>
<td>ChapterList</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'clfn'</td>
<td>ClipFileName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'clid'</td>
<td>ClipID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'clsf'</td>
<td>Classification</td>
<td class=c title=' / = Avoid'>undef/</td>
<td><span class=s><span class=n>(string in the form &quot;Entity=XXXX Index=### XXXXX&quot;, used in 3gp videos)</span></span></td></tr>
<tr>
<td>'cmid'</td>
<td>CameraID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cmnm'</td>
<td>Model</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'coll'</td>
<td>CollectionName</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'cprt'</td>
<td>Copyright</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr>
<td>'cver'</td>
<td>CodeVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'cvru'</td>
<td>CoverURI</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'date'</td>
<td>DateTimeOriginal</td>
<td class=c>string</td>
<td><span class=s><span class=n>(Apple Photos has been reported to show a crazy date/time for some MP4 files
containing this tag, but perhaps only if it is missing a time zone)</span></span></td></tr>
<tr class=b>
<td>'dscp'</td>
<td>Description</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr>
<td>'finm'</td>
<td>OriginalFileName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'fsid'</td>
<td>OriginalFilePath</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'gnre'</td>
<td>Genre</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'hinf'</td>
<td>HintTrackInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HintTrackInfo'>QuickTime HintTrackInfo Tags</a></td></tr>
<tr>
<td>'hinv'</td>
<td>HintVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'hnti'</td>
<td>HintInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HintInfo'>QuickTime HintInfo Tags</a></td></tr>
<tr>
<td>'htcb'</td>
<td>HTCBinary</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#HTCBinary'>QuickTime HTCBinary Tags</a></td></tr>
<tr class=b>
<td>'icnu'</td>
<td>IconURI</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'infi'</td>
<td>CameraInfo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Nextbase'>QuickTime Nextbase Tags</a></td></tr>
<tr class=b>
<td>'info'</td>
<td>FirmwareVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'infu'</td>
<td>InfoURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'kgtt'</td>
<td>TrackType</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'kywd'</td>
<td>Keywords</td>
<td class=c>no</td>
<td><span class=s><span class=n>(not writable because Apple doesn&#39;t follow the 3gp specification)</span></span></td></tr>
<tr class=b>
<td>'loci'</td>
<td>LocationInformation</td>
<td class=c title=' / = Avoid'>undef/</td>
<td><span class=s><span class=n>(string in the form &quot;XXXXX Role=XXX Lat=XXX Lon=XXX Alt=XXX Body=XXX
Notes=XXX&quot;, used in 3gp videos)</span></span></td></tr>
<tr>
<td>'lrcu'</td>
<td>LyricsURI</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'lvlm'</td>
<td>LevelMeter?</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'manu'</td>
<td>Make</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'mcvr'</td>
<td>PreviewImage</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'meta'</td>
<td>Meta</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Meta'>QuickTime Meta Tags</a></td></tr>
<tr class=b>
<td>'modl'</td>
<td>Model</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'nail'</td>
<td>ThumbnailTIFF</td>
<td class=c>no</td>
<td><span class=s><span class=n>(image found in some Insta360 videos, converted to TIFF format)</span></span></td></tr>
<tr class=b>
<td>'name'</td>
<td>Name</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'perf'</td>
<td>Performer</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'pmcc'</td>
<td>GarminSettings</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pose'</td>
<td>pose</td>
<td class=c>-</td>
<td>--&gt; <a href='Kodak.html#pose'>Kodak pose Tags</a></td></tr>
<tr class=b>
<td>'ptch'</td>
<td>Pitch</td>
<td class=c title=' / = Avoid'>rational64s/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'ptv '</td>
<td>PrintToVideo</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Video'>QuickTime Video Tags</a></td></tr>
<tr class=b>
<td>'rads'</td>
<td>Rads?</td>
<td class=c>rational64s</td>
<td>&nbsp;</td></tr>
<tr>
<td>'reel'</td>
<td>ReelName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'roll'</td>
<td>Roll</td>
<td class=c title=' / = Avoid'>rational64s/</td>
<td>&nbsp;</td></tr>
<tr>
<td>'rtng'</td>
<td>Rating</td>
<td class=c title=' / = Avoid'>undef/</td>
<td><span class=s><span class=n>(string in the form &quot;Entity=XXXX Criteria=XXXX XXXXX&quot;, used in 3gp videos)</span></span></td></tr>
<tr class=b>
<td>'scen'</td>
<td>Scene</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'scrn'</td>
<td>OlympusPreview</td>
<td class=c>-</td>
<td>--&gt; <a href='Olympus.html#scrn'>Olympus scrn Tags</a></td></tr>
<tr class=b>
<td>'shot'</td>
<td>ShotName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'slno'</td>
<td>SerialNumber</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'smta'</td>
<td>SamsungSmta</td>
<td class=c>-</td>
<td>--&gt; <a href='Samsung.html#smta'>Samsung smta Tags</a></td></tr>
<tr>
<td>'tags'</td>
<td>Audible_tags</td>
<td class=c>-</td>
<td>--&gt; <a href='Audible.html#tags'>Audible tags Tags</a></td></tr>
<tr class=b>
<td>'thmb'</td>
<td>MakerNotePentax5a
  <br>OlympusThumbnail
  <br>ThumbnailImage
  <br>ThumbnailPNG
  <br>UnknownThumbnail</td>
<td class=c>-<br>-<br>string<br>string<br>string</td>
<td>--&gt; <a href='Pentax.html'>Pentax Tags</a>
  <br>--&gt; <a href='Olympus.html#thmb'>Olympus thmb Tags</a></td></tr>
<tr>
<td>'time'</td>
<td>TimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'titl'</td>
<td>Title</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr>
<td>'tnam'</td>
<td>TrackName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'urat'</td>
<td>UserRating</td>
<td class=c title=' / = Avoid'>undef/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr>
<td>'uuid'</td>
<td>GarminSoftware
  <br>GarminModel
  <br>UUID-Unknown?</td>
<td class=c>string<br>no<br>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'vndr'</td>
<td>Vendor</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>'vrot'</td>
<td>AccelerometerData</td>
<td class=c>no</td>
<td><span class=s><span class=n>(accelerometer readings for each frame of the video, expressed as sets of
yaw, pitch and roll angles in degrees)</span></span></td></tr>
<tr class=b>
<td>'yrrc'</td>
<td>Year</td>
<td class=c title=' / = Avoid'>undef/</td>
<td><span class=s><span class=n>(used in 3gp videos)</span></span></td></tr>
<tr>
<td>"&copy;ART"</td>
<td>Artist</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;TIM"</td>
<td>StartTimecode</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;TSC"</td>
<td>StartTimeScale</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;TSZ"</td>
<td>StartTimeSampleSize</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;alb"</td>
<td>Album</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;arg"</td>
<td>Arranger</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ark"</td>
<td>ArrangerKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;cmt"</td>
<td>Comment</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;cok"</td>
<td>ComposerKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;com"</td>
<td>Composer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;cpy"</td>
<td>Copyright</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;day"</td>
<td>ContentCreateDate</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;dir"</td>
<td>Director</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ed1"</td>
<td>Edit1</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ed2"</td>
<td>Edit2</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ed3"</td>
<td>Edit3</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ed4"</td>
<td>Edit4</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ed5"</td>
<td>Edit5</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ed6"</td>
<td>Edit6</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ed7"</td>
<td>Edit7</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ed8"</td>
<td>Edit8</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;ed9"</td>
<td>Edit9</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;enc"</td>
<td>EncoderID</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;fmt"</td>
<td>Format</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;fpt"</td>
<td>Pitch</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;frl"</td>
<td>Roll</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;fyw"</td>
<td>Yaw</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;gen"</td>
<td>Genre</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;gpt"</td>
<td>CameraPitch</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;grl"</td>
<td>CameraRoll</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;grp"</td>
<td>Grouping</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;gyw"</td>
<td>CameraYaw</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;inf"</td>
<td>Information</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;isr"</td>
<td>ISRCCode</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;lab"</td>
<td>RecordLabelName</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;lal"</td>
<td>RecordLabelURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;lyr"</td>
<td>Lyrics</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;mak"</td>
<td>Make</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;mal"</td>
<td>MakerURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;mdl"</td>
<td>Model</td>
<td class=c title=' / = Avoid'>string/</td>
<td><span class=s><span class=n>(non-standard-format DJI tag)</span></span></td></tr>
<tr>
<td>"&copy;mod"</td>
<td>Model</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;nam"</td>
<td>Title</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;pdk"</td>
<td>ProducerKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;phg"</td>
<td>RecordingCopyright</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;prd"</td>
<td>Producer</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;prf"</td>
<td>Performers</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;prk"</td>
<td>PerformerKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;prl"</td>
<td>PerformerURL</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;req"</td>
<td>Requirements</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;snk"</td>
<td>SubtitleKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;snm"</td>
<td>Subtitle</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;src"</td>
<td>SourceCredits</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;swf"</td>
<td>SongWriter</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;swk"</td>
<td>SongWriterKeywords</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;swr"</td>
<td>SoftwareVersion</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;too"</td>
<td>Encoder</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;trk"</td>
<td>Track</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;wrt"</td>
<td>Composer</td>
<td class=c title=' / = Avoid'>string/</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;xsp"</td>
<td>SpeedX</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;xyz"</td>
<td>GPSCoordinates</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr>
<td>"&copy;ysp"</td>
<td>SpeedY</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>"&copy;zsp"</td>
<td>SpeedZ</td>
<td class=c>string</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='TomTom'>QuickTime TomTom Tags</a></h2>
<p>Tags found in TomTom Bandit Action Cam MP4 videos.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'TTAD'</td>
<td>TomTomAD</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Stream'>QuickTime Stream Tags</a></td></tr>
<tr class=b>
<td>'TTHL'</td>
<td>TomTomHL?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'TTID'</td>
<td>TomTomID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'TTVD'</td>
<td>TomTomVD</td>
<td class=c title=' + = List'>no+</td>
<td>&nbsp;</td></tr>
<tr>
<td>'TTVI'</td>
<td>TomTomVI?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HintTrackInfo'>QuickTime HintTrackInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'dimm'</td>
<td>ImmediateDataBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'dmax'</td>
<td>LargestPacketDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'dmed'</td>
<td>MediaTrackBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'drep'</td>
<td>RepeatedDataBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'maxr'</td>
<td>MaxDataRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'npck'</td>
<td>NumPackets</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'nump'</td>
<td>NumPackets</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'payt'</td>
<td>PayloadType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'pmax'</td>
<td>LargestPacketSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tmax'</td>
<td>MaxTransmissionTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tmin'</td>
<td>MinTransmissionTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'totl'</td>
<td>TotalBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tpaY'</td>
<td>TotalBytesNoRTPHeaders</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'tpay'</td>
<td>TotalBytesNoRTPHeaders</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'tpyl'</td>
<td>TotalBytesNoRTPHeaders</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'trpY'</td>
<td>TotalBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'trpy'</td>
<td>TotalBytes</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HintInfo'>QuickTime HintInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'rtp '</td>
<td>RealtimeStreamingProtocol</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'sdp '</td>
<td>StreamingDataProtocol</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='HTCBinary'>QuickTime HTCBinary Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr><td colspan=4 class=c><i>[no tags known]</i></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Nextbase'>QuickTime Nextbase Tags</a></h2>
<p>Tags found in &#39;infi&#39; atom from some Nextbase videos.  As well as these tags,
other existing tags are also extracted.  These tags are not currently
writable but they may all be removed by deleting the Nextbase group.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'2nd Cam'</td>
<td>SecondCam</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Alerts'</td>
<td>Alerts</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Alexa'</td>
<td>Alexa</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Alexa - Paired Device Name'</td>
<td>Alexa-PairedDeviceName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Alexa - Pairing'</td>
<td>Alexa-Pairing</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Alexa - Privacy Mode'</td>
<td>Alexa-PrivacyMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Alexa - Wake Word Language'</td>
<td>Alexa-WakeWordLanguage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Audio'</td>
<td>Audio</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Auto Power Off'</td>
<td>AutoPowerOff</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Battery Status'</td>
<td>BatteryStatus</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Bluetooth MAC Address'</td>
<td>BluetoothMACAddress</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Bluetooth Name'</td>
<td>BluetoothName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Country'</td>
<td>Country</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Default Settings'</td>
<td>DefaultSettings</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Device Sounds'</td>
<td>DeviceSounds</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Dual Files'</td>
<td>DualFiles</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Emergency SOS'</td>
<td>EmergencySOS</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Exposure'</td>
<td>Exposure</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Extreme Weather Mode'</td>
<td>ExtremeWeatherMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'FCC-ID'</td>
<td>FCC-ID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Firmware'</td>
<td>Firmware</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Firmware Version'</td>
<td>FirmwareVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Format SD Card'</td>
<td>FormatSDCard</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'G Sensor'</td>
<td>GSensor</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'GPS Stamp'</td>
<td>GPSStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Image Stabilisation'</td>
<td>ImageStabilisation</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Keep User Settings'</td>
<td>KeepUserSettings</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Language'</td>
<td>Language</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Linux'</td>
<td>Linux</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Model'</td>
<td>Model</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Model Stamp'</td>
<td>ModelStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'MyNextbase - Paired Device Name'</td>
<td>MyNextbase-PairedDeviceName</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'MyNextbase - Pairing'</td>
<td>MyNextbase-Pairing</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'NBCD'</td>
<td>NBCD</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Number / License Plate'</td>
<td>NumberLicensePlate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Parking Mode'</td>
<td>ParkingMode</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'RTOS'</td>
<td>RTOS</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Recording History'</td>
<td>RecordingHistory</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Resolution'</td>
<td>Resolution</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Reversing Camera'</td>
<td>ReversingCamera</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SD Card Class'</td>
<td>SDCardClass</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SD Card Format'</td>
<td>SDCardFormat</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SD Card Manf Date'</td>
<td>SDCardManfDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SD Card Manf ID'</td>
<td>SDCardManfID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SD Card Model No'</td>
<td>SDCardModelNo</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SD Card OEM ID'</td>
<td>SDCardOEMID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SD Card Serial No'</td>
<td>SDCardSerialNo</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SD Card Size'</td>
<td>SDCardSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'SD Card Type'</td>
<td>SDCardType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'SD Card Used Space'</td>
<td>SDCardUsedSpace</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Screen Dimming'</td>
<td>ScreenDimming</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Screen Saver'</td>
<td>ScreenSaver</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Serial No'</td>
<td>SerialNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Speed Stamp'</td>
<td>SpeedStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Speed Units'</td>
<td>SpeedUnits</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'System Info'</td>
<td>SystemInfo</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Time & Date'</td>
<td>TimeAndDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Time Lapse'</td>
<td>TimeLapse</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Time Stamp'</td>
<td>VideoTimeStamp</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Time Zone / DST'</td>
<td>TimeZoneDST</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Video Length'</td>
<td>VideoLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Wi-Fi MAC Address'</td>
<td>Wi-FiMACAddress</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'Wi-Fi Password'</td>
<td>Wi-FiPassword</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'Wi-Fi SSID'</td>
<td>Wi-FiSSID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td>'what3words'</td>
<td>What3Words</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Video'>QuickTime Video Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>DisplaySize</td>
<td class=c>no</td>
<td><span class=s>0 = Normal
  <br>1 = Double Size
  <br>2 = Half Size
  <br>3 = Full Screen
  <br>4 = Current Size</span></td></tr>
<tr class=b>
<td class=r title='6 = 0x6'>6</td>
<td>SlideShow</td>
<td class=c>no</td>
<td><span class=s>0 = No
  <br>1 = Yes</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='UserMedia'>QuickTime UserMedia Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'MTDT'</td>
<td>MetaData</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#MetaData'>QuickTime MetaData Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MetaData'>QuickTime MetaData Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0001 = 1'>0x0001</td>
<td>Title</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0003 = 3'>0x0003</td>
<td>ProductionDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x0004 = 4'>0x0004</td>
<td>Software</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td title='0x0005 = 5'>0x0005</td>
<td>Product</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x000a = 10'>0x000a</td>
<td>TrackProperty</td>
<td class=c>no</td>
<td><span class=s>[Value 0]
  <br>0x0 = No presentation
  <br>Bit 0 = Main track
  <br>[Value 1]
  <br>0x0 = No attributes
  <br>Bit 15 = Read only</span></td></tr>
<tr class=b>
<td title='0x000b = 11'>0x000b</td>
<td>TimeZone</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td title='0x000c = 12'>0x000c</td>
<td>ModifyDate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='MovieHeader'>QuickTime MovieHeader Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>MovieHeaderVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>CreateDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>ModifyDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>TimeScale</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>Duration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>PreferredRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>PreferredVolume</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='9 = 0x9'>9</td>
<td>MatrixStructure</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>PreviewTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='19 = 0x13'>19</td>
<td>PreviewDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='20 = 0x14'>20</td>
<td>PosterTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='21 = 0x15'>21</td>
<td>SelectionTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='22 = 0x16'>22</td>
<td>SelectionDuration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='23 = 0x17'>23</td>
<td>CurrentTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>NextTrackID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Preview'>QuickTime Preview Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index2</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>PreviewDate</td>
<td class=c>int32u</td>
<td><span class=s><span class=n>(converted from UTC to local time if the QuickTimeUTC option is set.  This
tag is part of a binary data structure so it may not be deleted -- instead
the value is set to zero if the tag is deleted individually)</span></span></td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>PreviewVersion</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='3 = 0x3'>3</td>
<td>PreviewAtomType</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>PreviewAtomIndex</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='SkipInfo'>QuickTime SkipInfo Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'thma'</td>
<td>ThumbnailImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'ver '</td>
<td>Version</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Profile'>QuickTime Profile Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'APRF'</td>
<td>AudioProfile</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#AudioProf'>QuickTime AudioProf Tags</a></td></tr>
<tr class=b>
<td>'FPRF'</td>
<td>FileGlobalProfile</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#FileProf'>QuickTime FileProf Tags</a></td></tr>
<tr>
<td>'OLYM'</td>
<td>OlympusOLYM</td>
<td class=c>-</td>
<td>--&gt; <a href='Olympus.html#OLYM'>Olympus OLYM Tags</a></td></tr>
<tr class=b>
<td>'VPRF'</td>
<td>VideoProfile</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#VideoProf'>QuickTime VideoProf Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='AudioProf'>QuickTime AudioProf Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>AudioProfileVersion?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>AudioTrackID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>AudioCodec</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>AudioCodecInfo?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>AudioAttributes</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Encrypted
  <br>Bit 1 = Variable bitrate
  <br>Bit 2 = Dual mono</span></td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>AudioAvgBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>AudioMaxBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>AudioSampleRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>AudioChannels</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='FileProf'>QuickTime FileProf Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>FileProfileVersion?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>FileFunctionFlags</td>
<td class=c>no</td>
<td><span class=s>Bit 28 = Fragmented
  <br>Bit 29 = Additional tracks
  <br>Bit 30 = Edited</span></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='VideoProf'>QuickTime VideoProf Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='0 = 0x0'>0</td>
<td>VideoProfileVersion?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='1 = 0x1'>1</td>
<td>VideoTrackID</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='2 = 0x2'>2</td>
<td>VideoCodec</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='3 = 0x3'>3</td>
<td>VideoCodecInfo?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='4 = 0x4'>4</td>
<td>VideoAttributes</td>
<td class=c>no</td>
<td><span class=s>Bit 0 = Encrypted
  <br>Bit 1 = Variable bitrate
  <br>Bit 2 = Variable frame rate
  <br>Bit 3 = Interlaced</span></td></tr>
<tr class=b>
<td class=r title='5 = 0x5'>5</td>
<td>VideoAvgBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='6 = 0x6'>6</td>
<td>VideoMaxBitrate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='7 = 0x7'>7</td>
<td>VideoAvgFrameRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='8 = 0x8'>8</td>
<td>VideoMaxFrameRate</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='9 = 0x9'>9</td>
<td>VideoSize</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>PixelAspectRatio</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Flip'>QuickTime Flip Tags</a></h2>
<p>Found in MP4 files from Flip Video cameras.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index4</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>PreviewImageWidth</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>PreviewImageHeight</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='13 = 0xd'>13</td>
<td>PreviewImageLength</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>SerialNumber</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='28 = 0x1c'>28</td>
<td>PreviewImage</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Tags360Fly'>QuickTime Tags360Fly Tags</a></h2>
<p>Timed metadata found in MP4 videos from the 360Fly.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td title='0x0001 = 1'>0x0001</td>
<td>Accel360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Accel360Fly'>QuickTime Accel360Fly Tags</a></td></tr>
<tr class=b>
<td title='0x0002 = 2'>0x0002</td>
<td>Gyro360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Gyro360Fly'>QuickTime Gyro360Fly Tags</a></td></tr>
<tr>
<td title='0x0003 = 3'>0x0003</td>
<td>Mag360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Mag360Fly'>QuickTime Mag360Fly Tags</a></td></tr>
<tr class=b>
<td title='0x0005 = 5'>0x0005</td>
<td>GPS360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#GPS360Fly'>QuickTime GPS360Fly Tags</a></td></tr>
<tr>
<td title='0x0006 = 6'>0x0006</td>
<td>Rot360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Rot360Fly'>QuickTime Rot360Fly Tags</a></td></tr>
<tr class=b>
<td title='0x00fa = 250'>0x00fa</td>
<td>Fusion360Fly</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#Fusion360Fly'>QuickTime Fusion360Fly Tags</a></td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Accel360Fly'>QuickTime Accel360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>AccelMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>AccelYPR</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Gyro360Fly'>QuickTime Gyro360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>GyroMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>GyroYPR</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Mag360Fly'>QuickTime Mag360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>MagMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>MagnetometerXYZ</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='GPS360Fly'>QuickTime GPS360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>GPSMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>GPSLatitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='14 = 0xe'>14</td>
<td>GPSLongitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='18 = 0x12'>18</td>
<td>GPSAltitude</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='22 = 0x16'>22</td>
<td>GPSSpeed</td>
<td class=c>no</td>
<td><span class=s><span class=n>(converted to km/hr)</span></span></td></tr>
<tr>
<td class=r title='24 = 0x18'>24</td>
<td>GPSTrack</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='26 = 0x1a'>26</td>
<td>Acceleration</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Rot360Fly'>QuickTime Rot360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>RotMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>RotationXYZ</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='Fusion360Fly'>QuickTime Fusion360Fly Tags</a></h2>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Index1</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td class=r title='1 = 0x1'>1</td>
<td>FusionMode?</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td class=r title='2 = 0x2'>2</td>
<td>SampleTime</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr>
<td class=r title='10 = 0xa'>10</td>
<td>FusionYPR</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
</table></td></tr></table></blockquote>

<h2><a name='ImageFile'>QuickTime ImageFile Tags</a></h2>
<p>Tags used in QTIF QuickTime Image Files.</p>
<blockquote>
<table class=frame><tr><td>
<table class=inner cellspacing=1>
<tr class=h><th>Tag&nbsp;ID</th><th>Tag Name</th>
<th>Writable</th><th>Values / <span class=n>Notes</span></th></tr>
<tr>
<td>'idat'</td>
<td>ImageData</td>
<td class=c>no</td>
<td>&nbsp;</td></tr>
<tr class=b>
<td>'idsc'</td>
<td>ImageDescription</td>
<td class=c>-</td>
<td>--&gt; <a href='QuickTime.html#VisualSampleDesc'>QuickTime VisualSampleDesc Tags</a></td></tr>
<tr>
<td>'iicc'</td>
<td>ICC_Profile</td>
<td class=c>-</td>
<td>--&gt; <a href='ICC_Profile.html'>ICC_Profile Tags</a></td></tr>
</table></td></tr></table></blockquote>

<hr>
(This document generated automatically by Image::ExifTool::BuildTagLookup)
<br><i>Last revised May 4, 2025</i>
<p class=lf><a href='index.html'>&lt;-- ExifTool Tag Names</a></p>
</body>
</html>
