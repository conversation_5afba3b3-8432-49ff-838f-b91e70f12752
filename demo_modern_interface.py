#!/usr/bin/env python3
"""
Demo of the modern interface design for Metadata Genie AI
This demonstrates the new sleek dark theme and modern UI components.
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QProgressBar, QTabWidget, QSplitter,
    QListWidget, QListWidgetItem, QTextEdit, QCheckBox, QGroupBox
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont

# Modern Dark Theme Stylesheet
MODERN_DARK_THEME = """
QWidget {
    background-color: #1a1a1a;
    color: #e0e0e0;
    font-family: 'Segoe UI', 'San Francisco', 'Helvetica Neue', Arial, sans-serif;
    font-size: 9pt;
}

QMainWindow, QDialog {
    background-color: #1a1a1a;
}

QTabWidget::pane {
    border: 1px solid #404040;
    background-color: #2d2d2d;
    border-radius: 8px;
    margin-top: -1px;
}

QTabBar::tab {
    background-color: #2d2d2d;
    color: #b0b0b0;
    padding: 10px 16px;
    border: 1px solid #404040;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-right: 2px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: #2d2d2d;
    color: #64b5f6;
    border-bottom: 2px solid #64b5f6;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background-color: #353535;
    color: #64b5f6;
}

QPushButton {
    background-color: #2d2d2d;
    color: #64b5f6;
    border: 2px solid #64b5f6;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #64b5f6;
    color: #1a1a1a;
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3);
}

QPushButton:pressed {
    background-color: #42a5f5;
    border-color: #42a5f5;
}

QPushButton:disabled {
    background-color: #2d2d2d;
    color: #666666;
    border-color: #404040;
}

QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 2px solid #404040;
    border-radius: 8px;
    padding: 8px 12px;
    selection-background-color: #1e3a5f;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #64b5f6;
    box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.2);
}

QProgressBar {
    border: 2px solid #404040;
    border-radius: 8px;
    background-color: #2d2d2d;
    text-align: center;
    font-weight: 500;
    height: 20px;
    color: #e0e0e0;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #64b5f6, stop:1 #42a5f5);
    border-radius: 6px;
    margin: 1px;
}

QListWidget, QTreeWidget, QTableWidget {
    background-color: #2d2d2d;
    alternate-background-color: #262626;
    border: 2px solid #404040;
    border-radius: 8px;
    selection-background-color: #1e3a5f;
    selection-color: #64b5f6;
}

QListWidget::item {
    padding: 8px;
    border-radius: 4px;
    margin: 2px;
}

QListWidget::item:selected {
    background-color: #1e3a5f;
    color: #64b5f6;
}

QListWidget::item:hover {
    background-color: #353535;
}

QGroupBox {
    font-weight: 600;
    border: 2px solid #404040;
    border-radius: 8px;
    margin-top: 8px;
    padding-top: 8px;
    color: #e0e0e0;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    color: #64b5f6;
}

QCheckBox {
    spacing: 8px;
    color: #e0e0e0;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #555555;
    border-radius: 4px;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #64b5f6;
    border-color: #64b5f6;
}

QCheckBox::indicator:hover {
    border-color: #64b5f6;
}

QSplitter::handle {
    background-color: #404040;
    border: 1px solid #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

QSplitter::handle:hover {
    background-color: #64b5f6;
}
"""

class ModernDemoWindow(QMainWindow):
    """Demo window showing the modern interface design."""
    
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        """Initialize the modern UI."""
        self.setWindowTitle("Metadata Genie AI - Modern Interface Demo")
        self.setMinimumSize(1200, 800)
        
        # Apply the modern dark theme
        self.setStyleSheet(MODERN_DARK_THEME)
        
        # Create central widget
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(16, 16, 16, 16)
        
        # Title
        title = QLabel("Modern Metadata Genie AI Interface")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: 700; color: #64b5f6; margin: 16px;")
        main_layout.addWidget(title)
        
        # Create splitter for main layout
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel
        left_panel = self.create_left_panel()
        
        # Right panel with tabs
        right_panel = self.create_right_panel()
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 600])
        
        main_layout.addWidget(splitter)
        
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
    def create_left_panel(self):
        """Create the left panel with image list and controls."""
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        left_layout.setSpacing(12)
        left_layout.setContentsMargins(8, 8, 8, 8)
        
        # Image list
        left_layout.addWidget(QLabel("Selected Images:"))
        
        image_list = QListWidget()
        image_list.setIconSize(QSize(100, 100))
        
        # Add some demo items
        for i in range(5):
            item = QListWidgetItem(f"sample_image_{i+1}.jpg")
            image_list.addItem(item)
        
        left_layout.addWidget(image_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        add_btn = QPushButton("Add Images")
        remove_btn = QPushButton("Remove Selected")
        clear_btn = QPushButton("Clear All")
        
        button_layout.addWidget(add_btn)
        button_layout.addWidget(remove_btn)
        button_layout.addWidget(clear_btn)
        left_layout.addLayout(button_layout)
        
        # Process buttons
        process_layout = QHBoxLayout()
        process_layout.setSpacing(8)
        
        process_selected_btn = QPushButton("Process Selected")
        process_all_btn = QPushButton("Process All")
        
        process_layout.addWidget(process_selected_btn)
        process_layout.addWidget(process_all_btn)
        left_layout.addLayout(process_layout)
        
        # Progress bar
        progress = QProgressBar()
        progress.setValue(65)
        left_layout.addWidget(progress)
        
        left_panel.setLayout(left_layout)
        return left_panel
        
    def create_right_panel(self):
        """Create the right panel with tabs."""
        tab_widget = QTabWidget()
        
        # Metadata tab
        metadata_tab = self.create_metadata_tab()
        tab_widget.addTab(metadata_tab, "Metadata Preview")
        
        # Logs tab
        logs_tab = self.create_logs_tab()
        tab_widget.addTab(logs_tab, "Logs")
        
        return tab_widget
        
    def create_metadata_tab(self):
        """Create the metadata preview tab."""
        metadata_widget = QWidget()
        layout = QVBoxLayout()
        
        # Description group
        desc_group = QGroupBox("Description")
        desc_layout = QVBoxLayout()
        desc_edit = QTextEdit()
        desc_edit.setPlainText("A beautiful landscape photo showing mountains and a lake at sunset.")
        desc_layout.addWidget(desc_edit)
        desc_group.setLayout(desc_layout)
        
        # Keywords group
        keywords_group = QGroupBox("Keywords")
        keywords_layout = QVBoxLayout()
        keywords_edit = QTextEdit()
        keywords_edit.setPlainText("landscape\nmountains\nlake\nsunset\nnature\nphotography")
        keywords_layout.addWidget(keywords_edit)
        keywords_group.setLayout(keywords_layout)
        
        # Checkboxes
        fields_group = QGroupBox("Fields to Update")
        fields_layout = QVBoxLayout()
        
        desc_check = QCheckBox("Description")
        desc_check.setChecked(True)
        caption_check = QCheckBox("Caption/Abstract")
        caption_check.setChecked(True)
        keywords_check = QCheckBox("Keywords")
        keywords_check.setChecked(True)
        
        fields_layout.addWidget(desc_check)
        fields_layout.addWidget(caption_check)
        fields_layout.addWidget(keywords_check)
        fields_group.setLayout(fields_layout)
        
        # Update button
        update_btn = QPushButton("Update Metadata")
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: 2px solid #4caf50;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #45a049;
                border-color: #45a049;
                box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
            }
        """)
        
        layout.addWidget(desc_group)
        layout.addWidget(keywords_group)
        layout.addWidget(fields_group)
        layout.addWidget(update_btn)
        
        metadata_widget.setLayout(layout)
        return metadata_widget
        
    def create_logs_tab(self):
        """Create the logs tab."""
        logs_widget = QWidget()
        layout = QVBoxLayout()
        
        log_text = QTextEdit()
        log_text.setReadOnly(True)
        log_text.setPlainText("""2024-01-15 10:30:15 - INFO - Application started
2024-01-15 10:30:16 - INFO - Theme applied: dark
2024-01-15 10:30:20 - INFO - Added 5 images to processing queue
2024-01-15 10:30:25 - INFO - Processing image 1 of 5: sample_image_1.jpg
2024-01-15 10:30:28 - INFO - Analysis completed for sample_image_1.jpg
2024-01-15 10:30:30 - INFO - Metadata updated for sample_image_1.jpg
2024-01-15 10:30:32 - INFO - Processing image 2 of 5: sample_image_2.jpg""")
        
        clear_btn = QPushButton("Clear Logs")
        
        layout.addWidget(log_text)
        layout.addWidget(clear_btn)
        
        logs_widget.setLayout(layout)
        return logs_widget

def main():
    """Run the demo application."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Metadata Genie AI Demo")
    app.setApplicationVersion("2.0")
    
    # Create and show the main window
    window = ModernDemoWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
