{"models_dir": "models", "vision_model": "microsoft/git-base", "object_detection_model": "yolov8n.pt", "max_batch_size": 10, "max_image_size": 4000, "thumbnail_size": 100, "confidence_threshold": 0.5, "supported_extensions": [".jpg", ".jpeg", ".png"], "temp_dir": "temp", "iptc_fields": {"description": true, "caption": true, "keywords": true, "headline": true}, "max_keywords": 10, "min_keyword_length": 3, "ui_theme": "dark", "splitter_sizes": [400, 600], "first_run": false, "check_for_updates": true, "log_level": "INFO"}