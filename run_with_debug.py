#!/usr/bin/env python3
"""
Metadata Genie AI - Debug Launcher

This script launches the Metadata Genie AI application with debug output enabled.
It's useful for development and troubleshooting.

Run this script instead of MetadataGenie.py when you need detailed debug information.
"""

import sys
import traceback
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Log startup information
logger = logging.getLogger("MetadataGenieAI")
logger.info("Starting Metadata Genie AI in debug mode")

try:
    from PyQt5.QtWidgets import QApplication
    from metadata_genie.gui.main_window import MainWindow

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Metadata Genie AI (Debug Mode)")

    # Create and show main window
    logger.info("Creating main window")
    window = MainWindow()
    window.show()

    # Run application
    logger.info("Entering main event loop")
    sys.exit(app.exec_())

except Exception as e:
    logger.error(f"Error starting application: {str(e)}")
    traceback.print_exc()
    input("Press Enter to exit...")
