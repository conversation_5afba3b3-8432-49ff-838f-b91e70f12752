#!/usr/bin/env python3
"""
Metadata Genie AI

A tool for automatically generating and embedding metadata into images
using advanced vision models.
"""

import sys
import os
import subprocess
import traceback
import pkg_resources
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt

def check_dependencies():
    """
    Check if all required dependencies are installed.
    If not, offer to install them.

    Returns:
        bool: True if all dependencies are installed or successfully installed, False otherwise
    """
    # Read requirements from file
    with open('requirements.txt', 'r') as f:
        requirements = [line.strip() for line in f.readlines() if line.strip()]

    # Check each requirement
    missing_packages = []
    for requirement in requirements:
        # Extract package name (remove version specifier)
        package_name = requirement.split('>=')[0].split('==')[0].strip()

        # Skip ONNX-related packages as we don't need them
        if 'onnx' in package_name.lower():
            continue

        try:
            pkg_resources.get_distribution(package_name)
        except pkg_resources.DistributionNotFound:
            # Skip ONNX-related packages even if they're missing
            if 'onnx' not in requirement.lower():
                missing_packages.append(requirement)

    # If there are missing packages, offer to install them
    if missing_packages:
        message = "The following dependencies are missing:\n\n"
        message += "\n".join(missing_packages)
        message += "\n\nWould you like to install them now?"

        reply = QMessageBox.question(
            None,
            "Missing Dependencies",
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            try:
                # Install missing packages
                subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
                QMessageBox.information(
                    None,
                    "Installation Complete",
                    "Dependencies installed successfully."
                )
                return True
            except subprocess.CalledProcessError as e:
                QMessageBox.critical(
                    None,
                    "Installation Error",
                    f"Error installing dependencies: {str(e)}\n\n"
                    "Please install the required dependencies manually:\n"
                    "pip install -r requirements.txt"
                )
                return False
        else:
            QMessageBox.warning(
                None,
                "Missing Dependencies",
                "The application may not function correctly without the required dependencies."
            )
            return False

    return True

def check_exiftool():
    """
    Check if ExifTool is installed.
    If not, offer to download and install it automatically.

    Returns:
        bool: True if ExifTool is installed or successfully installed, False otherwise
    """
    try:
        result = subprocess.run(['exiftool', '-ver'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True,
                               check=False)
        if result.returncode == 0:
            print(f"ExifTool found in PATH: version {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass

    # Check if exiftool is in the current directory
    if os.name == 'nt' and os.path.exists('exiftool.exe'):
        print("ExifTool found in current directory (Windows)")
        return True
    if os.name != 'nt' and os.path.exists('exiftool'):
        print("ExifTool found in current directory (Unix)")
        return True

    # ExifTool not found, offer to download and install
    message = (
        "ExifTool is required for handling image metadata but was not found on your system.\n\n"
        "Would you like to download and install ExifTool automatically?"
    )

    reply = QMessageBox.question(
        None,
        "ExifTool Not Found",
        message,
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.Yes
    )

    if reply == QMessageBox.Yes:
        return download_and_install_exiftool()
    else:
        message = (
            "ExifTool is required for this application to function correctly.\n\n"
            "You can install it manually:\n\n"
            "Windows: Download from https://exiftool.org/ and extract exiftool.exe to this directory\n"
            "macOS: Run 'brew install exiftool' in Terminal\n"
            "Linux: Run 'sudo apt-get install libimage-exiftool-perl' or equivalent for your distribution\n\n"
            "Would you like to open the ExifTool website now?"
        )

        reply = QMessageBox.question(
            None,
            "Manual Installation",
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            import webbrowser
            webbrowser.open("https://exiftool.org/")

        return False

def download_and_install_exiftool():
    """
    Download and install ExifTool.

    Returns:
        bool: True if installation was successful, False otherwise
    """
    import requests
    import shutil
    import traceback

    # Create a progress dialog
    progress_dialog = QMessageBox(
        QMessageBox.Information,
        "Downloading ExifTool",
        "Downloading ExifTool... Please wait.",
        QMessageBox.Cancel
    )
    progress_dialog.setStandardButtons(QMessageBox.Cancel)
    progress_dialog.show()
    QApplication.processEvents()

    try:
        # Different installation methods based on OS
        if os.name == 'nt':  # Windows
            # Download the standalone Windows executable
            windows_exe_url = "https://exiftool.org/exiftool-13.29_32.zip"
            exe_path = os.path.join(os.getcwd(), "exiftool.exe")
            windows_zip_path = os.path.join(os.getcwd(), "exiftool_win.zip")

            try:
                # Download the Windows zip package
                print(f"Downloading Windows executable from {windows_exe_url}")
                response = requests.get(windows_exe_url, stream=True)
                response.raise_for_status()

                with open(windows_zip_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                        QApplication.processEvents()
                        if progress_dialog.clickedButton():
                            progress_dialog.close()
                            if os.path.exists(windows_zip_path):
                                os.remove(windows_zip_path)
                            return False

                print(f"Windows package downloaded to {windows_zip_path}")

                # Extract the zip file
                print("Extracting ExifTool Windows package")
                import zipfile

                # Create a temporary directory for extraction
                temp_dir = os.path.join(os.getcwd(), "exiftool_temp")
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                os.makedirs(temp_dir)

                # Extract the zip file
                with zipfile.ZipFile(windows_zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)

                # Find the exiftool(-k).exe file
                exiftool_found = False
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file.lower() == "exiftool(-k).exe":
                            source_path = os.path.join(root, file)
                            print(f"Found ExifTool executable: {source_path}")

                            # Copy it to the destination
                            if os.path.exists(exe_path):
                                os.remove(exe_path)
                            shutil.copy2(source_path, exe_path)
                            exiftool_found = True
                            break

                    # Also look for the exiftool_files directory which is needed
                    if "exiftool_files" in dirs:
                        source_dir = os.path.join(root, "exiftool_files")
                        dest_dir = os.path.join(os.getcwd(), "exiftool_files")
                        print(f"Found exiftool_files directory: {source_dir}")

                        # Copy the directory
                        if os.path.exists(dest_dir):
                            shutil.rmtree(dest_dir)
                        shutil.copytree(source_dir, dest_dir)

                # Clean up
                if os.path.exists(windows_zip_path):
                    os.remove(windows_zip_path)
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)

                if not exiftool_found:
                    print("ExifTool executable not found in the downloaded package")
                    progress_dialog.close()
                    QMessageBox.critical(
                        None,
                        "Installation Failed",
                        "ExifTool executable not found in the downloaded package.\n\n"
                        "Please install ExifTool manually from https://exiftool.org/"
                    )
                    return False

                progress_dialog.close()

                # Verify installation
                if os.path.exists(exe_path):
                    print("ExifTool installation successful")
                    QMessageBox.information(
                        None,
                        "Installation Complete",
                        "ExifTool has been successfully installed."
                    )
                    return True
                else:
                    print("ExifTool installation failed - executable not found")
                    QMessageBox.critical(
                        None,
                        "Installation Failed",
                        "Failed to install ExifTool. The executable was not found after extraction.\n\n"
                        "Please install it manually from https://exiftool.org/"
                    )
                    return False

            except requests.exceptions.RequestException as e:
                progress_dialog.close()
                print(f"Download error: {str(e)}")
                QMessageBox.critical(
                    None,
                    "Download Error",
                    f"Error downloading ExifTool: {str(e)}\n\n"
                    "Please check your internet connection and try again, or install ExifTool manually."
                )
                return False

        elif os.name != 'nt':  # macOS and Linux
            # For macOS and Linux, we'll provide instructions for package managers
            if shutil.which('brew') is not None:  # macOS with Homebrew
                cmd = ['brew', 'install', 'exiftool']
                install_method = "Homebrew"
            elif shutil.which('apt-get') is not None:  # Debian/Ubuntu
                cmd = ['sudo', 'apt-get', 'install', '-y', 'libimage-exiftool-perl']
                install_method = "apt-get"
            elif shutil.which('dnf') is not None:  # Fedora
                cmd = ['sudo', 'dnf', 'install', '-y', 'perl-Image-ExifTool']
                install_method = "dnf"
            elif shutil.which('yum') is not None:  # CentOS/RHEL
                cmd = ['sudo', 'yum', 'install', '-y', 'perl-Image-ExifTool']
                install_method = "yum"
            else:
                progress_dialog.close()
                QMessageBox.critical(
                    None,
                    "Installation Failed",
                    "Could not determine package manager for your system. Please install ExifTool manually."
                )
                return False

            progress_dialog.close()

            # Ask for permission to run the package manager command
            message = f"ExifTool will be installed using {install_method}.\n\nCommand: {' '.join(cmd)}\n\nProceed with installation?"
            reply = QMessageBox.question(
                None,
                "Install ExifTool",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                try:
                    subprocess.check_call(cmd)
                    QMessageBox.information(
                        None,
                        "Installation Complete",
                        "ExifTool has been successfully installed."
                    )
                    return True
                except subprocess.CalledProcessError as e:
                    QMessageBox.critical(
                        None,
                        "Installation Failed",
                        f"Failed to install ExifTool: {str(e)}\n\nPlease install it manually."
                    )
                    return False
            else:
                return False

    except Exception as e:
        progress_dialog.close()
        error_details = traceback.format_exc()
        print(f"ExifTool installation error: {str(e)}")
        print(f"Error details:\n{error_details}")
        QMessageBox.critical(
            None,
            "Installation Error",
            f"An error occurred during ExifTool installation:\n\n{str(e)}\n\n"
            "Please install it manually from https://exiftool.org/"
        )
        return False

def main():
    """Main entry point for the application."""
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Metadata Genie AI")

    # Create splash screen
    splash_pixmap = QPixmap(400, 200)
    splash_pixmap.fill(Qt.white)
    splash = QSplashScreen(splash_pixmap)
    splash.showMessage("Loading Metadata Genie AI...", Qt.AlignCenter | Qt.AlignBottom)
    splash.show()
    app.processEvents()

    # Check dependencies
    if not check_dependencies():
        splash.close()
        return 1

    # Check ExifTool
    if not check_exiftool():
        splash.close()
        return 1

    try:
        # Import main window after dependencies are checked
        from metadata_genie.gui.main_window import MainWindow
        from metadata_genie.utils.theme import apply_theme
        from metadata_genie.utils.logger import logger

        # Apply theme before creating the main window
        try:
            apply_theme()
        except Exception as e:
            logger.error(f"Error applying theme: {str(e)}")
            # Continue with default theme if there's an error

        # Create main window
        window = MainWindow()
        window.show()

        # Close splash screen
        splash.finish(window)

        # Run application
        return app.exec_()
    except Exception as e:
        splash.close()
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "Error",
            f"An error occurred while starting the application:\n\n{str(e)}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
