import os
import subprocess
import json
import re
import shutil
from metadata_genie.utils.logger import logger
from metadata_genie.utils.config import config

class IPTCHandler:
    """
    Handler for IPTC metadata operations using ExifTool.
    Provides methods for reading, writing, and extracting metadata.
    """
    # IPTC field mappings
    IPTC_FIELDS = {
        'description': 'ObjectDescription',
        'caption': 'Caption-Abstract',
        'headline': 'Headline',
        'keywords': 'Keywords',
        'copyright': 'CopyrightNotice',
        'author': 'By-line',
        'credit': 'Credit',
        'source': 'Source',
        'city': 'City',
        'country': 'Country-PrimaryLocationName',
        'instructions': 'SpecialInstructions',
        'category': 'Category',
        'supplemental_categories': 'SupplementalCategories',
        'date_created': 'DateCreated',
        'urgency': 'Urgency'
    }
    
    # Common stop words for keyword extraction
    STOP_WORDS = {
        'a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 
        'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'of', 'from',
        'as', 'this', 'that', 'these', 'those', 'it', 'its', 'it\'s', 'they',
        'them', 'their', 'has', 'have', 'had', 'been', 'be', 'being', 'would',
        'could', 'should', 'can', 'may', 'might', 'must', 'shall', 'will',
        'i', 'you', 'he', 'she', 'we', 'who', 'what', 'where', 'when', 'why',
        'how', 'which', 'there', 'here', 'some', 'any', 'all', 'no', 'not',
        'very', 'just', 'more', 'most', 'other', 'such', 'only', 'than',
        'then', 'now', 'ever', 'too', 'also', 'even', 'back', 'up', 'down',
        'out', 'over', 'under', 'again', 'still', 'while', 'so', 'both'
    }
    
    def __init__(self):
        """Initialize the IPTC handler."""
        self.exiftool_path = self._find_exiftool()
        self.min_keyword_length = config.get("min_keyword_length", 3)
        self.max_keywords = config.get("max_keywords", 10)
    
    def _find_exiftool(self):
        """
        Find ExifTool executable in PATH or current directory.
        
        Returns:
            str or None: Path to ExifTool executable or None if not found
        """
        # Define the executable name based on OS
        exiftool_cmd = 'exiftool.exe' if os.name == 'nt' else 'exiftool'
        
        # Try to find exiftool in PATH
        try:
            path_exiftool = shutil.which(exiftool_cmd)
            if path_exiftool:
                result = subprocess.run(
                    [path_exiftool, '-ver'],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if result.returncode == 0:
                    logger.info(f"ExifTool found in PATH: version {result.stdout.strip()}")
                    return path_exiftool
        except Exception as e:
            logger.warning(f"Error checking ExifTool in PATH: {str(e)}")
        
        # Check if exiftool is in the current directory
        try:
            local_path = exiftool_cmd  # Use the same name based on OS
            
            if os.path.exists(local_path):
                local_exiftool = os.path.abspath(local_path)
                result = subprocess.run(
                    [local_exiftool, '-ver'],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if result.returncode == 0:
                    logger.info(f"ExifTool found in current directory: version {result.stdout.strip()}")
                    return local_exiftool
        except Exception as e:
            logger.warning(f"Error checking local ExifTool: {str(e)}")
        
        # ExifTool not found, provide instructions
        logger.error("ExifTool not found. Please install ExifTool and make sure it's in your PATH.")
        logger.info("You can download ExifTool from: https://exiftool.org/")
        return None
    
    def read_metadata(self, image_path):
        """
        Read IPTC metadata from an image.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            dict: Dictionary containing IPTC metadata
        """
        if not self.exiftool_path:
            logger.error("ExifTool not available. Cannot read metadata.")
            return {}
        
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return {}
        
        try:
            # Run exiftool to get metadata in JSON format
            # Get both IPTC and XMP metadata to ensure we capture everything
            result = subprocess.run(
                [self.exiftool_path, '-j', '-IPTC:all', '-XMP:all', image_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                logger.error(f"Error reading metadata: {result.stderr}")
                return {}
            
            # Parse JSON output
            try:
                metadata_json = json.loads(result.stdout)
                if not metadata_json or len(metadata_json) == 0:
                    logger.warning(f"No metadata found in {image_path}")
                    return {}
                
                raw_metadata = metadata_json[0]
                
                # Extract and normalize metadata
                normalized_metadata = {}
                
                # Process IPTC fields
                for our_key, iptc_key in self.IPTC_FIELDS.items():
                    # Check both IPTC and XMP namespaces
                    iptc_value = raw_metadata.get(f"IPTC:{iptc_key}")
                    xmp_value = raw_metadata.get(f"XMP:{iptc_key}")
                    
                    # Use the first non-empty value found
                    if iptc_value:
                        normalized_metadata[our_key] = iptc_value
                    elif xmp_value:
                        normalized_metadata[our_key] = xmp_value
                
                # Special handling for keywords which might be a list or a single value
                if 'keywords' in normalized_metadata:
                    keywords = normalized_metadata['keywords']
                    if not isinstance(keywords, list):
                        normalized_metadata['keywords'] = [keywords]
                
                return normalized_metadata
                
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing metadata JSON: {str(e)}")
                return {}
            
        except Exception as e:
            logger.error(f"Error reading metadata: {str(e)}")
            return {}
    
    def write_metadata(self, image_path, metadata):
        """
        Write IPTC metadata to an image.
        
        Args:
            image_path (str): Path to the image file
            metadata (dict): Dictionary containing metadata to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.exiftool_path:
            logger.error("ExifTool not available. Cannot write metadata.")
            return False
        
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return False
        
        # Create a backup of the original image
        try:
            backup_path = f"{image_path}.original"
            if not os.path.exists(backup_path):
                shutil.copy2(image_path, backup_path)
                logger.debug(f"Created backup of original image: {backup_path}")
        except Exception as e:
            logger.warning(f"Could not create backup of original image: {str(e)}")
        
        try:
            # Build command arguments
            args = [self.exiftool_path]
            
            # Add metadata fields
            for field, value in metadata.items():
                if field in self.IPTC_FIELDS and value is not None:
                    iptc_field = self.IPTC_FIELDS[field]
                    
                    # Handle different field types
                    if field == 'keywords' and isinstance(value, list):
                        # Clear existing keywords first
                        args.append('-IPTC:Keywords=')
                        
                        # Add each keyword
                        for keyword in value:
                            if keyword and keyword.strip():
                                # Sanitize the keyword
                                safe_keyword = self._sanitize_field_value(keyword)
                                args.append(f'-IPTC:Keywords+={safe_keyword}')
                    else:
                        # Handle single-value fields
                        if value and str(value).strip():
                            # Sanitize the value
                            safe_value = self._sanitize_field_value(value)
                            args.append(f'-IPTC:{iptc_field}={safe_value}')
            
            # Add image path and overwrite original
            args.extend(['-overwrite_original', image_path])
            
            # Run exiftool
            logger.debug(f"Running ExifTool with args: {args}")
            result = subprocess.run(
                args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                logger.error(f"Error writing metadata: {result.stderr}")
                return False
            
            logger.info(f"Metadata written to {image_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error writing metadata: {str(e)}")
            return False
    
    def _sanitize_field_value(self, value):
        """
        Sanitize a field value to prevent command injection.
        
        Args:
            value: The value to sanitize
            
        Returns:
            str: Sanitized value
        """
        # Convert to string if not already
        if not isinstance(value, str):
            value = str(value)
        
        # Remove any characters that could cause issues with ExifTool
        # This is a basic sanitization - adjust as needed
        value = value.replace('=', ' ')
        value = value.replace('\n', ' ')
        value = value.replace('\r', ' ')
        value = value.replace('\t', ' ')
        
        # Escape quotes
        value = value.replace('"', '\\"')
        value = value.replace("'", "\\'")
        
        return value
    
    def extract_keywords(self, description, max_keywords=None):
        """
        Extract keywords from a description.
        
        Args:
            description (str): Text description to extract keywords from
            max_keywords (int, optional): Maximum number of keywords to return
            
        Returns:
            list: List of extracted keywords
        """
        if not description:
            return []
        
        # Use config values if not specified
        if max_keywords is None:
            max_keywords = self.max_keywords
        
        min_length = self.min_keyword_length
        
        # Clean the description
        description = description.lower()
        
        # Remove punctuation and replace with spaces
        description = re.sub(r'[^\w\s]', ' ', description)
        
        # Split description into words
        words = description.split()
        
        # Remove stop words and short words
        keywords = [
            word for word in words 
            if word not in self.STOP_WORDS and len(word) >= min_length
        ]
        
        # Count word frequency
        word_counts = {}
        for word in keywords:
            if word in word_counts:
                word_counts[word] += 1
            else:
                word_counts[word] = 1
        
        # Sort by frequency (most frequent first)
        sorted_keywords = sorted(word_counts.keys(), key=lambda k: word_counts[k], reverse=True)
        
        # Limit to max_keywords
        return sorted_keywords[:max_keywords]
    
    def backup_image(self, image_path):
        """
        Create a backup of an image file.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            str or None: Path to the backup file or None if backup failed
        """
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return None
        
        try:
            backup_path = f"{image_path}.backup"
            shutil.copy2(image_path, backup_path)
            logger.info(f"Created backup of image: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            return None
    
    def restore_image(self, image_path):
        """
        Restore an image from its backup.
        
        Args:
            image_path (str): Path to the original image file
            
        Returns:
            bool: True if successful, False otherwise
        """
        backup_path = f"{image_path}.backup"
        if not os.path.exists(backup_path):
            logger.error(f"Backup file not found: {backup_path}")
            return False
        
        try:
            shutil.copy2(backup_path, image_path)
            logger.info(f"Restored image from backup: {image_path}")
            return True
        except Exception as e:
            logger.error(f"Error restoring from backup: {str(e)}")
            return False
