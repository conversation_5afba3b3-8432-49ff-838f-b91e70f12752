"""
Custom UI widgets for the Metadata Genie application.
Provides modern, card-based UI components inspired by the domotic assistant design.
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QPainter, QPainterPath, QColor, QFont


class CardWidget(QFrame):
    """
    A modern card-style widget with rounded corners and shadow effects.
    Inspired by the domotic assistant interface design.
    """

    clicked = pyqtSignal()

    def __init__(self, title="", subtitle="", icon=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.is_active = False
        self.is_hovered = False

        self.setup_ui()
        self.setup_style()
        self.setup_animations()

    def setup_ui(self):
        """Set up the card UI layout."""
        self.setFixedSize(180, 120)
        self.setFrameStyle(QFrame.NoFrame)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)

        # Icon area (if provided)
        if self.icon:
            self.icon_label = QLabel()
            self.icon_label.setAlignment(Qt.AlignCenter)
            self.icon_label.setFixedSize(32, 32)
            # Set icon here if it's a QPixmap or QIcon
            layout.addWidget(self.icon_label)

        # Title
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setWordWrap(True)
        font = QFont()
        font.setPointSize(11)
        font.setWeight(QFont.Bold)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)

        # Subtitle
        if self.subtitle:
            self.subtitle_label = QLabel(self.subtitle)
            self.subtitle_label.setAlignment(Qt.AlignCenter)
            self.subtitle_label.setWordWrap(True)
            font = QFont()
            font.setPointSize(9)
            self.subtitle_label.setFont(font)
            layout.addWidget(self.subtitle_label)

        layout.addStretch()

    def setup_style(self):
        """Apply the card styling."""
        self.setStyleSheet("""
            CardWidget {
                background-color: #253447;
                border: 2px solid #2d3e50;
                border-radius: 12px;
            }

            CardWidget:hover {
                background-color: #2c3e50;
                border-color: #5dade2;
            }

            QLabel {
                color: #e8eaed;
                background-color: transparent;
                border: none;
            }
        """)

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)

    def setup_animations(self):
        """Set up hover animations."""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

    def set_active(self, active):
        """Set the card's active state."""
        self.is_active = active
        if active:
            self.setStyleSheet("""
                CardWidget {
                    background-color: #34495e;
                    border: 2px solid #5dade2;
                    border-radius: 12px;
                }

                QLabel {
                    color: #5dade2;
                    background-color: transparent;
                    border: none;
                }
            """)
        else:
            self.setup_style()

    def enterEvent(self, event):
        """Handle mouse enter event."""
        self.is_hovered = True
        if not self.is_active:
            # Slight scale animation on hover
            current_rect = self.geometry()
            new_rect = QRect(
                current_rect.x() - 2,
                current_rect.y() - 2,
                current_rect.width() + 4,
                current_rect.height() + 4
            )
            self.animation.setStartValue(current_rect)
            self.animation.setEndValue(new_rect)
            self.animation.start()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event."""
        self.is_hovered = False
        if not self.is_active:
            # Return to original size
            current_rect = self.geometry()
            original_rect = QRect(
                current_rect.x() + 2,
                current_rect.y() + 2,
                current_rect.width() - 4,
                current_rect.height() - 4
            )
            self.animation.setStartValue(current_rect)
            self.animation.setEndValue(original_rect)
            self.animation.start()
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press event."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class StatusCard(CardWidget):
    """
    A specialized card widget for displaying status information.
    """

    def __init__(self, title="", value="", unit="", status="normal", parent=None):
        self.value = value
        self.unit = unit
        self.status = status
        super().__init__(title, "", None, parent)

    def setup_ui(self):
        """Set up the status card UI layout."""
        self.setFixedSize(160, 100)
        self.setFrameStyle(QFrame.NoFrame)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(4)

        # Title
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(9)
        font.setWeight(QFont.Medium)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)

        # Value and unit
        value_layout = QHBoxLayout()
        value_layout.setAlignment(Qt.AlignCenter)

        self.value_label = QLabel(self.value)
        font = QFont()
        font.setPointSize(16)
        font.setWeight(QFont.Bold)
        self.value_label.setFont(font)
        value_layout.addWidget(self.value_label)

        if self.unit:
            self.unit_label = QLabel(self.unit)
            font = QFont()
            font.setPointSize(10)
            self.unit_label.setFont(font)
            value_layout.addWidget(self.unit_label)

        layout.addLayout(value_layout)
        layout.addStretch()

    def update_value(self, value, unit=""):
        """Update the displayed value."""
        self.value = value
        self.unit = unit
        self.value_label.setText(str(value))
        if hasattr(self, 'unit_label') and unit:
            self.unit_label.setText(unit)

    def set_status(self, status):
        """Set the status color (normal, warning, error, success)."""
        self.status = status
        status_colors = {
            "normal": "#5dade2",
            "success": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c"
        }

        color = status_colors.get(status, "#5dade2")
        self.value_label.setStyleSheet(f"color: {color};")


class ActionButton(QPushButton):
    """
    A modern action button with hover effects.
    """

    def __init__(self, text="", icon=None, primary=True, parent=None):
        super().__init__(text, parent)
        self.primary = primary
        self.setup_style()

    def setup_style(self):
        """Apply button styling based on type."""
        if self.primary:
            self.setStyleSheet("""
                ActionButton {
                    background-color: #5dade2;
                    color: #1e2a3a;
                    border: 2px solid #5dade2;
                    border-radius: 12px;
                    padding: 12px 24px;
                    font-weight: 600;
                    font-size: 11pt;
                    min-height: 20px;
                }

                ActionButton:hover {
                    background-color: #3498db;
                    border-color: #3498db;
                }

                ActionButton:pressed {
                    background-color: #2980b9;
                    border-color: #2980b9;
                }
            """)
        else:
            self.setStyleSheet("""
                ActionButton {
                    background-color: transparent;
                    color: #5dade2;
                    border: 2px solid #5dade2;
                    border-radius: 12px;
                    padding: 12px 24px;
                    font-weight: 500;
                    font-size: 11pt;
                    min-height: 20px;
                }

                ActionButton:hover {
                    background-color: #5dade2;
                    color: #1e2a3a;
                }

                ActionButton:pressed {
                    background-color: #3498db;
                    border-color: #3498db;
                }
            """)
