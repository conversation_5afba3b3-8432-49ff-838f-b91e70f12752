import os
import time
import traceback
from PIL import Image, UnidentifiedImageError
import torch
from metadata_genie.utils.logger import logger
from metadata_genie.utils.config import config
from metadata_genie.models.model_manager import ModelManager
from metadata_genie.metadata.iptc_handler import IPTCHandler

class ImageAnalyzer:
    """
    Analyzes images using vision models and generates descriptions.
    Handles image processing, object detection, and metadata generation.
    """
    def __init__(self):
        """Initialize the image analyzer with models and handlers."""
        self.model_manager = ModelManager()
        self.iptc_handler = IPTCHandler()

        # Get configuration values
        self.max_image_size = config.get("max_image_size", 4000)
        self.confidence_threshold = config.get("confidence_threshold", 0.5)
        self.max_description_length = config.get("max_description_length", 500)
        self.headline_max_length = config.get("headline_max_length", 50)

        # Performance tracking
        self.processing_times = {
            'description': [],
            'object_detection': [],
            'metadata_extraction': [],
            'total': []
        }

        # Load models
        self.model_manager.load_models()

        # Track initialization status
        self.initialized = self.model_manager.vision_model is not None

    def analyze_image(self, image_path):
        """
        Analyze an image and return a detailed description.

        Args:
            image_path (str): Path to the image file

        Returns:
            dict: Dictionary containing analysis results or None if analysis failed
        """
        start_time = time.time()

        try:
            # Check if file exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return None

            # Check if file is a valid image
            try:
                # Open image
                image = Image.open(image_path)

                # Check image format
                if image.format not in ['JPEG', 'PNG', 'BMP', 'GIF', 'TIFF']:
                    logger.warning(f"Unsupported image format: {image.format}")

                # Resize large images to prevent memory issues
                image = self._resize_image_if_needed(image)

            except UnidentifiedImageError:
                logger.error(f"Not a valid image file: {image_path}")
                return None
            except Exception as e:
                logger.error(f"Error opening image: {str(e)}")
                return None

            # Get detailed description using vision model
            description_start = time.time()
            description = self._generate_description(image)
            description_time = time.time() - description_start
            self.processing_times['description'].append(description_time)
            logger.debug(f"Description generation took {description_time:.2f} seconds")

            # Get object detection results
            detection_start = time.time()
            objects = self._detect_objects(image_path)
            detection_time = time.time() - detection_start
            self.processing_times['object_detection'].append(detection_time)
            logger.debug(f"Object detection took {detection_time:.2f} seconds")

            # Extract keywords and create headline
            metadata_start = time.time()
            keywords = self.iptc_handler.extract_keywords(description)

            # Add detected objects to keywords if they're not already included
            for obj in objects:
                if obj.lower() not in [k.lower() for k in keywords]:
                    keywords.append(obj)

            # Limit keywords to max_keywords
            max_keywords = config.get("max_keywords", 10)
            keywords = keywords[:max_keywords]

            # Create headline from description
            headline = self._create_headline(description, self.headline_max_length)

            metadata_time = time.time() - metadata_start
            self.processing_times['metadata_extraction'].append(metadata_time)
            logger.debug(f"Metadata extraction took {metadata_time:.2f} seconds")

            # Return results
            results = {
                'description': description,
                'caption': description,  # Use same text for caption
                'headline': headline,
                'keywords': keywords,
                'objects': objects
            }

            # Add existing metadata if available
            existing_metadata = self.iptc_handler.read_metadata(image_path)
            if existing_metadata:
                # Only add fields that don't exist in our results
                for key, value in existing_metadata.items():
                    if key not in results and value:
                        results[key] = value

            total_time = time.time() - start_time
            self.processing_times['total'].append(total_time)
            logger.info(f"Total analysis time for {os.path.basename(image_path)}: {total_time:.2f} seconds")

            return results

        except Exception as e:
            logger.error(f"Error analyzing image: {str(e)}")
            logger.debug(traceback.format_exc())
            total_time = time.time() - start_time
            self.processing_times['total'].append(total_time)
            return None

    def _resize_image_if_needed(self, image):
        """
        Resize an image if it exceeds the maximum dimensions.

        Args:
            image (PIL.Image): The image to resize

        Returns:
            PIL.Image: The resized image or the original if no resize was needed
        """
        max_size = self.max_image_size

        # Check if image needs resizing
        width, height = image.size
        if width > max_size or height > max_size:
            # Calculate new dimensions while preserving aspect ratio
            if width > height:
                new_width = max_size
                new_height = int(height * (max_size / width))
            else:
                new_height = max_size
                new_width = int(width * (max_size / height))

            # Resize the image
            logger.info(f"Resizing image from {width}x{height} to {new_width}x{new_height}")
            return image.resize((new_width, new_height), Image.LANCZOS)

        return image

    def _generate_description(self, image):
        """
        Generate a detailed description of the image using the vision model.

        Args:
            image (PIL.Image): The image to describe

        Returns:
            str: Generated description or error message
        """
        try:
            if self.model_manager.vision_model is None or self.model_manager.vision_processor is None:
                logger.error("Vision model not loaded")
                return "No description available (model not loaded)"

            # Prepare image for the model
            inputs = self.model_manager.vision_processor(images=image, return_tensors="pt")

            # Generate description
            with torch.no_grad():
                generated_ids = self.model_manager.vision_model.generate(
                    pixel_values=inputs.pixel_values,
                    max_length=self.max_description_length // 4,  # Tokens are roughly 1/4 of characters
                    num_beams=5,
                    early_stopping=True
                )

            # Decode the generated text
            generated_text = self.model_manager.vision_processor.batch_decode(
                generated_ids, skip_special_tokens=True
            )[0].strip()

            # Truncate if too long
            if len(generated_text) > self.max_description_length:
                generated_text = generated_text[:self.max_description_length-3] + "..."

            return generated_text

        except torch.cuda.OutOfMemoryError:
            logger.error("CUDA out of memory error during description generation")
            return "No description available (GPU memory error)"
        except Exception as e:
            logger.error(f"Error generating description: {str(e)}")
            logger.debug(traceback.format_exc())
            return "No description available (error occurred)"

    def _detect_objects(self, image_path):
        """
        Detect objects in the image using the object detection model.

        Args:
            image_path (str): Path to the image file

        Returns:
            list: List of detected object names
        """
        try:
            if self.model_manager.object_detection_model is None:
                logger.error("Object detection model not loaded")
                return []

            # Make sure we're using CPU mode and PyTorch format
            try:
                # Set environment variables to ensure PyTorch format and CPU usage
                os.environ["CUDA_VISIBLE_DEVICES"] = "-1"  # Disable CUDA
                os.environ["ULTRALYTICS_ONNX_EXPORT"] = "0"  # Disable ONNX
                os.environ["ULTRALYTICS_FORMAT"] = "torch"  # Use PyTorch format

                # Run object detection with explicit CPU device
                with torch.no_grad():
                    results = self.model_manager.object_detection_model(
                        image_path,
                        device='cpu',
                        conf=self.confidence_threshold  # Use configured confidence threshold
                    )

                # Extract detected objects
                objects = []
                for result in results:
                    # Check if boxes attribute exists and is not empty
                    if hasattr(result, 'boxes') and len(result.boxes) > 0:
                        for i, box in enumerate(result.boxes):
                            # Safely extract class ID
                            if hasattr(box, 'cls') and len(box.cls) > 0:
                                class_id = int(box.cls[0].item())
                                # Safely get class name
                                if hasattr(result, 'names') and class_id in result.names:
                                    class_name = result.names[class_id]
                                    # Safely get confidence
                                    if hasattr(box, 'conf') and len(box.conf) > 0:
                                        confidence = box.conf[0].item()

                                        if confidence > self.confidence_threshold:
                                            objects.append(class_name)

                # Remove duplicates while preserving order
                seen = set()
                unique_objects = [obj for obj in objects if not (obj in seen or seen.add(obj))]

                return unique_objects

            except RuntimeError as e:
                error_msg = str(e)
                if "CUDA" in error_msg:
                    logger.error(f"CUDA error in object detection: {error_msg}")
                    return ["Object detection failed - CUDA error"]
                elif "ONNX" in error_msg or "onnx" in error_msg:
                    logger.error(f"ONNX error in object detection: {error_msg}")
                    return ["Object detection failed - ONNX error"]
                else:
                    logger.error(f"Runtime error in object detection: {error_msg}")
                    return ["Object detection failed - runtime error"]

        except Exception as e:
            logger.error(f"Error detecting objects: {str(e)}")
            logger.debug(traceback.format_exc())
            # Return a meaningful fallback
            return ["Object detection unavailable"]

    def _create_headline(self, description, max_length=50):
        """
        Create a headline from the description.

        Args:
            description (str): The description to create a headline from
            max_length (int): Maximum length of the headline

        Returns:
            str: Generated headline
        """
        if not description:
            return "No headline available"

        # Use the first sentence or truncate to max_length
        sentences = description.split('.')
        headline = sentences[0].strip()

        if len(headline) > max_length:
            headline = headline[:max_length-3] + "..."

        return headline

    def update_metadata(self, image_path, metadata):
        """
        Update the metadata of an image.

        Args:
            image_path (str): Path to the image file
            metadata (dict): Metadata to write

        Returns:
            bool: True if successful, False otherwise
        """
        # Create a backup of the image before modifying
        backup_path = self.iptc_handler.backup_image(image_path)
        if not backup_path:
            logger.warning(f"Could not create backup for {image_path}")

        # Update metadata
        success = self.iptc_handler.write_metadata(image_path, metadata)

        if success:
            logger.info(f"Metadata updated for {os.path.basename(image_path)}")
        else:
            logger.error(f"Failed to update metadata for {os.path.basename(image_path)}")

            # Try to restore from backup if update failed
            if backup_path and os.path.exists(backup_path):
                logger.info(f"Attempting to restore from backup")
                if self.iptc_handler.restore_image(image_path):
                    logger.info(f"Image restored from backup")
                else:
                    logger.error(f"Failed to restore image from backup")

        return success

    def process_image(self, image_path):
        """
        Process an image: analyze it and update its metadata.

        Args:
            image_path (str): Path to the image file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return False

            # Analyze image
            logger.info(f"Analyzing image: {os.path.basename(image_path)}")
            analysis_results = self.analyze_image(image_path)

            if analysis_results is None:
                logger.error(f"Failed to analyze image: {image_path}")
                return False

            # Update metadata
            logger.info(f"Updating metadata for: {os.path.basename(image_path)}")
            success = self.update_metadata(image_path, analysis_results)

            if success:
                logger.info(f"Successfully processed image: {os.path.basename(image_path)}")
            else:
                logger.error(f"Failed to update metadata for image: {os.path.basename(image_path)}")

            return success

        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            logger.debug(traceback.format_exc())
            return False

    def get_performance_stats(self):
        """
        Get performance statistics for image processing.

        Returns:
            dict: Dictionary containing performance statistics
        """
        stats = {}

        for key, times in self.processing_times.items():
            if times:
                stats[key] = {
                    'avg': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
            else:
                stats[key] = {
                    'avg': 0,
                    'min': 0,
                    'max': 0,
                    'count': 0
                }

        return stats
