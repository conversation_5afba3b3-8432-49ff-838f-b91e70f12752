import os
import json
import shutil
from metadata_genie.utils.logger import logger

class Config:
    """
    Configuration class for the Metadata Genie AI application.
    Handles loading, saving, and validating configuration settings.
    """
    DEFAULT_CONFIG = {
        # Model settings
        "models_dir": "models",
        "vision_model": "microsoft/git-base",
        "object_detection_model": "yolov8n.pt",

        # Processing settings
        "max_batch_size": 10,
        "max_image_size": 4000,  # Maximum dimension for image processing
        "thumbnail_size": 100,   # Size for thumbnails in the UI
        "confidence_threshold": 0.5,  # Threshold for object detection confidence

        # File settings
        "supported_extensions": [".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff"],
        "temp_dir": "temp",

        # IPTC metadata settings
        "iptc_fields": {
            "description": True,
            "caption": True,
            "keywords": True,
            "headline": True
        },

        # Keyword extraction settings
        "max_keywords": 10,
        "min_keyword_length": 3,

        # UI settings
        "ui_theme": "dark",  # Options: "light", "dark", "system" - Default to dark mode
        "splitter_sizes": [400, 600],  # Default sizes for the main splitter

        # Application settings
        "first_run": True,
        "check_for_updates": True,
        "log_level": "INFO"  # Options: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
    }

    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self._validate_config()
        self._ensure_directories()

    def load_config(self):
        """Load configuration from file or create default if it doesn't exist."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_file}")

                # Merge with default config to ensure all keys exist
                merged_config = self.DEFAULT_CONFIG.copy()
                self._deep_update(merged_config, config)
                return merged_config

            except Exception as e:
                logger.error(f"Error loading configuration: {str(e)}")
                logger.info("Using default configuration")

                # Backup corrupted config file
                if os.path.exists(self.config_file):
                    backup_file = f"{self.config_file}.bak"
                    try:
                        shutil.copy2(self.config_file, backup_file)
                        logger.info(f"Backed up corrupted config file to {backup_file}")
                    except Exception as backup_error:
                        logger.error(f"Failed to backup corrupted config file: {str(backup_error)}")

                return self.DEFAULT_CONFIG.copy()
        else:
            logger.info(f"Configuration file {self.config_file} not found. Using default configuration.")
            return self.DEFAULT_CONFIG.copy()

    def _deep_update(self, target, source):
        """Recursively update a dictionary with another dictionary."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_update(target[key], value)
            else:
                target[key] = value

    def _validate_config(self):
        """Validate configuration values and set defaults for invalid values."""
        # Validate numeric values
        numeric_keys = ["max_batch_size", "max_image_size", "thumbnail_size",
                        "confidence_threshold", "max_keywords", "min_keyword_length"]

        for key in numeric_keys:
            if key in self.config:
                try:
                    self.config[key] = float(self.config[key])
                    # Convert to int if it should be an integer
                    if key not in ["confidence_threshold"]:
                        self.config[key] = int(self.config[key])
                except (ValueError, TypeError):
                    logger.warning(f"Invalid value for {key}, using default: {self.DEFAULT_CONFIG[key]}")
                    self.config[key] = self.DEFAULT_CONFIG[key]

        # Validate supported extensions
        if "supported_extensions" in self.config:
            if not isinstance(self.config["supported_extensions"], list):
                logger.warning(f"Invalid value for supported_extensions, using default")
                self.config["supported_extensions"] = self.DEFAULT_CONFIG["supported_extensions"]
            else:
                # Ensure all extensions start with a dot
                self.config["supported_extensions"] = [
                    ext if ext.startswith('.') else f'.{ext}'
                    for ext in self.config["supported_extensions"]
                ]

        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.config.get("log_level") not in valid_log_levels:
            logger.warning(f"Invalid log level: {self.config.get('log_level')}, using INFO")
            self.config["log_level"] = "INFO"

        # Validate UI theme
        valid_themes = ["light", "dark", "system"]
        if self.config.get("ui_theme") not in valid_themes:
            logger.warning(f"Invalid UI theme: {self.config.get('ui_theme')}, using system")
            self.config["ui_theme"] = "system"

    def _ensure_directories(self):
        """Ensure that required directories exist."""
        directories = ["models_dir", "temp_dir"]
        for dir_key in directories:
            if dir_key in self.config:
                dir_path = self.config[dir_key]
                if dir_path:
                    os.makedirs(dir_path, exist_ok=True)
                    logger.debug(f"Ensured directory exists: {dir_path}")

    def save_config(self):
        """Save current configuration to file."""
        try:
            # Create directory if it doesn't exist
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir)

            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False

    def get(self, key, default=None):
        """Get a configuration value."""
        return self.config.get(key, default)

    def set(self, key, value):
        """Set a configuration value and save the configuration."""
        self.config[key] = value
        return self.save_config()

    def update(self, config_dict):
        """Update multiple configuration values at once."""
        self._deep_update(self.config, config_dict)
        self._validate_config()
        return self.save_config()

    def reset_to_defaults(self):
        """Reset configuration to default values."""
        self.config = self.DEFAULT_CONFIG.copy()
        return self.save_config()

    def is_first_run(self):
        """Check if this is the first run of the application."""
        return self.get("first_run", True)

    def set_first_run_completed(self):
        """Mark the first run as completed."""
        self.set("first_run", False)

# Create a global config instance
config = Config()
