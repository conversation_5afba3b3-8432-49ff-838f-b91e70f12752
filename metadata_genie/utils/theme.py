"""
Theme management for the Metadata Genie AI application.
Provides functions to apply light and dark themes using Qt Style Sheets.
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt

from metadata_genie.utils.logger import logger
from metadata_genie.utils.config import config

# Define the modern light theme stylesheet
LIGHT_STYLESHEET = """
QWidget {
    background-color: #fafafa;
    color: #1a1a1a;
    font-family: 'Segoe UI', 'San Francisco', 'Helvetica Neue', Arial, sans-serif;
    font-size: 9pt;
}

QMainWindow, QDialog {
    background-color: #fafafa;
}

QTabWidget::pane {
    border: 1px solid #e0e0e0;
    background-color: #ffffff;
    border-radius: 8px;
    margin-top: -1px;
}

QTabBar::tab {
    background-color: #f5f5f5;
    color: #424242;
    padding: 10px 16px;
    border: 1px solid #e0e0e0;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-right: 2px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background-color: #eeeeee;
    color: #1976d2;
}

QPushButton {
    background-color: #ffffff;
    color: #1976d2;
    border: 2px solid #1976d2;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #1976d2;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

QPushButton:pressed {
    background-color: #1565c0;
    border-color: #1565c0;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #9e9e9e;
    border-color: #e0e0e0;
}

QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    color: #1a1a1a;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px 12px;
    selection-background-color: #bbdefb;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

QProgressBar {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f5f5f5;
    text-align: center;
    font-weight: 500;
    height: 20px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1976d2, stop:1 #42a5f5);
    border-radius: 6px;
    margin: 1px;
}

QListWidget, QTreeWidget, QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #fafafa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
}

QListWidget::item {
    padding: 8px;
    border-radius: 4px;
    margin: 2px;
}

QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QListWidget::item:hover {
    background-color: #f5f5f5;
}

QHeaderView::section {
    background-color: #f5f5f5;
    color: #424242;
    padding: 8px;
    border: 1px solid #e0e0e0;
    font-weight: 600;
}

QScrollBar:vertical {
    border: none;
    background: #f5f5f5;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #bdbdbd;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: #9e9e9e;
}

QScrollBar:horizontal {
    border: none;
    background: #f5f5f5;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #bdbdbd;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: #9e9e9e;
}

QScrollBar::add-line, QScrollBar::sub-line {
    border: none;
    background: none;
}

QMenuBar {
    background-color: #fafafa;
    color: #1a1a1a;
    border-bottom: 1px solid #e0e0e0;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenu {
    background-color: #ffffff;
    color: #1a1a1a;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QStatusBar {
    background-color: #fafafa;
    color: #424242;
    border-top: 1px solid #e0e0e0;
    padding: 4px;
}

QGroupBox {
    font-weight: 600;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 8px;
    padding-top: 8px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    color: #1976d2;
}

QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdbdbd;
    border-radius: 4px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
}

QCheckBox::indicator:hover {
    border-color: #1976d2;
}
"""

# Define the modern dark blue theme stylesheet (inspired by domotic assistant design)
DARK_STYLESHEET = """
QWidget {
    background-color: #1e2a3a;
    color: #e8eaed;
    font-family: 'Segoe UI', 'San Francisco', 'Helvetica Neue', Arial, sans-serif;
    font-size: 9pt;
}

QMainWindow, QDialog {
    background-color: #1e2a3a;
}

QTabWidget::pane {
    border: 1px solid #2d3e50;
    background-color: #253447;
    border-radius: 12px;
    margin-top: -1px;
}

QTabBar::tab {
    background-color: #253447;
    color: #8a9ba8;
    padding: 12px 20px;
    border: 1px solid #2d3e50;
    border-bottom: none;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    margin-right: 3px;
    font-weight: 500;
    font-size: 10pt;
}

QTabBar::tab:selected {
    background-color: #34495e;
    color: #5dade2;
    border-bottom: 3px solid #5dade2;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background-color: #2c3e50;
    color: #5dade2;
}

QPushButton {
    background-color: #34495e;
    color: #e8eaed;
    border: 2px solid #34495e;
    border-radius: 12px;
    padding: 10px 20px;
    font-weight: 500;
    min-height: 24px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #5dade2;
    color: #1e2a3a;
    border-color: #5dade2;
}

QPushButton:pressed {
    background-color: #3498db;
    border-color: #3498db;
    color: #1e2a3a;
}

QPushButton:disabled {
    background-color: #2c3e50;
    color: #5a6c7d;
    border-color: #2c3e50;
}

QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #253447;
    color: #e8eaed;
    border: 2px solid #2d3e50;
    border-radius: 10px;
    padding: 10px 12px;
    selection-background-color: #34495e;
    font-size: 10pt;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #5dade2;
    background-color: #2c3e50;
}

QProgressBar {
    border: 2px solid #2d3e50;
    border-radius: 10px;
    background-color: #253447;
    text-align: center;
    font-weight: 500;
    height: 24px;
    color: #e8eaed;
    font-size: 10pt;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #5dade2, stop:1 #3498db);
    border-radius: 8px;
    margin: 2px;
}

QListWidget, QTreeWidget, QTableWidget {
    background-color: #253447;
    alternate-background-color: #2c3e50;
    border: 2px solid #2d3e50;
    border-radius: 12px;
    selection-background-color: #34495e;
    selection-color: #5dade2;
    font-size: 10pt;
}

QListWidget::item {
    padding: 10px;
    border-radius: 8px;
    margin: 3px;
}

QListWidget::item:selected {
    background-color: #34495e;
    color: #5dade2;
}

QListWidget::item:hover {
    background-color: #2c3e50;
    color: #5dade2;
}

QHeaderView::section {
    background-color: #253447;
    color: #8a9ba8;
    padding: 10px;
    border: 1px solid #2d3e50;
    font-weight: 600;
    font-size: 10pt;
}

QScrollBar:vertical {
    border: none;
    background: #253447;
    width: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #34495e;
    border-radius: 7px;
    min-height: 24px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: #5dade2;
}

QScrollBar:horizontal {
    border: none;
    background: #253447;
    height: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #34495e;
    border-radius: 7px;
    min-width: 24px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: #5dade2;
}

QScrollBar::add-line, QScrollBar::sub-line {
    border: none;
    background: none;
}

QMenuBar {
    background-color: #1e2a3a;
    color: #e8eaed;
    border-bottom: 1px solid #2d3e50;
    padding: 6px;
    font-size: 10pt;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 8px;
}

QMenuBar::item:selected {
    background-color: #34495e;
    color: #5dade2;
}

QMenu {
    background-color: #253447;
    color: #e8eaed;
    border: 2px solid #2d3e50;
    border-radius: 12px;
    padding: 6px;
    font-size: 10pt;
}

QMenu::item {
    padding: 10px 20px;
    border-radius: 8px;
}

QMenu::item:selected {
    background-color: #34495e;
    color: #5dade2;
}

QStatusBar {
    background-color: #1e2a3a;
    color: #8a9ba8;
    border-top: 1px solid #2d3e50;
    padding: 6px;
    font-size: 10pt;
}

QGroupBox {
    font-weight: 600;
    border: 2px solid #2d3e50;
    border-radius: 12px;
    margin-top: 10px;
    padding-top: 10px;
    color: #e8eaed;
    font-size: 10pt;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 0 10px 0 10px;
    color: #5dade2;
    font-weight: 600;
}

QCheckBox {
    spacing: 10px;
    color: #e8eaed;
    font-size: 10pt;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #34495e;
    border-radius: 6px;
    background-color: #253447;
}

QCheckBox::indicator:checked {
    background-color: #5dade2;
    border-color: #5dade2;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
}

QCheckBox::indicator:hover {
    border-color: #5dade2;
}

QSplitter::handle {
    background-color: #2d3e50;
    border: 1px solid #34495e;
}

QSplitter::handle:horizontal {
    width: 4px;
}

QSplitter::handle:vertical {
    height: 4px;
}

QSplitter::handle:hover {
    background-color: #5dade2;
}

QComboBox {
    background-color: #253447;
    color: #e8eaed;
    border: 2px solid #2d3e50;
    border-radius: 10px;
    padding: 8px 16px;
    min-height: 24px;
    font-size: 10pt;
}

QComboBox:hover {
    border-color: #5dade2;
    background-color: #2c3e50;
}

QComboBox::drop-down {
    border: none;
    width: 24px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #5dade2;
    margin-right: 8px;
}

QComboBox QAbstractItemView {
    background-color: #253447;
    color: #e8eaed;
    border: 2px solid #2d3e50;
    border-radius: 10px;
    selection-background-color: #5dade2;
    selection-color: #1e2a3a;
    padding: 4px;
}
"""

def apply_theme(theme=None):
    """
    Apply the specified theme to the application.

    Args:
        theme (str, optional): The theme to apply. Options: "light", "dark", "system".
            If None, uses the theme from the configuration.
    """
    if theme is None:
        theme = config.get("ui_theme", "system")

    app = QApplication.instance()
    if not app:
        logger.warning("Cannot apply theme: No QApplication instance found")
        return

    logger.info(f"Applying theme: {theme}")

    if theme == "system":
        # Use system theme (default Qt style)
        app.setStyleSheet("")
        app.setPalette(app.style().standardPalette())
        logger.info("Applied system theme")
    elif theme == "light":
        # Apply light theme
        app.setStyleSheet(LIGHT_STYLESHEET)
        logger.info("Applied light theme")
    elif theme == "dark":
        # Apply dark theme
        app.setStyleSheet(DARK_STYLESHEET)
        logger.info("Applied dark theme")
    else:
        logger.warning(f"Unknown theme: {theme}, using system theme")
        app.setStyleSheet("")
        app.setPalette(app.style().standardPalette())

    # Save the theme setting if it's different from the current one
    if theme != config.get("ui_theme"):
        config.set("ui_theme", theme)

def get_current_theme():
    """
    Get the current theme from the configuration.

    Returns:
        str: The current theme ("light", "dark", or "system")
    """
    return config.get("ui_theme", "system")

def toggle_theme():
    """
    Toggle between light and dark themes.
    If the current theme is system or light, switches to dark.
    If the current theme is dark, switches to light.

    Returns:
        str: The new theme that was applied
    """
    try:
        current_theme = get_current_theme()

        new_theme = "light" if current_theme == "dark" else "dark"
        apply_theme(new_theme)

        return new_theme
    except Exception as e:
        logger.error(f"Error in toggle_theme: {str(e)}")
        # Return the current theme if there was an error
        return get_current_theme()
