import logging
import os
import threading
import queue
from datetime import datetime
import traceback

class ThreadSafeLogger:
    """
    Thread-safe logger class for the Metadata Genie AI application.
    Handles logging to both console and file with proper thread safety.
    """
    # Map string log levels to logging module constants
    LOG_LEVELS = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    def __init__(self, log_level="INFO", max_log_files=10):
        self.logger = logging.getLogger("MetadataGenieAI")
        self.logger.setLevel(self.LOG_LEVELS.get(log_level, logging.INFO))
        
        # Clear any existing handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Create logs directory if it doesn't exist
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # Create a unique log file name based on the current date and time
        self.log_file = os.path.join('logs', f"metadata_genie_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(self.logger.level)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.logger.level)
        
        # Create formatter and add it to the handlers
        formatter = logging.Formatter('%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add the handlers to the logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # Set up thread-safe logging
        self.log_queue = queue.Queue()
        self.log_thread = None
        self.stop_event = threading.Event()
        
        # Clean up old log files
        self._cleanup_old_logs(max_log_files)
        
        # Start the logging thread
        self._start_log_thread()
        
        # Log startup information
        self.info(f"Logger initialized with level {log_level}")
        self.info(f"Log file: {self.log_file}")
    
    def _cleanup_old_logs(self, max_files):
        """Clean up old log files, keeping only the most recent ones."""
        try:
            log_dir = 'logs'
            if not os.path.exists(log_dir):
                return
                
            log_files = [os.path.join(log_dir, f) for f in os.listdir(log_dir) 
                        if f.startswith('metadata_genie_') and f.endswith('.log')]
            
            # Sort by modification time (newest first)
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # Remove old files
            for old_file in log_files[max_files:]:
                try:
                    os.remove(old_file)
                    print(f"Removed old log file: {old_file}")
                except Exception as e:
                    print(f"Error removing old log file {old_file}: {str(e)}")
        except Exception as e:
            print(f"Error cleaning up log files: {str(e)}")
    
    def _start_log_thread(self):
        """Start the logging thread."""
        if self.log_thread is None or not self.log_thread.is_alive():
            self.stop_event.clear()
            self.log_thread = threading.Thread(target=self._log_worker, name="LogThread")
            self.log_thread.daemon = True
            self.log_thread.start()
    
    def _log_worker(self):
        """Worker thread that processes log messages from the queue."""
        while not self.stop_event.is_set():
            try:
                # Get a log message from the queue with a timeout
                record = self.log_queue.get(block=True, timeout=0.5)
                
                if record is None:  # Sentinel value to stop the thread
                    break
                    
                # Process the log record
                level, message, exc_info = record
                
                if level == "DEBUG":
                    self.logger.debug(message, exc_info=exc_info)
                elif level == "INFO":
                    self.logger.info(message, exc_info=exc_info)
                elif level == "WARNING":
                    self.logger.warning(message, exc_info=exc_info)
                elif level == "ERROR":
                    self.logger.error(message, exc_info=exc_info)
                elif level == "CRITICAL":
                    self.logger.critical(message, exc_info=exc_info)
                
                # Mark the task as done
                self.log_queue.task_done()
                
            except queue.Empty:
                # Timeout occurred, just continue the loop
                continue
            except Exception as e:
                # Log the error to stderr since we can't use the logger
                print(f"Error in log worker: {str(e)}")
                traceback.print_exc()
    
    def _enqueue_log(self, level, message, exc_info=None):
        """Add a log message to the queue."""
        try:
            self.log_queue.put((level, message, exc_info))
        except Exception as e:
            # If queueing fails, log directly
            print(f"Error queueing log message: {str(e)}")
            print(f"Original message: {level} - {message}")
    
    def set_level(self, level):
        """Set the log level."""
        if level in self.LOG_LEVELS:
            numeric_level = self.LOG_LEVELS[level]
            self.logger.setLevel(numeric_level)
            
            # Update handlers
            for handler in self.logger.handlers:
                handler.setLevel(numeric_level)
                
            self.info(f"Log level set to {level}")
            return True
        else:
            self.warning(f"Invalid log level: {level}")
            return False
    
    def info(self, message, exc_info=False):
        """Log an info message."""
        self._enqueue_log("INFO", message, exc_info)
    
    def warning(self, message, exc_info=False):
        """Log a warning message."""
        self._enqueue_log("WARNING", message, exc_info)
    
    def error(self, message, exc_info=True):
        """Log an error message."""
        self._enqueue_log("ERROR", message, exc_info)
    
    def debug(self, message, exc_info=False):
        """Log a debug message."""
        self._enqueue_log("DEBUG", message, exc_info)
    
    def critical(self, message, exc_info=True):
        """Log a critical message."""
        self._enqueue_log("CRITICAL", message, exc_info)
    
    def exception(self, message):
        """Log an exception message with traceback."""
        self._enqueue_log("ERROR", message, True)
    
    def shutdown(self):
        """Shutdown the logger and clean up resources."""
        if self.log_thread and self.log_thread.is_alive():
            self.info("Shutting down logger")
            self.stop_event.set()
            self.log_queue.put(None)  # Sentinel to stop the thread
            self.log_thread.join(timeout=2.0)  # Wait for thread to finish
            
            # Process any remaining log messages
            while not self.log_queue.empty():
                try:
                    record = self.log_queue.get_nowait()
                    if record is not None:
                        level, message, exc_info = record
                        if level == "DEBUG":
                            self.logger.debug(message, exc_info=exc_info)
                        elif level == "INFO":
                            self.logger.info(message, exc_info=exc_info)
                        elif level == "WARNING":
                            self.logger.warning(message, exc_info=exc_info)
                        elif level == "ERROR":
                            self.logger.error(message, exc_info=exc_info)
                        elif level == "CRITICAL":
                            self.logger.critical(message, exc_info=exc_info)
                    self.log_queue.task_done()
                except queue.Empty:
                    break
            
            # Flush and close handlers
            for handler in self.logger.handlers:
                handler.flush()
                handler.close()

# Create a global logger instance
logger = ThreadSafeLogger()
